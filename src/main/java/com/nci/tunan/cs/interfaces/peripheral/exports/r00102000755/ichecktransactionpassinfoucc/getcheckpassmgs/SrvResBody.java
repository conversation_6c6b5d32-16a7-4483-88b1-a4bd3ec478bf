package com.nci.tunan.cs.interfaces.peripheral.exports.r00102000755.ichecktransactionpassinfoucc.getcheckpassmgs;
import java.io.Serializable;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;
import com.nci.udmp.component.serviceinvoke.message.body.hd.BizHeader;
import com.nci.tunan.cs.interfaces.peripheral.exports.r00102000755.ichecktransactionpassinfoucc.getcheckpassmgs.SrvResBizBody;

@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "SrvResBody", propOrder = { "bizHeader", "bizBody" })
public class SrvResBody implements Serializable {
    /**
     * serialVersionUID 
     */
    private static final long serialVersionUID = 1L;
    
    @XmlElement(required = true)
    protected BizHeader bizHeader;
    
    @XmlElement(required = true)
    protected SrvResBizBody bizBody;
    
    public BizHeader getBizHeader() {
        return bizHeader;
    }
    public void setBizHeader(BizHeader bizHeader) {
        this.bizHeader = bizHeader;
    }
    public SrvResBizBody getBizBody() {
        return bizBody;
    }
    public void setBizBody(SrvResBizBody bizBody) {
        this.bizBody = bizBody;
    }
}


