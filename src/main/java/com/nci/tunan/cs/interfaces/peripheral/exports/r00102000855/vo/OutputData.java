package com.nci.tunan.cs.interfaces.peripheral.exports.r00102000855.vo;

import java.io.Serializable;
import java.util.List;

import javax.xml.bind.annotation.XmlElement;
//投连退保提交接口
public class OutputData implements Serializable{
	private Result result;
	@XmlElement(name="Result")
	public Result getResult() {
		return result;
	}

	public void setResult(Result result) {
		this.result = result;
	}
	
}
