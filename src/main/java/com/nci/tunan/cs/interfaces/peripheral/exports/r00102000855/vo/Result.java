package com.nci.tunan.cs.interfaces.peripheral.exports.r00102000855.vo;

import java.io.Serializable;

import javax.xml.bind.annotation.XmlElement;
//投连退保提交接口
public class Result implements Serializable{
//	受理号
	private String EdorAcceptNo;	
//	退保金额
	private String GetMoney;
//校验返回提示语
	private String MarkedWords;
	
	@XmlElement(name="MarkedWords")
	public String getMarkedWords() {
		return MarkedWords;
	}
	public void setMarkedWords(String markedWords) {
		MarkedWords = markedWords;
	}
	@XmlElement(name="EdorAcceptNo")
	public String getEdorAcceptNo() {
		return EdorAcceptNo;
	}
	public void setEdorAcceptNo(String edorAcceptNo) {
		EdorAcceptNo = edorAcceptNo;
	}
	@XmlElement(name="GetMoney")
	public String getGetMoney() {
		return GetMoney;
	}
	public void setGetMoney(String getMoney) {
		GetMoney = getMoney;
	}	

}
