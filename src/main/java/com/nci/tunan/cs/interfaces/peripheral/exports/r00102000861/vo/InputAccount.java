package com.nci.tunan.cs.interfaces.peripheral.exports.r00102000861.vo;

import java.io.Serializable;
import java.util.Date;
import javax.xml.bind.annotation.XmlElement;

public class InputAccount implements Serializable{

	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;
	
	private String ContNo;
	private String GetUnitCount;//申请领取单位数
	private String Operator;//申请人
	private String AppType;//申请方式
	private String EdorAppDate;//保全申请提交日期
	private String EdorType;//批改项目
	private String EdorItemAppDate;//批改项目保全申请提交日期
	
	@XmlElement(name = "ContNo") 
	public String getContNo() {
		return ContNo;
	}
	public void setContNo(String contNo) {
		ContNo = contNo;
	}
	
	@XmlElement(name = "GetUnitCount") 
	public String getGetUnitCount() {
		return GetUnitCount;
	}
	public void setGetUnitCount(String getUnitCount) {
		GetUnitCount = getUnitCount;
	}
	
	@XmlElement(name = "Operator") 
	public String getOperator() {
		return Operator;
	}
	public void setOperator(String operator) {
		Operator = operator;
	}
	
	@XmlElement(name = "AppType") 
	public String getAppType() {
		return AppType;
	}
	public void setAppType(String appType) {
		AppType = appType;
	}
	
	@XmlElement(name = "EdorAppDate") 
	public String getEdorAppDate() {
		return EdorAppDate;
	}
	public void setEdorAppDate(String edorAppDate) {
		EdorAppDate = edorAppDate;
	}
	
	@XmlElement(name = "EdorType") 
	public String getEdorType() {
		return EdorType;
	}
	public void setEdorType(String edorType) {
		EdorType = edorType;
	}
	
	@XmlElement(name = "EdorItemAppDate") 
	public String getEdorItemAppDate() {
		return EdorItemAppDate;
	}
	public void setEdorItemAppDate(String edorItemAppDate) {
		EdorItemAppDate = edorItemAppDate;
	}	
	
	public static long getSerialversionuid() {
		return serialVersionUID;
	}
	


}
