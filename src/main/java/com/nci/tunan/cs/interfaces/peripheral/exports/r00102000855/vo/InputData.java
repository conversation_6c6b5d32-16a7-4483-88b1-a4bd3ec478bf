package com.nci.tunan.cs.interfaces.peripheral.exports.r00102000855.vo;

import java.io.Serializable;

import javax.xml.bind.annotation.XmlElement;
//投连退保提交接口
public class InputData implements Serializable{
//	保单号
	private String ContNo;	
//	本次领取方式
	private String GetForm;
//	申请方式
	private String AppType;	
//	申请人
	private String Operator;	
//	受理日期
	private String EdorAppDate;	
//	批改类型编码
	private String EdorType;
//	批改类型保全申请提交日期
	private String EdorItemAppDate;	
//	退保原因编码
	private String SurrReason;
	@XmlElement(name="ContNo")
	public String getContNo() {
		return ContNo;
	}
	public void setContNo(String contNo) {
		ContNo = contNo;
	}
	@XmlElement(name="GetForm")
	public String getGetForm() {
		return GetForm;
	}
	public void setGetForm(String getForm) {
		GetForm = getForm;
	}
	@XmlElement(name="AppType")
	public String getAppType() {
		return AppType;
	}
	public void setAppType(String appType) {
		AppType = appType;
	}
	@XmlElement(name="Operator")
	public String getOperator() {
		return Operator;
	}
	public void setOperator(String operator) {
		Operator = operator;
	}
	@XmlElement(name="EdorAppDate")
	public String getEdorAppDate() {
		return EdorAppDate;
	}
	public void setEdorAppDate(String edorAppDate) {
		EdorAppDate = edorAppDate;
	}
	@XmlElement(name="EdorType")
	public String getEdorType() {
		return EdorType;
	}
	public void setEdorType(String edorType) {
		EdorType = edorType;
	}
	@XmlElement(name="EdorItemAppDate")
	public String getEdorItemAppDate() {
		return EdorItemAppDate;
	}
	public void setEdorItemAppDate(String edorItemAppDate) {
		EdorItemAppDate = edorItemAppDate;
	}
	@XmlElement(name="SurrReason")
	public String getSurrReason() {
		return SurrReason;
	}
	public void setSurrReason(String surrReason) {
		SurrReason = surrReason;
	}	

}
