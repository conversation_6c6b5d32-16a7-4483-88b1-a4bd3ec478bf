package com.nci.tunan.cs.interfaces.peripheral.exports.r00102000908.vo;

import java.io.Serializable;

import javax.xml.bind.annotation.XmlElement;

public class OutputData implements Serializable {
	/** 
	* @Fields serialVersionUID : @invalid TODO(用一句话描述这个变量表示什么) 
	*/ 
	
	private static final long serialVersionUID = -7325838742193379445L;
	private Result result;
	@XmlElement(name = "Result")
	public Result getResult() {
		return result;
	}

	public void setResult(Result result) {
		this.result = result;
	}
}
