package com.nci.tunan.cs.interfaces.peripheral.exports.r00102000865.vo;

import java.io.Serializable;

import javax.xml.bind.annotation.XmlElement;
//资金划拨通知回调接口
public class Result implements Serializable{
//	处理结果
	private String ResultCd;
//	处理结果说明
	private String ResultMsg;
//	保单号
	private String ContNo;	
//	划账金额
	private String GetMoney;
//	划账日期
	private String AccDate;
	@XmlElement(name="ResultCd")
	public String getResultCd() {
		return ResultCd;
	}
	public void setResultCd(String resultCd) {
		ResultCd = resultCd;
	}
	@XmlElement(name="ResultMsg")
	public String getResultMsg() {
		return ResultMsg;
	}
	public void setResultMsg(String resultMsg) {
		ResultMsg = resultMsg;
	}
	@XmlElement(name="ContNo")
	public String getContNo() {
		return ContNo;
	}
	public void setContNo(String contNo) {
		ContNo = contNo;
	}
	@XmlElement(name="GetMoney")
	public String getGetMoney() {
		return GetMoney;
	}
	public void setGetMoney(String getMoney) {
		GetMoney = getMoney;
	}
	@XmlElement(name="AccDate")
	public String getAccDate() {
		return AccDate;
	}
	public void setAccDate(String accDate) {
		AccDate = accDate;
	}

}
