package com.nci.tunan.cs.interfaces.peripheral.exports.r00102000908.vo;

import java.io.Serializable;

import javax.xml.bind.annotation.XmlElement;

public class InputData implements Serializable {

	private static final long serialVersionUID = -1444850318126707352L;
	// 客户/保单号
	private String otherNo;
	// 申请号码类型
	private String otherNoType;
	// 申请人
	private String edorAppName;
	// 申请方式
	private String appType;
	// 保全申请提交日期
	private String edorAppDate;
	// 批改项目(新核心中没有用处)
	private String edorType;
	// 批改项目(新核心中没有用处)
	private String edorItemAppDate;
	//挂失类型
	private String reportByLoseForm;
	// 挂失原因
	private String reportByLoseReason;
	// 丢失日期
	private String reportByLoseDate;
	//丢失原因
	private String goonGetMethod1;
	
	@XmlElement(name="GoonGetMethod1")
	public String getGoonGetMethod1() {
		return goonGetMethod1;
	}
	public void setGoonGetMethod1(String goonGetMethod1) {
		this.goonGetMethod1 = goonGetMethod1;
	}
	@XmlElement(name = "OtherNo")
	public String getOtherNo() {
		return otherNo;
	}
	public void setOtherNo(String otherNo) {
		this.otherNo = otherNo;
	}
	@XmlElement(name = "OtherNoType")
	public String getOtherNoType() {
		return otherNoType;
	}
	public void setOtherNoType(String otherNoType) {
		this.otherNoType = otherNoType;
	}
	@XmlElement(name="EdorAppName")
	public String getEdorAppName() {
		return edorAppName;
	}
	public void setEdorAppName(String edorAppName) {
		this.edorAppName = edorAppName;
	}
	@XmlElement(name = "AppType")
	public String getAppType() {
		return appType;
	}
	public void setAppType(String appType) {
		this.appType = appType;
	}
	@XmlElement(name = "EdorAppDate")
	public String getEdorAppDate() {
		return edorAppDate;
	}
	public void setEdorAppDate(String edorAppDate) {
		this.edorAppDate = edorAppDate;
	}
	@XmlElement(name = "EdorType")
	public String getEdorType() {
		return edorType;
	}
	public void setEdorType(String edorType) {
		this.edorType = edorType;
	}
	@XmlElement(name = "EdorItemAppDate")
	public String getEdorItemAppDate() {
		return edorItemAppDate;
	}
	public void setEdorItemAppDate(String edorItemAppDate) {
		this.edorItemAppDate = edorItemAppDate;
	}
	@XmlElement(name = "ReportByLoseForm")
	public String getReportByLoseForm() {
		return reportByLoseForm;
	}
	public void setReportByLoseForm(String reportByLoseForm) {
		this.reportByLoseForm = reportByLoseForm;
	}
	@XmlElement(name = "ReportByLoseReason")
	public String getReportByLoseReason() {
		return reportByLoseReason;
	}
	public void setReportByLoseReason(String reportByLoseReason) {
		this.reportByLoseReason = reportByLoseReason;
	}
	@XmlElement(name = "ReportByLoseDate")
	public String getReportByLoseDate() {
		return reportByLoseDate;
	}
	public void setReportByLoseDate(String reportByLoseDate) {
		this.reportByLoseDate = reportByLoseDate;
	}
	
	

}
