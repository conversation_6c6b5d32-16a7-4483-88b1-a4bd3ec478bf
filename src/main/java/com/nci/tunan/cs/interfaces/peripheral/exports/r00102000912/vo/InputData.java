package com.nci.tunan.cs.interfaces.peripheral.exports.r00102000912.vo;

import java.io.Serializable;
import java.util.List;

import javax.xml.bind.annotation.XmlElement;


public class InputData implements Serializable{

	private static final long serialVersionUID = 1L;
	
	private String ContNo;
	private String InsuredNo;//被保险人客户号
	private String EdorType;//批改项目 只能录入IO，表示被保险人职业类别变更
	private String EdorAppDate;//保全申请提交日期	
	private String AppType;//申请方式
	private String EdorApp;//申请人姓名
	private String OccupationCode;//变更后的职业代码
	private String OccupationType;//变更后的职业类别代码
	
	@XmlElement(name = "ContNo") 
	public String getContNo() {
		return ContNo;
	}
	public void setContNo(String contNo) {
		ContNo = contNo;
	}	

	@XmlElement(name = "InsuredNo") 
	public String getInsuredNo() {
		return InsuredNo;
	}
	public void setInsuredNo(String insuredNo) {
		InsuredNo = insuredNo;
	}
	
	@XmlElement(name = "EdorApp") 
	public String getEdorApp() {
		return EdorApp;
	}
	public void setEdorApp(String edorApp) {
		EdorApp = edorApp;
	}
	
	@XmlElement(name = "OccupationCode") 
	public String getOccupationCode() {
		return OccupationCode;
	}
	public void setOccupationCode(String occupationCode) {
		OccupationCode = occupationCode;
	}
	
	@XmlElement(name = "OccupationType") 
	public String getOccupationType() {
		return OccupationType;
	}
	public void setOccupationType(String occupationType) {
		OccupationType = occupationType;
	}
	
	@XmlElement(name = "AppType") 
	public String getAppType() {
		return AppType;
	}
	public void setAppType(String appType) {
		AppType = appType;
	}
	
	@XmlElement(name = "EdorAppDate") 
	public String getEdorAppDate() {
		return EdorAppDate;
	}
	public void setEdorAppDate(String edorAppDate) {
		EdorAppDate = edorAppDate;
	}
	
	@XmlElement(name = "EdorType") 
	public String getEdorType() {
		return EdorType;
	}
	public void setEdorType(String edorType) {
		EdorType = edorType;
	}

	public static long getSerialversionuid() {
		return serialVersionUID;
	}
}
