package com.nci.tunan.cs.interfaces.peripheral.exports.r00102000908.vo;

import java.io.Serializable;

import javax.xml.bind.annotation.XmlElement;

public class Result implements Serializable {
	/** 
	* @Fields serialVersionUID : @invalid TODO(用一句话描述这个变量表示什么) 
	*/ 
	
	private static final long serialVersionUID = -3328157296389898895L;
	//打印路径(新核心中无)
	private String printURL;	
	//保全号码(新核心中无)
	private String edorNo;	
	//保全受理号
	private String edorAcceptNo;
	//交易处理结果
	private String dealResult;
	@XmlElement(name = "PrintURL")
	public String getPrintURL() {
		return printURL;
	}
	public void setPrintURL(String printURL) {
		this.printURL = printURL;
	}
	@XmlElement(name = "EdorNo")
	public String getEdorNo() {
		return edorNo;
	}
	public void setEdorNo(String edorNo) {
		this.edorNo = edorNo;
	}
	@XmlElement(name = "EdorAcceptNo")
	public String getEdorAcceptNo() {
		return edorAcceptNo;
	}
	public void setEdorAcceptNo(String edorAcceptNo) {
		this.edorAcceptNo = edorAcceptNo;
	}
	@XmlElement(name = "DealResult")
	public String getDealResult() {
		return dealResult;
	}
	public void setDealResult(String dealResult) {
		this.dealResult = dealResult;
	}	
	
}
	
