package com.nci.tunan.cs.interfaces.peripheral.exports.r00102000912.vo;

import java.io.Serializable;
import java.util.List;

import javax.xml.bind.annotation.XmlElement;

import com.lowagie.text.pdf.AcroFields.Item;

public class Result  implements Serializable {

	/** 
	* @Fields serialVersionUID 
	*/ 	
	private static final long serialVersionUID = 7570135099827732447L;
		
    private String EdorAcceptNo;//保全受理号
    private String EdorState;//保全批改状态代码
    private String EdorStateName;//操作错误时的错误信息
    
    
    @XmlElement(name = "EdorAcceptNo")
	public String getEdorAcceptNo() {
		return EdorAcceptNo;
	}
	public void setEdorAcceptNo(String edorAcceptNo) {
		EdorAcceptNo = edorAcceptNo;
	}
	
	@XmlElement(name = "EdorState")
	public String getEdorState() {
		return EdorState;
	}
	public void setEdorState(String edorState) {
		EdorState = edorState;
	}
	
	@XmlElement(name = "EdorStateName")
	public String getEdorStateName() {
		return EdorStateName;
	}
	public void setEdorStateName(String edorStateName) {
		EdorStateName = edorStateName;
	}
	
	
	

}
