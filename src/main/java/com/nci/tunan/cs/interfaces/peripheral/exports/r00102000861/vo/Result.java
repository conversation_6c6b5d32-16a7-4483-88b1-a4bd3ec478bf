package com.nci.tunan.cs.interfaces.peripheral.exports.r00102000861.vo;

import java.io.Serializable;
import java.util.List;

import javax.xml.bind.annotation.XmlElement;

import com.lowagie.text.pdf.AcroFields.Item;

public class Result  implements Serializable {

	/** 
	* @Fields serialVersionUID 
	*/ 	
	private static final long serialVersionUID = 7570135099827732447L;
	//保单号
//	private String contNo;
	private String edorAcceptNo; //受理号
	//领取预估金额
	private String getMoney;
	
	private String markedWords;
//	//剩余单位数
//	private String surplusUnitCount;
//	//剩余金额
//	private String surplusMoney;
//	// 记录集合
//	private List<Item> item;
	@XmlElement(name = "EdorAcceptNo")
	public String getEdorAcceptNo() {
		return edorAcceptNo;
	}
	public void setEdorAcceptNo(String edorAcceptNo) {
		this.edorAcceptNo = edorAcceptNo;
	}
	
	@XmlElement(name = "GetMoney")
	public String getGetMoney() {
		return getMoney;
	}
	public void setGetMoney(String getMoney) {
		this.getMoney = getMoney;
	}
	
	@XmlElement(name = "MarkedWords")
	public String getMarkedWords() {
		return markedWords;
	}
	public void setMarkedWords(String markedWords) {
		this.markedWords = markedWords;
	}
	
	
	//处理结果
//	private String resultCode;
	//处理结果说明
//	private String resultMsg;
	
	/*@XmlElement(name = "ContNo")
	public String getContNo() {
		return contNo;
	}
	public void setContNo(String contNo) {
		this.contNo = contNo;
	}*/
//
//	@XmlElement(name = "Item")
//	public List<Item> getItem() {
//		return item;
//	}
//	public void setItem(List<Item> item) {
//		this.item = item;
//	}

	/*@XmlElement(name = "ResultCode")
	public String getResultCode() {
		return resultCode;
	}
	public void setResultCode(String resultCode) {
		this.resultCode = resultCode;
	}
	@XmlElement(name = "ResultMsg")
	public String getResultMsg() {
		return resultMsg;
	}
	public void setResultMsg(String resultMsg) {
		this.resultMsg = resultMsg;
	}
	
	@XmlElement(name = "getMoney")
	public String getGetMoney() {
		return getMoney;
	}
	public void setGetMoney(String getMoney) {
		this.getMoney = getMoney;
	}*/
	
//	@XmlElement(name = "surplusUnitCount")
//	public String getSurplusUnitCount() {
//		return surplusUnitCount;
//	}
//	public void setSurplusUnitCount(String surplusUnitCount) {
//		this.surplusUnitCount = surplusUnitCount;
//	}
//	
//	@XmlElement(name = "surplusurplusMoneysUnitCount")
//	public String getSurplusMoney() {
//		return surplusMoney;
//	}
//	public void setSurplusMoney(String surplusMoney) {
//		this.surplusMoney = surplusMoney;
//	}
//	
}
