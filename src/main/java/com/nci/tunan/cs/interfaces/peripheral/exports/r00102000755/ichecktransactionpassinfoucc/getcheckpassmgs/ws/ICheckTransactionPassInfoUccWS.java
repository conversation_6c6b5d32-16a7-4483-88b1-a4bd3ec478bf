package com.nci.tunan.cs.interfaces.peripheral.exports.r00102000755.ichecktransactionpassinfoucc.getcheckpassmgs.ws;

import javax.jws.WebParam;
import javax.jws.WebService;
import javax.jws.soap.SOAPBinding;
import javax.xml.ws.Holder;
import com.nci.udmp.component.serviceinvoke.message.header.in.SysHeader;
import com.nci.tunan.cs.interfaces.peripheral.exports.r00102000755.ichecktransactionpassinfoucc.getcheckpassmgs.SrvReqBody;
import com.nci.tunan.cs.interfaces.peripheral.exports.r00102000755.ichecktransactionpassinfoucc.getcheckpassmgs.SrvResBody;

@WebService
@SOAPBinding(parameterStyle = SOAPBinding.ParameterStyle.BARE)
public interface ICheckTransactionPassInfoUccWS {
    public void getCheckPassMgs(
            @WebParam(name = "sysHeader", targetNamespace = "http://www.newchinalife.com/common/header/in", header = true, partName = "parametersReqHeader") SysHeader parametersReqHeader,
            @WebParam(name = "request", targetNamespace = "http://www.newchinalife.com/service/bd", partName = "parametersReqBody") SrvReqBody parametersReqBody,
            @WebParam(name = "sysHeader", targetNamespace = "http://www.newchinalife.com/common/header/in", header = true, mode = WebParam.Mode.OUT, partName = "parametersResHeader") Holder<SysHeader> parametersResHeader,
            @WebParam(name = "response", targetNamespace = "http://www.newchinalife.com/service/bd", mode = WebParam.Mode.OUT, partName = "parametersResBody") Holder<SrvResBody> parametersResBody);
}

