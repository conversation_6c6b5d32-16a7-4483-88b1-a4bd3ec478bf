package com.nci.tunan.cs.interfaces.peripheral.exports.r00102000755.ichecktransactionpassinfoucc.getcheckpassmgs;
import java.io.Serializable;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;
import com.nci.udmp.component.serviceinvoke.message.body.hd.BizHeader;
import com.nci.tunan.cs.interfaces.peripheral.exports.r00102000755.ichecktransactionpassinfoucc.getcheckpassmgs.SrvReqBizBody;

@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "SrvReqBody", propOrder = { "bizHeader", "bizBody" })
public class SrvReqBody implements Serializable {
    /**
     * serialVersionUID 
     */
    private static final long serialVersionUID = 1L;
    
    @XmlElement(required = true)
    protected BizHeader bizHeader;
    @XmlElement(required = true)
    protected SrvReqBizBody bizBody;
    
    public BizHeader getBizHeader() {
        return bizHeader;
    }
    public void setBizHeader(BizHeader bizHeader) {
        this.bizHeader = bizHeader;
    }
    public SrvReqBizBody getBizBody() {
        return bizBody;
    }
    public void setBizBody(SrvReqBizBody bizBody) {
        this.bizBody = bizBody;
    }
}

