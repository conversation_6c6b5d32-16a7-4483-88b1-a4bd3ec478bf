package com.nci.tunan.cs.interfaces.peripheral.exports.r00102000861.vo;

import java.io.Serializable;
import java.util.List;

import javax.xml.bind.annotation.XmlElement;

import com.nci.tunan.cs.interfaces.peripheral.exports.r00102000861.vo.Result;

public class OutputData implements Serializable{

	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;
	
	//private String Output;
	private Result result;//单节点

	
	/*public String getOutput() {
		return Output;
	}
	public void setOutput(String output) {
		Output = output;
	}*/

	@XmlElement(name = "Result")
	public Result getResult() {
		return result;
	}
	public void setResult(Result result) {
		this.result = result;
	}


}
