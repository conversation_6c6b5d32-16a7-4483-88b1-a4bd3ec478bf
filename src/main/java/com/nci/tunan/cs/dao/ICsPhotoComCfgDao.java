package com.nci.tunan.cs.dao;

import java.util.List;

import com.nci.tunan.cs.model.po.CsApplicationPO;
import com.nci.tunan.cs.model.po.CsPhotoComCfgPO;
import com.nci.tunan.cs.model.po.IdentityCheckRelustPO;

public interface ICsPhotoComCfgDao {
	/**
     * 
     * @description 查询保全人像比对验真业务配置表数据
     * @version
     * @title
     * <AUTHOR>
     * @return List<CsPhotoComCfgPO>
     */
	public List<CsPhotoComCfgPO> findCsPhotoComCfg(CsPhotoComCfgPO csPhotoComCfgPO);
	/**
	 * 查询人像比对验真结果 
	 */
	public IdentityCheckRelustPO findCsPhotoComResult(IdentityCheckRelustPO identityCheckRelustPO);
	
	public List<IdentityCheckRelustPO> findAllCsPhotoComResult(IdentityCheckRelustPO identityCheckRelustPO);
	
}
