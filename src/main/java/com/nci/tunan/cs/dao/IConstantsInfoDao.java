package com.nci.tunan.cs.dao;

import com.nci.tunan.clm.interfaces.model.po.ConstantsInfoPO;
import com.nci.tunan.cs.batch.autoREInterestRate.po.AutoREInterestRatePO;
import com.nci.tunan.pa.interfaces.model.po.CsbcacceptruleconfigPO;

import java.util.List;

/**
 * <AUTHOR> <EMAIL>
 * @version V1.0.0
 * @description IConstantsInfoDao
 * @.belongToModule cs-保全子系统
 * @date 2021年06月22日  18:15
 */
public interface IConstantsInfoDao {
    /**
     * 查询当前管理机构上级机构是否配置了“不区分申请方式双录均阻断校验机构”数据
     * @param constantsInfoPO
     * @return
     */
    List<ConstantsInfoPO> findAllUpOrganCodeNotCheckServiceTypeOrgan(ConstantsInfoPO constantsInfoPO);
    
    
    /**
     * 查询当前管理机构上级机构是否配置了“不区分申请方式双录均阻断校验机构”数据
     * @param constantsInfoPO
     * @return
     */
    List<ConstantsInfoPO> findAlltrustUpOrganCodeNotCheckServiceTypeOrgan(ConstantsInfoPO constantsInfoPO);

	/**
	 * @description
	 * @version
	 * @title v1.0
	 * <AUTHOR> <EMAIL>
	 * @param autoREInterestRatePO
	 * @return 
	*/
	int findAllAutoREInterestRateCounts(AutoREInterestRatePO autoREInterestRatePO);

	/**
	 * @description
	 * @version
	 * @title v1.0
	 * <AUTHOR> <EMAIL>
	 * @param autoREInterestRatePO
	 * @return 
	*/
	List<AutoREInterestRatePO> findAllAutoREInterestRate(AutoREInterestRatePO autoREInterestRatePO);
	/**
	 * @description
	 * @version
	 * @title v1.0
	 * <AUTHOR> <EMAIL>
	 * @param autoREInterestRatePO
	 * @return 
	 */
	List<ConstantsInfoPO> findAllRealNameVerifyOrg(ConstantsInfoPO constantsInfoPO);
	/**
	 * @description
	 * @version
	 * @title v1.0
	 * <AUTHOR> <EMAIL>
	 * @param autoREInterestRatePO
	 * @return 
	 */
	List<ConstantsInfoPO> findAllRealNameVerifyBlock(ConstantsInfoPO constantsInfoPO);
	
	
	List<CsbcacceptruleconfigPO> findallCsbcacceptruleconfigPO(CsbcacceptruleconfigPO csbcacceptruleconfigpo);
	/**
	 * 
	 * @description 
	 * @version
	 * @title
	 * <AUTHOR>
	 * @param constantsInfoPO
	 * @return
	 */
	List<ConstantsInfoPO> findPovertyReliefCalProductCode(ConstantsInfoPO constantsInfoPO);
	
	/**
	 * 
	 * @description 查询 校验开启日期
	 * @version
	 * @title
	 * <AUTHOR>
	 * @param constantsInfoPO
	 * @return
	 */
	public ConstantsInfoPO findConstantByKey(ConstantsInfoPO constantsInfoPO);
	
	/**
     * 查询信托险种配置
     * @param constantsInfoPO
     * @return
     */
	List<ConstantsInfoPO> findAllRealTrustInsurance(ConstantsInfoPO constantsInfoPO);
	
	/**
	 * 根据key和value 查询唯一
	 * @param constantsInfoPO
	 * @return
	 */
	public ConstantsInfoPO findConstantByKeyAndValue(ConstantsInfoPO constantsInfoPO);

	/**
	 * 超时原因配置查询
	 * @param constantsInfoPO
	 * @return
	 */
	public List<ConstantsInfoPO> findAllOutTimeConfig(ConstantsInfoPO constantsInfoPO);
}
