package com.nci.tunan.cs.dao;

import com.nci.udmp.framework.model.*;

import java.math.BigDecimal;
import java.util.Map;
import java.util.List;
import com.nci.tunan.cs.model.po.JobkindChangePO;


/**
 * 
 * @description 职业级别Dao接口
 * <AUTHOR> <EMAIL> 
 * @date 15-09-22 11:36:28 
 * @.belongToModule 保全系统-IJobkindChangeDao接口
 */
 public interface IJobkindChangeDao {
	 /**
     * @description 增加数据
     * @version
     * @title
     * <AUTHOR> <EMAIL>
     * @param jobkindChangePO 对象
     * @return JobkindChangePO 添加结果
     */
	 public JobkindChangePO addJobkindChange(JobkindChangePO jobkindChangePO);
	 
     /**
     * @description 删除数据
     * @version
     * @title
     * <AUTHOR> <EMAIL>
     * @param jobkindChangePO 对象
     * @return boolean 删除是否成功
     */
	 public boolean deleteJobkindChange(JobkindChangePO jobkindChangePO);
	 
     /**
     * @description 修改数据
     * @version
     * @title
     * <AUTHOR> <EMAIL>
     * @param jobkindChangePO 对象
     * @return JobkindChangePO 修改结果
     */
	 public JobkindChangePO updateJobkindChange(JobkindChangePO jobkindChangePO);
	 
     /**
     * @description 查询单条数据
     * @version
     * @title
     * <AUTHOR> <EMAIL>
     * @param jobkindChangePO 对象
     * @return JobkindChangePO 查询结果对象
     */
	 public JobkindChangePO findJobkindChange(JobkindChangePO jobkindChangePO);
	 
     /**
     * @description 查询所有数据
     * @version
     * @title
     * <AUTHOR> <EMAIL>
     * @param jobkindChangePO 对象
     * @return List<JobkindChangePO> 查询结果List
     */
	 public List<JobkindChangePO> findAllJobkindChange(JobkindChangePO jobkindChangePO);
	 
     /**
     * @description 查询数据条数
     * @version
     * @title
     * <AUTHOR> <EMAIL>
     * @param jobkindChangePO 对象
     * @return int 查询结果条数
     */
	 public int findJobkindChangeTotal(JobkindChangePO jobkindChangePO);

     /**
     * @description 分页查询数据
     * @version
     * @title
     * <AUTHOR> <EMAIL>
     * @param currentPage 当前页对象
     * @return CurrentPage<JobkindChangePO> 查询结果的当前页对象
     */
	 public CurrentPage<JobkindChangePO> queryJobkindChangeForPage(JobkindChangePO jobkindChangePO, CurrentPage<JobkindChangePO> currentPage);

     /**
     * @description 批量增加数据
     * @version
     * @title
     * <AUTHOR> <EMAIL>
     * @param jobkindChangePOList 对象列表
     * @return boolean 批量添加是否成功
     */
	 public boolean batchSaveJobkindChange(List<JobkindChangePO> jobkindChangePOList);

     /**
     * @description 批量修改数据
     * @version
     * @title
     * <AUTHOR> <EMAIL>
     * @param jobkindChangePOList 对象列表
     * @return boolean 批量修改是否成功
     */
	 public boolean batchUpdateJobkindChange(List<JobkindChangePO> jobkindChangePOList);

     /**
     * @description 批量删除数据
     * @version
     * @title
     * <AUTHOR> <EMAIL>
     * @param jobkindChangePOList 对象列表
     * @return boolean 批量删除是否成功
     */
	 public boolean batchDeleteJobkindChange(List<JobkindChangePO> jobkindChangePOList);

     /**
     * @description 查询所有数据 ，重新组装为map
     * @version
     * @title
     * <AUTHOR> <EMAIL>
     * @param jobkindChangePO 对象
     * @return List<Map<String, Object>> 查询结果存放到map中
     */
	 public List<Map<String, Object>> findAllMapJobkindChange(JobkindChangePO jobkindChangePO);
	 
	 /**
	 * @description 查询职业类别变更开始日期
     * @version
     * @title
     * <AUTHOR>
     * @param jobkindChangePO 对象
     * @return List<JobkindChangePO> 查询结果List
	  */
	 public  List<JobkindChangePO> findJobkindChangeStartDate(JobkindChangePO jobkindChangePO);
	 
	
	 /**
	  * 
	  * @description 查询职业代码是否是上海医保未做映射的216个职业代码
	  * @version V1.0.0
	  * @title
	  * <AUTHOR> <EMAIL>
	  * @param jobCode 职业代码
	  * @return
	  */
	public boolean selectIsShJobCode216(String jobCode);
	
	/**
	 * 
	 * @description 查询职业代码是否是上海医保职业代码
	 * @version V1.0.0
	 * @title
	 * <AUTHOR> <EMAIL>
	 * @param jobCode 职业代码
	 * @return
	 */
	public boolean selectIsShJobCode(String jobCode);
				
 }
 
