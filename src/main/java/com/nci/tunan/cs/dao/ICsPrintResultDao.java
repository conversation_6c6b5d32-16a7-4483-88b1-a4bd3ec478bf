package com.nci.tunan.cs.dao;

import java.util.List;

import com.nci.tunan.cs.model.po.CsPrintResultPO;

/**143327
 * <AUTHOR>
 *
 */
public interface ICsPrintResultDao {
	/**
     * @description 增加数据
     * @version
     * @title
     * <AUTHOR>
     * @param CsPrintResultPO 对象
     * @return CsPrintResultPO 添加结果
     */
	 public CsPrintResultPO addCsPrintResult(CsPrintResultPO csPrintResultPO);
	 
     /**
     * @description 删除数据
     * @version
     * @title
     * <AUTHOR>
     * @param CsPrintResultPO 对象
     * @return boolean 删除是否成功
     */
	 public boolean deleteCsPrintResult(CsPrintResultPO csPrintResultPO);
	 
     /**
     * @description 修改数据
     * @version
     * @title
     * <AUTHOR>
     * @param CsPrintResultPO 对象
     * @return CsPrintResultPO 修改结果
     */
	 public CsPrintResultPO updateCsPrintResult(CsPrintResultPO csPrintResultPO);
	 
     /**
     * @description 查询单条数据
     * @version
     * @title
     * <AUTHOR>
     * @param CsPrintResultPO 对象
     * @return CsPrintResultPO 查询结果对象
     */
	 public CsPrintResultPO findCsPrintResult(CsPrintResultPO csPrintResultPO);
	 
     /**
     * @description 查询所有数据
     * @version
     * @title
     * <AUTHOR>
     * @param CsPrintResultPO 对象
     * @return List<CsPrintResultPO> 查询结果List
     */
	 public List<CsPrintResultPO> findAllCsPrintResult(CsPrintResultPO csPrintResultPO);
}
