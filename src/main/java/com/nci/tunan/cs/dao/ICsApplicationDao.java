package com.nci.tunan.cs.dao;

import java.util.Map;

import com.nci.udmp.framework.model.*;
import com.nci.tunan.cs.model.po.AcceptTaskPoolPO;
import com.nci.tunan.cs.model.po.CsAcceptChangePO;
import com.nci.tunan.cs.model.po.CsApplicationPO;
import com.nci.tunan.cs.model.po.CsPolicyChangePO;
import com.nci.tunan.cs.model.po.CsPolicyHolderPO;
import com.nci.tunan.cs.model.po.CsQuotationPoolPO;
import com.nci.tunan.cs.model.po.HandWorkAssignPO;
import com.nci.tunan.cs.model.po.SurveyObjectPO;
import com.nci.tunan.pa.interfaces.model.po.ContractBusiProdPO;

import java.util.List;


/**
 * 
 * @description  ICsApplicationDao接口 
 * <AUTHOR>
 * @version V1.0.0
 * @.belongToModule CS-保全子系统
 * @date 2020年12月24日 下午2:49:14
 */
 public interface ICsApplicationDao {

     /**
      * @description 分页查询保全受理个人任务池
      * @version
      * @title
      * <AUTHOR> <EMAIL>
      * @see com.nci.tunan.cs.dao.ICsApplicationDao#queryJoinApplicationPage(com.nci.tunan.cs.model.po.CsApplicationPO, com.nci.udmp.framework.model.CurrentPage)
      * @param csApplicationPO  csApplicationPO
      * @param currentPage 当前页对象
      * @return CurrentPage<CsApplicationPO> 查询结果的当前页对象
      */
	 public CurrentPage<CsApplicationPO> queryJoinApplicationPage(CsApplicationPO csApplicationPO, CurrentPage<CsApplicationPO> currentPage);
	 
	 /**
	  * 
	  * @description 查询保全申请信息
	  * @version V1.0.0
	  * @title
	  * <AUTHOR>
	  * @param csApplicationPO 查询参数
	  * @return
	  */
	 public List<CsApplicationPO> findAllJoinApplication(CsApplicationPO csApplicationPO);
	 
	 /**
     * @description 增加数据
     * @version
     * @title
     * <AUTHOR> <EMAIL>
     * @param csApplicationPO 对象
     * @return CsApplicationPO 添加结果
     */
	 public CsApplicationPO addCsApplication(CsApplicationPO csApplicationPO);
	 
     /**
     * @description 删除数据
     * @version
     * @title
     * <AUTHOR> <EMAIL>
     * @param csApplicationPO 对象
     * @return boolean 删除是否成功
     */
	 public boolean deleteCsApplication(CsApplicationPO csApplicationPO);
	 
     /**
     * @description 修改数据
     * @version
     * @title
     * <AUTHOR> <EMAIL>
     * @param csApplicationPO 对象
     * @return CsApplicationPO 修改结果
     */
	 public CsApplicationPO updateCsApplication(CsApplicationPO csApplicationPO);
	 /**
      * @description 根据变更ID更改申请状态
      * @version
      * @title
      * <AUTHOR> <EMAIL>
      * @param csApplicationPO 对象
      * @return CsApplicationPO 修改结果
      */
  	 public CsApplicationPO updateCsAppliStatusById(CsApplicationPO csApplicationPO);
     /**
     * @description 查询单条数据
     * @version
     * @title
     * <AUTHOR> <EMAIL>
     * @param csApplicationPO 对象
     * @return CsApplicationPO 查询结果对象
     */
	 public CsApplicationPO findCsApplication(CsApplicationPO csApplicationPO);
	 /**
	 
     /**
	  * @description 查询单条数据
	  * @version
	  * @title
	  * <AUTHOR> <EMAIL>
	  * @param csApplicationPO 对象
	  * @return CsApplicationPO 查询结果对象
	  */
	 public List<CsApplicationPO> findCsApplication1(CsApplicationPO csApplicationPO);
	 /**
	  * 
	  * @description 根据主键查询
	  * @version v1.1.0
	  * @<NAME_EMAIL>
	  * @param 
	  * @return 
	  * @exception
	  */
	 public CsApplicationPO findCsApplicationByChangeId(CsApplicationPO csApplicationPO);
	 /**
	  * 
	  * @description 根据主键查询
	  * @version v1.1.0
	  * @<NAME_EMAIL>
	  * @param 
	  * @return 
	  * @exception
	  */
	/**保单投保人表PO*/
	 public List<CsPolicyHolderPO> findapplyIsPolicyHolder(CsPolicyHolderPO CsPolicyHolderPO);
     /**
     * @description 查询所有数据
     * @version
     * @title
     * <AUTHOR> <EMAIL>
     * @param csApplicationPO 对象
     * @return List<CsApplicationPO> 查询结果List
     */
	 public List<CsApplicationPO> findAllCsApplication(CsApplicationPO csApplicationPO);
	 
	 /**
	  * 
	  * @description 查询保全申请信息
	  * @version V1.0.0
	  * @title
	  * <AUTHOR>
	  * @param csApplicationPO 查询参数
	  * @return
	  */
	 public List<Map<String,Object>> findPolicyChangeById(CsApplicationPO csApplicationPO);
     /**
     * @description 查询数据条数
     * @version
     * @title
     * <AUTHOR> <EMAIL>
     * @param csApplicationPO 对象
     * @return int 查询结果条数
     */
	 public int findCsApplicationTotal(CsApplicationPO csApplicationPO);

     /**
     * @description 分页查询数据
     * @version
     * @title
     * <AUTHOR> <EMAIL>
     * @param currentPage 当前页对象
     * @return CurrentPage<CsApplicationPO> 查询结果的当前页对象
     */
	 public CurrentPage<CsApplicationPO> queryCsApplicationForPage(CsApplicationPO csApplicationPO, CurrentPage<CsApplicationPO> currentPage);

     /**
     * @description 批量增加数据
     * @version
     * @title
     * <AUTHOR> <EMAIL>
     * @param csApplicationPOList 对象列表
     * @return boolean 批量添加是否成功
     */
	 public boolean batchSaveCsApplication(List<CsApplicationPO> csApplicationPOList);
	 
	 /**
	  * 
	  * @description 检查被保人存在电话变更
	  * @version
	  * @title
	  * @<NAME_EMAIL> 
	  * @param csApplicationPO csApplicationPO
	  * @return
	  */
	 public List<Map<String, Object>> findInsuredPhoneChange(CsApplicationPO csApplicationPO);
	 
	 /**
	  * 
	  * @description 检查客户在某时间后发生电话修改次数
	  * @version
	  * @title
	  * @<NAME_EMAIL> 
	  * @param csApplicationPO csApplicationPO
	  * @return
	  */
	 public int findPhoneChangeCount(CsApplicationPO csApplicationPO);

     /**
     * @description 批量修改数据
     * @version
     * @title
     * <AUTHOR> <EMAIL>
     * @param csApplicationPOList 对象列表
     * @return boolean 批量修改是否成功
     */
	 public boolean batchUpdateCsApplication(List<CsApplicationPO> csApplicationPOList);

     /**
     * @description 批量删除数据
     * @version
     * @title
     * <AUTHOR> <EMAIL>
     * @param csApplicationPOList 对象列表
     * @return boolean 批量删除是否成功
     */
	 public boolean batchDeleteCsApplication(List<CsApplicationPO> csApplicationPOList);

     /**
     * @description 查询所有数据 ，重新组装为map
     * @version
     * @title
     * <AUTHOR> <EMAIL>
     * @param csApplicationPO 对象
     * @return List<Map<String, Object>> 查询结果存放到map中
     */
	 public List<Map<String, Object>> findAllMapCsApplication(CsApplicationPO csApplicationPO);
	 /**
	     * @description 根据客户id查询数据
	     * @version
	     * @title
	     * <AUTHOR> <EMAIL>
	     * @param csApplicationPO 对象
	     * @return List<CsApplicationPO> 查询结果List
	     */
	public List<CsApplicationPO> findCsApplicationByCustID(CsApplicationPO csApplicationPO);
/**
 * 
 * @description
 * @version
 * @title
 * @<NAME_EMAIL>
 * @param csApplicationPO 查询参数
 * @param copyCurrentPage 分页查询对象
 * @return
 */
	public CurrentPage<CsApplicationPO> queryCsApplicationForPageForTry(
			CsApplicationPO csApplicationPO,
			CurrentPage<CsApplicationPO> copyCurrentPage);

	/**
	* @description 根据业务员代码,保全申请提交日期,保全号,申请方式查询历次保全
	* @version
	* @title
	* <AUTHOR>
	* @param csApplicationPO 对象
	* @return List<CsApplicationPO> 查询结果List
	*/
	public List<CsApplicationPO> findGetMobileEdor(CsApplicationPO csApplicationPO);

	/**
	 * 
	 * @description  查询受理任务池
	 * @version
	 * @title
	 * @<NAME_EMAIL>
	 * @param csApplicationPO 查询参数
	 * @param copyCurrentPage 分页查询对象
	 * @return
	 */
public CurrentPage<AcceptTaskPoolPO> queryAcceptPoolPage(
		AcceptTaskPoolPO acceptTaskPoolPO,
		CurrentPage<AcceptTaskPoolPO> currentPage);
/**
 * 
 * @description  查询试算任务池
 * @version
 * @title
 * @<NAME_EMAIL>
 * @param csApplicationPO 查询参数
 * @param copyCurrentPage 分页查询对象
 * @return
 */
	public CurrentPage<CsQuotationPoolPO> queryQuotationPoolMsg(
			CsQuotationPoolPO csQuotationPoolPO,
			CurrentPage<CsQuotationPoolPO> currentPages);
	
	/**
	 * 
	 * @description 根据条件查询所有业务项目定义表信息
	 * @version V1.0.0
	 * @title
	 * <AUTHOR>
	 * @return
	 */
	public List<Map<String, Object>> mobFindAllService();
	
	/**
	 * 
	 * @description 查询客户证件号码
	 * @version V1.0.0
	 * @title
	 * <AUTHOR>
	 * @param surveyObjectPO 参数
	 * @return
	 */
	public SurveyObjectPO queryIdentifyNO(SurveyObjectPO surveyObjectPO);
	
	/**
	 * 
	 * @description 根据受理好查询申请信息
	 * @version V1.0.0
	 * @title
	 * <AUTHOR>
	 * @param csApplicationPO 查询参数
	 * @return
	 */
	public List<CsApplicationPO> queryApplicationByAcceptCode(CsApplicationPO csApplicationPO);
	
	/**
	 * @description  任务池组装数据
	 * @version
	 * @title
	 * @<NAME_EMAIL>
	 * @param acceptTaskPoolPO 任务池查询参数
	 * @return 
	*/
	public List<AcceptTaskPoolPO> queryAcceptTaskPoolForShow(AcceptTaskPoolPO acceptTaskPoolPO);
	/**
	 * 
	 * @description 查询申请信息
	 * @version V1.0.0
	 * @title
	 * <AUTHOR>
	 * @param policyCode 保单号
	 * @return
	 */
	public List<CsApplicationPO> findCsApplicationByPolicyCode(
			String policyCode);
	
	/**
	 * 
	 * @description 判断是客户的身份
	 * @version V1.0.0
	 * @title
	 * <AUTHOR>
	 * @param csApplicationPO 申请信息
	 * @return
	 */
	public CsApplicationPO isHoliderOrInsured(CsApplicationPO csApplicationPO);
	
	/**
	 * 
	 * @description 判断退保保费大于300万
	 * @version V1.0.0
	 * @title
	 * <AUTHOR>
	 * @param acceptCode 受理号
	 * @return
	 */
	public boolean feeAmountISBig(String acceptCode);
	
	/**
	 * 
	 * @description 是否有有效的补发记录
	 * @version V1.0.0
	 * @title
	 * <AUTHOR>
	 * @param policyCode 保单号
	 * @return
	 */
	public boolean isHaveLRLogByPolicyCode(String policyCode);

	/**
	 * 
	 * @description 获取保单上已经完成的保全申请提交日期
	 * @version V1.0.0
	 * @title
	 * <AUTHOR>
	 * @param maxCsAppTimePO 申请信息
	 * @return
	 */
	public List<CsAcceptChangePO> findMaxCsAppTimeByPolicyCode(
			CsApplicationPO maxCsAppTimePO);
	
	
	/**
	 * 
	 * @description 根据changeid查询保全申请数据
	 * @version V1.0.0
	 * @title
	 * <AUTHOR>
	 * @param csApplicationPO 查询参数
	 * @return
	 */
	public List<Map<String,Object>> findCsApplicationBySingleChangeId(CsApplicationPO csApplicationPO);

	/**
	 * 
	 * @description 查询自动分配信息
	 * @version V1.0.0
	 * @title
	 * <AUTHOR>
	 * @param handWorkAssignPO 查询条件
	 * @param currentPage 分页查询对象
	 * @return
	 */
	public CurrentPage<HandWorkAssignPO> queryCsApplyTaskList(HandWorkAssignPO handWorkAssignPO, CurrentPage<HandWorkAssignPO> currentPage);
	/**
	 * 
	 * @description 批量修改数据=====>手动分配 
	 * @version V1.0.0
	 * @title
	 * <AUTHOR>
	 * @param csApplicationPOList 修改内容
	 * @return
	 */
	public boolean batchUpdateCsApplicationForHand(List<CsApplicationPO> csApplicationPOList);
	/**
	 * 
	 * @description 查询信息
	 * @version V1.0.0
	 * @title
	 * <AUTHOR>
	 * @param csApplicationPO 查询参数
	 * @return
	 */
	public CsApplicationPO findAllSjqyLog(CsApplicationPO csApplicationPO);
	/**
	 * 
	 * @description 根据保单变更ID查询信息
	 * @version V1.0.0
	 * @title
	 * <AUTHOR>
	 * @param csApplicationPO 查询参数
	 * @return
	 */
	public CsApplicationPO findCsApplicationByPolicyChgId(CsApplicationPO csApplicationPO);
	
	/**
	 * 
	 * @description 根据申请号查询任务条数
	 * @version V1.0.0
	 * @title
	 * <AUTHOR>
	 * @param handWorkAssignPO 查询条件
	 * @return
	 */
	public List<HandWorkAssignPO> queryCsEntryTaskList(HandWorkAssignPO handWorkAssignPO);
	
	/**
	 * 
	 * @description 查询保单投保人名下其它保单的险种信息
	 * @version V1.0.0
	 * @title
	 * <AUTHOR>
	 * @param contractBusiProdPO 险种信息
	 * @return
	 */
	public List<ContractBusiProdPO> findAllContractBusiProd(ContractBusiProdPO contractBusiProdPO);

	/**
	 * 
	 * @description 根据保全项查询是否是保单层的保全，是的话返回true，不是的话返回false
	 * @version V1.0.0
	 * @title
	 * <AUTHOR>
	 * @param serviceCode 保全项
	 * @return
	 */
	public String isOrNotPolicyLevel(String serviceCode);

	/**
	 * 
	 * @description 统计该保单号在上海医保的数据库中是否存在，返回存在的条数
	 * @version V1.0.0
	 * @title
	 * <AUTHOR>
	 * @param policyCode 保单号
	 * @return
	 */
	int isOrNotSH(String policyCode);

	/**
	 * 
	 * @description 根据保单号查询此保单号的终止原因
	 * @version V1.0.0
	 * @title
	 * <AUTHOR>
	 * @param policyCode 保单号
	 * @return
	 */
	String getEndCause(String policyCode);

	/**
	 * 
	 * @description 根据变更号查询出医保卡号
	 * @version V1.0.0
	 * @title
	 * <AUTHOR>
	 * @param csApplicationPO 查询参数
	 * @return
	 */
	CsApplicationPO selectMedicalNo(CsApplicationPO csApplicationPO);
	
	/**
	 * 
	 * @description 更新申请状态为生效
	 * @version V1.0.0
	 * @title
	 * <AUTHOR>
	 * @param csApplicationPO 申请信息
	 * @return
	 */
	public CsApplicationPO updateCsApplicationAppStatusEffect(CsApplicationPO csApplicationPO);
	/**
	 * 
	 * @description 查询已上线的所有机构
	 * @version V1.0.0
	 * @title
	 * <AUTHOR>
	 * @param csApplicationPO 申请信息
	 * @return
	 */
	public List<Map<String, Object>> findAllSjqyOrgan(CsApplicationPO csApplicationPO);
	
	/**
	 * 
	 * @description 更改申请状态
	 * @version V1.0.0
	 * @title
	 * @<NAME_EMAIL>
	 * @param csApplicationPO 申请信息
	 * @return
	 */
	public CsApplicationPO updateApplyStatusByChangeId(CsApplicationPO csApplicationPO);

	/**
	 * 
	 * @description 查询手工分配任务
	 * @version V1.0.0
	 * @title
	 * @<NAME_EMAIL>
	 * @param queryPO 查询条件
	 * @return
	 */
    public List<CsApplicationPO> queryAssignTaskList(CsApplicationPO queryPO);
    /**
     * 
     * @description 查询用户权限
     * @version V1.0.0
     * @title
     * <AUTHOR>
     * @param userId 用户ID
     * @param type 类型
     * @return
     */
    public List<Map<String,Object>> queryUserPermission(long userId,String type);
	/**
	 * 
	 * @description  根据业务员代码查询业务员证件有效期
	 * @version
	 * @title
	 * <AUTHOR> <EMAIL>
	 * @param csAcceptChangeVO  参数拼装
	 * @param CHANGE_ID  保全申请ID
	 * @return
	 */
	public CsApplicationPO queryAgentCertValidity(CsApplicationPO csApplicationPO);
	
	/**
	 * 
	 * @description  退回至发起人，更新原因和受理状态
	 */
	public void updateCLMInfo(CsPolicyChangePO csPolicyChangePO,String status);
	/**
	 * 
	 * @description 更新理赔字段信息
	 */
	public void updateCSS(CsPolicyChangePO csPolicyChangePO);

	/**
	 * 
	 * @description 更新双人审核相关信息
	 * @version V1.0.0
	 * @title
	 * <AUTHOR>
	 * @param csApplicationPO
	 */
	public void updateCsDoubleAudit(CsApplicationPO csApplicationPO);

	public void updateSubmit(CsApplicationPO csApplicationPO);
	/*
	**
    * @description 查询保全受理超时时间
    * @version V1.0.0
    * @title
    * <AUTHOR>
    * @param csApplicationPO 保全申请信息封装PO对象
    * @return int 保全受理超时时间
    */
	 public int findCsOutTimeDay(CsApplicationPO csApplicationPO);
	 
	 /**
	     * 
	     * @description 受理超时发送短信数据查询
	     * @version
	     * @title
	     * <AUTHOR>
	     * @param map 批处理入参对象
	     * @return
	  */
	public List<Map<String, Object>> findOutTimeData(Map<String, Object> map);
	
	
	/**
     * @description 查询保全受理生效保全贷款续贷条数
     */
	public int findCsApplicationRlAndLnTotal(CsApplicationPO csApplicationPO);
	
	/**
     * @description 查询保全受理生效保全贷款续贷金额
     */
	public CsApplicationPO findCsApplicationRlAndLnSum(CsApplicationPO csApplicationPO);
	
	/**
     * @description 根据申请人客户id查询客户表中的职业相关信息
     */
	public CsApplicationPO findJobCodeFromCustomerByCustomerId(CsApplicationPO csApplicationPO);
	
	/**
     * @description 根据申请人客户id和受理号查询客户抄单表中的职业相关信息
     */
	public CsApplicationPO findJobCodeFromCsCustomerByCustomerId(CsApplicationPO csApplicationPO);

 }
 
