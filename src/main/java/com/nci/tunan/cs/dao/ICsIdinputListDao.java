package com.nci.tunan.cs.dao;

import com.nci.udmp.framework.model.*;
import java.util.Map;
import org.slf4j.Logger;
import java.util.List;
import com.nci.udmp.util.logging.LoggerFactory;
import com.nci.tunan.cs.model.po.CsIdinputListPO;
import com.nci.tunan.cs.dao.ICsIdinputListDao;

/**
 * @description ICsIdinputListDao接口
 * <AUTHOR>
 * @date 2022-07-26 10:14:47
 */
public interface ICsIdinputListDao {
    /**
     * @description 增加数据
     * @version
     * @title
     * <AUTHOR>
     * @param csIdinputListPO
     *            对象
     * @return CsIdinputListPO 添加结果
     */
    public CsIdinputListPO addCsIdinputList(CsIdinputListPO csIdinputListPO);

    /**
     * @description 删除数据
     * @version
     * @title
     * <AUTHOR>
     * @param csIdinputListPO
     *            对象
     * @return boolean 删除是否成功
     */
    public boolean deleteCsIdinputList(CsIdinputListPO csIdinputListPO);
    
    
    /**
     * 
     * @description 根据受理号删除数据
     * @version V1.0.0
     * @title
     * <AUTHOR>
     * @param csIdinputListPO 对象
     * @return 删除是否成功
     */
    public boolean deleteDataByAcceptCode(CsIdinputListPO csIdinputListPO);

    /**
     * @description 修改数据
     * @version
     * @title
     * <AUTHOR>
     * @param csIdinputListPO
     *            对象
     * @return CsIdinputListPO 修改结果
     */
    public CsIdinputListPO updateCsIdinputList(CsIdinputListPO csIdinputListPO);

    /**
     * @description 查询单条数据
     * @version
     * @title
     * <AUTHOR>
     * @param csIdinputListPO
     *            对象
     * @return CsIdinputListPO 查询结果对象
     */
    public CsIdinputListPO findCsIdinputList(CsIdinputListPO csIdinputListPO);

    /**
     * @description 查询所有数据
     * @version
     * @title
     * <AUTHOR>
     * @param csIdinputListPO
     *            对象
     * @return List<CsIdinputListPO> 查询结果List
     */
    public List<CsIdinputListPO> findAllCsIdinputList(CsIdinputListPO csIdinputListPO);

    /**
     * @description 查询数据条数
     * @version
     * @title
     * <AUTHOR>
     * @param csIdinputListPO
     *            对象
     * @return int 查询结果条数
     */
    public int findCsIdinputListTotal(CsIdinputListPO csIdinputListPO);

    /**
     * @description 分页查询数据
     * @version
     * @title
     * <AUTHOR>
     * @param currentPage
     *            当前页对象
     * @return CurrentPage<CsIdinputListPO> 查询结果的当前页对象
     */
    public CurrentPage<CsIdinputListPO> queryCsIdinputListForPage(CsIdinputListPO csIdinputListPO, CurrentPage<CsIdinputListPO> currentPage);

    /**
     * @description 批量增加数据
     * @version
     * @title
     * <AUTHOR>
     * @param csIdinputListPOList
     *            对象列表
     * @return boolean 批量添加是否成功
     */
    public boolean batchSaveCsIdinputList(List<CsIdinputListPO> csIdinputListPOList);

    /**
     * @description 批量修改数据
     * @version
     * @title
     * <AUTHOR>
     * @param csIdinputListPOList
     *            对象列表
     * @return boolean 批量修改是否成功
     */
    public boolean batchUpdateCsIdinputList(List<CsIdinputListPO> csIdinputListPOList);

    /**
     * @description 批量删除数据
     * @version
     * @title
     * <AUTHOR>
     * @param csIdinputListPOList
     *            对象列表
     * @return boolean 批量删除是否成功
     */
    public boolean batchDeleteCsIdinputList(List<CsIdinputListPO> csIdinputListPOList);

    /**
     * @description 查询所有数据 ，重新组装为map
     * @version
     * @title
     * <AUTHOR>
     * @param csIdinputListPO
     *            对象
     * @return List<Map<String, Object>> 查询结果存放到map中
     */
    public List<Map<String, Object>> findAllMapCsIdinputList(CsIdinputListPO csIdinputListPO);

    /**
     * 
     * @description 查询未读取信息
     * @version V1.0.0
     * @title
     * <AUTHOR>
     * @param csIdinputListPO
     * @return
     */
    public List<CsIdinputListPO> queryNoReadIDList(CsIdinputListPO csIdinputListPO);

    /**
     * 
     * @description 保存未读取原因
     * @version V1.0.0
     * @title
     * <AUTHOR>
     * @param csIdinputListPO
     */
    public void saveNotReadData(CsIdinputListPO csIdinputListPO);

}
