package com.nci.tunan.cs.dao;

import java.util.List;
import java.util.Map;

import com.nci.tunan.cs.model.po.OrderPO;
import com.nci.tunan.cs.model.po.TrustContractPO;
import com.nci.udmp.framework.model.CurrentPage;

/**
 * @description ITrustContractDao接口
 * <AUTHOR>
 * @date 2025-07-21 14:04:31
 */
public interface ITrustContractDao {
    /**
     * @description 增加数据
     * @version
     * @title
     * <AUTHOR>
     * @param trustContractPO
     *            对象
     * @return TrustContractPO 添加结果
     */
    public TrustContractPO addTrustContract(TrustContractPO trustContractPO);

    /**
     * @description 删除数据
     * @version
     * @title
     * <AUTHOR>
     * @param trustContractPO
     *            对象
     * @return boolean 删除是否成功
     */
    public boolean deleteTrustContract(TrustContractPO trustContractPO);

    /**
     * @description 修改数据
     * @version
     * @title
     * <AUTHOR>
     * @param trustContractPO
     *            对象
     * @return TrustContractPO 修改结果
     */
    public TrustContractPO updateTrustContract(TrustContractPO trustContractPO);

    /**
     * @description 查询单条数据
     * @version
     * @title
     * <AUTHOR>
     * @param trustContractPO
     *            对象
     * @return TrustContractPO 查询结果对象
     */
    public TrustContractPO findTrustContract(TrustContractPO trustContractPO);

    /**
     * @description 查询所有数据
     * @version
     * @title
     * <AUTHOR>
     * @param trustContractPO
     *            对象
     * @return List<TrustContractPO> 查询结果List
     */
    public List<TrustContractPO> findAllTrustContract(TrustContractPO trustContractPO);

    /**
     * @description 查询数据条数
     * @version
     * @title
     * <AUTHOR>
     * @param trustContractPO
     *            对象
     * @return int 查询结果条数
     */
    public int findTrustContractTotal(TrustContractPO trustContractPO);

    /**
     * @description 分页查询数据
     * @version
     * @title
     * <AUTHOR>
     * @param currentPage
     *            当前页对象
     * @return CurrentPage<TrustContractPO> 查询结果的当前页对象
     */
    public CurrentPage<TrustContractPO> queryTrustContractForPage(TrustContractPO trustContractPO, CurrentPage<TrustContractPO> currentPage);

    /**
     * @description 批量增加数据
     * @version
     * @title
     * <AUTHOR>
     * @param trustContractPOList
     *            对象列表
     * @return boolean 批量添加是否成功
     */
    public boolean batchSaveTrustContract(List<TrustContractPO> trustContractPOList);

    /**
     * @description 批量修改数据
     * @version
     * @title
     * <AUTHOR>
     * @param trustContractPOList
     *            对象列表
     * @return boolean 批量修改是否成功
     */
    public boolean batchUpdateTrustContract(List<TrustContractPO> trustContractPOList);

    /**
     * @description 批量删除数据
     * @version
     * @title
     * <AUTHOR>
     * @param trustContractPOList
     *            对象列表
     * @return boolean 批量删除是否成功
     */
    public boolean batchDeleteTrustContract(List<TrustContractPO> trustContractPOList);

    /**
     * @description 查询所有数据 ，重新组装为map
     * @version
     * @title
     * <AUTHOR>
     * @param trustContractPO
     *            对象
     * @return List<Map<String, Object>> 查询结果存放到map中
     */
    public List<Map<String, Object>> findAllMapTrustContract(TrustContractPO trustContractPO);


    /**
     * 
     * @description 保存信托合同信息维护订单信息
     * @version V1.0.0
     * @title
     * <AUTHOR>
     * @param orderPO
     * @return
     */
    public OrderPO addOrderData(OrderPO orderPO);
    
    /**
     * 
     * @description 查询序列号信息
     * @version V1.0.0
     * @title
     * <AUTHOR>
     * @param orderPO
     * @return
     */
    public OrderPO findOrderData(OrderPO orderPO);
    
    /**
     * 
     * @description  更新状态
     * @version V1.0.0
     * @title
     * <AUTHOR>
     * @param orderPO
     */
    public void updateOrderData(OrderPO orderPO);

}
