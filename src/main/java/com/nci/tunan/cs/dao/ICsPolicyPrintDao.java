package com.nci.tunan.cs.dao;

import com.nci.tunan.cs.model.po.CsPolicyPrintFeePO;
import com.nci.tunan.pa.interfaces.model.po.AgentPO;
import com.nci.tunan.pa.interfaces.model.po.PolicyPrintPO;
import com.nci.udmp.framework.model.CurrentPage;

import java.util.List;
import java.util.Map;


/** 
 * @description 保单打印件主表接口
 * <AUTHOR> 
 * @.belongToModule PA-保单管理系统-保单打印件主表接口
 * @date 2016-05-30 16:01:28  
 */
 public interface ICsPolicyPrintDao {
	 /**
     * @description 增加数据
     * @version
     * @title
     * <AUTHOR>
     * @param policyPrintPO 保单打印件主表入参对象
     * @return PolicyPrintPO 添加结果
     */
	 public PolicyPrintPO addPolicyPrint(PolicyPrintPO policyPrintPO);

     /**
     * @description 删除数据
     * @version
     * @title
     * <AUTHOR>
     * @param policyPrintPO 保单打印件主表入参对象
     * @return boolean 删除是否成功
     */
	 public boolean deletePolicyPrint(PolicyPrintPO policyPrintPO);

     /**
     * @description 修改数据
     * @version
     * @title
     * <AUTHOR>
     * @param policyPrintPO 保单打印件主表入参对象
     * @return PolicyPrintPO 修改结果
     */
	 public PolicyPrintPO updatePolicyPrint(PolicyPrintPO policyPrintPO);

     /**
     * @description 查询单条数据
     * @version
     * @title
     * <AUTHOR>
     * @param policyPrintPO 保单打印件主表入参对象
     * @return PolicyPrintPO 查询结果对象
     */
	 public PolicyPrintPO findPolicyPrint(PolicyPrintPO policyPrintPO);

     /**
     * @description 查询所有数据
     * @version
     * @title
     * <AUTHOR>
     * @param policyPrintPO 保单打印件主表入参对象
     * @return List<PolicyPrintPO> 查询结果List
     */
	 public List<PolicyPrintPO> findAllPolicyPrint(PolicyPrintPO policyPrintPO);

     /**
     * @description 查询数据条数
     * @version
     * @title
     * <AUTHOR>
     * @param policyPrintPO 保单打印件主表入参对象
     * @return int 查询结果条数
     */
	 public int findPolicyPrintTotal(PolicyPrintPO policyPrintPO);

     /**
     * @description 分页查询数据
     * @version
     * @title
     * <AUTHOR>
     * @param currentPage 当前页对象
     * @return CurrentPage<PolicyPrintPO> 查询结果的当前页对象
     */
	 public CurrentPage<PolicyPrintPO> queryPolicyPrintForPage(PolicyPrintPO policyPrintPO, CurrentPage<PolicyPrintPO> currentPage);

     /**
     * @description 分页查询制单费数据
     * @version
     * @title
     * <AUTHOR>
     * @param currentPage 当前页对象
     * @return CurrentPage<PolicyPrintPO> 查询结果的当前页对象
     */
	 public CurrentPage<CsPolicyPrintFeePO> findCsPolicyPrintFeeForPage(CsPolicyPrintFeePO csPolicyPrintFeePO, CurrentPage<CsPolicyPrintFeePO> currentPage);

	 /**
     * @description 批量增加数据
     * @version
     * @title
     * <AUTHOR>
     * @param policyPrintPOList 保单打印件主表入参对象列表
     * @return boolean 批量添加是否成功
     */
	 public boolean batchSavePolicyPrint(List<PolicyPrintPO> policyPrintPOList);

     /**
     * @description 批量修改数据
     * @version
     * @title
     * <AUTHOR>
     * @param policyPrintPOList 保单打印件主表入参对象列表
     * @return boolean 批量修改是否成功
     */
	 public boolean batchUpdatePolicyPrint(List<PolicyPrintPO> policyPrintPOList);

     /**
     * @description 批量删除数据
     * @version
     * @title
     * <AUTHOR>
     * @param policyPrintPOList 保单打印件主表入参对象列表
     * @return boolean 批量删除是否成功
     */
	 public boolean batchDeletePolicyPrint(List<PolicyPrintPO> policyPrintPOList);

     /**
     * @description 查询所有数据 ，重新组装为map
     * @version
     * @title
     * <AUTHOR>
     * @param policyPrintPO 保单打印件主表入参对象
     * @return List<Map<String, Object>> 查询结果存放到map中
     */
	 public List<Map<String, Object>> findAllMapPolicyPrint(PolicyPrintPO policyPrintPO);

	 /**
	  * @description 查询打印结果
      * @version
      * @title
      * <AUTHOR>
	  * @param policyPrintPO 保单打印件主表入参对象
	  * @return
	  */
	 public List<Map<String, Object>> queryPaperSupplement(
             PolicyPrintPO policyPrintPO);
	 
     /**
     * @description 查询业务员信息明细
     * @version
     * @title
     * <AUTHOR>
     * @param AgentPO 业务员信息
     * @return AgentPO 业务员信息
     */
	 public AgentPO findAgentCodeAll(AgentPO agentPO);
	/**
	 *
	 * @description 查询打印份数 printCounts
	 * @version
	 * @title
	 * <AUTHOR> <EMAIL>
	 * @param bpoWageExtPO  制单费用核算信息对象
	 * @return
	 */
	public int findBpoWagePrintCounts(CsPolicyPrintFeePO bpoWageExtPO);
	/**
	 *
	 * @description 查询打印印数 questionCounts
	 * @version
	 * @title
	 * <AUTHOR> <EMAIL>
	 * @param bpoWageExtPO  制单费用核算信息对象
	 * @return
	 */
	public int findBpoWageQuestionCounts(CsPolicyPrintFeePO bpoWageExtPO);
	/**
	 *
	 * @description 查询不合格份数printNum
	 * @version
	 * @title
	 * <AUTHOR> <EMAIL>
	 * @param bpoWageExtPO  制单费用核算信息对象
	 * @return
	 */
	public int findBpoWagePrintNum(CsPolicyPrintFeePO bpoWageExtPO);
	/**
	 *
	 * @description 查询不合格份数printNotNum
	 * @version
	 * @title
	 * <AUTHOR> <EMAIL>
	 * @param bpoWageExtPO  制单费用核算信息对象
	 * @return
	 */
	public int findBpoWagePrintNotNum(CsPolicyPrintFeePO bpoWageExtPO);
	/**
	 *
	 * @description 查询需付费印数num
	 * @version
	 * @title
	 * <AUTHOR> <EMAIL>
	 * @param bpoWageExtPO  制单费用核算信息对象
	 * @return
	 */
	public int findBpoWageNum(CsPolicyPrintFeePO bpoWageExtPO);
	/**
	 *
	 * @description 制单费用明细清单下载
	 * @version
	 * @title
	 * <AUTHOR> <EMAIL>
	 * @param bpoWageExtPO  制单费用核算信息对象
	 * @return
	 */
	public List<CsPolicyPrintFeePO> downloadMakeFeeCalLists(CsPolicyPrintFeePO bpoWageExtPO);
	/**
	 *
	 * @description 提取外包商回传不成功清单
	 * @version
	 * @title
	 * <AUTHOR> <EMAIL>
	 * @param bpoWageExtPO  制单费用核算信息对象
	 * @return
	 */
	public List<CsPolicyPrintFeePO> downLoadMakeFeeUnQualifyDate(
			CsPolicyPrintFeePO bpoWageExtPO);
	/**
	 *
	 * @description 139437查询保全打印过纸质保单的信息
	 * @version
	 * @title
	 * <AUTHOR>
	 * @param policyPrintPO 保单纸质打印信息对象  
	 * @return
	 */
	public List<PolicyPrintPO> findAllCsPolicyPrintInfo(PolicyPrintPO policyPrintPO);
 }
 