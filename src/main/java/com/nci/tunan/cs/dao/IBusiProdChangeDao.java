package com.nci.tunan.cs.dao;

import java.util.List;
import java.util.Map;

import com.nci.tunan.cs.model.po.BusiProdChangePO;
import com.nci.udmp.framework.model.CurrentPage;


/** 
 * @description IBusiProdChangeDao接口
 * <AUTHOR> 
 * @date 2020-12-02 18:19:36  
 */
 public interface IBusiProdChangeDao {
	 /**
     * @description 增加数据
     * @version
     * @title
     * <AUTHOR>
     * @param busiProdChangePO 对象
     * @return BusiProdChangePO 添加结果
     */
	 public BusiProdChangePO addBusiProdChange(BusiProdChangePO busiProdChangePO);
	 
     /**
     * @description 删除数据
     * @version
     * @title
     * <AUTHOR>
     * @param busiProdChangePO 对象
     * @return boolean 删除是否成功
     */
	 public boolean deleteBusiProdChange(BusiProdChangePO busiProdChangePO);
	 
     /**
     * @description 修改数据
     * @version
     * @title
     * <AUTHOR>
     * @param busiProdChangePO 对象
     * @return BusiProdChangePO 修改结果
     */
	 public BusiProdChangePO updateBusiProdChange(BusiProdChangePO busiProdChangePO);
	 
     /**
     * @description 查询单条数据
     * @version
     * @title
     * <AUTHOR>
     * @param busiProdChangePO 对象
     * @return BusiProdChangePO 查询结果对象
     */
	 public BusiProdChangePO findBusiProdChange(BusiProdChangePO busiProdChangePO);
	 
     /**
     * @description 查询所有数据
     * @version
     * @title
     * <AUTHOR>
     * @param busiProdChangePO 对象
     * @return List<BusiProdChangePO> 查询结果List
     */
	 public List<BusiProdChangePO> findAllBusiProdChange(BusiProdChangePO busiProdChangePO);
	 
     /**
     * @description 查询数据条数
     * @version
     * @title
     * <AUTHOR>
     * @param busiProdChangePO 对象
     * @return int 查询结果条数
     */
	 public int findBusiProdChangeTotal(BusiProdChangePO busiProdChangePO);

     /**
     * @description 分页查询数据
     * @version
     * @title
     * <AUTHOR>
     * @param currentPage 当前页对象
     * @return CurrentPage<BusiProdChangePO> 查询结果的当前页对象
     */
	 public CurrentPage<BusiProdChangePO> queryBusiProdChangeForPage(BusiProdChangePO busiProdChangePO, CurrentPage<BusiProdChangePO> currentPage);

     /**
     * @description 批量增加数据
     * @version
     * @title
     * <AUTHOR>
     * @param busiProdChangePOList 对象列表
     * @return boolean 批量添加是否成功
     */
	 public boolean batchSaveBusiProdChange(List<BusiProdChangePO> busiProdChangePOList);

     /**
     * @description 批量修改数据
     * @version
     * @title
     * <AUTHOR>
     * @param busiProdChangePOList 对象列表
     * @return boolean 批量修改是否成功
     */
	 public boolean batchUpdateBusiProdChange(List<BusiProdChangePO> busiProdChangePOList);

     /**
     * @description 批量删除数据
     * @version
     * @title
     * <AUTHOR>
     * @param busiProdChangePOList 对象列表
     * @return boolean 批量删除是否成功
     */
	 public boolean batchDeleteBusiProdChange(List<BusiProdChangePO> busiProdChangePOList);

     /**
     * @description 查询所有数据 ，重新组装为map
     * @version
     * @title
     * <AUTHOR>
     * @param busiProdChangePO 对象
     * @return List<Map<String, Object>> 查询结果存放到map中
     */
	 public List<Map<String, Object>> findAllMapBusiProdChange(BusiProdChangePO busiProdChangePO);
 
	 /**
	  * 
	  * @description 查询变更记录
	  * @version V1.0.0
	  * @title
	  * <AUTHOR>
	  * @param busiProdChangePO
	  * @return
	  */
     public BusiProdChangePO findBusiProdChangeData(BusiProdChangePO busiProdChangePO);
	 
 }
 