package com.nci.tunan.cs.dao;

import com.nci.udmp.framework.model.*;
import java.util.Map;
import org.slf4j.Logger;
import java.util.List;
import com.nci.tunan.cs.model.po.HtmlInputTypePO;
import com.nci.udmp.util.logging.LoggerFactory;


/** 
 * @description 页面录入元素类型Dao接口
 * <AUTHOR> <EMAIL> 
 * @date 2016-02-03 14:47:23 
 * @.belongToModule 保全子系统 
 */
 public interface IHtmlInputTypeDao {
	 /**
     * @description 增加数据
     * @version
     * @title
     * <AUTHOR> <EMAIL>
     * @param htmlInputTypePO 对象 1-录入框PO
     * @return HtmlInputTypePO 添加结果
     */
	/**1-录入框PO*/
	 public HtmlInputTypePO addHtmlInputType(HtmlInputTypePO htmlInputTypePO);
	 
     /**
     * @description 删除数据
     * @version
     * @title
     * <AUTHOR> <EMAIL>
     * @param htmlInputTypePO 对象 1-录入框PO
     * @return boolean 删除是否成功
     */
	/**1-录入框PO*/
	 public boolean deleteHtmlInputType(HtmlInputTypePO htmlInputTypePO);
	 
     /**
     * @description 修改数据
     * @version
     * @title
     * <AUTHOR> <EMAIL>
     * @param htmlInputTypePO 对象 1-录入框PO
     * @return HtmlInputTypePO 修改结果
     */
	/**1-录入框PO*/
	 public HtmlInputTypePO updateHtmlInputType(HtmlInputTypePO htmlInputTypePO);
	 
     /**
     * @description 查询单条数据
     * @version
     * @title
     * <AUTHOR> <EMAIL>
     * @param htmlInputTypePO 对象 1-录入框PO
     * @return HtmlInputTypePO 查询结果对象
     */
	/**1-录入框PO*/
	 public HtmlInputTypePO findHtmlInputType(HtmlInputTypePO htmlInputTypePO);
	 
     /**
     * @description 查询所有数据
     * @version
     * @title
     * <AUTHOR> <EMAIL>
     * @param htmlInputTypePO 对象 1-录入框PO
     * @return List<HtmlInputTypePO> 查询结果List
     */
	/**1-录入框PO*/
	 public List<HtmlInputTypePO> findAllHtmlInputType(HtmlInputTypePO htmlInputTypePO);
	 
     /**
     * @description 查询数据条数
     * @version
     * @title
     * <AUTHOR> <EMAIL>
     * @param htmlInputTypePO 对象 1-录入框PO
     * @return int 查询结果条数
     */
	/**1-录入框PO*/
	 public int findHtmlInputTypeTotal(HtmlInputTypePO htmlInputTypePO);

     /**
     * @description 分页查询数据
     * @version
     * @title
     * <AUTHOR> <EMAIL>
     * @param currentPage 当前页对象
     * @return CurrentPage<HtmlInputTypePO> 查询结果的当前页对象
     */
	/**1-录入框PO*/
	 public CurrentPage<HtmlInputTypePO> queryHtmlInputTypeForPage(HtmlInputTypePO htmlInputTypePO, CurrentPage<HtmlInputTypePO> currentPage);

     /**
     * @description 批量增加数据
     * @version
     * @title
     * <AUTHOR> <EMAIL>
     * @param htmlInputTypePOList 对象列表 1-录入框PO
     * @return boolean 批量添加是否成功
     */
	/**1-录入框PO*/
	 public boolean batchSaveHtmlInputType(List<HtmlInputTypePO> htmlInputTypePOList);

     /**
     * @description 批量修改数据
     * @version
     * @title
     * <AUTHOR> <EMAIL>
     * @param htmlInputTypePOList 对象列表 1-录入框PO
     * @return boolean 批量修改是否成功
     */
	/**1-录入框PO*/
	 public boolean batchUpdateHtmlInputType(List<HtmlInputTypePO> htmlInputTypePOList);

     /**
     * @description 批量删除数据
     * @version
     * @title
     * <AUTHOR> <EMAIL>
     * @param htmlInputTypePOList 对象列表 1-录入框PO
     * @return boolean 批量删除是否成功
     */
	/**1-录入框PO*/
	 public boolean batchDeleteHtmlInputType(List<HtmlInputTypePO> htmlInputTypePOList);

     /**
     * @description 查询所有数据 ，重新组装为map
     * @version
     * @title
     * <AUTHOR> <EMAIL>
     * @param htmlInputTypePO 对象 1-录入框PO
     * @return List<Map<String, Object>> 查询结果存放到map中
     */
	/**1-录入框PO*/
	 public List<Map<String, Object>> findAllMapHtmlInputType(HtmlInputTypePO htmlInputTypePO);
	 
 }
 
