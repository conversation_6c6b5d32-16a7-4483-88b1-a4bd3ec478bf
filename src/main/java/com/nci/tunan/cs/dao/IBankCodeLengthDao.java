package com.nci.tunan.cs.dao;

import com.nci.udmp.framework.model.*;
import java.util.Map;
import org.slf4j.Logger;
import java.util.List;
import com.nci.tunan.cs.model.po.BankCodeLengthPO;
import com.nci.udmp.util.logging.LoggerFactory;



/**
 * 
 * @description 银行号码长度配置
 * @<NAME_EMAIL> 
 * @date 2016-10-14 下午2:30:57
 * @.belongToModule 保全系统-银行号码长度配置
 */
 public interface IBankCodeLengthDao {
	 /**
     * @description 增加数据
     * @version
     * @title
     * <AUTHOR> <EMAIL>
     * @param bankCodeLengthPO 对象 银行号码长度配置表，支持对银行存折号、卡号的长度配置PO
     * @return BankCodeLengthPO 添加结果
     */
	 public BankCodeLengthPO addBankCodeLength(BankCodeLengthPO bankCodeLengthPO);
	 
     /**
     * @description 删除数据
     * @version
     * @title
     * <AUTHOR> <EMAIL>
     * @param bankCodeLengthPO 对象 银行号码长度配置表，支持对银行存折号、卡号的长度配置PO
     * @return boolean 删除是否成功
     */
	 public boolean deleteBankCodeLength(BankCodeLengthPO bankCodeLengthPO);
	 
     /**
     * @description 修改数据
     * @version
     * @title
     * <AUTHOR> <EMAIL>
     * @param bankCodeLengthPO 对象 银行号码长度配置表，支持对银行存折号、卡号的长度配置PO
     * @return BankCodeLengthPO 修改结果
     */
	 public BankCodeLengthPO updateBankCodeLength(BankCodeLengthPO bankCodeLengthPO);
	 
     /**
     * @description 查询单条数据
     * @version
     * @title
     * <AUTHOR> <EMAIL>
     * @param bankCodeLengthPO 对象 银行号码长度配置表，支持对银行存折号、卡号的长度配置PO
     * @return BankCodeLengthPO 查询结果对象
     */
	 public BankCodeLengthPO findBankCodeLength(BankCodeLengthPO bankCodeLengthPO);
	 
     /**
     * @description 查询所有数据
     * @version
     * @title
     * <AUTHOR> <EMAIL>
     * @param bankCodeLengthPO 对象 银行号码长度配置表，支持对银行存折号、卡号的长度配置PO
     * @return List<BankCodeLengthPO> 查询结果List
     */
	 public List<BankCodeLengthPO> findAllBankCodeLength(BankCodeLengthPO bankCodeLengthPO);
	 
     /**
     * @description 查询数据条数
     * @version
     * @title
     * <AUTHOR> <EMAIL>
     * @param bankCodeLengthPO 对象 银行号码长度配置表，支持对银行存折号、卡号的长度配置PO
     * @return int 查询结果条数
     */
	 public int findBankCodeLengthTotal(BankCodeLengthPO bankCodeLengthPO);

     /**
     * @description 分页查询数据
     * @version
     * @title
     * <AUTHOR> <EMAIL>
     * @param currentPage 当前页对象
     * @return CurrentPage<BankCodeLengthPO> 查询结果的当前页对象
     */
	 public CurrentPage<BankCodeLengthPO> queryBankCodeLengthForPage(BankCodeLengthPO bankCodeLengthPO, CurrentPage<BankCodeLengthPO> currentPage);

     /**
     * @description 批量增加数据
     * @version
     * @title
     * <AUTHOR> <EMAIL>
     * @param bankCodeLengthPOList 对象列表 银行号码长度配置表，支持对银行存折号、卡号的长度配置PO
     * @return boolean 批量添加是否成功
     */
	 public boolean batchSaveBankCodeLength(List<BankCodeLengthPO> bankCodeLengthPOList);

     /**
     * @description 批量修改数据
     * @version
     * @title
     * <AUTHOR> <EMAIL>
     * @param bankCodeLengthPOList 对象列表 银行号码长度配置表，支持对银行存折号、卡号的长度配置PO
     * @return boolean 批量修改是否成功
     */
	 public boolean batchUpdateBankCodeLength(List<BankCodeLengthPO> bankCodeLengthPOList);

     /**
     * @description 批量删除数据
     * @version
     * @title
     * <AUTHOR> <EMAIL>
     * @param bankCodeLengthPOList 对象列表 银行号码长度配置表，支持对银行存折号、卡号的长度配置PO
     * @return boolean 批量删除是否成功
     */
	 public boolean batchDeleteBankCodeLength(List<BankCodeLengthPO> bankCodeLengthPOList);

     /**
     * @description 查询所有数据 ，重新组装为map
     * @version
     * @title
     * <AUTHOR> <EMAIL>
     * @param bankCodeLengthPO 对象 银行号码长度配置表，支持对银行存折号、卡号的长度配置PO
     * @return List<Map<String, Object>> 查询结果存放到map中
     */
	 public List<Map<String, Object>> findAllMapBankCodeLength(BankCodeLengthPO bankCodeLengthPO);
	 
 }
 
