package com.nci.tunan.cs.dao;

import com.nci.tunan.cs.dao.ITrustCompanyDao;
import com.nci.udmp.framework.model.*;
import java.util.Map;
import org.slf4j.Logger;
import java.util.List;
import com.nci.udmp.util.logging.LoggerFactory;
import com.nci.tunan.cs.model.po.TrustCompanyPO;


/** 
 * @description ITrustCompanyDao接口
 * <AUTHOR> 
 * @date 2021-10-11 16:11:35  
 */
 public interface ITrustCompanyDao {
	 /**
     * @description 增加数据
     * @version
     * @title
     * <AUTHOR>
     * @param trustCompanyPO 对象
     * @return TrustCompanyPO 添加结果
     */
	 public TrustCompanyPO addTrustCompany(TrustCompanyPO trustCompanyPO);
	 
     /**
     * @description 删除数据
     * @version
     * @title
     * <AUTHOR>
     * @param trustCompanyPO 对象
     * @return boolean 删除是否成功
     */
	 public boolean deleteTrustCompany(TrustCompanyPO trustCompanyPO);
	 
     /**
     * @description 修改数据
     * @version
     * @title
     * <AUTHOR>
     * @param trustCompanyPO 对象
     * @return TrustCompanyPO 修改结果
     */
	 public TrustCompanyPO updateTrustCompany(TrustCompanyPO trustCompanyPO);
	 
     /**
     * @description 查询单条数据
     * @version
     * @title
     * <AUTHOR>
     * @param trustCompanyPO 对象
     * @return TrustCompanyPO 查询结果对象
     */
	 public TrustCompanyPO findTrustCompany(TrustCompanyPO trustCompanyPO);
	 
     /**
     * @description 查询所有数据
     * @version
     * @title
     * <AUTHOR>
     * @param trustCompanyPO 对象
     * @return List<TrustCompanyPO> 查询结果List
     */
	 public List<TrustCompanyPO> findAllTrustCompany(TrustCompanyPO trustCompanyPO);
	 
     /**
     * @description 查询数据条数
     * @version
     * @title
     * <AUTHOR>
     * @param trustCompanyPO 对象
     * @return int 查询结果条数
     */
	 public int findTrustCompanyTotal(TrustCompanyPO trustCompanyPO);

     /**
     * @description 分页查询数据
     * @version
     * @title
     * <AUTHOR>
     * @param currentPage 当前页对象
     * @return CurrentPage<TrustCompanyPO> 查询结果的当前页对象
     */
	 public CurrentPage<TrustCompanyPO> queryTrustCompanyForPage(TrustCompanyPO trustCompanyPO, CurrentPage<TrustCompanyPO> currentPage);

     /**
     * @description 批量增加数据
     * @version
     * @title
     * <AUTHOR>
     * @param trustCompanyPOList 对象列表
     * @return boolean 批量添加是否成功
     */
	 public boolean batchSaveTrustCompany(List<TrustCompanyPO> trustCompanyPOList);

     /**
     * @description 批量修改数据
     * @version
     * @title
     * <AUTHOR>
     * @param trustCompanyPOList 对象列表
     * @return boolean 批量修改是否成功
     */
	 public boolean batchUpdateTrustCompany(List<TrustCompanyPO> trustCompanyPOList);

     /**
     * @description 批量删除数据
     * @version
     * @title
     * <AUTHOR>
     * @param trustCompanyPOList 对象列表
     * @return boolean 批量删除是否成功
     */
	 public boolean batchDeleteTrustCompany(List<TrustCompanyPO> trustCompanyPOList);

     /**
     * @description 查询所有数据 ，重新组装为map
     * @version
     * @title
     * <AUTHOR>
     * @param trustCompanyPO 对象
     * @return List<Map<String, Object>> 查询结果存放到map中
     */
	 public List<Map<String, Object>> findAllMapTrustCompany(TrustCompanyPO trustCompanyPO);
	 
 }
 