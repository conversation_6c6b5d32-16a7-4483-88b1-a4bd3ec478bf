package com.nci.tunan.cs.dao;

import com.nci.tunan.cs.model.po.SZYBDataSendPO;
import com.nci.udmp.framework.model.CurrentPage;

/**
 * <AUTHOR> <EMAIL>
 * @version V1.0.0
 * @description 深圳医保数据补推DAO层接口
 * @.belongToModule cs-保全子系统
 * @date 2021年03月19日  18:08
 */
public interface ISZYBDataSendDao {
    CurrentPage<SZYBDataSendPO> querySZYBDataSendPageBOForPageTF(CurrentPage<SZYBDataSendPO> currentPage);
    CurrentPage<SZYBDataSendPO> querySZYBDataSendPageBOForPageZZ(CurrentPage<SZYBDataSendPO> currentPage);
}
