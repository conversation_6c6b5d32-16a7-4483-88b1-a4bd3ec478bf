package com.nci.tunan.cs.dao;

import java.util.List;

import com.nci.tunan.cs.model.po.ExtendRateCfgPO;



/**
 * 
 * @description   保全重要资料变更表外费率使用记录表Dao
 * <AUTHOR> <EMAIL>
 * @version V1.0.0
 * @.belongToModule  保全子系统
 * @date 2020年12月26日 上午10:54:55
 */
public interface IExtendRateCfgDao {
	
	
	
	/**
	 * 
	 * @description 查询表外费率使用配置表列表
	 * @version V1.0.0
	 * @title
	 * <AUTHOR> <EMAIL>
	 * @param extendRateCfgPO 表外费率使用配置表PO
	 * @return
	 */
	public List<ExtendRateCfgPO> findAllExtendRateCfg(ExtendRateCfgPO extendRateCfgPO);
	
	
	
	/**
	 * 
	 * @description 查单条表外费率使用配置表
	 * @version V1.0.0
	 * @title
	 * <AUTHOR> <EMAIL>
	 * @param extendRateCfgPO 表外费率使用配置表PO
	 * @return
	 */
	public ExtendRateCfgPO findExtendRateCfg(ExtendRateCfgPO extendRateCfgPO);

}
