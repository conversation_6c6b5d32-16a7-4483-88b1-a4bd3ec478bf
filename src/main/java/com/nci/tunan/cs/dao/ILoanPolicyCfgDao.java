package com.nci.tunan.cs.dao;

import java.util.List;
import java.util.Map;

import com.nci.tunan.cs.model.po.LoanPolicyCfgPO;
import com.nci.udmp.framework.model.CurrentPage;


 /** 
 * @description ILoanPolicyCfgDao接口
 * <AUTHOR> <EMAIL> 
 * @date 2019-12-21 下午2:23:37 
 * @.belongToModule CS-保全子系统 
*/
public interface ILoanPolicyCfgDao {
	 /**
     * @description 增加数据
     * @version
     * @title
     * <AUTHOR> <EMAIL>
     * @param loanPolicyCfgPO 对象
     * @return LoanPolicyCfgPO 添加结果
     */
	 public LoanPolicyCfgPO addLoanPolicyCfg(LoanPolicyCfgPO loanPolicyCfgPO);
	 
     /**
     * @description 删除数据
     * @version
     * @title
     * <AUTHOR> <EMAIL>
     * @param loanPolicyCfgPO 对象
     * @return boolean 删除是否成功
     */
	 public boolean deleteLoanPolicyCfg(LoanPolicyCfgPO loanPolicyCfgPO);
	 
     /**
     * @description 修改数据
     * @version
     * @title
     * <AUTHOR> <EMAIL>
     * @param loanPolicyCfgPO 对象
     * @return LoanPolicyCfgPO 修改结果
     */
	 public LoanPolicyCfgPO updateLoanPolicyCfg(LoanPolicyCfgPO loanPolicyCfgPO);
	 
     /**
     * @description 查询单条数据
     * @version
     * @title
     * <AUTHOR> <EMAIL>
     * @param loanPolicyCfgPO 对象
     * @return LoanPolicyCfgPO 查询结果对象
     */
	 public LoanPolicyCfgPO findLoanPolicyCfg(LoanPolicyCfgPO loanPolicyCfgPO);
	 
     /**
     * @description 查询所有数据
     * @version
     * @title
     * <AUTHOR> <EMAIL>
     * @param loanPolicyCfgPO 对象
     * @return List<LoanPolicyCfgPO> 查询结果List
     */
	 public List<LoanPolicyCfgPO> findAllLoanPolicyCfg(LoanPolicyCfgPO loanPolicyCfgPO);
	 
     /**
     * @description 查询数据条数
     * @version
     * @title
     * <AUTHOR> <EMAIL>
     * @param loanPolicyCfgPO 对象
     * @return int 查询结果条数
     */
	 public int findLoanPolicyCfgTotal(LoanPolicyCfgPO loanPolicyCfgPO);

     /**
     * @description 分页查询数据
     * @version
     * @title
     * <AUTHOR> <EMAIL>
     * @param currentPage 当前页对象
     * @return CurrentPage<LoanPolicyCfgPO> 查询结果的当前页对象
     */
	 public CurrentPage<LoanPolicyCfgPO> queryLoanPolicyCfgForPage(LoanPolicyCfgPO loanPolicyCfgPO, CurrentPage<LoanPolicyCfgPO> currentPage);

     /**
     * @description 批量增加数据
     * @version
     * @title
     * <AUTHOR> <EMAIL>
     * @param loanPolicyCfgPOList 对象列表
     * @return boolean 批量添加是否成功
     */
	 public boolean batchSaveLoanPolicyCfg(List<LoanPolicyCfgPO> loanPolicyCfgPOList);

     /**
     * @description 批量修改数据
     * @version
     * @title
     * <AUTHOR> <EMAIL>
     * @param loanPolicyCfgPOList 对象列表
     * @return boolean 批量修改是否成功
     */
	 public boolean batchUpdateLoanPolicyCfg(List<LoanPolicyCfgPO> loanPolicyCfgPOList);

     /**
     * @description 批量删除数据
     * @version
     * @title
     * <AUTHOR> <EMAIL>
     * @param loanPolicyCfgPOList 对象列表
     * @return boolean 批量删除是否成功
     */
	 public boolean batchDeleteLoanPolicyCfg(List<LoanPolicyCfgPO> loanPolicyCfgPOList);

     /**
     * @description 查询所有数据 ，重新组装为map
     * @version
     * @title
     * <AUTHOR> <EMAIL>
     * @param loanPolicyCfgPO 对象
     * @return List<Map<String, Object>> 查询结果存放到map中
     */
	 public List<Map<String, Object>> findAllMapLoanPolicyCfg(LoanPolicyCfgPO loanPolicyCfgPO);
	 
	  /**
	     * @description 查询Rio比例
	     * @version
	     * @title
	     * <AUTHOR>
	     * @param loanPolicyCfgPO 对象
	     * @return LoanPolicyCfgPO 
	     */
	     public LoanPolicyCfgPO findRioLoanPolicyCfg( LoanPolicyCfgPO  loanPolicyCfgPO);
	    
	     /**
	  	 * 
	  	 * @description 保单层贷款配置-取消
	  	 * @version
	  	 * @title
	  	 * @<NAME_EMAIL>
	  	 * @param loanPolicyCfgVO
	  	 */
	 	public LoanPolicyCfgPO cancleLoanPolicyCfg(LoanPolicyCfgPO loanPolicyCfgPO);
	 	/**
		 * 删除某保单下该险种下的配置以及其子表的配置
		 * @param copyProperties
		 */
		public void delLoanPolicyCfg(LoanPolicyCfgPO loanPolicyCfgPO);

		/**
		 * 删除某保单下该险种下的配置以及其子表的配置
		 * @param copyProperties
		 */
		public void delLoanPolicyCfgRate(LoanPolicyCfgPO loanPolicyCfgPO);
 
		/**
		 * 通过主键修改受理号
		 */
		public void updateLoanPolicyCfgAcceptNo(LoanPolicyCfgPO loanPolicyCfgPO);

		/**
		 * 查询配置信息，去重
		 */
		public CurrentPage<LoanPolicyCfgPO> queryLoanPolicyCfgForPageList(LoanPolicyCfgPO loanPolicyCfgPO,
				CurrentPage<LoanPolicyCfgPO> currentPagePO);

		/**
		 * @description 查询保单主表总条数
		 * @version *******
		 * @title
		 * <AUTHOR> <EMAIL>
		 * @param loanPolicyCfgPO 查询参数
		 * @return 返回总条数
		*/
		int findContractMasterTotal(LoanPolicyCfgPO loanPolicyCfgPO);
 }
 
