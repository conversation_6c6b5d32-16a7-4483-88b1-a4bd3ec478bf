package com.nci.tunan.cs.dao;

import java.util.List;
import java.util.Map;

import com.nci.tunan.cs.model.po.FreeLookPeriodCfgPO;
import com.nci.tunan.cs.model.po.PolicyStatePO;
import com.nci.tunan.pa.interfaces.model.po.ContractMasterPO;
import com.nci.tunan.pa.interfaces.model.po.PremArapPO;

/**
 * @description 查询保单状态Dao接口
 * @<NAME_EMAIL> 
 * @date 2015-05-15
 * @.belongToModule 保全子系统  查询保单状态Dao接口
 */
public interface ICsPolicyStateDao {
	
	
	/**
	 * @description  查询保单状态中的保单贷款是否存在
	 * @version  v1.1
	 * @title
	 * <AUTHOR> <EMAIL>
	 * @param policyStatePO
	 * @param sqlStr
	 * @return List<Map<String,Object>>
	 */
	public List<Map<String,Object>> findPolicyState(PolicyStatePO policyStatePO,String sqlStr);
	/**
	 * @description  查询犹豫期配置list
	 * @version  v1.1
	 * @title
	 * <AUTHOR> <EMAIL>
	 * @param freeLookPeriodCfgPO
	 * @param sqlStr
	 * @return  List<FreeLookPeriodCfgPO>
	 */
	public List<FreeLookPeriodCfgPO> findFreeLookPeriodCfg(FreeLookPeriodCfgPO freeLookPeriodCfgPO,String sqlStr);
	
	/**
	 * @description   获取保单基本信息
	 * @version  v1.1
	 * @title
	 * <AUTHOR> <EMAIL>
	 * @param contractMasterPO
	 * @return  ContractMasterPO
	 */
	public ContractMasterPO findContractMaster(ContractMasterPO contractMasterPO);
	
	/**
	 * @description 查询保单是否包含688险种
	 * @version  v1.1
	 * @title
	 * <AUTHOR> <EMAIL>
	 * @param policyStatePO
	 * @return
	 */
    public int queryIsConstants688(PolicyStatePO policyStatePO);

    /**
     * @description  查询保单基本信息
     * @version  v1.1
     * @title
     * <AUTHOR> <EMAIL>
     * @param policyStatePO
     * @return
     */
	public List<Map<String, Object>> findPolicyBaseInfoList(PolicyStatePO policyStatePO);
	/**
	 * @description  查询保全基本信息
	 * @version  v1.1
	 * @title
	 * <AUTHOR> <EMAIL>
	 * @param policyStatePO
	 * @return
	 */
	public List<Map<String, Object>> findAcceptBaseInfoList(PolicyStatePO policyStatePO);
	/**
	 * 
	 * @description 查询险种基本信息
	 * @version  v1.1
	 * @title
	 * <AUTHOR> <EMAIL>
	 * @param policyStatePO
	 * @return
	 */
	public List<Map<String, Object>> findBusiProdBaseInfo(PolicyStatePO policyStatePO);
	/**
	 * @description   查询付费基本信息
	 * @version  v1.1
	 * @title
	 * <AUTHOR> <EMAIL>
	 * @param policyStatePO
	 * @return
	 */
	public List<Map<String, Object>> findPayPlanBaseInfo(PolicyStatePO policyStatePO);
	/**
	 * 
	 * @description  查询应收应付状态
	 * @version  v1.1
	 * @title
	 * <AUTHOR> <EMAIL>
	 * @param policyStatePO
	 * @return
	 */
	public List<Map<String, Object>> findPremArapBaseInfo(PolicyStatePO policyStatePO);
	
	/**
	 * @description  查询账户状态
	 * @version  v1.1
	 * @title
	 * <AUTHOR> <EMAIL>
	 * @param policyStatePO
	 * @return
	 */
	public List<Map<String, Object>> findAccountBaseInfo(PolicyStatePO policyStatePO);
	
	/**
	 * 
	 * @description  保单应缴日信息
	 * @version  v1.1
	 * @title
	 * <AUTHOR> <EMAIL>
	 * @param policyStatePO
	 * @return
	 */
	public List<Map<String, Object>> findExtendBaseInfo(PolicyStatePO policyStatePO);
	
	/**
	 * @description  判断顾客身份
	 * @version  v1.1
	 * @title
	 * <AUTHOR> <EMAIL>
	 * @param policyStatePO
	 * @return
	 */
	public List<Map<String, Object>> getIsHolderOrInsured(PolicyStatePO policyStatePO);
	
	/**
	 * 
	 * @description 查询是否有应付未实付记录
	 * @version
	 * @title
	 * <AUTHOR>
	 * @param policyStatePO
	 * @return 
	 */
    public int findNoPayCount(PolicyStatePO policyStatePO);
    /**
     * @description  查询未核销的收付费数据
     * @version  v1.1
     * @title
     * <AUTHOR> <EMAIL>
     * @param policyStatePO
     * @return
     */
    public int findNoPayStatusCount(PolicyStatePO policyStatePO);
	/**
	 * @description  查询收付费状态
	 * @version  v1.1
	 * @title
	 * <AUTHOR> <EMAIL>
	 * @param policyStatePO
	 * @return
	 */
    public int findPremArapStatus(PremArapPO policyStatePO);
    
    /**
     * @description 查询保单下是否存在“生存金、年金批处理”自动推送收费系统的生存金、年金，且该笔生存金年金未实际给付
     * @version
     * @title
     * <AUTHOR>
     * @param policyStatePO
     * @return
     */
	public List<Map<String, Object>> findBatchGrantSurvivalByPolicyCode(PolicyStatePO policyStatePO);
	
	/**
	 * 
	 * @description 查询关联保单的失效日期
	 * @version V1.0.0
	 * @title
	 * <AUTHOR>
	 * @param policyCode
	 * @return
	 */
	public PolicyStatePO queryRelationPolicyLapseDate(PolicyStatePO policyStatePO) ;
}
