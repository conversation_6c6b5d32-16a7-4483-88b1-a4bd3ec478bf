package com.nci.tunan.cs.dao;

import java.util.List;
import java.util.Map;

import com.nci.tunan.cs.model.po.CsInsuredCustomerPO;
import com.nci.tunan.cs.model.po.CsInsuredListPO;
import com.nci.tunan.pa.batch.survivalAnnuityMaturity.po.QueryPO;
import com.nci.udmp.framework.model.CurrentPage;


/** 
 * @description ICsInsuredListDao接口
 * <AUTHOR> <EMAIL> 
 * @.belongToModule 保全
 * @date 2015-06-03 17:37:43  
 */
 public interface ICsInsuredListDao {
	 /**
     * @description 增加数据
     * @version
     * @title
     * <AUTHOR> <EMAIL>
     * @param param csInsuredListPO 对象 保单被保人列表PO
     * @return CsInsuredListPO 添加结果
     */
	/**保单被保人列表PO*/
	 public CsInsuredListPO addCsInsuredList(CsInsuredListPO csInsuredListPO);
	 
     /**
     * @description 删除数据
     * @version
     * @title
     * <AUTHOR> <EMAIL>
     * @param param csInsuredListPO 对象 保单被保人列表PO
     * @return boolean 删除是否成功
     */
	/**保单被保人列表PO*/
	 public boolean deleteCsInsuredList(CsInsuredListPO csInsuredListPO);
	 
     /**
     * @description 修改数据
     * @version
     * @title
     * <AUTHOR> <EMAIL>
     * @param param csInsuredListPO 对象 保单被保人列表PO
     * @return CsInsuredListPO 修改结果
     */
	/**保单被保人列表PO*/
	 public CsInsuredListPO updateCsInsuredList(CsInsuredListPO csInsuredListPO);
	 
     /**
     * @description 查询单条数据
     * @version
     * @title
     * <AUTHOR> <EMAIL>
     * @param param csInsuredListPO 对象 保单被保人列表PO
     * @return CsInsuredListPO 查询结果对象
     */
	/**保单被保人列表PO*/
	 public CsInsuredListPO findCsInsuredList(CsInsuredListPO csInsuredListPO);
	 /**
	  * @description 查询单条数据
	  * @version
	  * @title
	  * <AUTHOR> <EMAIL>
	  * @param param csInsuredListPO 对象 保单被保人列表PO
	  * @return CsInsuredListPO 查询结果对象
	  */
	/**保单被保人列表PO*/
	 public CsInsuredListPO findCsInsuredList1(CsInsuredListPO csInsuredListPO);
	 
     /**
     * @description 查询所有数据
     * @version
     * @title
     * <AUTHOR> <EMAIL>
     * @param param csInsuredListPO 对象 保单被保人列表PO
     * @return List<CsInsuredListPO> 查询结果List
     */
	/**保单被保人列表PO*/
	 public List<CsInsuredListPO> findAllCsInsuredList(CsInsuredListPO csInsuredListPO);
	 /**141437
		 * @param csInsuredListPO
		 * @return
		 */
		public CsInsuredListPO querySocialInsurance(CsInsuredListPO csInsuredListPO);
     /**
     * @description 查询数据条数
     * @version
     * @title
     * <AUTHOR> <EMAIL>
     * @param param csInsuredListPO 对象 保单被保人列表PO
     * @return int 查询结果条数
     */
	/**保单被保人列表PO*/
	 public int findCsInsuredListTotal(CsInsuredListPO csInsuredListPO);

     /**
     * @description 分页查询数据
     * @version
     * @title
     * <AUTHOR> <EMAIL>
     * @param param currentPage 当前页对象
     * @return CurrentPage<CsInsuredListPO> 查询结果的当前页对象
     */
	/**保单被保人列表PO*/
	 public CurrentPage<CsInsuredListPO> queryCsInsuredListForPage(CsInsuredListPO csInsuredListPO, CurrentPage<CsInsuredListPO> currentPage);

     /**
     * @description 批量增加数据
     * @version
     * @title
     * <AUTHOR> <EMAIL>
     * @param param csInsuredListPOList 对象列表 保单被保人列表PO
     * @return boolean 批量添加是否成功
     */
	/**保单被保人列表PO*/
	 public boolean batchSaveCsInsuredList(List<CsInsuredListPO> csInsuredListPOList);

     /**
     * @description 批量修改数据
     * @version
     * @title
     * <AUTHOR> <EMAIL>
     * @param param csInsuredListPOList 对象列表 保单被保人列表PO
     * @return boolean 批量修改是否成功
     */
	/**保单被保人列表PO*/
	 public boolean batchUpdateCsInsuredList(List<CsInsuredListPO> csInsuredListPOList);

     /**
     * @description 批量删除数据
     * @version
     * @title
     * <AUTHOR> <EMAIL>
     * @param param csInsuredListPOList 对象列表 保单被保人列表PO
     * @return boolean 批量删除是否成功
     */
	/**保单被保人列表PO*/
	 public boolean batchDeleteCsInsuredList(List<CsInsuredListPO> csInsuredListPOList);

     /**
     * @description 查询所有数据 ，重新组装为map
     * @version
     * @title
     * <AUTHOR> <EMAIL>
     * @param param csInsuredListPO 对象 保单被保人列表PO
     * @return List<Map<String, Object>> 查询结果存放到map中
     */
	/**保单被保人列表PO*/
	 public List<Map<String, Object>> findAllMapCsInsuredList(CsInsuredListPO csInsuredListPO);
	 //更新被保人投保年龄
	 public boolean updateInsuredAge(List<CsInsuredListPO> csInsuredListPOList);
	 /**
	  * 按chgID删除被保人
	  * @param param csInsuredListPO 保单被保人列表PO
	  * @return
	  */
	/**保单被保人列表PO*/
	 public boolean deleteCsInsuredListByChgID(CsInsuredListPO csInsuredListPO);
	 
	 /**保单被保人信息
	  * 
	  * @param param csInsuredCustomerPO 客户表PO
	  * @return
	  */
	/**客户表PO*/
	 public List<CsInsuredCustomerPO> findCsInsuredLists(CsInsuredCustomerPO csInsuredCustomerPO);
	 
	 /**投保人或被保人历史保单的被保人信息
	  * 
	  * @param param csInsuredCustomerPO 客户表PO
	  * @return
	  */
	/**客户表PO*/
	 public List<CsInsuredCustomerPO> findInsuredLists(CsInsuredCustomerPO csInsuredCustomerPO);
	
	/**根据保单号查询所有被保人id
     * @version
     * @title
     * <AUTHOR>
     * @param param csInsuredListPO 对象 保单被保人列表PO
     * @return List<CsInsuredListPO> 查询结果
     */
	/**保单被保人列表PO*/
	 public List<CsInsuredListPO> findAllCustomerIdByPolicyCode(CsInsuredListPO csInsuredListPO);
	 /**根据保单号和被保人姓名查询被保人id
	  * @version
	  * @title
	  * <AUTHOR>
	  * @param param csInsuredListPO 对象 保单被保人列表PO
	  * @return CsInsuredListPO 查询结果
	  */
	/**保单被保人列表PO*/
	 public CsInsuredListPO findCustomerIdByPolicyCodeAndName(CsInsuredListPO csInsuredListPO);
	 /**根据保全变更ID删除操作
     * @version
     * @title
     * <AUTHOR>
     * @param param csInsuredListPO 对象 保单被保人列表PO
     * @return List<CsInsuredListPO> 查询结果
     */
	/**保单被保人列表PO*/
	 public boolean deleteCsInsuredListByChangeID(CsInsuredListPO csInsuredListPO);

	/**保单被保人列表PO*/
	public boolean deleteCsInsuredListByPolicyChgId(CsInsuredListPO csInsuredListPO);
	/**
	 * 告知查询被保人 信息
	 * @param param csInsuredListPO 保单被保人列表PO
	 * @return
	 */
	/**保单被保人列表PO*/
	public List<Map<String,  Object>> findAllCsInsuredListForHI(CsInsuredListPO csInsuredListPO);
	
	/**
     * @description 根据保单号查询所有被保人
     * @version
     * @title
     * <AUTHOR> <EMAIL>
     * @param param csInsuredListPO 对象 保单被保人列表PO
     * @return List<CsInsuredListPO> 查询结果List
     */
	/**保单被保人列表PO*/
	 public List<CsInsuredListPO> queryInsuredListByPolicy(CsInsuredListPO csInsuredListPO);
	 
	 /**
     * @description 根据申请、受理ID和客户查询受理的相关保单
     * @version
     * @title
     * <AUTHOR> <EMAIL>
     * @param param csInsuredListPO 对象 保单被保人列表PO
     * @return List<CsInsuredListPO> 查询结果List
     */
	/**保单被保人列表PO*/
	 public List<CsInsuredListPO> findCsInsuredByChangeIdAcceptCustomer(CsInsuredListPO csInsuredListPO);
	 
	 /**
	  * 
	  * @description 修改社保状态
	  * @version V1.0.0
	  * @title
	  * @<NAME_EMAIL>
	  * @param param csInsuredListPO 保单被保人列表PO
	  */
	/**保单被保人列表PO*/
	 public void updateInsuredSociSecu(CsInsuredListPO csInsuredListPO);
	 /**
	  * 
	  * @description 保全新增附加险查询被保人信息
	  * @version
	  * @title
	  * <AUTHOR> <EMAIL>
	  * @param param queryPO  busi_item_id  policy_code
	  * @return
	  */
	public List<QueryPO> queryInsuredInfoForEffectOfNs(QueryPO queryPO);
	 /**
     * @description 根据acceptid查询所有数据
     * @version
     * @title
     * @<NAME_EMAIL>
     * @param param csInsuredListPO 对象 保单被保人列表PO
     * @return List<CsInsuredListPO> 查询结果List
     */
	public List<CsInsuredListPO> acceptIdQueryCsInsuredList(CsInsuredListPO csInsuredListPO);
	/**
     * @description 根据custome_id和policy_id查询该数据是否存在
     * @version
     * @title
     * @<NAME_EMAIL>
     * @param param csInsuredListPO 对象 保单被保人列表PO
     * @return List<CsInsuredListPO> 查询结果int
     */
	public int customeIdQueryCsInsuredCount(CsInsuredListPO csInsuredListPO);
	
	/**
	 * 通过policyChgId查询年收入和既往年收入
	 */
    public List<CsInsuredListPO> queryNowOrHistoricalAnnualIncome(CsInsuredListPO csInsuredListPO);
  //83003
	public List<CsInsuredCustomerPO> findCsInsuredInfoLists(CsInsuredCustomerPO csInsuredCustomerPO);
	/**
     * @description 查询CS保单被保人列表单条数据
     * @version
     * @title
     * <AUTHOR>
     * @param CsInsuredListPO 保单被保人列表对象
     * @return CsInsuredListPO 查询结果对象
     */
	 public CsInsuredListPO findCsInsuredListCs(CsInsuredListPO CsInsuredListPO);
	 
	 List<CsInsuredListPO> findCsInsuredListInfo(CsInsuredListPO csInsuredListPO);

     public List<CsInsuredListPO> queryCsInsuredList(CsInsuredListPO csInsuredListPO);

     public CsInsuredListPO queryInsuredNames(CsInsuredListPO csInsuredListPO);
     /**
      * @description 查询申请下被保人列表对象
      * @version
      * @title
      * <AUTHOR>
      * @param CsInsuredListPO 保单被保人列表对象
      * @return CsInsuredListPO 查询结果对象
      */
     public List<CsInsuredListPO> findCsInsuredListsForRLAndLN(CsInsuredListPO csInsuredListPO);
     
 }
 
