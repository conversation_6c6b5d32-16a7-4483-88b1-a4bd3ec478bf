package com.nci.tunan.cs.dao;

import java.util.List;

import com.nci.tunan.cs.model.po.FjNsInsuredListPO;
import com.nci.tunan.cs.model.po.FjNsPolicyHolderPO;
import com.nci.tunan.cs.model.po.FjNsPolicyPO;
import com.nci.tunan.cs.model.po.FjNsRiskPO;
import com.nci.tunan.cs.model.po.FjRiskPO;
/**
 * 
 * @description IQueryNsPolicyDao接口
 * @<NAME_EMAIL> 
 * @date 2015-5-15
 * @.belongToModule
 */
public interface IQueryNsPolicyDao {
	/**
	 * 
	 * @description 查询新增附加险保单信息
	 * @version
	 * @title
	 * <AUTHOR> <EMAIL>
	 * @param paramFjNsPolicyPO
	 * @return List<FjNsPolicyPO>
	 */
	public List<FjNsPolicyPO> queryFjNsPolicy(FjNsPolicyPO paramFjNsPolicyPO);
	/**
	 * 
	 * @description 查询险种
	 * @version
	 * @title
	 * <AUTHOR> <EMAIL>
	 * @param paramFjRiskPO
	 * @return List<FjRiskPO>
	 */
	public List<FjRiskPO> queryFjRisk(FjRiskPO paramFjRiskPO);

	/**
	 * 
	 * @description 查询新增附加险险种
	 * @version
	 * @title
	 * <AUTHOR> <EMAIL>
	 * @param paramFjNsRiskPO
	 * @return List<FjNsRiskPO>
	 */
	public List<FjNsRiskPO> queryFjNsRisk(FjNsRiskPO paramFjNsRiskPO);

	/**
	 * 
	 * @description 保单投保人表PO
	 * @version
	 * @title
	 * <AUTHOR> <EMAIL>
	 * @param paramFjNsPolicyHolderPO
	 * @return List<FjNsPolicyHolderPO>
	 */
	public List<FjNsPolicyHolderPO> queryFjNsPolicyHolder(FjNsPolicyHolderPO paramFjNsPolicyHolderPO);

	/**
	 * 
	 * @description 保单被保人列表PO
	 * @version
	 * @title
	 * <AUTHOR> <EMAIL>
	 * @param paramFjNsInsuredListPO
	 * @return List<FjNsInsuredListPO>
	 */
	public List<FjNsInsuredListPO> queryFjNsInsured(FjNsInsuredListPO paramFjNsInsuredListPO);
	
	/**
	 * 
	 * @description 查询代码
	 * @version
	 * @title
	 * <AUTHOR> <EMAIL>
	 * @param paramFjNsPolicyPO
	 * @return List<FjNsPolicyPO>
	 */
	public List<FjNsPolicyPO> queryGreyCode(FjNsPolicyPO paramFjNsPolicyPO);
}
