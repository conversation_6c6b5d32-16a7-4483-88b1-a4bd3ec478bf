package com.nci.tunan.cs.dao;

import java.util.List;

import com.nci.tunan.cs.model.po.BonusMsfCfgPO;
import com.nci.udmp.framework.model.CurrentPage;
/**
 * 
 * @description ICsBonusMsfCfgDao接口 
 * <AUTHOR> <EMAIL> 
 * @date 2015-05-15 下午8:42:16 
 * @.belongToModule 保全系统-ICsBonusMsfCfgDao接口
 */
public interface ICsBonusMsfCfgDao {
	/**
	 * 
	 * @description 添加红利短信范围配置
	 * @version
	 * @title
	 * <AUTHOR> <EMAIL> 
	 * @param bonusMsfCfgPO bonusMsfCfgPO对象
	 * @return BonusMsfCfgPO
	 */
	public BonusMsfCfgPO add(BonusMsfCfgPO bonusMsfCfgPO);
	
	/**
	 * 
	 * @description 添加红利短信范围配置历史
	 * @version
	 * @title
	 * <AUTHOR> <EMAIL>
	 * @param bonusMsfCfgHisPO BonusMsfCfgPO对象
	 * @return BonusMsfCfgPO
	 */
	public BonusMsfCfgPO addHis(BonusMsfCfgPO bonusMsfCfgHisPO);
	
	/**
	 * 
	 * @description 根据条件获取全部的信息
	 * @version
	 * @title
	 * <AUTHOR> <EMAIL>
	 * @param bonusMsfCfgPO BonusMsfCfgPO对象
	 * @return BonusMsfCfgPO
	 */
	public List<BonusMsfCfgPO> findAllBonusMsfCfgPOs(BonusMsfCfgPO bonusMsfCfgPO);
	
	/**
	 * 
	 * @description 删除红利短信范围配置
	 * @version
	 * @title
	 * <AUTHOR> <EMAIL>
	 * @param bonusMsfCfgPO BonusMsfCfgPO对象
	 * @return 布尔
	 */
	public boolean detBonusMsfCfgPO(BonusMsfCfgPO bonusMsfCfgPO);
	
	/**
	 * 
	 * @description 分页查询红利短信范围配置
	 * @version
	 * @title
	 * <AUTHOR> <EMAIL>
	 * @param bonusMsfCfgPO bonusMsfCfgPO对象
	 * @param currentPage 分页对象
	 * @return BonusMsfCfgPO
	 */
	public CurrentPage<BonusMsfCfgPO> queryCsBonusMsfForPage(BonusMsfCfgPO bonusMsfCfgPO,CurrentPage<BonusMsfCfgPO> currentPage);
	
	/**
	 * 
	 * @description 分页查询红利短信范围历史配置
	 * @version
	 * @title
	 * <AUTHOR> <EMAIL>
	 * @param bonusMsfCfgHisPO BonusMsfCfgPO对象
	 * @param currentPage 分页对象
	 * @return BonusMsfCfgPO
	 */
	public CurrentPage<BonusMsfCfgPO> queryCsBonusMsfHisForPage(BonusMsfCfgPO bonusMsfCfgHisPO,CurrentPage<BonusMsfCfgPO> currentPage);

	/**
	 * 
	 * @description 查询单条数据
	 * @version
	 * @title
	 * <AUTHOR> <EMAIL>
	 * @param bonusMsfCfgHisPO BonusMsfCfgPO对象
	 * @return BonusMsfCfgPO
	 */
	public BonusMsfCfgPO findBonusMsfCfgPO(BonusMsfCfgPO bonusMsfCfgHisPO);
	
}
