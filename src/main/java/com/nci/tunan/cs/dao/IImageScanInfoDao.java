package com.nci.tunan.cs.dao;


import java.util.List;
import java.util.Map;

import com.nci.tunan.cs.model.po.ImageScanInfoPO;
import com.nci.udmp.framework.model.CurrentPage;


/**
 * 
 * @description 影像扫描信息表
 * <AUTHOR> <EMAIL> 
 * @date 2015年05月15日
 * @.belongToModule 保全子系统-影像扫描信息表
 */
 public interface IImageScanInfoDao {
	 /**
     * @description 增加数据
     * @version V1.0.0
     * @title
     * <AUTHOR> <EMAIL>
     * @param ImageScanInfoPO 对象
     * @return ImageScanInfoPO 添加结果
     */
	 public ImageScanInfoPO addImageScanInfo(ImageScanInfoPO imageScanInfoPO);
	 
     /**
     * @description 删除数据
     * @version V1.0.0
     * @title
     * <AUTHOR> <EMAIL>
     * @param ImageScanInfoPO 对象
     * @return boolean 删除是否成功
     */
	 public boolean deleteImageScanInfo(ImageScanInfoPO imageScanInfoPO);
	 
     /**
     * @description 删除数据 通过申请号
     * @version V1.0.0
     * @title
     * <AUTHOR> <EMAIL>
     * @param ImageScanInfoPO 对象
     * @return boolean 删除是否成功
     */
	 public boolean deleteImageScanInfoByApplyCode(ImageScanInfoPO imageScanInfoPO);
	 
     /**
     * @description 修改数据
     * @version V1.0.0
     * @title
     * <AUTHOR> <EMAIL>
     * @param ImageScanInfoPO 对象
     * @return ImageScanInfoPO 修改结果
     */
	 public ImageScanInfoPO updateImageScanInfo(ImageScanInfoPO imageScanInfoPO);
	 
     /**
     * @description 查询单条数据
     * @version V1.0.0
     * @title
     * <AUTHOR> <EMAIL>
     * @param ImageScanInfoPO 对象
     * @return ImageScanInfoPO 查询结果对象
     */
	 public ImageScanInfoPO findImageScanInfo(ImageScanInfoPO imageScanInfoPO);
	 
     /**
     * @description 查询所有数据
     * @version V1.0.0
     * @title
     * <AUTHOR> <EMAIL>
     * @param ImageScanInfoPO 对象
     * @return List<ImageScanInfoPO> 查询结果List
     */
	 public List<ImageScanInfoPO> findAllImageScanInfo(ImageScanInfoPO imageScanInfoPO);
	 
     /**
     * @description 查询数据条数
     * @version V1.0.0
     * @title
     * <AUTHOR> <EMAIL>
     * @param ImageScanInfoPO 对象
     * @return int 查询结果条数
     */
	 public int findImageScanInfoTotal(ImageScanInfoPO imageScanInfoPO);

     /**
     * @description 分页查询数据
     * @version V1.0.0
     * @title
     * <AUTHOR> <EMAIL>
     * @param currentPage 当前页对象
     * @return CurrentPage<ImageScanInfoPO> 查询结果的当前页对象
     */
	 public CurrentPage<ImageScanInfoPO> queryImageScanInfoForPage(ImageScanInfoPO imageScanInfoPO, CurrentPage<ImageScanInfoPO> currentPage);

     /**
     * @description 批量增加数据
     * @version V1.0.0
     * @title
     * <AUTHOR> <EMAIL>
     * @param ImageScanInfoPOList 对象列表
     * @return boolean 批量添加是否成功
     */
	 public boolean batchSaveImageScanInfo(List<ImageScanInfoPO> imageScanInfoPOList);

     /**
     * @description 批量修改数据
     * @version V1.0.0
     * @title
     * <AUTHOR> <EMAIL>
     * @param ImageScanInfoPOList 对象列表
     * @return boolean 批量修改是否成功
     */
	 public boolean batchUpdateImageScanInfo(List<ImageScanInfoPO> imageScanInfoPOList);

     /**
     * @description 批量删除数据
     * @version V1.0.0
     * @title
     * <AUTHOR> <EMAIL>
     * @param ImageScanInfoPOList 对象列表
     * @return boolean 批量删除是否成功
     */
	 public boolean batchDeleteImageScanInfo(List<ImageScanInfoPO> imageScanInfoPOList);
	 
	 /**116569 是否存在本地上传的非音视频资料
	 * @param imageScanInfoPO
	 * @return
	 */
	public int findImageImportTotal(ImageScanInfoPO imageScanInfoPO);
     /**
     * @description 查询所有数据 ，重新组装为map
     * @version V1.0.0
     * @title
     * <AUTHOR> <EMAIL>
     * @param ImageScanInfoPO 对象
     * @return List<Map<String, Object>> 查询结果存放到map中
     */
	 public List<Map<String, Object>> findAllMapImageScanInfo(ImageScanInfoPO imageScanInfoPO);
	 
 }
 
