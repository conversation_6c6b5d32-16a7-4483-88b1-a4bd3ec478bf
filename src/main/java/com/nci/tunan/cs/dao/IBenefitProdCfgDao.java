package com.nci.tunan.cs.dao;

import com.nci.tunan.cs.model.po.AddDutySaLimitCfgPO;
import com.nci.tunan.cs.model.po.AddRiderUniversalCfgPO;
import com.nci.tunan.cs.model.po.ApplicantTypeCfgPO;
import com.nci.tunan.cs.model.po.AutoSignCfgPO;
import com.nci.tunan.cs.model.po.AutoVisitCfgPO;
import com.nci.tunan.cs.model.po.BatchSurrenderEventPO;
import com.nci.tunan.cs.model.po.BatchSurrenderImportErrPO;
import com.nci.tunan.cs.model.po.BatchSurrenderImportPO;
import com.nci.tunan.cs.model.po.BatchSurrenderPolicyPO;
import com.nci.tunan.cs.model.po.BenefitProdCfgPO;
import com.nci.udmp.framework.model.*;

import java.util.Map;
import java.util.List;

import com.nci.udmp.util.logging.LoggerFactory;

import org.slf4j.Logger;


/** 
 * @description IBenefitProdCfgDao接口
 * <AUTHOR> <EMAIL> 
 * @date 2015-06-03 17:37:43
 * @.belongToModule 保全子系统   
 */
 public interface IBenefitProdCfgDao {
	 /**
     * @description 增加数据
     * @version
     * @title
     * <AUTHOR> <EMAIL>
     * @param benefitProdCfgPO 对象
     * @return BenefitProdCfgPO 添加结果
     */
	 public BenefitProdCfgPO addBenefitProdCfg(BenefitProdCfgPO benefitProdCfgPO);
	 
     /**
     * @description 删除数据
     * @version
     * @title
     * <AUTHOR> <EMAIL>
     * @param benefitProdCfgPO 对象
     * @return boolean 删除是否成功
     */
	 public boolean deleteBenefitProdCfg(BenefitProdCfgPO benefitProdCfgPO);
	 
     /**
     * @description 修改数据
     * @version
     * @title
     * <AUTHOR> <EMAIL>
     * @param benefitProdCfgPO 对象
     * @return BenefitProdCfgPO 修改结果
     */
	 public BenefitProdCfgPO updateBenefitProdCfg(BenefitProdCfgPO benefitProdCfgPO);
	 
     /**
     * @description 查询单条数据
     * @version
     * @title
     * <AUTHOR> <EMAIL>
     * @param benefitProdCfgPO 对象
     * @return BenefitProdCfgPO 查询结果对象
     */
	 public BenefitProdCfgPO findBenefitProdCfg(BenefitProdCfgPO benefitProdCfgPO);
	 
     /**
     * @description 查询所有数据
     * @version
     * @title
     * <AUTHOR> <EMAIL>
     * @param benefitProdCfgPO 对象
     * @return List<BenefitProdCfgPO> 查询结果List
     */
	 public List<BenefitProdCfgPO> findAllBenefitProdCfg(BenefitProdCfgPO benefitProdCfgPO);
	 
     /**
     * @description 查询数据条数
     * @version
     * @title
     * <AUTHOR> <EMAIL>
     * @param benefitProdCfgPO 对象
     * @return int 查询结果条数
     */
	 public int findBenefitProdCfgTotal(BenefitProdCfgPO benefitProdCfgPO);

     /**
     * @description 分页查询数据
     * @version
     * @title
     * <AUTHOR> <EMAIL>
     * @param currentPage 当前页对象
     * @return CurrentPage<BenefitProdCfgPO> 查询结果的当前页对象
     */
	 public CurrentPage<BenefitProdCfgPO> queryBenefitProdCfgForPage(BenefitProdCfgPO benefitProdCfgPO, CurrentPage<BenefitProdCfgPO> currentPage);

     /**
     * @description 批量增加数据
     * @version
     * @title
     * <AUTHOR> <EMAIL>
     * @param benefitProdCfgPOList 对象列表
     * @return boolean 批量添加是否成功
     */
	 public boolean batchSaveBenefitProdCfg(List<BenefitProdCfgPO> benefitProdCfgPOList);

     /**
     * @description 批量修改数据
     * @version
     * @title
     * <AUTHOR> <EMAIL>
     * @param benefitProdCfgPOList 对象列表
     * @return boolean 批量修改是否成功
     */
	 public boolean batchUpdateBenefitProdCfg(List<BenefitProdCfgPO> benefitProdCfgPOList);

     /**
     * @description 批量删除数据
     * @version
     * @title
     * <AUTHOR> <EMAIL>
     * @param benefitProdCfgPOList 对象列表
     * @return boolean 批量删除是否成功
     */
	 public boolean batchDeleteBenefitProdCfg(List<BenefitProdCfgPO> benefitProdCfgPOList);

     /**
     * @description 查询所有数据 ，重新组装为map
     * @version
     * @title
     * <AUTHOR> <EMAIL>
     * @param benefitProdCfgPO 对象
     * @return List<Map<String, Object>> 查询结果存放到map中
     */
	 public List<Map<String, Object>> findAllMapBenefitProdCfg(BenefitProdCfgPO benefitProdCfgPO);
	 
 }
 
