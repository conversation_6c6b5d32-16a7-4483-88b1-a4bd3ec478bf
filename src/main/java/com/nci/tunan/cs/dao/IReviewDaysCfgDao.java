package com.nci.tunan.cs.dao;

import com.nci.tunan.cs.model.po.*;

import org.slf4j.Logger;

import java.util.List;

import com.nci.udmp.framework.model.*;
import com.nci.udmp.util.logging.LoggerFactory;

import java.util.Map;


/** 
 * 
 * @description IReviewDaysCfgDao
 * <AUTHOR> <EMAIL>
 * @version V1.0.0
 * @.belongToModule cs-保全子系统
 * @date 2020年12月30日  下午7:44:09
 */

 public interface IReviewDaysCfgDao {
	 /**
     * @description 增加数据
     * @version
     * @title
     * <AUTHOR> <EMAIL>
     * @param reviewDaysCfgPO 对象
     * @return ReviewDaysCfgPO 添加结果
     */
	 public ReviewDaysCfgPO addReviewDaysCfg(ReviewDaysCfgPO reviewDaysCfgPO);
	 
     /**
     * @description 删除数据
     * @version
     * @title
     * <AUTHOR> <EMAIL>
     * @param reviewDaysCfgPO 对象
     * @return boolean 删除是否成功
     */
	 public boolean deleteReviewDaysCfg(ReviewDaysCfgPO reviewDaysCfgPO);
	 
     /**
     * @description 修改数据
     * @version
     * @title
     * <AUTHOR> <EMAIL>
     * @param reviewDaysCfgPO 对象
     * @return ReviewDaysCfgPO 修改结果
     */
	 public ReviewDaysCfgPO updateReviewDaysCfg(ReviewDaysCfgPO reviewDaysCfgPO);
	 
     /**
     * @description 查询单条数据
     * @version
     * @title
     * <AUTHOR> <EMAIL>
     * @param reviewDaysCfgPO 对象
     * @return ReviewDaysCfgPO 查询结果对象
     */
	 public ReviewDaysCfgPO findReviewDaysCfg(ReviewDaysCfgPO reviewDaysCfgPO);
	 
     /**
     * @description 查询所有数据
     * @version
     * @title
     * <AUTHOR> <EMAIL>
     * @param reviewDaysCfgPO 对象
     * @return List<ReviewDaysCfgPO> 查询结果List
     */
	 public List<ReviewDaysCfgPO> findAllReviewDaysCfg(ReviewDaysCfgPO reviewDaysCfgPO);
	 
     /**
     * @description 查询数据条数
     * @version
     * @title
     * <AUTHOR> <EMAIL>
     * @param reviewDaysCfgPO 对象
     * @return int 查询结果条数
     */
	 public int findReviewDaysCfgTotal(ReviewDaysCfgPO reviewDaysCfgPO);

     /**
     * @description 分页查询数据
     * @version
     * @title
     * <AUTHOR> <EMAIL>
     * @param currentPage 当前页对象
     * @return CurrentPage<ReviewDaysCfgPO> 查询结果的当前页对象
     */
	 public CurrentPage<ReviewDaysCfgPO> queryReviewDaysCfgForPage(ReviewDaysCfgPO reviewDaysCfgPO, CurrentPage<ReviewDaysCfgPO> currentPage);

     /**
     * @description 批量增加数据
     * @version
     * @title
     * <AUTHOR> <EMAIL>
     * @param reviewDaysCfgPOList 对象列表
     * @return boolean 批量添加是否成功
     */
	 public boolean batchSaveReviewDaysCfg(List<ReviewDaysCfgPO> reviewDaysCfgPOList);

     /**
     * @description 批量修改数据
     * @version
     * @title
     * <AUTHOR> <EMAIL>
     * @param reviewDaysCfgPOList 对象列表
     * @return boolean 批量修改是否成功
     */
	 public boolean batchUpdateReviewDaysCfg(List<ReviewDaysCfgPO> reviewDaysCfgPOList);

     /**
     * @description 批量删除数据
     * @version
     * @title
     * <AUTHOR> <EMAIL>
     * @param reviewDaysCfgPOList 对象列表
     * @return boolean 批量删除是否成功
     */
	 public boolean batchDeleteReviewDaysCfg(List<ReviewDaysCfgPO> reviewDaysCfgPOList);

     /**
     * @description 查询所有数据 ，重新组装为map
     * @version
     * @title
     * <AUTHOR> <EMAIL>
     * @param reviewDaysCfgPO 对象
     * @return List<Map<String, Object>> 查询结果存放到map中
     */
	 public List<Map<String, Object>> findAllMapReviewDaysCfg(ReviewDaysCfgPO reviewDaysCfgPO);
	 
 }
 
