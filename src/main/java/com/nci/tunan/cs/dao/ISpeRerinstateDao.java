package com.nci.tunan.cs.dao;

import java.util.List;
import java.util.Map;

import com.nci.tunan.cs.model.po.CsPayChangeTrancePO;
import com.nci.tunan.cs.model.po.ServiceTableCfgPO;
import com.nci.tunan.cs.model.po.SpeRerinstatePO;
import com.nci.tunan.cs.model.vo.CsPayChangeTranceVO;
import com.nci.udmp.framework.model.CurrentPage;
/** 
 * @description  特殊复效dao接口类
 * <AUTHOR> <EMAIL> 
 * @date 2019-12-21 下午3:45:36 
 * @.belongToModule CS-保全子系统 
*/
public interface ISpeRerinstateDao {
	 /**
     * @description 增加数据
     * @version *******
     * @title
     * <AUTHOR> <EMAIL>
     * @param serviceTableCfgPO 对象
     * @return ServiceTableCfgPO 添加结果
     */
	 public SpeRerinstatePO addSpeRerinstate(SpeRerinstatePO speRerinstatePO);
	 
 public boolean deleteSpeRerinstate(SpeRerinstatePO speRerinstatePO);
	 
	 public List<SpeRerinstatePO> findSpeRerinstate(SpeRerinstatePO speRerinstatePO);
}
