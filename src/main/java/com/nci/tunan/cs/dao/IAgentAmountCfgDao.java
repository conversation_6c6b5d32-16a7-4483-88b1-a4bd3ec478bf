package com.nci.tunan.cs.dao;

import com.nci.tunan.pa.interfaces.model.po.CsAgentAccTypePO;
import com.nci.tunan.pa.interfaces.model.po.CsAgentAmountCfgPO;
import com.nci.tunan.pa.interfaces.model.po.CsPostCodeCfgPO;
import com.nci.tunan.pa.interfaces.model.po.ServicePO;
import com.nci.udmp.framework.model.*;
import java.util.Map;
import org.slf4j.Logger;
import java.util.List;
import com.nci.udmp.util.logging.LoggerFactory;
import com.nci.tunan.cs.dao.IAgentAmountCfgDao;
import com.nci.tunan.cs.model.po.CsSalesChannelPO;


/** 
 * @description ICsPostCodeCfgDao接口
 * <AUTHOR> 
 * @date 2025-03-14 11:08:29  
 */
public interface IAgentAmountCfgDao {
	

	/**
	 * @description 删除数据
	 * @version
	 * @title
	 * <AUTHOR>
	 * @param csPostCodeCfgPO
	 *            对象
	 * @return boolean 删除是否成功
	 */
	public boolean deleteCsAgentAmountCfg(CsAgentAmountCfgPO csAgentAmountCfgPO);

	/**
	 * @description 查询销售渠道
	 * @version
	 * @title
	 * <AUTHOR> <EMAIL>
	 * @param CsSalesChannelPO对象
	 * @return List<ServicePO> 查询结果存放到map中
	 */
	public List<CsSalesChannelPO> findSalesChannelLX(CsSalesChannelPO csSalesChannelPO);

	/**
	 * @description 查询保全项目
	 * @version
	 * @title
	 * <AUTHOR> <EMAIL>
	 * @param ServicePO对象
	 * @return List<ServicePO> 查询结果存放到map中
	 */
	public List<ServicePO> findAllServiceLX(ServicePO copyProperties);

	/**
	 * 
	 * @description 点击查询
	 * @version
	 * @title
	 * @<NAME_EMAIL>
	 * @see
	 * @param CurrentPage<PayToNonStakeHoldersPO> 保全项信息VO对象
	 * @return
	 */
	public CurrentPage<CsAgentAmountCfgPO> getCsAgentAmountCfgVOPage(CsAgentAmountCfgPO csAgentAmountCfgPO,
			CurrentPage<CsAgentAmountCfgPO> currentPagePO);

	/**
	 * @description 查询账户类型  01;原交费账户    02;非原交费账户
	 * @version
	 * @title
	 * <AUTHOR>
	*/
	public List<CsAgentAccTypePO> findAllCsAgentAccTypeLX(CsAgentAccTypePO csAgentAccTypePO);
	/**
	 * @description 增加数据
	 * @version
	 * @title
	 * <AUTHOR>
	 * @param csAgentAmountCfgPO 对象
	 * @return CsAgentAmountCfgPO 添加结果
	 */
	public CsAgentAmountCfgPO addCsAgentAmountCfg(CsAgentAmountCfgPO csAgentAmountCfgPO);
	/**
	 * @description 查询数据
	 * @version
	 * @title
	 * <AUTHOR>
	 * @param csAgentAmountCfgPO 对象
	 * @return CsAgentAmountCfgPO 添加结果
	 */
	public List<CsAgentAmountCfgPO> getCsAgentAmountCfgforCheck(CsAgentAmountCfgPO csAgentAmountCfgPO);


}
 