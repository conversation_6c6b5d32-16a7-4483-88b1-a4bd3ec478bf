package com.nci.tunan.cs.dao;

import com.nci.tunan.pa.interfaces.model.po.CsPostCodeCfgPO;
import com.nci.udmp.framework.model.*;
import java.util.Map;
import org.slf4j.Logger;
import java.util.List;
import com.nci.udmp.util.logging.LoggerFactory;
import com.nci.tunan.cs.dao.ICsPostCodeCfgDao;


/** 
 * @description ICsPostCodeCfgDao接口
 * <AUTHOR> 
 * @date 2025-03-14 11:08:29  
 */
 public interface ICsPostCodeCfgDao {
	 /**
     * @description 增加数据
     * @version
     * @title
     * <AUTHOR>
     * @param csPostCodeCfgPO 对象
     * @return CsPostCodeCfgPO 添加结果
     */
	 public CsPostCodeCfgPO addCsPostCodeCfg(CsPostCodeCfgPO csPostCodeCfgPO);
	 
     /**
     * @description 删除数据
     * @version
     * @title
     * <AUTHOR>
     * @param csPostCodeCfgPO 对象
     * @return boolean 删除是否成功
     */
	 public boolean deleteCsPostCodeCfg(CsPostCodeCfgPO csPostCodeCfgPO);
	 
     /**
     * @description 修改数据
     * @version
     * @title
     * <AUTHOR>
     * @param csPostCodeCfgPO 对象
     * @return CsPostCodeCfgPO 修改结果
     */
	 public CsPostCodeCfgPO updateCsPostCodeCfg(CsPostCodeCfgPO csPostCodeCfgPO);
	 
     /**
     * @description 查询单条数据
     * @version
     * @title
     * <AUTHOR>
     * @param csPostCodeCfgPO 对象
     * @return CsPostCodeCfgPO 查询结果对象
     */
	 public CsPostCodeCfgPO findCsPostCodeCfg(CsPostCodeCfgPO csPostCodeCfgPO);
	 
     /**
     * @description 查询所有数据
     * @version
     * @title
     * <AUTHOR>
     * @param csPostCodeCfgPO 对象
     * @return List<CsPostCodeCfgPO> 查询结果List
     */
	 public List<CsPostCodeCfgPO> findAllCsPostCodeCfg(CsPostCodeCfgPO csPostCodeCfgPO);
	 
    
     /**
     * @description 分页查询数据
     * @version
     * @title
     * <AUTHOR>
     * @param currentPage 当前页对象
     * @return CurrentPage<CsPostCodeCfgPO> 查询结果的当前页对象
     */
	 public CurrentPage<CsPostCodeCfgPO> queryCsPostCodeCfgForPage(CsPostCodeCfgPO csPostCodeCfgPO, CurrentPage<CsPostCodeCfgPO> currentPage);

     /**
     * @description 批量增加数据
     * @version
     * @title
     * <AUTHOR>
     * @param csPostCodeCfgPOList 对象列表
     * @return boolean 批量添加是否成功
     */
	 public boolean batchSaveCsPostCodeCfg(List<CsPostCodeCfgPO> csPostCodeCfgPOList);

     /**
     * @description 批量修改数据
     * @version
     * @title
     * <AUTHOR>
     * @param csPostCodeCfgPOList 对象列表
     * @return boolean 批量修改是否成功
     */
	 public boolean batchUpdateCsPostCodeCfg(List<CsPostCodeCfgPO> csPostCodeCfgPOList);

     /**
     * @description 批量删除数据
     * @version
     * @title
     * <AUTHOR>
     * @param csPostCodeCfgPOList 对象列表
     * @return boolean 批量删除是否成功
     */
	 public boolean batchDeleteCsPostCodeCfg(List<CsPostCodeCfgPO> csPostCodeCfgPOList);

     /**
     * @description 查询所有数据 ，重新组装为map
     * @version
     * @title
     * <AUTHOR>
     * @param csPostCodeCfgPO 对象
     * @return List<Map<String, Object>> 查询结果存放到map中
     */
	 public List<Map<String, Object>> findAllMapCsPostCodeCfg(CsPostCodeCfgPO csPostCodeCfgPO);
	 
 }
 