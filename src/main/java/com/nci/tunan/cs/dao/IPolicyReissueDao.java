package com.nci.tunan.cs.dao;

import java.util.List;
import java.util.Map;

import com.nci.tunan.cs.model.po.CsAcceptChangePO;
import com.nci.tunan.cs.model.po.PolicyReissuePO;
import com.nci.udmp.framework.model.CurrentPage;



/**
 * 
 * @description 保单补发
 * <AUTHOR> <EMAIL> 
 * @date 2015-06-03 下午5:37:43 
 * @.belongToModule 保全系统-保单补发
 */
 public interface IPolicyReissueDao {
	 /**
     * @description 增加数据
     * @version
     * @title
     * <AUTHOR> <EMAIL>
     * @param policyReissuePO 对象
     * @return PolicyReissuePO 添加结果
     */
	 public PolicyReissuePO addPolicyReissue(PolicyReissuePO policyReissuePO);
	 
     /**
     * @description 删除数据
     * @version
     * @title
     * <AUTHOR> <EMAIL>
     * @param policyReissuePO 对象
     * @return boolean 删除是否成功
     */
	 public boolean deletePolicyReissue(PolicyReissuePO policyReissuePO);
	 
     /**
     * @description 修改数据
     * @version
     * @title
     * <AUTHOR> <EMAIL>
     * @param policyReissuePO 对象
     * @return PolicyReissuePO 修改结果
     */
	 public PolicyReissuePO updatePolicyReissue(PolicyReissuePO policyReissuePO);
	 
     /**
     * @description 查询单条数据
     * @version
     * @title
     * <AUTHOR> <EMAIL>
     * @param policyReissuePO 对象
     * @return PolicyReissuePO 查询结果对象
     */
	 public PolicyReissuePO findPolicyReissue(PolicyReissuePO policyReissuePO);
	 
     /**
     * @description 查询所有数据
     * @version
     * @title
     * <AUTHOR> <EMAIL>
     * @param policyReissuePO 对象
     * @return List<PolicyReissuePO> 查询结果List
     */
	 public List<PolicyReissuePO> findAllPolicyReissue(PolicyReissuePO policyReissuePO);
	 
     /**
     * @description 查询数据条数
     * @version
     * @title
     * <AUTHOR> <EMAIL>
     * @param policyReissuePO 对象
     * @return int 查询结果条数
     */
	 public int findPolicyReissueTotal(PolicyReissuePO policyReissuePO);

     /**
     * @description 分页查询数据
     * @version
     * @title
     * <AUTHOR> <EMAIL>
     * @param currentPage 当前页对象
     * @return CurrentPage<PolicyReissuePO> 查询结果的当前页对象
     */
	 public CurrentPage<PolicyReissuePO> queryPolicyReissueForPage(PolicyReissuePO policyReissuePO, CurrentPage<PolicyReissuePO> currentPage);

     /**
     * @description 批量增加数据
     * @version
     * @title
     * <AUTHOR> <EMAIL>
     * @param policyReissuePOList 对象列表
     * @return boolean 批量添加是否成功
     */
	 public boolean batchSavePolicyReissue(List<PolicyReissuePO> policyReissuePOList);

     /**
     * @description 批量修改数据
     * @version
     * @title
     * <AUTHOR> <EMAIL>
     * @param policyReissuePOList 对象列表
     * @return boolean 批量修改是否成功
     */
	 public boolean batchUpdatePolicyReissue(List<PolicyReissuePO> policyReissuePOList);

     /**
     * @description 批量删除数据
     * @version
     * @title
     * <AUTHOR> <EMAIL>
     * @param policyReissuePOList 对象列表
     * @return boolean 批量删除是否成功
     */
	 public boolean batchDeletePolicyReissue(List<PolicyReissuePO> policyReissuePOList);

     /**
     * @description 查询所有数据 ，重新组装为map
     * @version
     * @title
     * <AUTHOR> <EMAIL>
     * @param policyReissuePO 对象
     * @return List<Map<String, Object>> 查询结果存放到map中
     */
	 public List<Map<String, Object>> findAllMapPolicyReissue(PolicyReissuePO policyReissuePO);

	 /**
	  * 
	  * @description 删除数据
	  * @version
	  * @title
	  * @<NAME_EMAIL>
	  * @param policyReissuePOs 保单补发信息
	  * @return
	  */
	 public boolean batchDeleteByCondition(List<PolicyReissuePO> policyReissuePOs);
	 
	 /**
	  * 
	  * @description 查询单条保单补发原因（最近的一条）
	  * @version
	  * @title
	  * @<NAME_EMAIL> 
	  * @param policyReissuePO 保单补发信息
	  * @return
	  */
	 public PolicyReissuePO findApplyReason(PolicyReissuePO policyReissuePO);


	 /**
	  * 
	  * @description 获取保单补发次数
	  * @version
	  * @title
	  * @<NAME_EMAIL>
	  * @param polciyReissuePO 保单补发信息
	  * @return
	  */
	public int findPolicyReissueCounts(PolicyReissuePO polciyReissuePO);
	/**
	 * 
	 * @description 查询保单补发
	 * @version
	 * @title
	 * @<NAME_EMAIL>
	 * @param daolicyReissuePO 保单补发信息
	 * @return
	 */
	public List<PolicyReissuePO>  findAllPolicyReissueLRTimes(PolicyReissuePO daolicyReissuePO);
	/**
	 * 
	 * @description 查询保单补发
	 * @version
	 * @title
	 * @<NAME_EMAIL>
	 * @param daolicyReissuePO 保单补发信息
	 * @return
	 */
	List<PolicyReissuePO> findAllPolicyReissues(PolicyReissuePO daolicyReissuePO);
		 
	/**
	 * 
	 * @description 查询保单补发
	 * @version
	 * @title
	 * @<NAME_EMAIL>
	 * @param daolicyReissuePO 保单补发信息
	 * @return
	 */
	List<PolicyReissuePO> findPolicyReissues(PolicyReissuePO daolicyReissuePO); 
 }
 
