package com.nci.tunan.cs.dao;

import com.nci.tunan.cs.impl.CssInsQuality.model.CssInsQualicyPO;
import com.nci.udmp.framework.model.CurrentPage;

/**
 * 
 * @description  核心质检接口 
 * <AUTHOR>
 * @version V1.0.0
 * @.belongToModule 保全子系统
 * @date 2020年12月17日 下午2:50:49
 */
public interface ICssInsQualicyDao {

	/**
	 * 
	 * @description 结果维度为：按件数统计
	 * @version V1.0.0
	 * @title
	 * <AUTHOR>
	 * @param cssInsQualicyPO cssInsQualicyPO
	 * @param currentPage currentPage
	 * @return
	 */
	public CurrentPage<CssInsQualicyPO> queryCssInsResultByResultLatitude1(
			CssInsQualicyPO cssInsQualicyPO,CurrentPage<CssInsQualicyPO> currentPage);
	/**
	 * 
	 * @description 结果维度为：问题件明细   差错来源为：质检
	 * @version V1.0.0
	 * @title
	 * <AUTHOR>
	 * @param cssInsQualicyPO cssInsQualicyPO
	 * @param currentPage currentPage
	 * @return
	 */
	public CurrentPage<CssInsQualicyPO> queryCssInsResultByQuality(
			CssInsQualicyPO cssInsQualicyPO,CurrentPage<CssInsQualicyPO> currentPage);
	/**
	 * 
	 * @description 结果维度为：问题件明细
	 * @version V1.0.0
	 * @title
	 * <AUTHOR>
	 * @param cssInsQualicyPO cssInsQualicyPO
	 * @param currentPage currentPage
	 * @return
	 */
	public CurrentPage<CssInsQualicyPO> queryCssInsResultByError(
			CssInsQualicyPO cssInsQualicyPO,CurrentPage<CssInsQualicyPO> currentPage);
	
	/**
	 * 
	 * @description 根据机构或者个人查询受理件数
	 * @version V1.0.0
	 * @title
	 * <AUTHOR>
	 * @param cssInsQualicyPO cssInsQualicyPO
	 * @return
	 */
	public int queryAcceptByOrganOrUser(CssInsQualicyPO cssInsQualicyPO);
	/**
	 * 
	 * @description 根据机构或者个人查询抽检件数
	 * @version V1.0.0
	 * @title
	 * <AUTHOR>
	 * @param cssInsQualicyPO cssInsQualicyPO
	 * @return
	 */
	public int queryInsTotalByOrganOrUser(CssInsQualicyPO cssInsQualicyPO);
	/**
	 * 
	 * @description 根据机构或者个人查询不合格件数
	 * @version V1.0.0
	 * @title
	 * <AUTHOR>
	 * @param cssInsQualicyPO cssInsQualicyPO
	 * @return
	 */
	public int queryInsErrByOrganOrUser(CssInsQualicyPO cssInsQualicyPO);
	
	
}
