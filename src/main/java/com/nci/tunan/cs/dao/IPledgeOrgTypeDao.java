package com.nci.tunan.cs.dao;

import com.nci.tunan.cs.model.po.PledgeOrgTypePO;
import java.util.List;

import com.nci.udmp.framework.model.*;

import java.util.Map;

/**
 * 
 * @description IPledgeOrgTypeDao接口
 * <AUTHOR> <EMAIL> 
 * @date 2015-06-03 17:37:43 
 * @.belongToModule 保全系统-IPledgeOrgTypeDao接口
 */
 public interface IPledgeOrgTypeDao {
	 /**
     * @description 增加数据
     * @version
     * @title
     * <AUTHOR> <EMAIL>
     * @param pledgeOrgTypePO 对象 质押对象表PO
     * @return PledgeOrgTypePO 添加结果
     */
	/**质押对象表PO*/
	 public PledgeOrgTypePO addPledgeOrgType(PledgeOrgTypePO pledgeOrgTypePO);
	 
     /**
     * @description 删除数据
     * @version
     * @title
     * <AUTHOR> <EMAIL>
     * @param pledgeOrgTypePO 对象 质押对象表PO
     * @return boolean 删除是否成功
     */
	/**质押对象表PO*/
	 public boolean deletePledgeOrgType(PledgeOrgTypePO pledgeOrgTypePO);
	 
     /**
     * @description 修改数据
     * @version
     * @title
     * <AUTHOR> <EMAIL>
     * @param pledgeOrgTypePO 对象 质押对象表PO
     * @return PledgeOrgTypePO 修改结果
     */
	/**质押对象表PO*/
	 public PledgeOrgTypePO updatePledgeOrgType(PledgeOrgTypePO pledgeOrgTypePO);
	 
     /**
     * @description 查询单条数据
     * @version
     * @title
     * <AUTHOR> <EMAIL>
     * @param pledgeOrgTypePO 对象 质押对象表PO
     * @return PledgeOrgTypePO 查询结果对象
     */
	/**质押对象表PO*/
	 public PledgeOrgTypePO findPledgeOrgType(PledgeOrgTypePO pledgeOrgTypePO);
	 
     /**
     * @description 查询所有数据
     * @version
     * @title
     * <AUTHOR> <EMAIL>
     * @param pledgeOrgTypePO 对象 质押对象表PO
     * @return List<PledgeOrgTypePO> 查询结果List
     */
	/**质押对象表PO*/
	 public List<PledgeOrgTypePO> findAllPledgeOrgType(PledgeOrgTypePO pledgeOrgTypePO);
	 
     /**
     * @description 查询数据条数
     * @version
     * @title
     * <AUTHOR> <EMAIL>
     * @param pledgeOrgTypePO 对象 质押对象表PO
     * @return int 查询结果条数
     */
	/**质押对象表PO*/
	 public int findPledgeOrgTypeTotal(PledgeOrgTypePO pledgeOrgTypePO);

     /**
     * @description 分页查询数据
     * @version
     * @title
     * <AUTHOR> <EMAIL>
     * @param currentPage 当前页对象
     * @return CurrentPage<PledgeOrgTypePO> 查询结果的当前页对象
     */
	/**质押对象表PO*/
	 public CurrentPage<PledgeOrgTypePO> queryPledgeOrgTypeForPage(PledgeOrgTypePO pledgeOrgTypePO, CurrentPage<PledgeOrgTypePO> currentPage);

     /**
     * @description 批量增加数据
     * @version
     * @title
     * <AUTHOR> <EMAIL>
     * @param pledgeOrgTypePOList 对象列表 质押对象表PO
     * @return boolean 批量添加是否成功
     */
	/**质押对象表PO*/
	 public boolean batchSavePledgeOrgType(List<PledgeOrgTypePO> pledgeOrgTypePOList);

     /**
     * @description 批量修改数据
     * @version
     * @title
     * <AUTHOR> <EMAIL>
     * @param pledgeOrgTypePOList 对象列表 质押对象表PO
     * @return boolean 批量修改是否成功
     */
	/**质押对象表PO*/
	 public boolean batchUpdatePledgeOrgType(List<PledgeOrgTypePO> pledgeOrgTypePOList);

     /**
     * @description 批量删除数据
     * @version
     * @title
     * <AUTHOR> <EMAIL>
     * @param pledgeOrgTypePOList 对象列表 质押对象表PO
     * @return boolean 批量删除是否成功
     */
	/**质押对象表PO*/
	 public boolean batchDeletePledgeOrgType(List<PledgeOrgTypePO> pledgeOrgTypePOList);

     /**
     * @description 查询所有数据 ，重新组装为map
     * @version
     * @title
     * <AUTHOR> <EMAIL>
     * @param pledgeOrgTypePO 对象 质押对象表PO
     * @return List<Map<String, Object>> 查询结果存放到map中
     */
	/**质押对象表PO*/
	 public List<Map<String, Object>> findAllMapPledgeOrgType(PledgeOrgTypePO pledgeOrgTypePO);
	 
 }
 
