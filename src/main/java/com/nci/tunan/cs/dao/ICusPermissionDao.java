package com.nci.tunan.cs.dao;

import java.util.List;

import com.nci.udmp.app.dao.po.PermissionRatetablePO;
import com.nci.udmp.app.dao.po.PermissionTypePO;

/**
 * @description  用户权限Dao接口
 * @<NAME_EMAIL> 
 * @date 2015-05-15
 * @.belongToModule 保全子系统  用户权限Dao接口
 */
public interface ICusPermissionDao {
	/**
	 * @description   查询权限配置信息
	 * @version  v1.1
	 * @title
	 * <AUTHOR> <EMAIL>
	 * @param permissionType
	 * @return PermissionTypePO
	 */
	public PermissionTypePO findPermissionType(PermissionTypePO permissionType);

	/**
	 * 
	 * @description  查询权限基本信息
	 * @version V1.0.0
	 * @title
	 * <AUTHOR>
	 * @param permissionRatetablePO
	 * @return  List<PermissionRatetablePO>
	 */
	public List<PermissionRatetablePO> findPermissionRatetableList(
			PermissionRatetablePO permissionRatetablePO);
}
