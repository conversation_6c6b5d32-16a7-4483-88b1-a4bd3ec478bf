package com.nci.tunan.cs.dao;

import com.nci.udmp.framework.model.*;
import java.util.Map;
import org.slf4j.Logger;
import java.util.List;
import com.nci.tunan.cs.model.po.CsCheckConfigPO;
import com.nci.udmp.util.logging.LoggerFactory;
import com.nci.tunan.cs.dao.ICsCheckConfigDao;


/** 
 * @description ICsCheckConfigDao接口
 * <AUTHOR> 
 * @date 2022-10-12 13:15:53  
 */
 public interface ICsCheckConfigDao {
	 /**
     * @description 增加数据
     * @version
     * @title
     * <AUTHOR>
     * @param csCheckConfigPO 对象
     * @return CsCheckConfigPO 添加结果
     */
	 public CsCheckConfigPO addCsCheckConfig(CsCheckConfigPO csCheckConfigPO);
	 
     /**
     * @description 删除数据
     * @version
     * @title
     * <AUTHOR>
     * @param csCheckConfigPO 对象
     * @return boolean 删除是否成功
     */
	 public boolean deleteCsCheckConfig(CsCheckConfigPO csCheckConfigPO);
	 
     /**
     * @description 修改数据
     * @version
     * @title
     * <AUTHOR>
     * @param csCheckConfigPO 对象
     * @return CsCheckConfigPO 修改结果
     */
	 public CsCheckConfigPO updateCsCheckConfig(CsCheckConfigPO csCheckConfigPO);
	 
     /**
     * @description 查询单条数据
     * @version
     * @title
     * <AUTHOR>
     * @param csCheckConfigPO 对象
     * @return CsCheckConfigPO 查询结果对象
     */
	 public CsCheckConfigPO findCsCheckConfig(CsCheckConfigPO csCheckConfigPO);
	 
     /**
     * @description 查询所有数据
     * @version
     * @title
     * <AUTHOR>
     * @param csCheckConfigPO 对象
     * @return List<CsCheckConfigPO> 查询结果List
     */
	 public List<CsCheckConfigPO> findAllCsCheckConfig(CsCheckConfigPO csCheckConfigPO);
	 
     /**
     * @description 查询数据条数
     * @version
     * @title
     * <AUTHOR>
     * @param csCheckConfigPO 对象
     * @return int 查询结果条数
     */
	 public int findCsCheckConfigTotal(CsCheckConfigPO csCheckConfigPO);

     /**
     * @description 分页查询数据
     * @version
     * @title
     * <AUTHOR>
     * @param currentPage 当前页对象
     * @return CurrentPage<CsCheckConfigPO> 查询结果的当前页对象
     */
	 public CurrentPage<CsCheckConfigPO> queryCsCheckConfigForPage(CsCheckConfigPO csCheckConfigPO, CurrentPage<CsCheckConfigPO> currentPage);

     /**
     * @description 批量增加数据
     * @version
     * @title
     * <AUTHOR>
     * @param csCheckConfigPOList 对象列表
     * @return boolean 批量添加是否成功
     */
	 public boolean batchSaveCsCheckConfig(List<CsCheckConfigPO> csCheckConfigPOList);

     /**
     * @description 批量修改数据
     * @version
     * @title
     * <AUTHOR>
     * @param csCheckConfigPOList 对象列表
     * @return boolean 批量修改是否成功
     */
	 public boolean batchUpdateCsCheckConfig(List<CsCheckConfigPO> csCheckConfigPOList);

     /**
     * @description 批量删除数据
     * @version
     * @title
     * <AUTHOR>
     * @param csCheckConfigPOList 对象列表
     * @return boolean 批量删除是否成功
     */
	 public boolean batchDeleteCsCheckConfig(List<CsCheckConfigPO> csCheckConfigPOList);

     /**
     * @description 查询所有数据 ，重新组装为map
     * @version
     * @title
     * <AUTHOR>
     * @param csCheckConfigPO 对象
     * @return List<Map<String, Object>> 查询结果存放到map中
     */
	 public List<Map<String, Object>> findAllMapCsCheckConfig(CsCheckConfigPO csCheckConfigPO);
	 /**
     * @description 机构层--查询邮箱分公司配置
     * @version
     * @title
     * <AUTHOR>
     * @param csCheckConfigPO 对象
     * @return CsCheckConfigPO 查询结果对象
     */
	 public CsCheckConfigPO findsCheckConfigByCheckCode(CsCheckConfigPO csCheckConfigPO);
	 /**
     * @description 客户曾--查询邮箱分公司配置:通过changeId和customerId
     * @version
     * @title
     * <AUTHOR>
     * @param csCheckConfigPO 对象
     * @return CsCheckConfigPO 查询结果对象
     */
	 public CsCheckConfigPO findCsCheckConfigByCustomerIdAndChangeId(CsCheckConfigPO csCheckConfigPO);
 }
 