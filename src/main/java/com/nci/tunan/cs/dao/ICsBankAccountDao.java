package com.nci.tunan.cs.dao;

import com.nci.tunan.cs.model.po.AddDutySaLimitCfgPO;
import com.nci.tunan.cs.model.po.AddRiderUniversalCfgPO;
import com.nci.tunan.cs.model.po.ApplicantTypeCfgPO;
import com.nci.tunan.cs.model.po.AutoSignCfgPO;
import com.nci.tunan.cs.model.po.AutoVisitCfgPO;
import com.nci.tunan.cs.model.po.BatchSurrenderEventPO;
import com.nci.tunan.cs.model.po.BatchSurrenderImportErrPO;
import com.nci.tunan.cs.model.po.BatchSurrenderImportPO;
import com.nci.tunan.cs.model.po.BatchSurrenderPolicyPO;
import com.nci.tunan.cs.model.po.BenefitProdCfgPO;
import com.nci.tunan.cs.model.po.BusiProdBasicCfgPO;
import com.nci.tunan.cs.model.po.CapitalDistributePO;
import com.nci.tunan.cs.model.po.CashValuePrintPO;
import com.nci.tunan.cs.model.po.ChangePO;
import com.nci.tunan.cs.model.po.CostFeeCfgPO;
import com.nci.tunan.cs.model.po.CsAcceptChangePO;
import com.nci.tunan.cs.model.po.CsAddressPO;
import com.nci.tunan.cs.model.po.CsAdjustPO;
import com.nci.tunan.cs.model.po.CsAppDocCfgPO;
import com.nci.tunan.cs.model.po.CsAppDocPO;
import com.nci.tunan.cs.model.po.CsApplicationPO;
import com.nci.tunan.cs.model.po.CsAreaOrganPO;
import com.nci.tunan.cs.model.po.CsAreaPO;
import com.nci.tunan.cs.model.po.CsAreaPersonPO;
import com.nci.tunan.cs.model.po.CsAuthCfgPO;
import com.nci.tunan.cs.model.po.CsAuthGradePO;
import com.nci.tunan.cs.model.po.CsBankAccountPO;
import com.nci.tunan.cs.model.po.CsBenefitInsuredPO;
import com.nci.tunan.cs.model.po.CsContactPO;
import com.nci.tunan.cs.model.po.CsContractAgentPO;
import com.nci.tunan.cs.model.po.CsPremArapPO;
import com.nci.udmp.framework.model.*;

import java.util.Map;

import org.slf4j.Logger;

import java.util.List;

import com.nci.udmp.util.logging.LoggerFactory;


 /** 
 * @description ICsBankAccountDao接口
 * <AUTHOR> <EMAIL> 
 * @date 2019-12-21 下午2:46:16 
 * @.belongToModule CS-保全子系统 
*/
public interface ICsBankAccountDao {
	 /**
     * @description 增加数据
     * @version
     * @title
     * <AUTHOR> <EMAIL>
     * @param csBankAccountPO 对象 银行账户信息PO
     * @return CsBankAccountPO 添加结果
     */
	 public CsBankAccountPO addCsBankAccount(CsBankAccountPO csBankAccountPO);
	 
     /**
     * @description 删除数据
     * @version
     * @title
     * <AUTHOR> <EMAIL>
     * @param csBankAccountPO 对象 银行账户信息PO
     * @return boolean 删除是否成功
     */
	 public boolean deleteCsBankAccount(CsBankAccountPO csBankAccountPO);
	 
     /**
     * @description 修改数据
     * @version
     * @title
     * <AUTHOR> <EMAIL>
     * @param csBankAccountPO 对象 银行账户信息PO
     * @return CsBankAccountPO 修改结果
     */
	 public CsBankAccountPO updateCsBankAccount(CsBankAccountPO csBankAccountPO);
	 
     /**
     * @description 查询单条数据
     * @version
     * @title
     * <AUTHOR> <EMAIL>
     * @param csBankAccountPO 对象 银行账户信息PO
     * @return CsBankAccountPO 查询结果对象
     */
	 public CsBankAccountPO findCsBankAccount(CsBankAccountPO csBankAccountPO);
	 /**
	  * @description 查询单条数据
	  * @version
	  * @title
	  * <AUTHOR> <EMAIL>
	  * @param csBankAccountPO 对象 银行账户信息PO
	  * @return CsBankAccountPO 查询结果对象
	  */
	 public List<CsBankAccountPO> findCsBankAccountList(CsBankAccountPO csBankAccountPO);
	 /**
	  * @description 查询多条数据
	  * @version
	  * @title
	  * <AUTHOR> <EMAIL>
	  * @param csBankAccountPO 对象 银行账户信息PO
	  * @return CsBankAccountPO 查询结果对象
	  */
	 public List<CsBankAccountPO> findPABankAccount(CsBankAccountPO csBankAccountPO);
	 
     /**
     * @description 查询所有数据
     * @version
     * @title
     * <AUTHOR> <EMAIL>
     * @param csBankAccountPO 对象 银行账户信息PO
     * @return List<CsBankAccountPO> 查询结果List
     */
	 public List<CsBankAccountPO> findAllCsBankAccount(CsBankAccountPO csBankAccountPO);
	 
     /**
     * @description 查询数据条数
     * @version
     * @title
     * <AUTHOR> <EMAIL>
     * @param csBankAccountPO 对象 银行账户信息PO
     * @return int 查询结果条数
     */
	 public int findCsBankAccountTotal(CsBankAccountPO csBankAccountPO);

     /**
     * @description 分页查询数据
     * @version
     * @title
     * <AUTHOR> <EMAIL>
     * @param currentPage 当前页对象
     * @return CurrentPage<CsBankAccountPO> 查询结果的当前页对象
     */
	 public CurrentPage<CsBankAccountPO> queryCsBankAccountForPage(CsBankAccountPO csBankAccountPO, CurrentPage<CsBankAccountPO> currentPage);

	 /**
	 * @description 查询符合机构的银行
	 * @version *******
	 * @title 查询符合机构的银行
	 * <AUTHOR> <EMAIL>
	 * @param csPremArapPO 查询参数
	 * @return 返回保全收付费信息
	*/
	public List<CsPremArapPO> findBankByOrgan(CsPremArapPO csPremArapPO);
	 /**
	 * @description 查询organ收付费信息
	 * @version *******
	 * @title 查询organ收付费信息
	 * <AUTHOR> <EMAIL>
	 * @param csPremArapPO 查询参数
	 * @return 返回organ保全收付费信息
	*/
	public List<CsPremArapPO> findBankByOrganAll(CsPremArapPO csPremArapPO);
	 /**
	 * @description 查询某银行的保全收付费信息
	 * @version *******
	 * @title 查询某银行的保全收付费信息
	 * <AUTHOR> <EMAIL>
	 * @param csPremArapPO 查询参数
	 * @return 返回查询某银行的保全收付费数据
	*/
	public List<CsPremArapPO> findBank(CsPremArapPO csPremArapPO);
	 
	 /**
	 * @description 查询focus保全收付费数据
	 * @version *******
	 * @title 查询focus保全收付费数据
	 * <AUTHOR> <EMAIL>
	 * @param csPremArapPO 查询参数
	 * @return 返回查询focus保全收付费数据
	*/
	public List<CsPremArapPO> findBankByFocus(CsPremArapPO csPremArapPO);
	 
     /**
     * @description 批量增加数据
     * @version
     * @title
     * <AUTHOR> <EMAIL>
     * @param csBankAccountPOList 对象列表 银行账户信息PO
     * @return boolean 批量添加是否成功
     */
	 public boolean batchSaveCsBankAccount(List<CsBankAccountPO> csBankAccountPOList);

     /**
     * @description 批量修改数据
     * @version
     * @title
     * <AUTHOR> <EMAIL>
     * @param csBankAccountPOList 对象列表 银行账户信息PO
     * @return boolean 批量修改是否成功
     */
	 public boolean batchUpdateCsBankAccount(List<CsBankAccountPO> csBankAccountPOList);

     /**
     * @description 批量删除数据
     * @version
     * @title
     * <AUTHOR> <EMAIL>
     * @param csBankAccountPOList 对象列表 银行账户信息PO
     * @return boolean 批量删除是否成功
     */
	 public boolean batchDeleteCsBankAccount(List<CsBankAccountPO> csBankAccountPOList);

     /**
     * @description 查询所有数据 ，重新组装为map
     * @version
     * @title
     * <AUTHOR> <EMAIL>
     * @param csBankAccountPO 对象 银行账户信息PO
     * @return List<Map<String, Object>> 查询结果存放到map中
     */
	 public List<Map<String, Object>> findAllMapCsBankAccount(CsBankAccountPO csBankAccountPO);
	 
	 //@invalid	 R00101000054领取形式变更（初始化） 
	 /**
	 * @description 查询某个银行号的银行抄单信息
	 * @version *******
	 * @title 查询某个银行号的保全收付费信息
	 * <AUTHOR> <EMAIL>
	 * @param csBankAccountPO 查询参数
	 * @return  返回查询某个银行号的银行抄单信息
	*/
	public List<CsBankAccountPO> queryCsBankAccount(CsBankAccountPO csBankAccountPO);

	 /**
	  * 
	  * @description 排除操作类型为删除的银行账户
	  * @version
	  * @title
	  * @<NAME_EMAIL>
	 * @param csBankAccountPO  银行账户信息PO
	  * @return
	  */
     public List<CsBankAccountPO> findCsBankAccountForNormal(CsBankAccountPO csBankAccountPO);
     
     /**
     * @description 通过AcceptId删除本次受理的银行信息
     * @version *******
     * @title 通过AcceptId删除本次受理的银行信息
     * <AUTHOR> <EMAIL>
     * @param csBankAccountPO 删除银行信息
     * @return 是否删除成功
    */
    public boolean deleteCsBankAccountByAcceptId(CsBankAccountPO csBankAccountPO);

    /**
     * 
     * #158068 查询缴费变更信息前，银行账户信息。
     * @param csBankAccountPO
     * @return
     */
	public CsBankAccountPO queryPCBefore(CsBankAccountPO csBankAccountPO);
    
 }
 
