package com.nci.tunan.cap.interfaces.model.bo.statement;

import java.util.List;

/** 
 * @description 日结报文信息
 * <AUTHOR> <EMAIL> 
 * @date 2024-1-11 上午9:27:24  
 * @.belongToModule 收付费-日结报文推送
*/
/** 
 * @description 
 * <AUTHOR> <EMAIL> 
 * @date 2024-1-11 上午9:30:27  
*/
public class Root {
    
    /** 
    * @Fields StatementName : 日结类型名称
    */ 
    private String StatementName;
    
    /** 
    * @Fields StatementID : 日结类型编码，参见“日结名称”中“-”前的英文和数字组合 
    * 如：CS001
    */ 
    private String StatementID;
    
    /** 
    * @Fields StatementSerial : 日结流水号，日结文件的文件名。
    */ 
    private String StatementSerial;
    
    
    /** 
    * @Fields Header :  日结表头
    */ 
    private Header Header;
    
    
    /** 
    * @Fields ColumnData : 日结列区域
    */ 
    private List<ColumnData> ColumnList;
    
    
    /** 
    * @Fields RowList : 日结行区域
    */ 
    private List<RowData> RowList;
    
    /** 
     * @Fields varianceList : 差异说明部分
     */
    private List<VarianceDetail> VarianceList;


    public String getStatementName() {
        return StatementName;
    }


    public void setStatementName(String statementName) {
        StatementName = statementName;
    }


    public String getStatementID() {
        return StatementID;
    }


    public void setStatementID(String statementID) {
        StatementID = statementID;
    }


    public String getStatementSerial() {
        return StatementSerial;
    }


    public void setStatementSerial(String statementSerial) {
        StatementSerial = statementSerial;
    }


    public Header getHeader() {
        return Header;
    }


    public void setHeader(Header header) {
        Header = header;
    }


    public List<ColumnData> getColumnList() {
        return ColumnList;
    }


    public void setColumnList(List<ColumnData> columnList) {
        ColumnList = columnList;
    }


    public List<RowData> getRowList() {
        return RowList;
    }


    public void setRowList(List<RowData> rowList) {
        RowList = rowList;
    }


    public List<VarianceDetail> getVarianceList() {
        return VarianceList;
    }


    public void setVarianceList(List<VarianceDetail> varianceList) {
        VarianceList = varianceList;
    }
}
