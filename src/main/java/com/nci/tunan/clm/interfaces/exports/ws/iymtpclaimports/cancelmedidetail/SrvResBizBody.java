package com.nci.tunan.clm.interfaces.exports.ws.iymtpclaimports.cancelmedidetail;

import java.io.Serializable;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;

import com.nci.tunan.clm.interfaces.vo.iymtpclaimports.OutputDataResVO;

@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "SrvResBizBody")
public class SrvResBizBody implements Serializable {
    /**
     * serialVersionUID 
     */
    private static final   long serialVersionUID = 1L;
    
    @XmlElement(name = "outputData")
    protected OutputDataResVO outputData;

	public OutputDataResVO getOutputData() {
		return outputData;
	}

	public void setOutputData(OutputDataResVO outputData) {
		this.outputData = outputData;
	}

}



