package com.nci.tunan.clm.interfaces.exports.ws.iymtpclaimports.grantmedidetail;

import java.io.Serializable;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;

import com.nci.tunan.clm.interfaces.vo.iymtpclaimports.GrantMediDetailReqVO;

@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "SrvReqBizBody")
public class SrvReqBizBody implements Serializable {
    /**
     * serialVersionUID 
     */
    private static final   long serialVersionUID = 1L;
    
    @XmlElement(name = "inputData")
    protected GrantMediDetailReqVO inputData;

	public GrantMediDetailReqVO getInputData() {
		return inputData;
	}

	public void setInputData(GrantMediDetailReqVO inputData) {
		this.inputData = inputData;
	}
    
}


