package com.nci.tunan.clm.dao;

import com.nci.tunan.clm.interfaces.model.po.ClaimHighriskOrgInfoPO;
import com.nci.udmp.framework.model.*;

import java.util.Map;

import org.slf4j.Logger;

import java.util.List;

import com.nci.udmp.util.logging.LoggerFactory;


/** 
 * @description IClaimHighriskOrgInfoDao接口
 * <AUTHOR> 
 * @date 2024-12-16 10:33:45  
 */
 public interface IClaimHighriskOrgInfoDao {
	 /**
     * @description 增加数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimHighriskOrgInfoPO 对象
     * @return ClaimHighriskOrgInfoPO 添加结果
     */
	 public ClaimHighriskOrgInfoPO addClaimHighriskOrgInfo(ClaimHighriskOrgInfoPO claimHighriskOrgInfoPO);
	 
     /**
     * @description 删除数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimHighriskOrgInfoPO 对象
     * @return boolean 删除是否成功
     */
	 public boolean deleteClaimHighriskOrgInfo(ClaimHighriskOrgInfoPO claimHighriskOrgInfoPO);
	 
     /**
     * @description 修改数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimHighriskOrgInfoPO 对象
     * @return ClaimHighriskOrgInfoPO 修改结果
     */
	 public ClaimHighriskOrgInfoPO updateClaimHighriskOrgInfo(ClaimHighriskOrgInfoPO claimHighriskOrgInfoPO);
	 
     /**
     * @description 查询单条数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimHighriskOrgInfoPO 对象
     * @return ClaimHighriskOrgInfoPO 查询结果对象
     */
	 public ClaimHighriskOrgInfoPO findClaimHighriskOrgInfo(ClaimHighriskOrgInfoPO claimHighriskOrgInfoPO);
	 
     /**
     * @description 查询所有数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimHighriskOrgInfoPO 对象
     * @return List<ClaimHighriskOrgInfoPO> 查询结果List
     */
	 public List<ClaimHighriskOrgInfoPO> findAllClaimHighriskOrgInfo(ClaimHighriskOrgInfoPO claimHighriskOrgInfoPO);
	 
     /**
     * @description 查询数据条数
     * @version
     * @title
     * <AUTHOR>
     * @param claimHighriskOrgInfoPO 对象
     * @return int 查询结果条数
     */
	 public int findClaimHighriskOrgInfoTotal(ClaimHighriskOrgInfoPO claimHighriskOrgInfoPO);

     /**
     * @description 分页查询数据
     * @version
     * @title
     * <AUTHOR>
     * @param currentPage 当前页对象
     * @return CurrentPage<ClaimHighriskOrgInfoPO> 查询结果的当前页对象
     */
	 public CurrentPage<ClaimHighriskOrgInfoPO> queryClaimHighriskOrgInfoForPage(ClaimHighriskOrgInfoPO claimHighriskOrgInfoPO, CurrentPage<ClaimHighriskOrgInfoPO> currentPage);

     /**
     * @description 批量增加数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimHighriskOrgInfoPOList 对象列表
     * @return boolean 批量添加是否成功
     */
	 public boolean batchSaveClaimHighriskOrgInfo(List<ClaimHighriskOrgInfoPO> claimHighriskOrgInfoPOList);

     /**
     * @description 批量修改数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimHighriskOrgInfoPOList 对象列表
     * @return boolean 批量修改是否成功
     */
	 public boolean batchUpdateClaimHighriskOrgInfo(List<ClaimHighriskOrgInfoPO> claimHighriskOrgInfoPOList);

     /**
     * @description 批量删除数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimHighriskOrgInfoPOList 对象列表
     * @return boolean 批量删除是否成功
     */
	 public boolean batchDeleteClaimHighriskOrgInfo(List<ClaimHighriskOrgInfoPO> claimHighriskOrgInfoPOList);

     /**
     * @description 查询所有数据 ，重新组装为map
     * @version
     * @title
     * <AUTHOR>
     * @param claimHighriskOrgInfoPO 对象
     * @return List<Map<String, Object>> 查询结果存放到map中
     */
	 public List<Map<String, Object>> findAllMapClaimHighriskOrgInfo(ClaimHighriskOrgInfoPO claimHighriskOrgInfoPO);
	 
 }
 