package com.nci.tunan.clm.dao;

import java.util.List; 

import com.nci.tunan.clm.interfaces.model.po.ClaimInstalmentPO;
/**
 * 
 * @description 理赔分期给付应领接口
 * <AUTHOR> 
 * @.belongToModule CLM-理赔系统-理赔分期给付
 * @date 2015-05-15 下午4:38:54
 */
public interface IClaimInstalmentBatchDao {
    /**
     * 
     * @description 查询自动发起分期给付的数据个数
     * @version
     * @title
     * @<NAME_EMAIL> 
     * @param map
     * @return 自动发起分期给付的数据个数
     */
    public int queryClaimInstalmentBatchCount(ClaimInstalmentPO claimInstalmentPO);
    /**
     * 
     * @description 查询自动发起分期给付批处理分配的数据
     * @version
     * @title
     * @<NAME_EMAIL> 
     * @param map
     * @return 自动发起分期给付批处理分配的数据
     */
    public List<ClaimInstalmentPO> queryClaimInstalmentBatch(ClaimInstalmentPO claimInstalmentPO);
}
