package com.nci.tunan.clm.dao;

import com.nci.udmp.framework.model.*;
import java.util.Map;
import org.slf4j.Logger;
import java.util.List;
import com.nci.udmp.util.logging.LoggerFactory;
import com.nci.tunan.clm.interfaces.model.po.ClaimIntelAuditPcasePO;


/** 
 * @description IClaimIntelAuditPcaseDao接口
 * <AUTHOR> 
 * @date 2022-07-25 10:20:13  
 */
 public interface IClaimIntelAuditPcaseDao {
	 /**
     * @description 增加数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimIntelAuditPcasePO 对象
     * @return ClaimIntelAuditPcasePO 添加结果
     */
	 public ClaimIntelAuditPcasePO addClaimIntelAuditPcase(ClaimIntelAuditPcasePO claimIntelAuditPcasePO);
	 
     /**
     * @description 删除数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimIntelAuditPcasePO 对象
     * @return boolean 删除是否成功
     */
	 public boolean deleteClaimIntelAuditPcase(ClaimIntelAuditPcasePO claimIntelAuditPcasePO);
	 
     /**
     * @description 修改数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimIntelAuditPcasePO 对象
     * @return ClaimIntelAuditPcasePO 修改结果
     */
	 public ClaimIntelAuditPcasePO updateClaimIntelAuditPcase(ClaimIntelAuditPcasePO claimIntelAuditPcasePO);
	 
     /**
     * @description 查询单条数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimIntelAuditPcasePO 对象
     * @return ClaimIntelAuditPcasePO 查询结果对象
     */
	 public ClaimIntelAuditPcasePO findClaimIntelAuditPcase(ClaimIntelAuditPcasePO claimIntelAuditPcasePO);
	 
     /**
     * @description 查询所有数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimIntelAuditPcasePO 对象
     * @return List<ClaimIntelAuditPcasePO> 查询结果List
     */
	 public List<ClaimIntelAuditPcasePO> findAllClaimIntelAuditPcase(ClaimIntelAuditPcasePO claimIntelAuditPcasePO);
	 
     /**
     * @description 查询数据条数
     * @version
     * @title
     * <AUTHOR>
     * @param claimIntelAuditPcasePO 对象
     * @return int 查询结果条数
     */
	 public int findClaimIntelAuditPcaseTotal(ClaimIntelAuditPcasePO claimIntelAuditPcasePO);

     /**
     * @description 分页查询数据
     * @version
     * @title
     * <AUTHOR>
     * @param currentPage 当前页对象
     * @return CurrentPage<ClaimIntelAuditPcasePO> 查询结果的当前页对象
     */
	 public CurrentPage<ClaimIntelAuditPcasePO> queryClaimIntelAuditPcaseForPage(ClaimIntelAuditPcasePO claimIntelAuditPcasePO, CurrentPage<ClaimIntelAuditPcasePO> currentPage);

     /**
     * @description 批量增加数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimIntelAuditPcasePOList 对象列表
     * @return boolean 批量添加是否成功
     */
	 public boolean batchSaveClaimIntelAuditPcase(List<ClaimIntelAuditPcasePO> claimIntelAuditPcasePOList);

     /**
     * @description 批量修改数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimIntelAuditPcasePOList 对象列表
     * @return boolean 批量修改是否成功
     */
	 public boolean batchUpdateClaimIntelAuditPcase(List<ClaimIntelAuditPcasePO> claimIntelAuditPcasePOList);

     /**
     * @description 批量删除数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimIntelAuditPcasePOList 对象列表
     * @return boolean 批量删除是否成功
     */
	 public boolean batchDeleteClaimIntelAuditPcase(List<ClaimIntelAuditPcasePO> claimIntelAuditPcasePOList);

     /**
     * @description 查询所有数据 ，重新组装为map
     * @version
     * @title
     * <AUTHOR>
     * @param claimIntelAuditPcasePO 对象
     * @return List<Map<String, Object>> 查询结果存放到map中
     */
	 public List<Map<String, Object>> findAllMapClaimIntelAuditPcase(ClaimIntelAuditPcasePO claimIntelAuditPcasePO);
	 /**
     * @description 根据赔案ID删除数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimIntelAuditPcasePO 对象
     * @return boolean 删除是否成功
     */
 	 public boolean deleteClaimIntelAuditPcaseByCaseId(ClaimIntelAuditPcasePO claimIntelAuditPcasePO);
	 
 }
 