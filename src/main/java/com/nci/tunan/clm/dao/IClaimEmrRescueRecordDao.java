package com.nci.tunan.clm.dao;

import com.nci.udmp.framework.model.*;
import java.util.Map;
import org.slf4j.Logger;
import java.util.List;
import com.nci.tunan.clm.interfaces.model.po.ClaimEmrRescueRecordPO;
import com.nci.udmp.util.logging.LoggerFactory;


/** 
 * @description IClaimEmrRescueRecordDao接口
 * <AUTHOR> 
 * @date 2024-10-09 11:05:02  
 */
 public interface IClaimEmrRescueRecordDao {
	 /**
     * @description 增加数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimEmrRescueRecordPO 对象
     * @return ClaimEmrRescueRecordPO 添加结果
     */
	 public ClaimEmrRescueRecordPO addClaimEmrRescueRecord(ClaimEmrRescueRecordPO claimEmrRescueRecordPO);
	 
     /**
     * @description 删除数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimEmrRescueRecordPO 对象
     * @return boolean 删除是否成功
     */
	 public boolean deleteClaimEmrRescueRecord(ClaimEmrRescueRecordPO claimEmrRescueRecordPO);
	 /**
	  * @description 删除数据根据EmrId
	  * @version
	  * @title
	  * <AUTHOR>
	  * @param claimEmrRescueRecordPO 对象
	  * @return boolean 删除是否成功
	  */
	 public boolean deleteClaimEmrRescueRecordByEmrId(ClaimEmrRescueRecordPO claimEmrRescueRecordPO);
	 
     /**
     * @description 修改数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimEmrRescueRecordPO 对象
     * @return ClaimEmrRescueRecordPO 修改结果
     */
	 public ClaimEmrRescueRecordPO updateClaimEmrRescueRecord(ClaimEmrRescueRecordPO claimEmrRescueRecordPO);
	 
     /**
     * @description 查询单条数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimEmrRescueRecordPO 对象
     * @return ClaimEmrRescueRecordPO 查询结果对象
     */
	 public ClaimEmrRescueRecordPO findClaimEmrRescueRecord(ClaimEmrRescueRecordPO claimEmrRescueRecordPO);
	 
     /**
     * @description 查询所有数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimEmrRescueRecordPO 对象
     * @return List<ClaimEmrRescueRecordPO> 查询结果List
     */
	 public List<ClaimEmrRescueRecordPO> findAllClaimEmrRescueRecord(ClaimEmrRescueRecordPO claimEmrRescueRecordPO);
	 
     /**
     * @description 查询数据条数
     * @version
     * @title
     * <AUTHOR>
     * @param claimEmrRescueRecordPO 对象
     * @return int 查询结果条数
     */
	 public int findClaimEmrRescueRecordTotal(ClaimEmrRescueRecordPO claimEmrRescueRecordPO);

     /**
     * @description 分页查询数据
     * @version
     * @title
     * <AUTHOR>
     * @param currentPage 当前页对象
     * @return CurrentPage<ClaimEmrRescueRecordPO> 查询结果的当前页对象
     */
	 public CurrentPage<ClaimEmrRescueRecordPO> queryClaimEmrRescueRecordForPage(ClaimEmrRescueRecordPO claimEmrRescueRecordPO, CurrentPage<ClaimEmrRescueRecordPO> currentPage);

     /**
     * @description 批量增加数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimEmrRescueRecordPOList 对象列表
     * @return boolean 批量添加是否成功
     */
	 public boolean batchSaveClaimEmrRescueRecord(List<ClaimEmrRescueRecordPO> claimEmrRescueRecordPOList);

     /**
     * @description 批量修改数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimEmrRescueRecordPOList 对象列表
     * @return boolean 批量修改是否成功
     */
	 public boolean batchUpdateClaimEmrRescueRecord(List<ClaimEmrRescueRecordPO> claimEmrRescueRecordPOList);

     /**
     * @description 批量删除数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimEmrRescueRecordPOList 对象列表
     * @return boolean 批量删除是否成功
     */
	 public boolean batchDeleteClaimEmrRescueRecord(List<ClaimEmrRescueRecordPO> claimEmrRescueRecordPOList);

     /**
     * @description 查询所有数据 ，重新组装为map
     * @version
     * @title
     * <AUTHOR>
     * @param claimEmrRescueRecordPO 对象
     * @return List<Map<String, Object>> 查询结果存放到map中
     */
	 public List<Map<String, Object>> findAllMapClaimEmrRescueRecord(ClaimEmrRescueRecordPO claimEmrRescueRecordPO);
	 
 }
 