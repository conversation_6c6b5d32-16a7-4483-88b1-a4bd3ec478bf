package com.nci.tunan.clm.dao;

import java.util.List;
import java.util.Map;

import com.nci.tunan.clm.interfaces.model.po.ClaimMediElecBillLogPO;
import com.nci.udmp.framework.model.CurrentPage;


/** 
 * @description IClaimMediElecBillLogDao接口
 * <AUTHOR> 
 * @date 2025-06-27 16:16:41  
 */
 public interface IClaimMediElecBillLogDao {
	 /**
     * @description 增加数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimMediElecBillLogPO 对象
     * @return ClaimMediElecBillLogPO 添加结果
     */
	 public ClaimMediElecBillLogPO addClaimMediElecBillLog(ClaimMediElecBillLogPO claimMediElecBillLogPO);
	 
     /**
     * @description 删除数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimMediElecBillLogPO 对象
     * @return boolean 删除是否成功
     */
	 public boolean deleteClaimMediElecBillLog(ClaimMediElecBillLogPO claimMediElecBillLogPO);
	 
     /**
     * @description 修改数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimMediElecBillLogPO 对象
     * @return ClaimMediElecBillLogPO 修改结果
     */
	 public ClaimMediElecBillLogPO updateClaimMediElecBillLog(ClaimMediElecBillLogPO claimMediElecBillLogPO);
	 
     /**
     * @description 查询单条数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimMediElecBillLogPO 对象
     * @return ClaimMediElecBillLogPO 查询结果对象
     */
	 public ClaimMediElecBillLogPO findClaimMediElecBillLog(ClaimMediElecBillLogPO claimMediElecBillLogPO);
	 
     /**
     * @description 查询所有数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimMediElecBillLogPO 对象
     * @return List<ClaimMediElecBillLogPO> 查询结果List
     */
	 public List<ClaimMediElecBillLogPO> findAllClaimMediElecBillLog(ClaimMediElecBillLogPO claimMediElecBillLogPO);
	 
     /**
     * @description 查询数据条数
     * @version
     * @title
     * <AUTHOR>
     * @param claimMediElecBillLogPO 对象
     * @return int 查询结果条数
     */
	 public int findClaimMediElecBillLogTotal(ClaimMediElecBillLogPO claimMediElecBillLogPO);

     /**
     * @description 分页查询数据
     * @version
     * @title
     * <AUTHOR>
     * @param currentPage 当前页对象
     * @return CurrentPage<ClaimMediElecBillLogPO> 查询结果的当前页对象
     */
	 public CurrentPage<ClaimMediElecBillLogPO> queryClaimMediElecBillLogForPage(ClaimMediElecBillLogPO claimMediElecBillLogPO, CurrentPage<ClaimMediElecBillLogPO> currentPage);

     /**
     * @description 批量增加数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimMediElecBillLogPOList 对象列表
     * @return boolean 批量添加是否成功
     */
	 public boolean batchSaveClaimMediElecBillLog(List<ClaimMediElecBillLogPO> claimMediElecBillLogPOList);

     /**
     * @description 批量修改数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimMediElecBillLogPOList 对象列表
     * @return boolean 批量修改是否成功
     */
	 public boolean batchUpdateClaimMediElecBillLog(List<ClaimMediElecBillLogPO> claimMediElecBillLogPOList);

     /**
     * @description 批量删除数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimMediElecBillLogPOList 对象列表
     * @return boolean 批量删除是否成功
     */
	 public boolean batchDeleteClaimMediElecBillLog(List<ClaimMediElecBillLogPO> claimMediElecBillLogPOList);

     /**
     * @description 查询所有数据 ，重新组装为map
     * @version
     * @title
     * <AUTHOR>
     * @param claimMediElecBillLogPO 对象
     * @return List<Map<String, Object>> 查询结果存放到map中
     */
	 public List<Map<String, Object>> findAllMapClaimMediElecBillLog(ClaimMediElecBillLogPO claimMediElecBillLogPO);
	 
 }
 