package com.nci.tunan.clm.dao;

import java.util.List;
import java.util.Map;

import com.nci.tunan.clm.interfaces.model.po.ClaimCasePO;
import com.nci.tunan.clm.interfaces.model.po.ClaimDirectCheckCasePO;
import com.nci.udmp.framework.model.CurrentPage;


/** 
 * @description IClaimDirectCheckCaseDao接口
 * <AUTHOR> 
 * @date 2021-11-08 11:12:29  
 */
 public interface IClaimDirectCheckCaseDao {
	 /**
     * @description 增加数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimDirectCheckCasePO 对象
     * @return ClaimDirectCheckCasePO 添加结果
     */
	 public ClaimDirectCheckCasePO addClaimDirectCheckCase(ClaimDirectCheckCasePO claimDirectCheckCasePO);
	 
     /**
     * @description 删除数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimDirectCheckCasePO 对象
     * @return boolean 删除是否成功
     */
	 public boolean deleteClaimDirectCheckCase(ClaimDirectCheckCasePO claimDirectCheckCasePO);
	 
     /**
     * @description 修改数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimDirectCheckCasePO 对象
     * @return ClaimDirectCheckCasePO 修改结果
     */
	 public ClaimDirectCheckCasePO updateClaimDirectCheckCase(ClaimDirectCheckCasePO claimDirectCheckCasePO);
	 
     /**
     * @description 查询单条数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimDirectCheckCasePO 对象
     * @return ClaimDirectCheckCasePO 查询结果对象
     */
	 public ClaimDirectCheckCasePO findClaimDirectCheckCase(ClaimDirectCheckCasePO claimDirectCheckCasePO);
	 
     /**
     * @description 查询所有数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimDirectCheckCasePO 对象
     * @return List<ClaimDirectCheckCasePO> 查询结果List
     */
	 public List<ClaimDirectCheckCasePO> findAllClaimDirectCheckCase(ClaimDirectCheckCasePO claimDirectCheckCasePO);
	 
     /**
     * @description 查询数据条数
     * @version
     * @title
     * <AUTHOR>
     * @param claimDirectCheckCasePO 对象
     * @return int 查询结果条数
     */
	 public int findClaimDirectCheckCaseTotal(ClaimDirectCheckCasePO claimDirectCheckCasePO);

     /**
     * @description 分页查询数据
     * @version
     * @title
     * <AUTHOR>
     * @param currentPage 当前页对象
     * @return CurrentPage<ClaimDirectCheckCasePO> 查询结果的当前页对象
     */
	 public CurrentPage<ClaimDirectCheckCasePO> queryClaimDirectCheckCaseForPage(ClaimDirectCheckCasePO claimDirectCheckCasePO, CurrentPage<ClaimDirectCheckCasePO> currentPage);

     /**
     * @description 批量增加数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimDirectCheckCasePOList 对象列表
     * @return boolean 批量添加是否成功
     */
	 public boolean batchSaveClaimDirectCheckCase(List<ClaimDirectCheckCasePO> claimDirectCheckCasePOList);

     /**
     * @description 批量修改数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimDirectCheckCasePOList 对象列表
     * @return boolean 批量修改是否成功
     */
	 public boolean batchUpdateClaimDirectCheckCase(List<ClaimDirectCheckCasePO> claimDirectCheckCasePOList);

     /**
     * @description 批量删除数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimDirectCheckCasePOList 对象列表
     * @return boolean 批量删除是否成功
     */
	 public boolean batchDeleteClaimDirectCheckCase(List<ClaimDirectCheckCasePO> claimDirectCheckCasePOList);

     /**
     * @description 查询所有数据 ，重新组装为map
     * @version
     * @title
     * <AUTHOR>
     * @param claimDirectCheckCasePO 对象
     * @return List<Map<String, Object>> 查询结果存放到map中
     */
	 public List<Map<String, Object>> findAllMapClaimDirectCheckCase(ClaimDirectCheckCasePO claimDirectCheckCasePO);
	 
	 /**
	  * 直连数据调取核查页面-查询共享池列表
	  * @description
	  * @version
	  * @title
	  * <AUTHOR> <EMAIL>
	  * @param claimCasePO
	  * @param currentPage
	  * @return
	  */
	public CurrentPage<ClaimCasePO> queryApplyCaseByCondition(ClaimCasePO claimCasePO, CurrentPage<ClaimCasePO> currentPage);

	 /**
	  * 直连数据调取核查页面-页面初始化
	  * @description
	  * @version
	  * @title
	  * <AUTHOR> <EMAIL>
	  * @param claimCasePO
	  * @param currentPage
	  * @return
	  */
	public CurrentPage<ClaimCasePO> directDataCheckSharePoolInit(ClaimCasePO claimCasePO, CurrentPage<ClaimCasePO> currentPage);

	/**
	 * 直连数据调取核查页面-任务申请至个人池
	 * @description
	 * @version
	 * @title
	 * <AUTHOR> <EMAIL>
	 * @param claimDirectCheckCasePO
	 */
	public void applySelfPool(ClaimDirectCheckCasePO claimDirectCheckCasePO);

	/**
	 * 根据赔案ID更新完成标识
	 * @description
	 * @version
	 * @title
	 * <AUTHOR> <EMAIL>
	 * @param claimDirectCheckCasePO
	 */
	public void updateFinishByCaseId(ClaimDirectCheckCasePO claimDirectCheckCasePO);
	 
 }
 