package com.nci.tunan.clm.dao;

import com.nci.tunan.clm.interfaces.model.po.ClaimReSurveyAccTypePO;
import com.nci.udmp.framework.model.CurrentPage;
import java.util.Map;
import java.util.List;


/** 
 * @description IClaimReSurveyAccTypeDao复勘调查理赔类型接口
 * <AUTHOR>
 * @.belongToModule CLM-理赔系统-复勘调查理赔
 * @date 2015-09-24 11:08:43  
 */
 public interface IClaimReSurveyAccTypeDao {
	 /**
     * @description 增加数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimReSurveyAccTypePO 复勘调查理赔类型对象
     * @return ClaimReSurveyAccTypePO 添加结果
     */
	 public ClaimReSurveyAccTypePO addClaimReSurveyAccType(ClaimReSurveyAccTypePO claimReSurveyAccTypePO);
	 
     /**
     * @description 删除数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimReSurveyAccTypePO 复勘调查理赔类型对象
     * @return boolean 删除是否成功
     */
	 public boolean deleteClaimReSurveyAccType(ClaimReSurveyAccTypePO claimReSurveyAccTypePO);
	 
     /**
     * @description 修改数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimReSurveyAccTypePO 复勘调查理赔类型对象
     * @return ClaimReSurveyAccTypePO 修改结果
     */
	 public ClaimReSurveyAccTypePO updateClaimReSurveyAccType(ClaimReSurveyAccTypePO claimReSurveyAccTypePO);
	 
     /**
     * @description 查询单条数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimReSurveyAccTypePO 复勘调查理赔类型对象
     * @return ClaimReSurveyAccTypePO 查询结果对象
     */
	 public ClaimReSurveyAccTypePO findClaimReSurveyAccType(ClaimReSurveyAccTypePO claimReSurveyAccTypePO);
	 
     /**
     * @description 查询所有数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimReSurveyAccTypePO 复勘调查理赔类型对象
     * @return List<ClaimReSurveyAccTypePO> 查询结果List
     */
	 public List<ClaimReSurveyAccTypePO> findAllClaimReSurveyAccType(ClaimReSurveyAccTypePO claimReSurveyAccTypePO);
	 
     /**
     * @description 查询数据条数
     * @version
     * @title
     * <AUTHOR>
     * @param claimReSurveyAccTypePO 复勘调查理赔类型对象
     * @return int 查询结果条数
     */
	 public int findClaimReSurveyAccTypeTotal(ClaimReSurveyAccTypePO claimReSurveyAccTypePO);

     /**
     * @description 分页查询数据
     * @version
     * @title
     * <AUTHOR>
     * @param currentPage 当前页对象
     * @return CurrentPage<ClaimReSurveyAccTypePO> 查询结果的当前页对象
     */
	 public CurrentPage<ClaimReSurveyAccTypePO> queryClaimReSurveyAccTypeForPage(ClaimReSurveyAccTypePO claimReSurveyAccTypePO, CurrentPage<ClaimReSurveyAccTypePO> currentPage);

     /**
     * @description 批量增加数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimReSurveyAccTypePOList 复勘调查理赔类型对象列表
     * @return boolean 批量添加是否成功
     */
	 public boolean batchSaveClaimReSurveyAccType(List<ClaimReSurveyAccTypePO> claimReSurveyAccTypePOList);

     /**
     * @description 批量修改数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimReSurveyAccTypePOList 复勘调查理赔类型对象列表
     * @return boolean 批量修改是否成功
     */
	 public boolean batchUpdateClaimReSurveyAccType(List<ClaimReSurveyAccTypePO> claimReSurveyAccTypePOList);

     /**
     * @description 批量删除数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimReSurveyAccTypePOList 复勘调查理赔类型对象列表
     * @return boolean 批量删除是否成功
     */
	 public boolean batchDeleteClaimReSurveyAccType(List<ClaimReSurveyAccTypePO> claimReSurveyAccTypePOList);

     /**
     * @description 查询所有数据 ，重新组装为map
     * @version
     * @title
     * <AUTHOR>
     * @param claimReSurveyAccTypePO 复勘调查理赔类型对象
     * @return List<Map<String, Object>> 查询结果存放到map中
     */
	 public List<Map<String, Object>> findAllMapClaimReSurveyAccType(ClaimReSurveyAccTypePO claimReSurveyAccTypePO);
	 /**
	     * @description 批量添加复勘调查理赔类型数据
	     * <AUTHOR> <EMAIL>
	     * @param claimReSurveyAccTypePOs 复勘调查理赔类型
	     */
     public void batchSaveReSurveyAccType(List<ClaimReSurveyAccTypePO> claimReSurveyAccTypePOs);
     /**
      * 
      * @description 通过planId查询所有复勘调查理赔类型数据
      * <AUTHOR> <EMAIL>
      * @param  reSurveyAccTypePO  复勘调查理赔类型对象参数
      * @return List<ClaimReSurveyAccTypePO> 复勘调查理赔类型
      * @throws BizException
      */
     public List<ClaimReSurveyAccTypePO> queryAllReSurveyRule(ClaimReSurveyAccTypePO reSurveyAccTypePO);
     /**
      * @description 删除数据
      * @version
      * @title
      * <AUTHOR> <EMAIL>
      * @param claimReSurveyAccTypePO 复勘调查理赔类型对象
      */
     public void deleteReSurveyAccTypeByPlanId(ClaimReSurveyAccTypePO claimReSurveyAccTypePO);
	 
 }
 