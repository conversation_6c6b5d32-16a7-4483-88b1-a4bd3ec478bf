package com.nci.tunan.clm.dao;

import java.util.List;
import java.util.Map;

import com.nci.tunan.clm.interfaces.model.po.ClaimDirectConnApplyPO;
import com.nci.udmp.framework.model.CurrentPage;


/** 
 * @description IClaimDirectConnApplyDao接口
 * <AUTHOR> 
 * @date 2021-11-08 11:12:29  
 */
 public interface IClaimDirectConnApplyDao {
	 /**
     * @description 增加数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimDirectConnApplyPO 对象
     * @return ClaimDirectConnApplyPO 添加结果
     */
	 public ClaimDirectConnApplyPO addClaimDirectConnApply(ClaimDirectConnApplyPO claimDirectConnApplyPO);
	 
     /**
     * @description 删除数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimDirectConnApplyPO 对象
     * @return boolean 删除是否成功
     */
	 public boolean deleteClaimDirectConnApply(ClaimDirectConnApplyPO claimDirectConnApplyPO);
	 
     /**
     * @description 修改数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimDirectConnApplyPO 对象
     * @return ClaimDirectConnApplyPO 修改结果
     */
	 public ClaimDirectConnApplyPO updateClaimDirectConnApply(ClaimDirectConnApplyPO claimDirectConnApplyPO);
	 
     /**
     * @description 查询单条数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimDirectConnApplyPO 对象
     * @return ClaimDirectConnApplyPO 查询结果对象
     */
	 public ClaimDirectConnApplyPO findClaimDirectConnApply(ClaimDirectConnApplyPO claimDirectConnApplyPO);
	 
     /**
     * @description 查询所有数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimDirectConnApplyPO 对象
     * @return List<ClaimDirectConnApplyPO> 查询结果List
     */
	 public List<ClaimDirectConnApplyPO> findAllClaimDirectConnApply(ClaimDirectConnApplyPO claimDirectConnApplyPO);
	 
     /**
     * @description 查询数据条数
     * @version
     * @title
     * <AUTHOR>
     * @param claimDirectConnApplyPO 对象
     * @return int 查询结果条数
     */
	 public int findClaimDirectConnApplyTotal(ClaimDirectConnApplyPO claimDirectConnApplyPO);

     /**
     * @description 分页查询数据
     * @version
     * @title
     * <AUTHOR>
     * @param currentPage 当前页对象
     * @return CurrentPage<ClaimDirectConnApplyPO> 查询结果的当前页对象
     */
	 public CurrentPage<ClaimDirectConnApplyPO> queryClaimDirectConnApplyForPage(ClaimDirectConnApplyPO claimDirectConnApplyPO, CurrentPage<ClaimDirectConnApplyPO> currentPage);

     /**
     * @description 批量增加数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimDirectConnApplyPOList 对象列表
     * @return boolean 批量添加是否成功
     */
	 public boolean batchSaveClaimDirectConnApply(List<ClaimDirectConnApplyPO> claimDirectConnApplyPOList);

     /**
     * @description 批量修改数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimDirectConnApplyPOList 对象列表
     * @return boolean 批量修改是否成功
     */
	 public boolean batchUpdateClaimDirectConnApply(List<ClaimDirectConnApplyPO> claimDirectConnApplyPOList);

     /**
     * @description 批量删除数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimDirectConnApplyPOList 对象列表
     * @return boolean 批量删除是否成功
     */
	 public boolean batchDeleteClaimDirectConnApply(List<ClaimDirectConnApplyPO> claimDirectConnApplyPOList);

     /**
     * @description 查询所有数据 ，重新组装为map
     * @version
     * @title
     * <AUTHOR>
     * @param claimDirectConnApplyPO 对象
     * @return List<Map<String, Object>> 查询结果存放到map中
     */
	 public List<Map<String, Object>> findAllMapClaimDirectConnApply(ClaimDirectConnApplyPO claimDirectConnApplyPO);

	/**
	 * @description 查询10分钟就诊信息未返回的数据
	 * @version
	 * @title
	 * <AUTHOR> <EMAIL>
	 * @param claimDirectConnApplyPO 理赔直连接口信息
	 * @return 需要修改直连调取失败原因的数据
	*/
	public List<ClaimDirectConnApplyPO> queyBatchDirectCheckTimeout(
			ClaimDirectConnApplyPO claimDirectConnApplyPO);
	 

	 /**
	  * 根据赔案ID查询表数据
	  * @description
	  * @version
	  * @title
	  * <AUTHOR> <EMAIL>
	  * @param claimDirectConnApplyPO
	  * @return
	  */
	 public ClaimDirectConnApplyPO findClaimDirectConnApplyByCaseId(ClaimDirectConnApplyPO claimDirectConnApplyPO);
	/**
	 * @description 查询直连11位流水号
	 * <AUTHOR>
	 * @param BigDecimal
	 * @return 返回结果
	 */
	public String findDirectConnRandom(ClaimDirectConnApplyPO claimDirectConnApplyPO);
	 
 }
 