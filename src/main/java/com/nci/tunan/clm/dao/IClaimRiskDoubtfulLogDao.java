package com.nci.tunan.clm.dao;

import com.nci.tunan.clm.interfaces.model.po.ClaimRiskDoubtfulLogPO;
import com.nci.udmp.framework.model.CurrentPage;
import java.util.Map;
import java.util.List;


/** 
 * @description IClaimRiskDoubtfulLogDao案件风险疑点信息记录接口
 * <AUTHOR>
 * @.belongToModule CLM-理赔系统
 * @date 2019-03-18 16:50:15  
 */
 public interface IClaimRiskDoubtfulLogDao {
	 /**
     * @description 增加数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimRiskDoubtfulLogPO 案件风险疑点信息记录对象
     * @return ClaimRiskDoubtfulLogPO 添加结果
     */
	 public ClaimRiskDoubtfulLogPO addClaimRiskDoubtfulLog(ClaimRiskDoubtfulLogPO claimRiskDoubtfulLogPO);
	 
     /**
     * @description 删除数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimRiskDoubtfulLogPO 案件风险疑点信息记录对象
     * @return boolean 删除是否成功
     */
	 public boolean deleteClaimRiskDoubtfulLog(ClaimRiskDoubtfulLogPO claimRiskDoubtfulLogPO);
	 
     /**
     * @description 修改数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimRiskDoubtfulLogPO 案件风险疑点信息记录对象
     * @return ClaimRiskDoubtfulLogPO 修改结果
     */
	 public ClaimRiskDoubtfulLogPO updateClaimRiskDoubtfulLog(ClaimRiskDoubtfulLogPO claimRiskDoubtfulLogPO);
	 
     /**
     * @description 查询单条数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimRiskDoubtfulLogPO 案件风险疑点信息记录对象
     * @return ClaimRiskDoubtfulLogPO 查询结果对象
     */
	 public ClaimRiskDoubtfulLogPO findClaimRiskDoubtfulLog(ClaimRiskDoubtfulLogPO claimRiskDoubtfulLogPO);
	 
     /**
     * @description 查询所有数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimRiskDoubtfulLogPO 案件风险疑点信息记录对象
     * @return List<ClaimRiskDoubtfulLogPO> 查询结果List
     */
	 public List<ClaimRiskDoubtfulLogPO> findAllClaimRiskDoubtfulLog(ClaimRiskDoubtfulLogPO claimRiskDoubtfulLogPO);
	 
     /**
     * @description 查询数据条数
     * @version
     * @title
     * <AUTHOR>
     * @param claimRiskDoubtfulLogPO 案件风险疑点信息记录对象
     * @return int 查询结果条数
     */
	 public int findClaimRiskDoubtfulLogTotal(ClaimRiskDoubtfulLogPO claimRiskDoubtfulLogPO);

     /**
     * @description 分页查询数据
     * @version
     * @title
     * <AUTHOR>
     * @param currentPage 当前页对象
     * @return CurrentPage<ClaimRiskDoubtfulLogPO> 查询结果的当前页对象
     */
	 public CurrentPage<ClaimRiskDoubtfulLogPO> queryClaimRiskDoubtfulLogForPage(ClaimRiskDoubtfulLogPO claimRiskDoubtfulLogPO, CurrentPage<ClaimRiskDoubtfulLogPO> currentPage);

     /**
     * @description 批量增加数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimRiskDoubtfulLogPOList 案件风险疑点信息记录对象列表
     * @return boolean 批量添加是否成功
     */
	 public boolean batchSaveClaimRiskDoubtfulLog(List<ClaimRiskDoubtfulLogPO> claimRiskDoubtfulLogPOList);

     /**
     * @description 批量修改数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimRiskDoubtfulLogPOList 案件风险疑点信息记录对象列表
     * @return boolean 批量修改是否成功
     */
	 public boolean batchUpdateClaimRiskDoubtfulLog(List<ClaimRiskDoubtfulLogPO> claimRiskDoubtfulLogPOList);

     /**
     * @description 批量删除数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimRiskDoubtfulLogPOList 案件风险疑点信息记录对象列表
     * @return boolean 批量删除是否成功
     */
	 public boolean batchDeleteClaimRiskDoubtfulLog(List<ClaimRiskDoubtfulLogPO> claimRiskDoubtfulLogPOList);

     /**
     * @description 查询所有数据 ，重新组装为map
     * @version
     * @title
     * <AUTHOR>
     * @param claimRiskDoubtfulLogPO 案件风险疑点信息记录对象
     * @return List<Map<String, Object>> 查询结果存放到map中
     */
	 public List<Map<String, Object>> findAllMapClaimRiskDoubtfulLog(ClaimRiskDoubtfulLogPO claimRiskDoubtfulLogPO);
	 
	 /**
	     * @description 分页查询数据
	     * @version
	     * @title
	     * <AUTHOR>
	     * @param currentPage 当前页对象
	     * @return CurrentPage<ClaimRiskDoubtfulLogPO> 查询结果的当前页对象
	     */
		 public CurrentPage<ClaimRiskDoubtfulLogPO> findClaimRiskDoubtfulLogForPage(ClaimRiskDoubtfulLogPO claimRiskDoubtfulLogPO, CurrentPage<ClaimRiskDoubtfulLogPO> currentPage);
	 
	 
	 
 }
 