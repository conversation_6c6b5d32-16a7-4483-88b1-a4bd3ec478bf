package com.nci.tunan.clm.dao;

import com.nci.tunan.clm.interfaces.model.po.ClaimDirectLogRecordPO;
import com.nci.udmp.framework.model.*;
import java.util.Map;
import org.slf4j.Logger;
import java.util.List;
import com.nci.udmp.util.logging.LoggerFactory;


/** 
 * @description IClaimDirectLogRecordDao接口
 * <AUTHOR> 
 * @date 2021-03-23 15:25:00  
 */
 public interface IClaimDirectLogRecordDao {
	 /**
     * @description 增加数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimDirectLogRecordPO 对象
     * @return ClaimDirectLogRecordPO 添加结果
     */
	 public ClaimDirectLogRecordPO addClaimDirectLogRecord(ClaimDirectLogRecordPO claimDirectLogRecordPO);
	 
     /**
     * @description 删除数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimDirectLogRecordPO 对象
     * @return boolean 删除是否成功
     */
	 public boolean deleteClaimDirectLogRecord(ClaimDirectLogRecordPO claimDirectLogRecordPO);
	 
     /**
     * @description 修改数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimDirectLogRecordPO 对象
     * @return ClaimDirectLogRecordPO 修改结果
     */
	 public ClaimDirectLogRecordPO updateClaimDirectLogRecord(ClaimDirectLogRecordPO claimDirectLogRecordPO);
	 
     /**
     * @description 查询单条数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimDirectLogRecordPO 对象
     * @return ClaimDirectLogRecordPO 查询结果对象
     */
	 public ClaimDirectLogRecordPO findClaimDirectLogRecord(ClaimDirectLogRecordPO claimDirectLogRecordPO);
	 
     /**
     * @description 查询所有数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimDirectLogRecordPO 对象
     * @return List<ClaimDirectLogRecordPO> 查询结果List
     */
	 public List<ClaimDirectLogRecordPO> findAllClaimDirectLogRecord(ClaimDirectLogRecordPO claimDirectLogRecordPO);
	 
     /**
     * @description 查询数据条数
     * @version
     * @title
     * <AUTHOR>
     * @param claimDirectLogRecordPO 对象
     * @return int 查询结果条数
     */
	 public int findClaimDirectLogRecordTotal(ClaimDirectLogRecordPO claimDirectLogRecordPO);

     /**
     * @description 分页查询数据
     * @version
     * @title
     * <AUTHOR>
     * @param currentPage 当前页对象
     * @return CurrentPage<ClaimDirectLogRecordPO> 查询结果的当前页对象
     */
	 public CurrentPage<ClaimDirectLogRecordPO> queryClaimDirectLogRecordForPage(ClaimDirectLogRecordPO claimDirectLogRecordPO, CurrentPage<ClaimDirectLogRecordPO> currentPage);

     /**
     * @description 批量增加数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimDirectLogRecordPOList 对象列表
     * @return boolean 批量添加是否成功
     */
	 public boolean batchSaveClaimDirectLogRecord(List<ClaimDirectLogRecordPO> claimDirectLogRecordPOList);

     /**
     * @description 批量修改数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimDirectLogRecordPOList 对象列表
     * @return boolean 批量修改是否成功
     */
	 public boolean batchUpdateClaimDirectLogRecord(List<ClaimDirectLogRecordPO> claimDirectLogRecordPOList);

     /**
     * @description 批量删除数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimDirectLogRecordPOList 对象列表
     * @return boolean 批量删除是否成功
     */
	 public boolean batchDeleteClaimDirectLogRecord(List<ClaimDirectLogRecordPO> claimDirectLogRecordPOList);

     /**
     * @description 查询所有数据 ，重新组装为map
     * @version
     * @title
     * <AUTHOR>
     * @param claimDirectLogRecordPO 对象
     * @return List<Map<String, Object>> 查询结果存放到map中
     */
	 public List<Map<String, Object>> findAllMapClaimDirectLogRecord(ClaimDirectLogRecordPO claimDirectLogRecordPO);
	 
 }
 