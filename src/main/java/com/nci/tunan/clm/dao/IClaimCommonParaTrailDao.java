package com.nci.tunan.clm.dao;

import java.util.List;
import java.util.Map;

import com.nci.tunan.clm.interfaces.model.po.ClaimCommonParaTrailPO;
import com.nci.udmp.framework.model.CurrentPage;

/**
 * 
 * @description ClaimCommonParaTrailDaoImpl实现类
 * <AUTHOR> 
 * @.belongToModule CLM-理赔系统-公共参数变更
 * @date 2015-05-15 下午3:35:44
 */
 public interface IClaimCommonParaTrailDao {
	 /**
     * @description 增加数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimCommonParaTrailPO 对象
     * @return ClaimCommonParaTrailPO 添加结果
     */
	 public ClaimCommonParaTrailPO addClaimCommonParaTrail(ClaimCommonParaTrailPO claimCommonParaTrailPO);
	 
     /**
     * @description 删除数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimCommonParaTrailPO 对象
     * @return boolean 删除是否成功
     */
	 public boolean deleteClaimCommonParaTrail(ClaimCommonParaTrailPO claimCommonParaTrailPO);
	 
     /**
     * @description 修改数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimCommonParaTrailPO 对象
     * @return ClaimCommonParaTrailPO 修改结果
     */
	 public ClaimCommonParaTrailPO updateClaimCommonParaTrail(ClaimCommonParaTrailPO claimCommonParaTrailPO);
	 
     /**
     * @description 查询单条数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimCommonParaTrailPO 对象
     * @return ClaimCommonParaTrailPO 查询结果对象
     */
	 public ClaimCommonParaTrailPO findClaimCommonParaTrail(ClaimCommonParaTrailPO claimCommonParaTrailPO);
	 
     /**
     * @description 查询所有数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimCommonParaTrailPO 对象
     * @return List<ClaimCommonParaTrailPO> 查询结果List
     */
	 public List<ClaimCommonParaTrailPO> findAllClaimCommonParaTrail(ClaimCommonParaTrailPO claimCommonParaTrailPO);
	 
     /**
     * @description 查询数据条数
     * @version
     * @title
     * <AUTHOR>
     * @param claimCommonParaTrailPO 对象
     * @return int 查询结果条数
     */
	 public int findClaimCommonParaTrailTotal(ClaimCommonParaTrailPO claimCommonParaTrailPO);

     /**
     * @description 分页查询数据
     * @version
     * @title
     * <AUTHOR>
     * @param currentPage 当前页对象
     * @return CurrentPage<ClaimCommonParaTrailPO> 查询结果的当前页对象
     */
	 public CurrentPage<ClaimCommonParaTrailPO> queryClaimCommonParaTrailForPage(ClaimCommonParaTrailPO claimCommonParaTrailPO, CurrentPage<ClaimCommonParaTrailPO> currentPage);

     /**
     * @description 批量增加数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimCommonParaTrailPOList 对象列表
     * @return boolean 批量添加是否成功
     */
	 public boolean batchSaveClaimCommonParaTrail(List<ClaimCommonParaTrailPO> claimCommonParaTrailPOList);

     /**
     * @description 批量修改数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimCommonParaTrailPOList 对象列表
     * @return boolean 批量修改是否成功
     */
	 public boolean batchUpdateClaimCommonParaTrail(List<ClaimCommonParaTrailPO> claimCommonParaTrailPOList);

     /**
     * @description 批量删除数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimCommonParaTrailPOList 对象列表
     * @return boolean 批量删除是否成功
     */
	 public boolean batchDeleteClaimCommonParaTrail(List<ClaimCommonParaTrailPO> claimCommonParaTrailPOList);

     /**
     * @description 查询所有数据 ，重新组装为map
     * @version
     * @title
     * <AUTHOR>
     * @param claimCommonParaTrailPO 对象
     * @return List<Map<String, Object>> 查询结果存放到map中
     */
	 public List<Map<String, Object>> findAllMapClaimCommonParaTrail(ClaimCommonParaTrailPO claimCommonParaTrailPO);
	 
 }
 