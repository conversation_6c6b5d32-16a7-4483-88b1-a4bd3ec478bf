package com.nci.tunan.clm.dao;

import com.nci.udmp.framework.model.*;
import java.util.Map;
import org.slf4j.Logger;
import java.util.List;
import com.nci.tunan.clm.interfaces.model.po.ClaimCbitElecBillLogPO;
import com.nci.udmp.util.logging.LoggerFactory;


/** 
 * @description IClaimCbitElecBillLogDao接口
 * <AUTHOR> 
 * @date 2024-07-18 16:03:44  
 */
 public interface IClaimCbitElecBillLogDao {
	 /**
     * @description 增加数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimCbitElecBillLogPO 对象
     * @return ClaimCbitElecBillLogPO 添加结果
     */
	 public ClaimCbitElecBillLogPO addClaimCbitElecBillLog(ClaimCbitElecBillLogPO claimCbitElecBillLogPO);
	 
     /**
     * @description 删除数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimCbitElecBillLogPO 对象
     * @return boolean 删除是否成功
     */
	 public boolean deleteClaimCbitElecBillLog(ClaimCbitElecBillLogPO claimCbitElecBillLogPO);
	 
     /**
     * @description 修改数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimCbitElecBillLogPO 对象
     * @return ClaimCbitElecBillLogPO 修改结果
     */
	 public ClaimCbitElecBillLogPO updateClaimCbitElecBillLog(ClaimCbitElecBillLogPO claimCbitElecBillLogPO);
	 
     /**
     * @description 查询单条数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimCbitElecBillLogPO 对象
     * @return ClaimCbitElecBillLogPO 查询结果对象
     */
	 public ClaimCbitElecBillLogPO findClaimCbitElecBillLog(ClaimCbitElecBillLogPO claimCbitElecBillLogPO);
	 
     /**
     * @description 查询所有数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimCbitElecBillLogPO 对象
     * @return List<ClaimCbitElecBillLogPO> 查询结果List
     */
	 public List<ClaimCbitElecBillLogPO> findAllClaimCbitElecBillLog(ClaimCbitElecBillLogPO claimCbitElecBillLogPO);
	 
     /**
     * @description 查询数据条数
     * @version
     * @title
     * <AUTHOR>
     * @param claimCbitElecBillLogPO 对象
     * @return int 查询结果条数
     */
	 public int findClaimCbitElecBillLogTotal(ClaimCbitElecBillLogPO claimCbitElecBillLogPO);

     /**
     * @description 分页查询数据
     * @version
     * @title
     * <AUTHOR>
     * @param currentPage 当前页对象
     * @return CurrentPage<ClaimCbitElecBillLogPO> 查询结果的当前页对象
     */
	 public CurrentPage<ClaimCbitElecBillLogPO> queryClaimCbitElecBillLogForPage(ClaimCbitElecBillLogPO claimCbitElecBillLogPO, CurrentPage<ClaimCbitElecBillLogPO> currentPage);

     /**
     * @description 批量增加数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimCbitElecBillLogPOList 对象列表
     * @return boolean 批量添加是否成功
     */
	 public boolean batchSaveClaimCbitElecBillLog(List<ClaimCbitElecBillLogPO> claimCbitElecBillLogPOList);

     /**
     * @description 批量修改数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimCbitElecBillLogPOList 对象列表
     * @return boolean 批量修改是否成功
     */
	 public boolean batchUpdateClaimCbitElecBillLog(List<ClaimCbitElecBillLogPO> claimCbitElecBillLogPOList);

     /**
     * @description 批量删除数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimCbitElecBillLogPOList 对象列表
     * @return boolean 批量删除是否成功
     */
	 public boolean batchDeleteClaimCbitElecBillLog(List<ClaimCbitElecBillLogPO> claimCbitElecBillLogPOList);

     /**
     * @description 查询所有数据 ，重新组装为map
     * @version
     * @title
     * <AUTHOR>
     * @param claimCbitElecBillLogPO 对象
     * @return List<Map<String, Object>> 查询结果存放到map中
     */
	 public List<Map<String, Object>> findAllMapClaimCbitElecBillLog(ClaimCbitElecBillLogPO claimCbitElecBillLogPO);
	 
 }
 