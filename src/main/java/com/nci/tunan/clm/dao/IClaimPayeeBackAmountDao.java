package com.nci.tunan.clm.dao;

import java.util.List;
import java.util.Map;

import com.nci.tunan.clm.interfaces.model.po.ClaimPayeeBackAmountPO;
import com.nci.udmp.framework.model.CurrentPage;

/**
 * 
 * @description 缴款人追偿金额信息表Dao
 * <AUTHOR> 
 * @.belongToModule CLM-理赔系统
 * @date 2015-05-15 下午4:52:37
 */
 public interface IClaimPayeeBackAmountDao {
	 /**
     * @description 增加数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimPayeeBackAmountPO 缴款人追偿金额信息对象
     * @return ClaimPayeeBackAmountPO 添加结果
     */
	 public ClaimPayeeBackAmountPO addClaimPayeeBackAmount(ClaimPayeeBackAmountPO claimPayeeBackAmountPO);
	 
     /**
     * @description 删除数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimPayeeBackAmountPO 缴款人追偿金额信息对象
     * @return boolean 删除是否成功
     */
	 public boolean deleteClaimPayeeBackAmount(ClaimPayeeBackAmountPO claimPayeeBackAmountPO);
	 /**
 	  * @description 删除数据根据BackAmountId
 	  * @version
 	  * @title
 	  * <AUTHOR>
 	  * @param claimPayeeBackAmountPO 缴款人追偿金额信息对象
 	  * @return boolean 删除是否成功
 	  */
 	 public boolean deletePayeeBackByBackAmountId(ClaimPayeeBackAmountPO claimPayeeBackAmountPO);
	 
     /**
     * @description 修改数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimPayeeBackAmountPO 缴款人追偿金额信息对象
     * @return ClaimPayeeBackAmountPO 修改结果
     */
	 public ClaimPayeeBackAmountPO updateClaimPayeeBackAmount(ClaimPayeeBackAmountPO claimPayeeBackAmountPO);
	 
     /**
     * @description 查询单条数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimPayeeBackAmountPO 缴款人追偿金额信息对象
     * @return ClaimPayeeBackAmountPO 查询结果对象
     */
	 public ClaimPayeeBackAmountPO findClaimPayeeBackAmount(ClaimPayeeBackAmountPO claimPayeeBackAmountPO);
	 
     /**
     * @description 查询所有数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimPayeeBackAmountPO 缴款人追偿金额信息对象
     * @return List<ClaimPayeeBackAmountPO> 查询结果List
     */
	 public List<ClaimPayeeBackAmountPO> findAllClaimPayeeBackAmount(ClaimPayeeBackAmountPO claimPayeeBackAmountPO);
	 
     /**
     * @description 查询数据条数
     * @version
     * @title
     * <AUTHOR>
     * @param claimPayeeBackAmountPO 缴款人追偿金额信息对象
     * @return int 查询结果条数
     */
	 public int findClaimPayeeBackAmountTotal(ClaimPayeeBackAmountPO claimPayeeBackAmountPO);

     /**
     * @description 分页查询数据
     * @version
     * @title
     * <AUTHOR>
     * @param currentPage 当前页对象
     * @return CurrentPage<ClaimPayeeBackAmountPO> 查询结果的当前页对象
     */
	 public CurrentPage<ClaimPayeeBackAmountPO> queryClaimPayeeBackAmountForPage(ClaimPayeeBackAmountPO claimPayeeBackAmountPO, CurrentPage<ClaimPayeeBackAmountPO> currentPage);

     /**
     * @description 批量增加数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimPayeeBackAmountPOList 缴款人追偿金额信息对象列表
     * @return boolean 批量添加是否成功
     */
	 public boolean batchSaveClaimPayeeBackAmount(List<ClaimPayeeBackAmountPO> claimPayeeBackAmountPOList);

     /**
     * @description 批量修改数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimPayeeBackAmountPOList 缴款人追偿金额信息对象列表
     * @return boolean 批量修改是否成功
     */
	 public boolean batchUpdateClaimPayeeBackAmount(List<ClaimPayeeBackAmountPO> claimPayeeBackAmountPOList);

     /**
     * @description 批量删除数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimPayeeBackAmountPOList 缴款人追偿金额信息对象列表
     * @return boolean 批量删除是否成功
     */
	 public boolean batchDeleteClaimPayeeBackAmount(List<ClaimPayeeBackAmountPO> claimPayeeBackAmountPOList);

     /**
     * @description 查询所有数据 ，重新组装为map
     * @version
     * @title
     * <AUTHOR>
     * @param claimPayeeBackAmountPO 缴款人追偿金额信息对象
     * @return List<Map<String, Object>> 查询结果存放到map中
     */
	 public List<Map<String, Object>> findAllMapClaimPayeeBackAmount(ClaimPayeeBackAmountPO claimPayeeBackAmountPO);
	 
 }
 