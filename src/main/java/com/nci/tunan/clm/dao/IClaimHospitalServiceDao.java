package com.nci.tunan.clm.dao;

import java.util.List;
import java.util.Map;

import com.nci.tunan.clm.interfaces.model.po.ClaimHospitalServicePO;
import com.nci.udmp.framework.model.CurrentPage;


/**
 * @description 理赔医院信息参数表Dao
 * <AUTHOR> 
 * @.belongToModule CLM-理赔系统
 * @date 2015-05-15 下午4:36:24
 */
 public interface IClaimHospitalServiceDao {
	 /**
     * @description 增加数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimHospitalServicePO 医院信息参数对象
     * @return ClaimHospitalServicePO 添加结果
     */
	 public ClaimHospitalServicePO addClaimHospitalService(ClaimHospitalServicePO claimHospitalServicePO);
	 
     /**
     * @description 删除数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimHospitalServicePO 医院信息参数对象
     * @return boolean 删除是否成功
     */
	 public boolean deleteClaimHospitalService(ClaimHospitalServicePO claimHospitalServicePO);
	 
     /**
     * @description 修改数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimHospitalServicePO 医院信息参数对象
     * @return ClaimHospitalServicePO 修改结果
     */
	 public ClaimHospitalServicePO updateClaimHospitalService(ClaimHospitalServicePO claimHospitalServicePO);
	 
     /**
     * @description 查询单条数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimHospitalServicePO 医院信息参数对象
     * @return ClaimHospitalServicePO 查询结果对象
     */
	 public ClaimHospitalServicePO findClaimHospitalService(ClaimHospitalServicePO claimHospitalServicePO);
	 
     /**
     * @description 查询所有数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimHospitalServicePO 医院信息参数对象
     * @return List<ClaimHospitalServicePO> 查询结果List
     */
	 public List<ClaimHospitalServicePO> findAllClaimHospitalService(ClaimHospitalServicePO claimHospitalServicePO);
	 
     /**
     * @description 查询数据条数
     * @version
     * @title
     * <AUTHOR>
     * @param claimHospitalServicePO 医院信息参数对象
     * @return int 查询结果条数
     */
	 public int findClaimHospitalServiceTotal(ClaimHospitalServicePO claimHospitalServicePO);

     /**
     * @description 分页查询数据
     * @version
     * @title
     * <AUTHOR>
     * @param currentPage 当前页对象
     * @return CurrentPage<ClaimHospitalServicePO> 查询结果的当前页对象
     */
	 public CurrentPage<ClaimHospitalServicePO> queryClaimHospitalServiceForPage(ClaimHospitalServicePO claimHospitalServicePO, CurrentPage<ClaimHospitalServicePO> currentPage);

     /**
     * @description 批量增加数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimHospitalServicePOList 医院信息参数对象列表
     * @return boolean 批量添加是否成功
     */
	 public boolean batchSaveClaimHospitalService(List<ClaimHospitalServicePO> claimHospitalServicePOList);

     /**
     * @description 批量修改数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimHospitalServicePOList 医院信息参数对象列表
     * @return boolean 批量修改是否成功
     */
	 public boolean batchUpdateClaimHospitalService(List<ClaimHospitalServicePO> claimHospitalServicePOList);

     /**
     * @description 批量删除数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimHospitalServicePOList 医院信息参数对象列表
     * @return boolean 批量删除是否成功
     */
	 public boolean batchDeleteClaimHospitalService(List<ClaimHospitalServicePO> claimHospitalServicePOList);

     /**
     * @description 查询所有数据 ，重新组装为map
     * @version
     * @title
     * <AUTHOR>
     * @param claimHospitalServicePO 医院信息参数对象
     * @return List<Map<String, Object>> 查询结果存放到map中
     */
	 public List<Map<String, Object>> findAllMapClaimHospitalService(ClaimHospitalServicePO claimHospitalServicePO);
	 
	 /**
	  * @description 查询单条数据
	  * @version
	  * @title
	  * <AUTHOR> <EMAIL> 
	  * @param claimHospitalServicePO  医院信息参数
	  * @return 医院信息参数
	  */
	 public ClaimHospitalServicePO queryClaimHospitalService(ClaimHospitalServicePO claimHospitalServicePO);
	 
 }
 