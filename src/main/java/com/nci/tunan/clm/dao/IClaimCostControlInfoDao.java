package com.nci.tunan.clm.dao;

import com.nci.udmp.framework.model.*;
import java.util.Map;
import org.slf4j.Logger;
import java.util.List;
import com.nci.udmp.util.logging.LoggerFactory;
import com.nci.tunan.clm.interfaces.model.po.ClaimCostControlInfoPO;


/** 
 * @description IClaimCostControlInfoDao接口
 * <AUTHOR> 
 * @date 2024-12-06 11:06:00  
 */
 public interface IClaimCostControlInfoDao {
	 /**
     * @description 增加数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimCostControlInfoPO 对象
     * @return ClaimCostControlInfoPO 添加结果
     */
	 public ClaimCostControlInfoPO addClaimCostControlInfo(ClaimCostControlInfoPO claimCostControlInfoPO);
	 
     /**
     * @description 删除数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimCostControlInfoPO 对象
     * @return boolean 删除是否成功
     */
	 public boolean deleteClaimCostControlInfo(ClaimCostControlInfoPO claimCostControlInfoPO);
	 
     /**
     * @description 修改数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimCostControlInfoPO 对象
     * @return ClaimCostControlInfoPO 修改结果
     */
	 public ClaimCostControlInfoPO updateClaimCostControlInfo(ClaimCostControlInfoPO claimCostControlInfoPO);
	 
     /**
     * @description 查询单条数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimCostControlInfoPO 对象
     * @return ClaimCostControlInfoPO 查询结果对象
     */
	 public ClaimCostControlInfoPO findClaimCostControlInfo(ClaimCostControlInfoPO claimCostControlInfoPO);
	 
	 /**
      * @description 根据caseId查询数据
      * @version
      * @title
      * <AUTHOR>
      * @param claimCostControlInfoPO 对象
      * @return ClaimCostControlInfoPO 查询结果对象
      */
	 public ClaimCostControlInfoPO findClaimCostControlInfoByCaseId(ClaimCostControlInfoPO claimCostControlInfoPO);
	 
     /**
     * @description 查询所有数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimCostControlInfoPO 对象
     * @return List<ClaimCostControlInfoPO> 查询结果List
     */
	 public List<ClaimCostControlInfoPO> findAllClaimCostControlInfo(ClaimCostControlInfoPO claimCostControlInfoPO);
	 
     /**
     * @description 查询数据条数
     * @version
     * @title
     * <AUTHOR>
     * @param claimCostControlInfoPO 对象
     * @return int 查询结果条数
     */
	 public int findClaimCostControlInfoTotal(ClaimCostControlInfoPO claimCostControlInfoPO);

     /**
     * @description 分页查询数据
     * @version
     * @title
     * <AUTHOR>
     * @param currentPage 当前页对象
     * @return CurrentPage<ClaimCostControlInfoPO> 查询结果的当前页对象
     */
	 public CurrentPage<ClaimCostControlInfoPO> queryClaimCostControlInfoForPage(ClaimCostControlInfoPO claimCostControlInfoPO, CurrentPage<ClaimCostControlInfoPO> currentPage);

     /**
     * @description 批量增加数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimCostControlInfoPOList 对象列表
     * @return boolean 批量添加是否成功
     */
	 public boolean batchSaveClaimCostControlInfo(List<ClaimCostControlInfoPO> claimCostControlInfoPOList);

     /**
     * @description 批量修改数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimCostControlInfoPOList 对象列表
     * @return boolean 批量修改是否成功
     */
	 public boolean batchUpdateClaimCostControlInfo(List<ClaimCostControlInfoPO> claimCostControlInfoPOList);

     /**
     * @description 批量删除数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimCostControlInfoPOList 对象列表
     * @return boolean 批量删除是否成功
     */
	 public boolean batchDeleteClaimCostControlInfo(List<ClaimCostControlInfoPO> claimCostControlInfoPOList);

     /**
     * @description 查询所有数据 ，重新组装为map
     * @version
     * @title
     * <AUTHOR>
     * @param claimCostControlInfoPO 对象
     * @return List<Map<String, Object>> 查询结果存放到map中
     */
	 public List<Map<String, Object>> findAllMapClaimCostControlInfo(ClaimCostControlInfoPO claimCostControlInfoPO);
	 
 }
 