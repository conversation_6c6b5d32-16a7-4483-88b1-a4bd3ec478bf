package com.nci.tunan.clm.dao;

import java.util.Map;
import com.nci.udmp.framework.model.*;
import org.slf4j.Logger;

import java.util.List;

import com.nci.tunan.clm.interfaces.model.po.ClaimInsuredRelationPO;
import com.nci.udmp.util.logging.LoggerFactory;


/** 
 * @description IClaimInsuredRelationDao接口
 * <AUTHOR> 
 * @date 2020-08-27 15:00:49  
 */
 public interface IClaimInsuredRelationDao {
	 /**
     * @description 增加数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimInsuredRelationPO 对象
     * @return ClaimInsuredRelationPO 添加结果
     */
	 public ClaimInsuredRelationPO addClaimInsuredRelation(ClaimInsuredRelationPO claimInsuredRelationPO);
	 
     /**
     * @description 删除数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimInsuredRelationPO 对象
     * @return boolean 删除是否成功
     */
	 public boolean deleteClaimInsuredRelation(ClaimInsuredRelationPO claimInsuredRelationPO);
	 
     /**
     * @description 修改数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimInsuredRelationPO 对象
     * @return ClaimInsuredRelationPO 修改结果
     */
	 public ClaimInsuredRelationPO updateClaimInsuredRelation(ClaimInsuredRelationPO claimInsuredRelationPO);
	 
     /**
     * @description 查询单条数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimInsuredRelationPO 对象
     * @return ClaimInsuredRelationPO 查询结果对象
     */
	 public ClaimInsuredRelationPO findClaimInsuredRelation(ClaimInsuredRelationPO claimInsuredRelationPO);
	 
     /**
     * @description 查询所有数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimInsuredRelationPO 对象
     * @return List<ClaimInsuredRelationPO> 查询结果List
     */
	 public List<ClaimInsuredRelationPO> findAllClaimInsuredRelation(ClaimInsuredRelationPO claimInsuredRelationPO);
	 
     /**
     * @description 查询数据条数
     * @version
     * @title
     * <AUTHOR>
     * @param claimInsuredRelationPO 对象
     * @return int 查询结果条数
     */
	 public int findClaimInsuredRelationTotal(ClaimInsuredRelationPO claimInsuredRelationPO);

     /**
     * @description 分页查询数据
     * @version
     * @title
     * <AUTHOR>
     * @param currentPage 当前页对象
     * @return CurrentPage<ClaimInsuredRelationPO> 查询结果的当前页对象
     */
	 public CurrentPage<ClaimInsuredRelationPO> queryClaimInsuredRelationForPage(ClaimInsuredRelationPO claimInsuredRelationPO, CurrentPage<ClaimInsuredRelationPO> currentPage);

     /**
     * @description 批量增加数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimInsuredRelationPOList 对象列表
     * @return boolean 批量添加是否成功
     */
	 public boolean batchSaveClaimInsuredRelation(List<ClaimInsuredRelationPO> claimInsuredRelationPOList);

     /**
     * @description 批量修改数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimInsuredRelationPOList 对象列表
     * @return boolean 批量修改是否成功
     */
	 public boolean batchUpdateClaimInsuredRelation(List<ClaimInsuredRelationPO> claimInsuredRelationPOList);

     /**
     * @description 批量删除数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimInsuredRelationPOList 对象列表
     * @return boolean 批量删除是否成功
     */
	 public boolean batchDeleteClaimInsuredRelation(List<ClaimInsuredRelationPO> claimInsuredRelationPOList);

     /**
     * @description 查询所有数据 ，重新组装为map
     * @version
     * @title
     * <AUTHOR>
     * @param claimInsuredRelationPO 对象
     * @return List<Map<String, Object>> 查询结果存放到map中
     */
	 public List<Map<String, Object>> findAllMapClaimInsuredRelation(ClaimInsuredRelationPO claimInsuredRelationPO);
	 
 }
 