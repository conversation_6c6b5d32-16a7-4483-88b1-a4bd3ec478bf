package com.nci.tunan.clm.dao;

import java.util.List;
import java.util.Map;
import com.nci.tunan.clm.interfaces.model.po.ClaimInstalmentAuditPO;
import com.nci.udmp.framework.model.CurrentPage;

/**
 * @description 理赔分期给付审核表Dao
 * <AUTHOR> 
 * @.belongToModule CLM-理赔系统-理赔分期给付
 * @date 2015-05-15 下午4:38:10
 */
public interface IClaimInstalmentAuditDao {
    /**
     * @description 增加理赔分期给付数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimInstalmentAuditPO
     *            理赔分期给付审核对象
     * @return ClaimInstalmentAuditPO 添加结果
     */
    public ClaimInstalmentAuditPO addClaimInstalmentAudit(ClaimInstalmentAuditPO claimInstalmentAuditPO);

    /**
     * @description 删除理赔分期给付数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimInstalmentAuditPO
     *            理赔分期给付审核对象
     * @return boolean 删除是否成功
     */
    public boolean deleteClaimInstalmentAudit(ClaimInstalmentAuditPO claimInstalmentAuditPO);

    /**
     * @description 修改理赔分期给付数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimInstalmentAuditPO
     *            理赔分期给付审核对象
     * @return ClaimInstalmentAuditPO 修改结果
     */
    public ClaimInstalmentAuditPO updateClaimInstalmentAudit(ClaimInstalmentAuditPO claimInstalmentAuditPO);

    /**
     * @description 查询理赔分期给付单条数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimInstalmentAuditPO
     *            理赔分期给付审核对象
     * @return ClaimInstalmentAuditPO 查询结果对象
     */
    public ClaimInstalmentAuditPO findClaimInstalmentAudit(ClaimInstalmentAuditPO claimInstalmentAuditPO);

    /**
     * @description 查询所有理赔分期给付数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimInstalmentAuditPO
     *            理赔分期给付审核对象
     * @return List<ClaimInstalmentAuditPO> 查询结果List
     */
    public List<ClaimInstalmentAuditPO> findAllClaimInstalmentAudit(ClaimInstalmentAuditPO claimInstalmentAuditPO);

    /**
     * @description 查询理赔分期给付数据条数
     * @version
     * @title
     * <AUTHOR>
     * @param claimInstalmentAuditPO
     *            理赔分期给付审核对象
     * @return int 查询结果条数
     */
    public int findClaimInstalmentAuditTotal(ClaimInstalmentAuditPO claimInstalmentAuditPO);

    /**
     * @description 分页查询理赔分期给付数据
     * @version
     * @title
     * <AUTHOR>
     * @param currentPage
     *            当前页对象
     * @return CurrentPage<ClaimInstalmentAuditPO> 查询结果的当前页对象
     */
    public CurrentPage<ClaimInstalmentAuditPO> queryClaimInstalmentAuditForPage(
            ClaimInstalmentAuditPO claimInstalmentAuditPO, CurrentPage<ClaimInstalmentAuditPO> currentPage);

    /**
     * @description 批量增加理赔分期给付数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimInstalmentAuditPOList
     *            理赔分期给付审核对象列表
     * @return boolean 批量添加是否成功
     */
    public boolean batchSaveClaimInstalmentAudit(List<ClaimInstalmentAuditPO> claimInstalmentAuditPOList);

    /**
     * @description 批量修改理赔分期给付数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimInstalmentAuditPOList
     *            理赔分期给付审核对象列表
     * @return boolean 批量修改是否成功
     */
    public boolean batchUpdateClaimInstalmentAudit(List<ClaimInstalmentAuditPO> claimInstalmentAuditPOList);

    /**
     * @description 批量删除理赔分期给付数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimInstalmentAuditPOList
     *            理赔分期给付审核对象列表
     * @return boolean 批量删除是否成功
     */
    public boolean batchDeleteClaimInstalmentAudit(List<ClaimInstalmentAuditPO> claimInstalmentAuditPOList);

    /**
     * @description 查询所有理赔分期给付数据 ，重新组装为map
     * @version
     * @title
     * <AUTHOR>
     * @param claimInstalmentAuditPO
     *            理赔分期给付审核对象
     * @return List<Map<String, Object>> 查询结果存放到map中
     */
    public List<Map<String, Object>> findAllMapClaimInstalmentAudit(ClaimInstalmentAuditPO claimInstalmentAuditPO);

    /**
     * 根据分期给付应领ID查询给付结论表
     * @description
     * @version
     * @title
     * @<NAME_EMAIL>
     * @param claimInstalmentAuditPO 理赔分期给付审核对象
     * @return
     */
    public ClaimInstalmentAuditPO findClaimInstalmentAuditByInsId(ClaimInstalmentAuditPO claimInstalmentAuditPO);
    /**
     * 查询出险人状态
     * @description
     * @version
     * @title
     * @<NAME_EMAIL>
     * @param claimInstalmentAuditPO 理赔分期给付审核对象
     * @return
     */
    public List<ClaimInstalmentAuditPO> findInsuredState(ClaimInstalmentAuditPO claimInstalmentAuditPO);
}
