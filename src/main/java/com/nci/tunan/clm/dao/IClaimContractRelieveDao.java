package com.nci.tunan.clm.dao;

import com.nci.tunan.clm.interfaces.model.bo.ClaimDocumentNoticeBO;
import com.nci.tunan.clm.interfaces.model.po.ClaimContractRelievePO;
import com.nci.udmp.framework.model.*;

import java.util.Map;

import org.slf4j.Logger;

import java.util.List;

import com.nci.udmp.util.logging.LoggerFactory;

/**
 * 
 * @description IClaimContractRelieveDao合同解除通知书接口
 * <AUTHOR> 
 * @.belongToModule CLM-理赔系统
 * @date 2015-05-15 下午3:37:36
 */
 public interface IClaimContractRelieveDao {
	 /**
     * @description 增加数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimContractRelievePO 对象
     * @return ClaimContractRelievePO 添加结果
     */
	 public ClaimContractRelievePO addClaimContractRelieve(ClaimContractRelievePO claimContractRelievePO);
	 
     /**
     * @description 删除数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimContractRelievePO 合同解除通知书对象
     * @return boolean 删除是否成功
     */
	 public boolean deleteClaimContractRelieve(ClaimContractRelievePO claimContractRelievePO);
	 
     /**
     * @description 修改数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimContractRelievePO 合同解除通知书对象
     * @return ClaimContractRelievePO 修改结果
     */
	 public ClaimContractRelievePO updateClaimContractRelieve(ClaimContractRelievePO claimContractRelievePO);
	 
     /**
     * @description 查询单条数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimContractRelievePO 对象
     * @return ClaimContractRelievePO 查询结果对象
     */
	 public ClaimContractRelievePO findClaimContractRelieve(ClaimContractRelievePO claimContractRelievePO);
	 
     /**
     * @description 查询所有数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimContractRelievePO 合同解除通知书对象
     * @return List<ClaimContractRelievePO> 查询结果List
     */
	 public List<ClaimContractRelievePO> findAllClaimContractRelieve(ClaimContractRelievePO claimContractRelievePO);
	 
     /**
     * @description 查询数据条数
     * @version
     * @title
     * <AUTHOR>
     * @param claimContractRelievePO 合同解除通知书对象
     * @return int 查询结果条数
     */
	 public int findClaimContractRelieveTotal(ClaimContractRelievePO claimContractRelievePO);

     /**
     * @description 分页查询数据
     * @version
     * @title
     * <AUTHOR>
     * @param currentPage 当前页对象
     * @return CurrentPage<ClaimContractRelievePO> 查询结果的当前页对象
     */
	 public CurrentPage<ClaimContractRelievePO> queryClaimContractRelieveForPage(ClaimContractRelievePO claimContractRelievePO, CurrentPage<ClaimContractRelievePO> currentPage);

     /**
     * @description 批量增加数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimContractRelievePOList 合同解除通知书对象列表
     * @return boolean 批量添加是否成功
     */
	 public boolean batchSaveClaimContractRelieve(List<ClaimContractRelievePO> claimContractRelievePOList);

     /**
     * @description 批量修改数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimContractRelievePOList 合同解除通知书对象列表
     * @return boolean 批量修改是否成功
     */
	 public boolean batchUpdateClaimContractRelieve(List<ClaimContractRelievePO> claimContractRelievePOList);

     /**
     * @description 批量删除数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimContractRelievePOList 合同解除通知书对象列表
     * @return boolean 批量删除是否成功
     */
	 public boolean batchDeleteClaimContractRelieve(List<ClaimContractRelievePO> claimContractRelievePOList);

     /**
     * @description 查询所有数据 ，重新组装为map
     * @version
     * @title
     * <AUTHOR>
     * @param claimContractRelievePO 对象
     * @return List<Map<String, Object>> 查询结果存放到map中
     */
	 public List<Map<String, Object>> findAllMapClaimContractRelieve(ClaimContractRelievePO claimContractRelievePO);

	 /**
	  * 
	  * @description  判断是否预览
	  * @version V1.0.0
	  * @title
	  * <AUTHOR>
	  * @param claimContractRelievePO 合同解除通知书对象
	  * @return
	  */
	public ClaimContractRelievePO updateClaimContractRelieveRelieveFlag(
			ClaimContractRelievePO claimContractRelievePO);

	/**
 	 * 根据caseID删除
 	 * @description
 	 * @version V1.0.0
 	 * @title
 	 * <AUTHOR> <EMAIL>
 	 * @date 2015-05-15 下午6:49:44 
 	 * @param claimContractRelievePO 合同解除通知书对象
 	 * @return
 	 */
	public boolean deleteClaimContractRelieveByCaseId(
			ClaimContractRelievePO claimContractRelievePO);
	
	
	/**
     * @description 查询符合条件的解除通知书数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimContractRelievePO 合同解除通知书对象
     * @return List<ClaimContractRelievePO> 查询同解除通知书结果List
     */
	 public List<ClaimContractRelievePO> findAllClaimContracts(ClaimContractRelievePO claimContractRelievePO);
	 
 }
 