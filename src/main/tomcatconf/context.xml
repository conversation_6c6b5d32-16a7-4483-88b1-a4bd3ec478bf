<?xml version="1.0" encoding="UTF-8"?>
<Context>
	<!-- zongji -->

	<!-- <Resource name="jdbc/UdmpJndiDataSource" auth="Container"
		type="javax.sql.DataSource" driverClassName="oracle.jdbc.driver.OracleDriver"
		url="****************************************" username="APP___PAS__TEMPUSER"
		password="app___pas#1379 " maxActive="20" maxIdle="10" maxWait="10000" />
	<Resource name="jdbc/UdmpDataSource" auth="Container"
		type="javax.sql.DataSource" driverClassName="oracle.jdbc.driver.OracleDriver"
		url="****************************************" username="APP___PAS__TEMPUSER"
		password="app___pas#1379 " maxActive="20" maxIdle="10" maxWait="10000" />
	<Resource name="jdbc/UdmpCommonDataSource" auth="Container"
		type="javax.sql.DataSource" driverClassName="oracle.jdbc.driver.OracleDriver"
		url="****************************************" username="APP___PAS__TEMPUSER"
		password="app___pas#1379 " maxActive="20" maxIdle="10" maxWait="10000" /> -->


	<!-- 总集成开发环境 -->
	<!--  <Resource name="jdbc/UdmpJndiDataSource" auth="Container"
        type="javax.sql.DataSource" driverClassName="oracle.jdbc.driver.OracleDriver"
        url="***************************************" username="APP___PAS__DBUSER"
        password="PAS#123" maxActive="20" maxIdle="10" maxWait="10000" />
    <Resource name="jdbc/UdmpCommonDataSource" auth="Container"
        type="javax.sql.DataSource" driverClassName="oracle.jdbc.driver.OracleDriver"
        url="***************************************" username="APP___PAS__DBUSER"
        password="PAS#123" maxActive="20" maxIdle="10" maxWait="10000" />
    <Resource name="jdbc/UdmpDataSource" auth="Container"
        type="javax.sql.DataSource" driverClassName="oracle.jdbc.driver.OracleDriver"
        url="***************************************" username="APP___PAS__DBUSER"
        password="PAS#123" maxActive="20" maxIdle="10" maxWait="10000" />     -->
	<!--总集环境  -->
	<Resource name="jdbc/UdmpJndiDataSource" auth="Container"
			  type="javax.sql.DataSource" driverClassName="oracle.jdbc.driver.OracleDriver"
			  url="**********************************" username="APP___PAS__DBUSER"
			  password="PAS_NAKDFQ#new" maxActive="20" maxIdle="10" maxWait="10000" />
	<Resource name="jdbc/UdmpCommonDataSource" auth="Container"
			  type="javax.sql.DataSource" driverClassName="oracle.jdbc.driver.OracleDriver"
			  url="**********************************" username="APP___PAS__DBUSER"
			  password="PAS_NAKDFQ#new" maxActive="20" maxIdle="10" maxWait="10000" />
	<Resource name="jdbc/UdmpDataSource" auth="Container"
			  type="javax.sql.DataSource" driverClassName="oracle.jdbc.driver.OracleDriver"
			  url="**********************************" username="APP___PAS__DBUSER"
			  password="PAS_NAKDFQ#new" maxActive="20" maxIdle="10" maxWait="10000" />

	<!-- 并行 -->
	<!-- <Resource name="jdbc/UdmpJndiDataSource" auth="Container"
		type="javax.sql.DataSource" driverClassName="oracle.jdbc.driver.OracleDriver"
		url="*************************************" username="APP___PAS__DBUSER"
		password="APP___PAS__dnw#1234" maxActive="20" maxIdle="10" maxWait="10000" />
	<Resource name="jdbc/UdmpCommonDataSource" auth="Container"
		type="javax.sql.DataSource" driverClassName="oracle.jdbc.driver.OracleDriver"
		url="*************************************" username="APP___PAS__DBUSER"
		password="APP___PAS__dnw#1234" maxActive="20" maxIdle="10" maxWait="10000" />
	<Resource name="jdbc/UdmpDataSource" auth="Container"
		type="javax.sql.DataSource" driverClassName="oracle.jdbc.driver.OracleDriver"
		url="*************************************" username="APP___PAS__DBUSER"
		password="APP___PAS__dnw#1234" maxActive="20" maxIdle="10" maxWait="10000" />
	<Resource name="jdbc/BankDataSource" auth="Container"
		type="javax.sql.DataSource" driverClassName="oracle.jdbc.driver.OracleDriver"
		url="*************************************" username="APP___PAS__DBUSER"
		password="APP___PAS__dnw#1234" maxActive="20" maxIdle="10" maxWait="10000" /> -->

	<!--
	<Resource name="jdbc/UdmpJndiDataSource" auth="Container"
		type="javax.sql.DataSource" driverClassName="oracle.jdbc.driver.OracleDriver"
		url="******************************************" username="APP___PAS__DBUSER"
		password="PAS_PSWD123" maxActive="20" maxIdle="10" maxWait="10000" />
	<Resource name="jdbc/UdmpCommonDataSource" auth="Container"
		type="javax.sql.DataSource" driverClassName="oracle.jdbc.driver.OracleDriver"
		url="******************************************" username="APP___PAS__DBUSER"
		password="PAS_PSWD123" maxActive="20" maxIdle="10" maxWait="10000" />
	<Resource name="jdbc/UdmpDataSource" auth="Container"
		type="javax.sql.DataSource" driverClassName="oracle.jdbc.driver.OracleDriver"
		url="******************************************" username="APP___PAS__DBUSER"
		password="PAS_PSWD123" maxActive="20" maxIdle="10" maxWait="10000" />
 -->
	<!-- 多数据源-接入渠道数据库 -->
	<!-- 	<Resource maxWait="10000" maxIdle="10" maxActive="20" -->
	<!-- 		password="jrqd" username="jrqd" url="*******************************************" -->
	<!-- 		driverClassName="oracle.jdbc.driver.OracleDriver" type="javax.sql.DataSource" -->
	<!-- 		auth="Container" name="jdbc/BankDataSource" /> -->

	<!-- 	<Resource name="jdbc/BankDataSource" auth="Container" -->
	<!-- 		type="javax.sql.DataSource" driverClassName="oracle.jdbc.driver.OracleDriver" -->
	<!-- 		url="***************************************" username="APP___PAS__DBUSER" -->
	<!-- 		password="PAS#123" maxActive="20" maxIdle="10" maxWait="10000" /> -->
	<Resource name="jdbc/BankDataSource" auth="Container"
			  type="javax.sql.DataSource" driverClassName="oracle.jdbc.driver.OracleDriver"
			  url="**********************************" username="APP___PAS__DBUSER"
			  password="PAS_NAKDFQ#new" maxActive="20" maxIdle="10" maxWait="10000" />

	<Resource name="jdbc/NBSJndiDataSource" auth="Container"
			  type="javax.sql.DataSource" driverClassName="oracle.jdbc.driver.OracleDriver"
			  url="jdbc:oracle:thin:@**********/sdyzjnbsdb" username="APP___NB__DBUSER"
			  password="NB_PSWD123" maxActive="20" maxIdle="10" maxWait="10000" />

	<!-- 多数据源-老核心数据库 -->
	<!-- <Resource name="jdbc/OldDataSource" auth="Container"
			type="javax.sql.DataSource" driverClassName="oracle.jdbc.driver.OracleDriver"
			url="**********************************" username="ncl_1"
			password="ncl_1" maxActive="20" maxIdle="10" maxWait="10000" /> -->
</Context>
