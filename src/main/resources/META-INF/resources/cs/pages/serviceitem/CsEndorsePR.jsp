<%@ page language="java" contentType="text/html; charset=utf-8"
	pageEncoding="utf-8"%>
<%@ taglib uri="http://www.git.com.cn/taglib/udmp/field" prefix="Field"%>
<%@ taglib uri="/struts-tags" prefix="s"%><%@include file="/udmpCommon.jsp"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<%-- <s:set var="ctx">${pageContext.request.contextPath}</s:set> --%>
<%@ include file="/cs/pages/common/jsp/common_ctx.jsp"%>
<script type="text/javascript" src="${ctx}/cs/js/cs_common.js"></script>
<link rel="stylesheet" href="${ctx }/cs/css/cs_common.css" type="text/css">
<link rel="stylesheet" href="${ctx }/cs/css/public.css" type="text/css">
<script type="text/javascript" src="${ctx}/cs/js/DomZoomZX.js"></script>
<script type = "text/javascript" src="${ctx}/cs/js/cs_ifSamePerson.js"></script>
<script>
	$(document).ready(function(){
        var D = DomZoomZX('input,select,textarea,td')
	})
</script>
<script type="text/javascript">
	$(document).ready(function() {
		_cs_initMultipleBox();
		$("#updateEndInfo1", navTab.getCurrentPanel()).hide();
	});
</script>
<!-- 帮助菜单 -->
<script src="${ctx }/udmp/plugins/ribbon/jquery.asyncorgtree.js"
	type="text/javascript"></script>
<script type="text/javascript">
	//帮助部分动作
	function MM_changeProp(objId, theProp, theValue) {
		var obj = null;
		with (document) {
			if (getElementById) {
				obj = getElementById(objId);
			}
		}
		if (obj) {
			if (theValue == true || theValue == false) {
				eval("obj.style." + theProp + "=" + theValue);
			} else {
				eval("obj.style." + theProp + "='" + theValue + "'");
			}
		}
	};
</script>
<style type="text/css">
th,td {
	white-space: normal;
}

.searchBar li label {
	width: 90px;
}
</style>
<!-- ********帮助菜单***********begin********* -->
<s:include value="/cs/pages/common/jsp/helpMenu.jsp"></s:include>
<!-- *********帮助菜单***********end********* -->
 <s:include value="csEndorseProgress.jsp" />



<div class="backgroundCollor"  layoutH="140">

		<div class="tabsContent">
			<!--第一个页签-->
			<div class="pageContent">
		         <s:include value="customerInfo_list.jsp" />
						<input name="queryFlag" id="queryFlag" value="${queryFlag}"
							type="hidden" />
                       <div class="divfclass">
								<h1>
							    <img src="images/tubiao.png" >变更前信息 
								</h1>
						</div>
						<div class="tabdivclass">
								<table id="basicRemarkTable" class="list" style="width: 100%">
									<thead>
										<tr>
											<th>保单号</th>
											<th>管理机构</th>
											<th>险种代码</th>
											<th>险种名称</th>
											<th>保额/份数</th>
											<th>保费</th>
											<th>生效日期</th>
											<th>下次缴费日</th>
										</tr>
									</thead>
									<tbody>
										<s:iterator value="groupPRVOs" status="st" var="var">
											<tr>

												<td><s:property value="policyCode" /></td>
												<input id="policyCode" type="hidden"  value="${policyCode}"/>
												<td><s:property value="organCode" /></td>
												<td><s:property value="busiProdCode" /></td>
												<td><Field:codeValue tableName="APP___PAS__DBUSER.T_BUSINESS_PRODUCT"
														value="${busiPrdId}" /></td>
												<td><s:property value="basicAmount" /></td>
												<td><s:property value="totalPremAf" /></td>
												<td><s:date format="yyyy-MM-dd" name="validateDate"></s:date></td>
												<td><s:date format="yyyy-MM-dd" name="payDueDate"></s:date></td>
												<%-- <td><s:property value="validateDate" /></td>
												<td><s:property value="payDueDate" /></td>
 --%>
												
											</tr>
										</s:iterator>
									</tbody>
								</table>
							</div>

						<!-- 机构变更 -->
						<!-- onsubmit="return divSearch(this,'updateEndInfo')" -->
						<form method="post"
							action="${ctx }/cs/serviceitem_pr/save_PA_csEndorsePRAction.action"
							class="required-validate"
							onsubmit="return validateCallback(this)" id="csEndorsePRAction">
							<input id="customerId" type="hidden" name="customerId" value="${customerId}"/> 
							<input id="acceptId" type="hidden" name="acceptId" value="${acceptId}"/> 
							<input id="changeId" type="hidden" name="changeId" value="${changeId}"/> 
							<input id="busiPrdId" type="hidden" name="busiPrdId" value="${busiPrdId}"/> 
							<input name="organCode" type="hidden" value="${groupPRVO.organCode}"/>
							<input name="organName" type="hidden" value="${groupPRVO.organName}"/>
                             <div class="divfclass">
						 	    <h1>
								<img src="images/tubiao.png" >机构变更
								</h1>
							 </div>
							<div class="pageFormInfoContent">
								
							      	<dl>
										<dt>迁至管理机构1</dt>
										<dd ${queryFlag==1?"disabled":"" } style="width: 240px">
											<div>
												<input id="organCodeAfter" style="width: 30px;border-right:0px"
												   onblur="checckOrgancode()" onfocus="unChecckOrgancode()"
													name="groupPRVO.organCode"  
													value="${oragnCode}" type="text"  clickId="menuBtn"  needAll="true"
													class="organ"  showOrgName="autoAddValue2" />
												<input id="autoAddValue2" class="public_textInput"
													name="groupPRVO.organName" style="width:110px;"
													value="${oragnName}" type="text" 
													readOnly /> <a id="menuBtn" class="btnLook" href="#"></a>
											</div>
										</dd>
									</dl>
							
									<div class="pageFormdiv" id="saveMesId">
									 	<button type="button" class="but_blue" onclick="checkMseeage()">保存</button>
								    </div>
							</div>
						</form>
						<div  id="updateEndInfo">
							<s:include value="CsEndorsePREnd.jsp"></s:include>
						</div>
						<div  id="updateEndInfo1">
							<s:include value="CsEndorsePREnd.jsp"></s:include>
						</div>
						<s:if test="isOrganCodeFlag == 1">
								<div>
									<s:include value="/cs/pages/common/jsp/SinceMutualnsurance.jsp"></s:include>
								</div>
						</s:if>
								
				</div>
			</div>
</div>


				<s:include value="/cs/pages/serviceitem/serviceItemBottomBar.jsp"></s:include>


<!-- js代码  -->
<script type="text/javascript">


	function checckOrgancode() {
		//var data = [ {} ];
		var organCodeQuery = $("#organCodeAfter").val();
		$
				.ajax({
					type : "post",
					url : "${ctx}/queryAllOrganTree_organTreeAction.action?1=1&organCodeQuery="
							+ organCodeQuery,
					async : false,
					dataType : "json",
					success : function(data) {
						if(data != null && organCodeQuery != "" ){
							if (data[0] != null) {
								$("#autoAddValue2").val(data[0].organName);
							}
						}
					}
				});

	}

	function unChecckOrgancode() {
		$("#autoAddValue2").val("");
	}

	function busiNameValue() {
		$("#basicRemarkTable").find("tr").each(
				function(i) {
					if (i == 0) {
					} else {
						$("#basicRemarkTable1").find("tr").eq(i).find("td").eq(
								3).text($(this).find("td").eq(3).text());
					}
				});
	}

	function checkMseeage() {
		var changeId = $("#changeId").val();
		var acceptId = $("#acceptId").val();
		var json = "";
		$
				.ajax({
					url : "${ctx}/cs/serviceitem_ag/checkLifeFlag_PA_csEndorsePRAction.action",
					type : "post",
					dataType : 'text',
					data : "changeId=" + changeId + "&acceptId=" + acceptId,
					cache : false,
					success : function(data) {
						if (data.indexOf("{") == 0) {
							json = jQuery.parseJSON(data);
							if (json.statusCode != 200) {
								if (json.message == undefined
										|| json.message == '') {
									checkPolicy();
								} else {
									alertMsg.confirm(json.message + " 是否继续？", {
										okCall : function() {
											checkPolicy();
										}
									});
								}
							}
						} else {
							checkPolicy();
						}
					}
				});

	}

	function checkPolicy() {
		var policyCode = $("#policyCode").val();
		var changeId = $("#changeId").val();
		var json = "";
		$
				.ajax({
					url : "${ctx}/cs/serviceitem_ag/checkPolicy_PA_csEndorsePRAction.action",
					type : "post",
					dataType : 'text',
					data : "changeId=" + changeId + "&policyCode=" + policyCode,
					cache : false,
					success : function(data) {
						if (data.indexOf("{") == 0) {
							json = jQuery.parseJSON(data);
							if (json.statusCode != 200) {
								if (json.message == undefined
										|| json.message == '') {
									save('csEndorsePRAction');
								} else {
									alertMsg.confirm(json.message + " 是否继续？", {
										okCall : function() {
											save('csEndorsePRAction');
										}
									});
								}
							}
						} else {
							save('csEndorsePRAction');
						}
					}
				});
	}

	function save(formId, divId) {
		var $form = $("#" + formId, navTab.getCurrentPanel());
		var organCodeAfter = $("#organCodeAfter").val();
		
		var acceptId = $("#acceptId").val();
		
		if ($("#organCodeAfter").val() == '') {
			alertMsg.info("请选择要变更的管理机构");
			return false;
		} else if ($("#autoAddValue2").val() == '') {
			alertMsg.info("机构代码不存在对应管理机构");
			return false;
		} else {

			$
					.ajax({
						type : 'POST',
						url : "${ctx}/cs/serviceitem_pr/PlanFlag_PA_csEndorsePRAction.action",
						data : $form.serializeArray(),
						cache : false,
						success : function(data) {
							if (data.indexOf("{") == 0) {
								var json = DWZ.jsonEval(data);
								if (json.statusCode == 400) {// 状态码为400 说明当前选择的保单管理机构则不能办理保单迁移  阻断
									alertMsg.error(json.message);
									return false;
								} else if (json.statusCode == 301) {
									alertMsg
											.confirm(
													json.message,
													{
														okCall : function() {

															alertMsg
																	.confirm(
																			"请确认是否需要保存录入信息",
																			{
																				okCall : function() {
																					//$("#" + divId, navTab.getCurrentPanel()).show();
																					//$form.submit();

																					if ($(
																							"#tex")
																							.val() == ''
																							|| $(
																									"#tex1")
																									.val() == ''
																							|| $(
																									"#tex2")
																									.val() == ''
																							|| $(
																									"#tex3")
																									.val() == ''
																							|| $(
																									"#tex4")
																									.val() == ''
																							|| $(
																									"#tex5")
																									.val() == ''
																							|| $(
																									"#tex6")
																									.val() == ''
																							|| $(
																									"#tex7")
																									.val() == '') {
																						alertMsg
																								.confirm("地址，邮编，电话可能有未录入的信息！");
																					}

																					$
																							.ajax({
																								type : 'POST',
																								url : $form
																										.attr("action"),
																								data : $form
																										.serializeArray(),
																								cache : false,
																								success : function(
																										response) {
																									var json = DWZ
																											.jsonEval(response);
																									if (json.statusCode == DWZ.statusCode.error) {
																										alertMsg
																												.error(json.message);
																									} else if (json.statusCode == DWZ.statusCode.timeout) {
																										DWZ
																												.loadLogin();
																									} else if (json.statusCode == "200") {
																										alertMsg
																												.confirm(
																														"变更保单管理机构后，保费将发生变化请注意！",
																														{
																															okCall : function() {
																																$
																																		.ajax({
																																			type : 'POST',
																																			url : "${ctx}/cs/serviceitem_pr/savea_PA_csEndorsePRAction.action",
																																			data : $form
																																					.serializeArray(),
																																			cache : false,
																																			success : function(
																																					response) {
																																				var json = DWZ
																																						.jsonEval(response);
																																				if (json.statusCode == DWZ.statusCode.error) {
																																					alertMsg
																																							.error(json.message);
																																				} else if (json.statusCode == DWZ.statusCode.timeout) {
																																					DWZ
																																							.loadLogin();
																																				} else {
																																					var $updateEndInfoBox = $(
																																							"#updateEndInfo",
																																							navTab
																																									.getCurrentPanel());
																																					$updateEndInfoBox
																																							.html(
																																									response)
																																							.initUI();
																																					alertMsg
																																							.info("保存成功!");
																																					if(organCodeAfter.substring(0,4)=='8621'){
																																						checkPreviousNaturalYear(acceptId);
																																						
																																					}
																																					busiNameValue();
																																				}
																																			}
																																		});
																															},
																															cancleCall : function() {
																															}
																														});
																									} else {
																										var $updateEndInfoBox = $(
																												"#updateEndInfo",
																												navTab
																														.getCurrentPanel());
																										$updateEndInfoBox
																												.html(
																														response)
																												.initUI();
																										alertMsg
																												.info("保存成功!");
																										if(organCodeAfter.substring(0,4)=='8621'){
																											checkPreviousNaturalYear(acceptId);
																											
																										}
																										busiNameValue();
																									}
																								},
																								error : DWZ.ajaxError
																							});
																				},
																				cancleCall : function() {
																				}
																			});
														},
														cancleCall : function() {
														}
													});
								} else {
									saveForm(formId);
									
								}
							}
						}
					});

		}

	}
	/* function reusltAjaxDone(json) {
		DWZ.ajaxDone(json);
		if (json.statusCode == DWZ.statusCode.ok) {
			$("#updateEndInfo", navTab.getCurrentPanel()).show();
		} else {
			$("#updateEndInfo", navTab.getCurrentPanel()).hide();
		}
	}
	 */
	//下一步
	function saveForm(formId) {
		var $form = $("#" + formId, navTab.getCurrentPanel());
		var acceptId = $("#acceptId").val();
		var organCodeAfter = $("#organCodeAfter").val();
		alertMsg
				.confirm(
						"请确认是否需要保存录入信息",
						{
							okCall : function() {
								//$("#" + divId, navTab.getCurrentPanel()).show();
								//$form.submit();

								if ($("#tex").val() == ''
										|| $("#tex1").val() == ''
										|| $("#tex2").val() == ''
										|| $("#tex3").val() == ''
										|| $("#tex4").val() == ''
										|| $("#tex5").val() == ''
										|| $("#tex6").val() == ''
										|| $("#tex7").val() == '') {
									alertMsg.confirm("地址，邮编，电话可能有未录入的信息！");
								}

								$
										.ajax({
											type : 'POST',
											url : $form.attr("action"),
											data : $form.serializeArray(),
											cache : false,
											success : function(response) {
												var json = DWZ
														.jsonEval(response);
												if (json.statusCode == DWZ.statusCode.error) {
													alertMsg
															.error(json.message);
												} else if (json.statusCode == DWZ.statusCode.timeout) {
													DWZ.loadLogin();
												} else if (json.statusCode == "200") {
													alertMsg
															.confirm(
																	"变更保单管理机构后，保费将发生变化请注意！",
																	{
																		okCall : function() {
																			$
																					.ajax({
																						type : 'POST',
																						url : "${ctx}/cs/serviceitem_pr/savea_PA_csEndorsePRAction.action",
																						data : $form
																								.serializeArray(),
																						cache : false,
																						success : function(
																								response) {
																							var json = DWZ
																									.jsonEval(response);
																							if (json.statusCode == DWZ.statusCode.error) {
																								alertMsg
																										.error(json.message);
																							} else if (json.statusCode == DWZ.statusCode.timeout) {
																								DWZ
																										.loadLogin();
																							} else {
																								var $updateEndInfoBox = $(
																										"#updateEndInfo",
																										navTab
																												.getCurrentPanel());
																								$updateEndInfoBox
																										.html(
																												response)
																										.initUI();
																								alertMsg
																										.info("保存成功!");
																								busiNameValue();
																								if(organCodeAfter.substring(0,4)=='8621'){
																									checkPreviousNaturalYear(acceptId);
																									
																								}
																							}
																						}
																					});
																		},
																		cancleCall : function() {
																		}
																	});
												} else {
													var $updateEndInfoBox = $(
															"#updateEndInfo",
															navTab
																	.getCurrentPanel());
													$updateEndInfoBox.html(
															response).initUI();
													alertMsg.info("保存成功!");
													busiNameValue();
													if(organCodeAfter.substring(0,4)=='8621'){
														checkPreviousNaturalYear(acceptId);
														
													}
												}
											},
											error : DWZ.ajaxError
										});
							},
							cancleCall : function() {
							}
						});
	}
	function next() {
		var val1 = $("#changeId").val();
		var val2 = $("#acceptId").val();
		var val3 = $("#customerId").val();
		var title = "受理信息录入";
		var tabid = "_aplPermit_M";
		var fresh = eval("true");
		var external = eval("false");
		var url = "${ctx }/cs/csEntry/showAcceptAndArapInfo_PA_csEntryAction.action?&menuId=${menuId}&changeId="
				+ val1 + "&acceptId=" + val2 + "&customerId=" + val3;
		navTab.openTab(tabid, url, {
			title : title,
			fresh : fresh,
			external : external
		});

	}
	//页面加载的时候判断 queryFlag 如果是1则隐藏按钮	
	$(document).ready(function() {
		var queryFlag = $("#queryFlag", navTab.getCurrentPanel()).val();
		if ('1' == queryFlag) {
			//保存按钮隐藏
			$("#saveMesId", navTab.getCurrentPanel()).hide();
			$("#step_header", navTab.getCurrentPanel()).hide();
			$("#formBar", navTab.getCurrentPanel()).hide();
		}
	});
</script>