<!-- 保单解冻页面 -->
<%@ page contentType="text/html; charset=utf-8"%>
<%@ taglib uri="/struts-tags" prefix="s"%><%@include file="/udmpCommon.jsp"%>
<%@ taglib uri="http://www.git.com.cn/taglib/udmp/field" prefix="Field"%>
<%@ include file="/cs/pages/common/jsp/common_ctx.jsp"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/fmt" prefix="fmt"%>
<script type="text/javascript" src="${ctx}/cs/js/cs_common.js"></script>
<link rel="stylesheet" href="${ctx}/cs/css/cs_common.css" type="text/css">
<link rel="stylesheet" href="${ctx}/cs/css/public.css" type="text/css">
<script type="text/javascript" src="${ctx}/cs/js/DomZoomZX.js"></script>
<script>
	$(document).ready(function(){
        var D = DomZoomZX('input,select,textarea,td')
	})
</script>
<style type="text/css">
/* th,td { */
/* 	white-space: normal; */
/* } */

/* .searchBar li label { */
/* 	width: 60px; */
/* } */
</style>
<!-- ********帮助菜单***********begin********* -->
<s:include value="/cs/pages/common/jsp/helpMenu.jsp"></s:include>
<!-- *********帮助菜单***********end********* -->

<s:if test="queryFlag!=1">
	<s:include value="csEndorseProgress.jsp"></s:include>
</s:if>

<div >

		<div  layoutH="110">
			
			<div class="backgroundCollor">
				<input type="hidden" id="customerId" name="customerId" value="${customerId}"/>
				<input type="hidden" id="applyCode" name="applyCode" value="${applyCode}"/>
				<input type="hidden" id="policyCode" name="policyCode" value="${fzFreezePolicyVOs[0].csContractMasterVO.policyCode}"/>
				<input name="queryFlag" id="queryFlag" value="${queryFlag}" type="hidden"/>
				<div class="divfclass">
					<h1>
						<img src="cs/img/icon/tubiao.png">保单解冻
					</h1>
				</div>
				<div class="backgroundCollor">
				<s:include value="customerInfo_list.jsp" />
				
					<!--  显示查询到的需要冻结的保单 -->
				<div class="pageContent">
						<div class="divfclass">
							<h1>
								<img src="cs/img/icon/tubiao.png">保单信息
							</h1>
						</div>
						<div class="tabdivclass">
							<table class="list" width="100%">
								<thead>
									<tr>
										<th>序号</th>
										<th>保单号</th>
										<th>投保人</th>
										<th>被保人</th>
										<th>生效日期</th>
										<th>保险起期</th>
										<th>保险止期</th>
										<th>冻结起期</th>
										<th>冻结止期</th>
										
									</tr>
								</thead>
								<tbody align="center">
									<s:iterator value="policyInformationVOs" status="st" id="qr">
										<tr>
											<td>${st.index+1}</td>
											<td>${policyCode}</td>
											<td>${HName}</td>
											<td>${LName}</td>
											<td><fmt:formatDate value="${validateDate}" pattern="yyyy-MM-dd" /></td>
											<td><fmt:formatDate value="${applyDate}" pattern="yyyy-MM-dd" /></td>
											<td><fmt:formatDate value="${expiryDate}" pattern="yyyy-MM-dd" /></td>
											<td><fmt:formatDate value="${freezeDate}" pattern="yyyy-MM-dd" /></td>
											<td><fmt:formatDate value="${freezeEndDate}" pattern="yyyy-MM-dd" /></td>												
										</tr>
									</s:iterator>
								</tbody>

							</table>
						</div>
					</div>
				</div>
				
				<div class="backgroundCollor" >
						<div class="divfclass">
							<h1>
								<img src="cs/img/icon/tubiao.png">解冻信息
							</h1>
						</div>
				<div class="panelPageFormContent">
					<form method="post"
						action="${ctx}/cs/serviceitem_cf/updateUnfreezePolicyInfo_PA_csEndorseCFAction.action"
						class="required-validate"
						onsubmit="return validateCallback(this);"
						id="saveUnFreezeInfoForm">

						<dl>
							<dt>解冻日期</dt>
							<dd>
								<input class="required" id="unfreezeDate"
									name="cfFreezePolicyResultInfoVO.unfreezeDate"
									type="expandDateYMD"
									value="<s:date format="yyyy-MM-dd" name="cfFreezePolicyResultInfoVO.unfreezeDate"></s:date>" />
									<a id="date_taskDateBegin" class="inputDateButton" href="javascript:;">选择</a>
							</dd>
						</dl>
						<dl>
							<dt><font>*</font>解冻原因</dt>
							<dd>
								<Field:codeTable cssClass="combox" nullOption="true"
									name="cfFreezePolicyResultInfoVO.unfreezeCause"
									id="unfreezeCause"
									value="${cfFreezePolicyResultInfoVO.unfreezeCause}"
									tableName="APP___PAS__DBUSER.T_UNFREEZE_CAUSE" />
							</dd>
						</dl>
						<div class="tabdivclass">
							<table style="display: none" id="updateUnFreezedTable" table_saveStatus="1">
								<thead>
									<tr>
										<th colName="policyCode">保单号</th>
									</tr>
								</thead>
								<tbody>
									<s:iterator value="policyInformationVOs" status="status">
										<tr align="center" id="showTr" tr_saveStatus="1">
											<td>${policyCode}</td>
										</tr>
									</s:iterator>
								</tbody>

							</table>
							<input type="hidden" id="jsons" value="jsons">
							<div class="pageFormdiv" id="saveMesId">
								<button type="button" class="but_blue"
									onclick="updateUnfreezePolicyInfo('saveUnFreezeInfoForm')">保存</button>
							</div>
						</div>
					</form>
				</div>
			</div>
				
				
				<s:include value="/cs/pages/serviceitem/serviceItemBottomBar.jsp"></s:include>
			</div>
<!-- 			<div class="tabsFooter"> -->
<!-- 				<div class="tabsFooterContent"></div> -->
<!-- 			</div> -->
		</div>
	</div>
<script type="text/javascript">
/* 	function updateUnfreezePolicyInfo()
{
	 var $form = $("#saveUnFreezeInfoForm");
	$form.submit();	 
	
} */
// &"unfreezeCause"= "{jsonString:'"+ _jsons + "',jsonUnfreezeCause:'"+unfreezeCause.val()+"',jsonUnfreezeDate:'"+unfreezeDate+"'}"
             //'jsonString='+_json+ '&jsonUnfreezeCause:'+unfreezeCause.val()+'&jsonUnfreezeDate:'+unfreezeDate
//"jsonString="+_json+"&jsonUnfreezeCause="+unfreezeCause.val()+"&jsonUnfreezeDate="+unfreezeDate

function updateUnfreezePolicyInfo(formId){
	
	 //获取解冻原因
	var unfreezeCause=$("#unfreezeCause option:selected").val();
	//获取解冻日期的值
	var unfreezeDate=$('#unfreezeDate').val();
	if(unfreezeCause==""){
		alertMsg.error("必填项信息未完整录入，不能受理保单解冻，请确认。");
	}else if(unfreezeDate.length==0){
		alertMsg.error("必填项信息未完整录入，不能受理保单解冻，请确认。");
	}else{
	alertMsg.confirm("请确认是否需要保存解冻信息", {
		okCall : function() { 
		
				var _jsons="";
				var $table = $("#updateUnFreezedTable",navTab.getCurrentPanel());		
				_jsons+= _cs_tableToJson($table); 
				$("#jsons").val(_jsons);
				var acceptId= ${acceptId } ;
				var changeId= ${changeId } ;
				var  rootPath= getRootPath();
				$.ajax({	
					type:"post",
					url:rootPath+'/cs/serviceitem_cf/updateUnfreezePolicyInfo_PA_csEndorseCFAction.action',
					cache:false,
					data:"jsonString="+$("#jsons").val()+"&jsonUnfreezeCause="+unfreezeCause+"&jsonUnfreezeDate="+unfreezeDate+"&changeId="+changeId+"&acceptId="+acceptId,
					dataType:"text",
					success:function(data){
							if(trim(data)=="1"){
							alertMsg.correct("保单解冻保存成功!");
						}
						else
						if(trim(data)=="0")
						{
							alertMsg.error("保单没有被冻结，请确认。!");
						}
						else{
							alertMsg.error("必填项信息未完整录入，不能受理保单冻结保全项，请确认。");
						}
						
						//$("#saveUnFreezeInfoForm",navTab.getCurrentPanel()).submit();
						
					},
					
					error:function(){alertMsg.error("必填项信息未完整录入，不能受理保单冻结保全项，请确认。");}
				});	
			},
	/* 	}, */
		cancleCall:function(){
			
		}
	});
	}

};

//下一步
function next() {
	var val1 =document.getElementById("customerId").value;
	var val2 =document.getElementById("acceptId").value;
	var val3 =document.getElementById("changeId").value;
	var title = "受理信息录入";
	var tabid = "_aplPermit_M";
	var fresh = eval("true");
	var external = eval("false");
	var url = "${ctx}/cs/csEntry/showAcceptAndArapInfo_PA_csEntryAction.action?&menuId=${menuId}&changeId="
			+ val3 + "&acceptId=" + val2 + "&customerId=" + val1;
	navTab.openTab(tabid, url, {
		title : title,
		fresh : fresh,
		external : external
	});

}

//帮助部分动作
function MM_changeProp(objId, theProp, theValue) {
	var obj = null;
	with (document) {
		if (getElementById) {
			obj = getElementById(objId);
		}
	}
	if (obj) {
		if (theValue == true || theValue == false) {
			eval("obj.style." + theProp + "=" + theValue);
		} else {
			eval("obj.style." + theProp + "='" + theValue + "'");
		}
	}
};


//页面加载的时候判断 queryFlag 如果是1则隐藏按钮	
$(document).ready(function(){
	var queryFlag = $("#queryFlag").val();
	  if('1'==queryFlag){
		//保存按钮隐藏
		$("#saveMesId", navTab.getCurrentPanel()).hide();
		$("#step_header",navTab.getCurrentPanel()).hide();
		$("#formBar",navTab.getCurrentPanel()).hide();
	}  
}); 
</script>