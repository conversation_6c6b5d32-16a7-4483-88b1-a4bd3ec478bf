<%@ page language="java" contentType="text/html; charset=utf-8"
	pageEncoding="utf-8" import="java.util.*,java.text.*"%>
<%@ taglib uri="/struts-tags" prefix="s"%><%@include file="/udmpCommon.jsp"%>
<%@ taglib uri="http://www.git.com.cn/taglib/udmp/field" prefix="Field"%>
<%@ include file="/cs/pages/common/jsp/common_ctx.jsp"%>
<script type="text/javascript" src="${ctx}/cs/js/cs_common.js"></script>
<link rel="stylesheet" href="${ctx}/cs/css/public.css" type="text/css"> 
<!-- ********帮助菜单***********begin********* -->
<s:include value="/cs/pages/common/jsp/helpMenu.jsp"></s:include>
<!-- *********帮助菜单***********end********* -->
<%-- 进度条 --%>
<s:include value="csEndorseProgress.jsp" />

<div class="panelPageFormContent">
	<div class="divfclass">
		<h1>
			<img src="${ctx}/cs/img/icon/tubiao.png">红利领取
		</h1>
	</div>
	<div class="pageContent" layoutH="150">
		<!-- 客戶信息 -->
		<div class="panelPageFormContent" >
			<%-- <div class="divfclass">
				<h1>
					<img src="${ctx}/cs/img/icon/tubiao.png">客户信息
				</h1>
			</div> --%>

			<div class="panelPageFormContent">
			<!--客户的5项基本信息（姓名、出生日期、证件类型、证件号码、性别） -->
			<s:include value="customerInfo_list.jsp" />
			<s:if test="verifyFlag==1">
				<s:include value="verifyInfo_list.jsp" />
			</s:if>
				<%-- <dl>
					<dt>姓名</dt>
					<dd>
						<input id="customerName" name="csCustomerVO.customerName"
							type="text" value="${csCustomerVO.customerName }"
							disabled="disabled" />
					</dd>
				</dl>
				<dl>
					<dt>性别</dt>
					<dd>
						<input type="text" id="customerGender" name="customerGender"
							value="<Field:codeValue  tableName="APP___PAS__DBUSER.T_GENDER" value="${csCustomerVO.customerGender}" /> "
							readonly="readonly" disabled="disabled" />
					</dd>
				</dl>
				<dl>
					<dt>生日</dt>
					<dd>
						<input name="csCustomerVO.customerBirthday" type="text"
							value=<s:date format="yyyy-MM-dd" name="csCustomerVO.customerBirthday"></s:date>
							disabled="disabled" />
					</dd>
				</dl>
				<dl>
					<dt>证件类型</dt>
					<dd>
						<input type="text" readonly="readonly" id="customerCertType"
							name="customerCertType"
							value="<Field:codeValue  tableName="APP___PAS__DBUSER.T_CERTI_TYPE" value="${csCustomerVO.customerCertType}" />"
							disabled="disabled" />
					</dd>
				</dl>
				<dl>
					<dt>证件号码</dt>
					<dd>
						<input type="expandCertiCode"
							name="csCustomerVO.customerCertiCode"
							value="${csCustomerVO.customerCertiCode}" disabled="disabled" />
					</dd>
				</dl> --%>

				<!--客户id-->
				<input type="hidden" id="customerId" name="customerId"
					value="${customerId}" />
				<!--保全变更Id-->
				<input type="hidden" id="changeId" name="changeId"
					value="${changeId}" />
				<!--保全受理号 -->
				<input type="hidden" id="acceptId" name="acceptId"
					value="${acceptId}" />
				<!--客户id-->
				<input type="hidden" id="insuredId" name="insuredId"
					value="${csCustomerVO.insuredId}" />
				<!--保全变更Id-->
				<input type="hidden" id="insuredName" name="insuredName"
					value="${csCustomerVO.insuredName}" />
			</div>
		</div>

		<!--变更信息的json字符串[保存银行账号时会使用] -->
		<input id="jsons" name="jsons" type="hidden" value="" />

		<!-- 应领未领红利 -->
		<div class="main_tabdiv">
		<div class="panelPageFormContent" >
			<div class="divfclass">
				<h1>
					<img src="${ctx}/cs/img/icon/tubiao.png">应领未领红利
				</h1>
			</div>
			<div class="tabdivclass">
				<table id="bonusPremInfo" class="list" width="100%">
					<thead>
						<tr>
							<th>保单号</th>
							<th>险种代码</th>
							<th>险种名称</th>
							<th>应付期数</th>
							<th>应领日期</th>
							<th>应领金额</th>
							<!-- <th style="display: none">productId</th> -->
						</tr>
					</thead>
					<tbody>
						<s:iterator value="csEndorseGRylwlVOList" status="st"
							id="csPremArapVOList">
							<tr align="center">
								<td>${policyCode}</td>
								<td>${busiProdCode}</td>
								<td><Field:codeValue
											tableName="APP___PAS__DBUSER.T_BUSINESS_PRODUCT"
											value="${busiPrdId}" /></td>
								<td>${payNum}</td>
								<td><s:date format="yyyy-MM-dd" name="payDueDate" /></td>
								<td>${feeAmount}</td>
							</tr>
						</s:iterator>
					</tbody>
				</table>
			</div>

			<!-- 変更信息录入 -->
			<form action="">
				<div class="panelPageFormContent" style="margin: 10px;">
					<div class="divfclass">
						<h1>
							<img src="${ctx}/cs/img/icon/tubiao.png">变更信息录入 
						</h1>
					</div>
					
					<!-- 
						<dl>
							<dt>续期支取形式</dt>
							<dd>
								<Field:codeTable nullOption="true" id="newPayMode"
									name="newPayMode"
									tableName="APP___PAS__DBUSER.T_SURVIVAL_W_MODE" value=""
									onChange="newModeChange(this)" disabled="false" />
							</dd>
						</dl>
 					-->
						<div class="divfclass">
							<h1>
								<img src="${ctx}/cs/img/icon/tubiao.png">续期红利领取形式 
							</h1>
						</div>
						<div class="divfclass">
							<table id="infoTable" name="hlGetTable" class="list" width="100%">
								<thead>
									<tr>
										<th >序号</th>
										<th colName="policyCode" style="width: 150px">保单号</th>
										<th colName="busiProdCode" style="width: 150px">险种代码</th>
										<th colName="busiProdName" style="width: 150px">险种名称</th>
										<th colName="bonusMode" style="width: 210px" inputType="select">红利领取形式</th>
										<th colName="bonusWMode" inputType="select">收付费方式</th>
										<th>银行账号</th>
										<th>户名</th>
										<th>银行代码/开户银行</th>
										<th colName="bankAccount"  style="display: none">bankAccount</th>
										<th colName="accoName"  style="display: none">accoName</th>
										<th colName="bankCode" style="display: none">bankCode</th>
										<th colName="accountId" style="display: none">accountId</th>
										<th colName="customerId" style="display: none">领取人ID</th>
										<th colName="changeId" style="display: none">changeId</th>
										<th colName="policyChgId" style="display: none">policyChgId</th>
										<th colName="policyId" style="display: none">policyId</th>
										<th colName="busiItemId" style="display: none">busiItemId</th>
										<th colName="acceptId" style="display: none">acceptId</th>
										<th colName="planId" style="display: none">planId</th>
										<th colName="correspondentNo" style="display: none">correspondentNo</th>
									</tr>
								</thead>
								
								<tbody id="policyBankMsg">
									<s:iterator value="csEndorseGRFeeVOList" status="st"
										var='itemVO'>
										<tr align="center" tr_saveStatus="1">
											<td><s:property value="#st.index+1" /></td>
											<td>${policyCode}</td>
											<td>${busiProdCode}</td>
											<td>
												${productNameStd}
											</td>
											<td>
												<select name="changeSurvivalMode" style="width: 200px"  onchange="newPlanChange(this);" <s:if test='changeBonusMode != 1'> disabled=true </s:if>>
													<option value="1" <s:if test="changeBonusMode == 1 && changeBonusWMode == 1"> selected </s:if>>
	                                                  	现金领取-需办理保全项目领取
	                                                </option>
	                                                <option value="1" <s:if test="changeBonusMode == 1 && changeBonusWMode != 1"> selected </s:if>>
	                                                  	现金领取-约定银行转账
	                                                </option>
	                                                
	                                                <s:if test="changeBonusMode != 1">
	                                                	<option value="2" <s:if test="changeBonusMode == 2"> selected</s:if>>累积生息</option>
                                                    	<option value="3" <s:if test="changeBonusMode == 3"> selected</s:if>>抵缴保费</option>
                                                    	<option value="4" <s:if test="changeBonusMode == 4"> selected</s:if>>转万能账户</option>
	                                                </s:if>
		                                         </select>	
											</td>
											
											<td class='newBonusWModes'>
												<select id="newPayMode" class="payModeClass"  onchange="newModeChange(this)" name="payMode" <s:if test='changeBonusMode != 1 || (changeBonusMode == 1 && changeBonusWMode == 1 )'> style="display:none" </s:if>>
												     <s:if test="changeBonusMode != 1">
												     	<option value=""></option>
												     </s:if>
												     <s:else>
												     	 <option selected style="display: none;" value="1"></option>
												     	 <option value="2" <s:if test="changeBonusWMode == 2"> selected </s:if>>
	                                                  		银行转账（制返盘）
		                                                 </option>
		                                                 <option value="3" <s:if test="changeBonusWMode == 3"> selected </s:if>>
		                                                  	网上银行
		                                                 </option>
												     </s:else>
		                                         </select>	
											</td>
											<td>
												<select <s:if test="changeBonusMode != 1 || (changeBonusMode == 1 && changeBonusWMode!=2) ">style="display: none"</s:if>
													id="banklistNew" name="banklist" onchange="getBankCode(this)">
												
												<div class="banklistInput" <s:if test="changeBonusMode != 1 || (changeBonusMode == 1 && changeBonusWMode!=3) ">style="display: none"</s:if>>
											    	<input <s:if test="changeBonusMode != 1 || (changeBonusMode == 1 && changeBonusWMode!=3) ">style="display: none"</s:if> class="bankAccountInput"  onchange="checkAccountBank(this)" value=""/>
											    </div>
												
											</td>
											<td>
												<input type="text" class="accoNameInput" <s:if test="changeBonusMode != 1 || (changeBonusMode == 1 && (changeBonusWMode1=2 || changeBonusWMode1=3)) ">style="display: none"</s:if> onchange="checkAccountName(this)" value="${accoName}" readOnly = true/>
											</td>
											<td>
												<input type="text" class="bankCodeReadOnly" value="${bankCode}" <s:if test="changeBonusMode != 1 || (changeBonusMode == 1 && changeBonusWMode!=2)">style="display: none"</s:if> readOnly = true />
												
												<div class="banklistInput" <s:if test="changeBonusMode != 1 || (changeBonusMode == 1 && changeBonusWMode!=3) ">style="display: none"</s:if>>
											    	<input class="inputOrg1" name="org1.orgNum" value="" type="hidden"/><!-- 联行号 -->
											    	<input class="inputCreator" name= "org1.creator" value="" type="hidden"/><!-- 联行号对应的银行代码 -->
											   		<input class="required" name="org1.orgName" type="text" postField="keyword" readonly="readonly"
									suggestFields="orgNum,orgName,creator" suggestUrl="cs/pages/csEntry/districtBankCodeQuery.jsp" lookupGroup="org1"/>
													<a class="btnLook" href="${ctx}/cs/csEntry/selectReturnNumber_PA_csEntryAction.action" 
													lookupGroup="org1" style="float: right;">查找带回</a>
											    </div>
											</td>
											<td style="display: none">${bankAccount}</td>
											<td style="display: none">${accoName}</td>
											<td style="display: none">${bankCode}</td>
											
											<td style="display: none">${accountId}</td>
											<td style="display: none">${customerId}</td>
											<td style="display: none">${changeId}</td>
											<td style="display: none">${policyChgId}</td>
											<td style="display: none">${policyId}</td>
											<td style="display: none">${busiItemId}</td>
											<td style="display: none">${acceptId}</td>
											<td style="display: none">${planId}</td>
											<td style="display: none" class="correspondentNoTd" name="correspondentNo">${correspondentNo}</td>
										</tr>
									</s:iterator>
								</tbody>
							</table>
						</div>
						
						<!-- 银行信息 -->
						<div id="_grBank" style="display: none">
							<s:include value="/cs/pages/common/jsp/CsBankAccountpubsave.jsp"></s:include>
						</div>
						
						
						
						<div>

						<!-- 按钮 -->
						<div class="pageFormdiv">
							<button class="but_blue" type="button" onclick="saveGRMsg()">保存</button>
						</div>

					</div>
					</div>
				
			</form>
			<!-- 变更后的信息 -->
			<%-- <div id="changeBankMsg">
				<s:include value="/cs/pages/serviceitem/CsEndorseGR_update.jsp"></s:include>				
			</div> --%>
			<div class="panelPageFormContent" style="margin: 10px;">
				<div class="divfclass">
					<h1>
						<img src="${ctx}/cs/img/icon/tubiao.png">变更后信息
					</h1>
				</div>
				<div class="tabdivclass">
					<table id="baseInfo" class="list" width="100%">
						<thead>
							<tr>
								<th>保单号</th>
								<th>险种代码</th>
								<th>险种名称</th>
								<th>应付期数</th>
								<th>应领日期</th>
								<th>应领金额</th>
								<th style="display: none">productId</th>
							</tr>
						</thead>
						<tbody>
							<s:iterator value="csEndorseGRylwlVOList">
								<tr align="center">
									<s:if test="queryFlag==1">disabled="disabled"</s:if>
									<td>${policyCode}</td>
									<td>${busiProdCode}</td>
									<td><Field:codeValue
												tableName="APP___PAS__DBUSER.T_BUSINESS_PRODUCT"
												value="${busiPrdId}" /></td>
									<td>${payNum}</td>
									<td><s:date format="yyyy-MM-dd" name="payDueDate" /></td>
									<td>${feeAmount}</td>
								</tr>
							</s:iterator>
						</tbody>
					</table>
				</div>
			</div>

			<s:include value="/cs/pages/serviceitem/serviceItemBottomBar.jsp"></s:include>
		</div>
		</div>
	</div>
</div>
<script type="text/javascript">
	$(function() {
		loadBankInfo();
	});

	function loadBankInfo() {
		var table = $("#bankAccountTable");
		var rows = table.find("tbody tr");
		var str = "";

		//循环下拉框 
		var bankinfo = $("select[name='banklist']");
		for (var i = 0; i < bankinfo.length; i++) {
			str = "<option value=''>请选择</option>";
			var obj = bankinfo[i];
			for (var n = 0; n < rows.length; n++) {
				var issueBankName = $(rows[n]).find("td").eq(4).text();
				var bankCode = $(rows[n]).find("td").eq(5).text();
				var bankAccount = $(rows[n]).find("td").eq(6).find("input").val();
				var accountId = $(rows[n]).find("td").eq(10).text();
				var accoName = $(rows[n]).find("td").eq(7).text();
				// 		console.info(accountId);
				str += "<option id='"+accountId+"' name='"+bankCode+"' value='"+ bankCode + "/"+bankAccount+ "/"+accoName+ "/"+issueBankName+"'>" + bankCode + "/" + bankAccount +"</option>";
			}
			$(obj).html(str);
		}
		
		
		var $tableShow = $("#infoTable", navTab.getCurrentPanel());
		var rowsTable = $tableShow.find("tr");
		
		var payModeBoolean = false;//默认false表示，所有的收付费方式没有一个为银行转账（控制客户银行账号的展示与否）
		for (var k = 1; k < rowsTable.length; k++) {
			//判断传递的红利领取方式为现金领取
			var survivalMode = $("#infoTable", navTab.getCurrentPanel()).find("tr:eq("+k+")").find('td:eq(4)').find("select[name='changeSurvivalMode']", navTab.getCurrentPanel()).val();
			var payMode = $("#infoTable", navTab.getCurrentPanel()).find("tr:eq("+k+")").find('td:eq(5)').find("select[name='payMode']", navTab.getCurrentPanel()).val();
			
			if(survivalMode == 1 && payMode == 2){
				if(payModeBoolean == false){
					payModeBoolean = true;
				}
				
				$("#infoTable", navTab.getCurrentPanel()).find("tr:eq("+k+")").find("select[name='banklist']").get(0).options[1].selected = true;
				$("#infoTable", navTab.getCurrentPanel()).find("tr:eq("+k+")").find("select[name='banklist']").removeAttr("disabled");
				$("#infoTable", navTab.getCurrentPanel()).find("tr:eq("+k+")").find("select[name='banklist']").attr("style", "display:block;");
				
				//隐藏下拉带回的内容
				$("#infoTable", navTab.getCurrentPanel()).find("tr:eq("+k+")").find(".banklistInput").attr("style", "display:none;");
				$("#infoTable", navTab.getCurrentPanel()).find("tr:eq("+k+")").find("input[name='banklist']").val("");
				
				$("#infoTable", navTab.getCurrentPanel()).find("tr:eq("+k+")").find(".bankAccountInput").val("");
				$("#infoTable", navTab.getCurrentPanel()).find("tr:eq("+k+")").find(".accoNameInput").val("");

				$("#infoTable", navTab.getCurrentPanel()).find("tr:eq("+k+")").find("select[name='banklist']").get(0).options[1].selected = true;
				var bankCodeAccount = $("#infoTable", navTab.getCurrentPanel()).find('td:eq(6)').find("select[name='banklist']", navTab.getCurrentPanel()).val();
				$("#infoTable", navTab.getCurrentPanel()).find("tr:eq("+k+")").find('td:eq(9)').text(bankCodeAccount.split("/")[1]);
				$("#infoTable", navTab.getCurrentPanel()).find("tr:eq("+k+")").find('td:eq(10)').text(bankCodeAccount.split("/")[2]);
				$("#infoTable", navTab.getCurrentPanel()).find("tr:eq("+k+")").find('td:eq(11)').text(bankCodeAccount.split("/")[0]);
				$("#infoTable", navTab.getCurrentPanel()).find("tr:eq("+k+")").find(".accoNameInput", navTab.getCurrentPanel()).val(bankCodeAccount.split("/")[2]);
				$("#infoTable", navTab.getCurrentPanel()).find("tr:eq("+k+")").find(".bankCodeReadOnly", navTab.getCurrentPanel()).val(bankCodeAccount.split("/")[0] + "/"+bankCodeAccount.split("/")[3]);

				
				//181559:银行转账（制返盘）银行账号、户名只读
				$("#infoTable", navTab.getCurrentPanel()).find("tr:eq("+k+")").find(".bankCodeReadOnly").attr("style", "display:black;");
				$("#infoTable", navTab.getCurrentPanel()).find("tr:eq("+k+")").find(".bankAccountInput").attr("style", "display:none;");
				$("#infoTable", navTab.getCurrentPanel()).find("tr:eq("+k+")").find(".accoNameInput", navTab.getCurrentPanel()).prop("readOnly", true);
				
				
			}else if(survivalMode == 1 && payMode == 3){
				$("#infoTable", navTab.getCurrentPanel()).find("tr:eq("+k+")").find("select[name='banklist']").attr("disabled","true");
				$("#infoTable", navTab.getCurrentPanel()).find("tr:eq("+k+")").find("select[name='banklist']").attr("style", "display:none;");
				$("#infoTable", navTab.getCurrentPanel()).find("tr:eq("+k+")").find(".banklistInput").attr("style", "display:block;");

				$("#infoTable", navTab.getCurrentPanel()).find("tr:eq("+k+")").find('td:eq(9)').text("");
				$("#infoTable", navTab.getCurrentPanel()).find("tr:eq("+k+")").find('td:eq(10)').text("");
				$("#infoTable", navTab.getCurrentPanel()).find("tr:eq("+k+")").find('td:eq(11)').text("");
				$("#infoTable", navTab.getCurrentPanel()).find("tr:eq("+k+")").find(".bankAccountInput").val("");
				$("#infoTable", navTab.getCurrentPanel()).find("tr:eq("+k+")").find(".accoNameInput").val("");
				$("#infoTable", navTab.getCurrentPanel()).find("tr:eq("+k+")").find(".bankCodeReadOnly", navTab.getCurrentPanel()).val("");
				
				$("#infoTable", navTab.getCurrentPanel()).find("tr:eq("+k+")").find("#banklist", navTab.getCurrentPanel()).val("");
				
				//181559:银行转账（制返盘）银行账号、户名 可编辑输入
				$("#infoTable", navTab.getCurrentPanel()).find("tr:eq("+k+")").find(".bankCodeReadOnly").attr("style", "display:none;");
				$("#infoTable", navTab.getCurrentPanel()).find("tr:eq("+k+")").find(".bankAccountInput").attr("style", "display:black;");
				$("#infoTable", navTab.getCurrentPanel()).find("tr:eq("+k+")").find(".accoNameInput", navTab.getCurrentPanel()).prop("readOnly", false);
			}
		}
		
		//此处控制客户银行账号的展示与否
		if(payModeBoolean){
			$("#_grBank").show();
		}else{
			$("#_grBank").hide();
		}
		
		
		
	}

	//续期支取形式修改
	function newModeChange(line) {
		
		var table = $("#infoTable", navTab.getCurrentPanel());
		var rows = table.find("tr");
		var surviWM = line.value;
		
		var payModeBoolean = false;//默认false表示，所有的收付费方式没有一个为银行转账（控制客户银行账号的展示与否）
		for (var n = 1; n < rows.length; n++) {
			//判断收付费方式中是否存在银行转账（制返盘）
			var payMode = $(rows[n]).find(".payModeClass").val();
			if(payMode == 2 && payModeBoolean == false){
				payModeBoolean = true;
			}
		}
		
		//此处控制客户银行账号的展示与否
		if(payModeBoolean){
			$("#_grBank").show();
		}else{
			$("#_grBank").hide();
		}
		
		if(line.value == 2){			
			$(line).closest('tr').find("select[name='banklist']").removeAttr("disabled");
			$(line).closest('tr').find("select[name='banklist']").attr("style", "display:block;");
			
			//隐藏下拉带回的内容
			$(line).closest('tr').find(".banklistInput").attr("style", "display:none;");
			$(line).parent().parent().find("input[name='banklist']").val("");
			
			$(line).closest('tr').find(".bankAccountInput").val("");
			$(line).closest('tr').find(".accoNameInput").val("");

			$(line).closest('tr').find("select[name='banklist']").get(0).options[1].selected = true;
			var bankCodeAccount = $(line).closest('tr').find('td:eq(6)').find("select[name='banklist']", navTab.getCurrentPanel()).val();
			$(line).parent().parent().find('td:eq(9)').text(bankCodeAccount.split("/")[1]);
			$(line).parent().parent().find('td:eq(10)').text(bankCodeAccount.split("/")[2]);
			$(line).parent().parent().find('td:eq(11)').text(bankCodeAccount.split("/")[0]);
			$(line).closest('tr').find(".accoNameInput", navTab.getCurrentPanel()).val(bankCodeAccount.split("/")[2]);
			$(line).closest('tr').find(".bankCodeReadOnly", navTab.getCurrentPanel()).val(bankCodeAccount.split("/")[0] + "/"+bankCodeAccount.split("/")[3]);
			
			//181559:银行转账（制返盘）银行账号、户名只读
			$(line).closest('tr').find(".bankCodeReadOnly").attr("style", "display:black;");
			$(line).closest('tr').find(".bankAccountInput").attr("style", "display:none;");
			$(line).closest('tr').find(".accoNameInput", navTab.getCurrentPanel()).prop("readOnly", true);
		}else if(line.value == 3){
			$(line).closest('tr').find("select[name='banklist']").attr("disabled","true");
			$(line).closest('tr').find("select[name='banklist']").attr("style", "display:none;");
			$(line).closest('tr').find(".banklistInput").attr("style", "display:block;");

			$(line).parent().parent().find('td:eq(9)').text("");
			$(line).parent().parent().find('td:eq(10)').text("");
			$(line).parent().parent().find('td:eq(11)').text("");
			
			$(line).closest('tr').find(".bankAccountInput").val("");
			$(line).closest('tr').find(".accoNameInput").val("");
			
			
			
			$(line).closest('tr').find("#banklist", navTab.getCurrentPanel()).val("");
			
			//181559:银行转账（制返盘）银行账号、户名 可编辑输入
			$(line).closest('tr').find(".bankCodeReadOnly").attr("style", "display:none;");
			$(line).closest('tr').find(".bankAccountInput").attr("style", "display:black;");
			$(line).closest('tr').find(".accoNameInput", navTab.getCurrentPanel()).prop("readOnly", false);
			
			
		}
		
		
		
		
		
	}

	function changeModeChange(obj) {
		var $td = $(obj).parents("td").next();

		if (obj.value == 1) {
			$td.find("select").attr("readonly", true);
		}
	}

	
	//红利领取信息保存按钮
function saveGRMsg() {
	var $table = $("#infoTable", navTab.getCurrentPanel());
	var rows = $table.find("tr");
	
	var selectTextFlag = false;
	for (var k = 1; k < rows.length; k++) {
		
		var selectText = $("#infoTable", navTab.getCurrentPanel()).find("tr:eq("+k+")").find('td:eq(4)').find("select[name='changeSurvivalMode']").find('option:selected').text().trim();
		
		//判断传递的红利领取方式为现金领取
		var survivalMode = $("#infoTable", navTab.getCurrentPanel()).find("tr:eq("+k+")").find('td:eq(4)').find("select[name='changeSurvivalMode']", navTab.getCurrentPanel()).val();
		var payMode = $("#infoTable", navTab.getCurrentPanel()).find("tr:eq("+k+")").find('td:eq(5)').find("select[name='payMode']", navTab.getCurrentPanel()).val();
		
		if(selectText.indexOf("约定银行") != -1 && payMode != 2 && payMode != 3){
			selectTextFlag = true;
		}
		
		//如果收付费方式为网上银行，银行账号和户名赋值为输入的内容，且银行代码为联行号对应的代码，联行号不为空
		if(survivalMode == 1 && payMode == 3){
			var inputOrg1 = $("#infoTable", navTab.getCurrentPanel()).find("tr:eq("+k+")").find(".inputOrg1", navTab.getCurrentPanel()).val();
			if(inputOrg1 != null || inputOrg1 != ''){
				$("#infoTable", navTab.getCurrentPanel()).find("tr:eq("+k+")").find(".correspondentNoTd", navTab.getCurrentPanel()).text(inputOrg1);
			}
			//获得联行号所对应的银行代码，并将联行号对应的银行代码赋值
			var bankCode = $("#infoTable", navTab.getCurrentPanel()).find("tr:eq("+k+")").find(".inputCreator", navTab.getCurrentPanel()).val();
			$("#infoTable", navTab.getCurrentPanel()).find("tr:eq("+k+")").find('td:eq(11)').text(bankCode);
		}
	}
	
	if(selectTextFlag==true){
		alertMsg.error("现金领取-约定银行转账时，收付费方式不能为空！"); 
	}else{
		alertMsg.confirm("确认保存以上信息？",{
			okCall : function() {
				var $table = $("#infoTable", navTab.getCurrentPanel());
				var _jsons = _cs_tableToJson($table);
				//console.info("GR获得的值为----》"+_jsons);
				
				$.ajax({
						url : "${ctx}/cs/serviceitem_gr/saveGRMsg_PA_csEndorseGRAction.action",
						type : "post",
						dataType : 'html',
						data : "acceptId="+$("#acceptId").val()+"&jsonString="+ encodeURI(_jsons),
						cache : false,
						success : function(response) {
							alertMsg.correct("信息保存成功");										
							var json = DWZ.jsonEval(response);
							if (undefined == json.statusCode) {
								/* $("#changeBankMsg").html("");
								$("#changeBankMsg").html(response).initUI(); */
							} else {
								alertMsg.error(json.message); 
							}	
						}
					});
				 
			},
			cancelCall : function() {
				
			}
		});
	}
		
		
		
		
		
	}	
	
	function getBankCode(obj){
		//给银行账号一列赋值
		if(obj.value == "" ||obj.value == null){
			$(obj).parent().parent().find('td:eq(9)').text("");
			$(obj).parent().parent().find('td:eq(10)').text("");
			$(obj).parent().parent().find('td:eq(11)').text("");
			$(obj).parent().parent().find('.accoNameInput').val("");
			$(obj).parent().parent().find('.bankCodeReadOnly').val("");
			$(obj).parent().parent().find('.bankAccountInput').val("");
			
		}else{
			$(obj).parent().parent().find('td:eq(9)').text(obj.value.split("/")[1]);
			$(obj).parent().parent().find('td:eq(10)').text(obj.value.split("/")[2]);
			$(obj).parent().parent().find('td:eq(11)').text(obj.value.split("/")[0]);
			$(obj).parent().parent().find('.accoNameInput').val(obj.value.split("/")[2]);
			$(obj).parent().parent().find('.bankCodeReadOnly').val(obj.value.split("/")[0]+"/"+obj.value.split("/")[3]);
			$(obj).parent().parent().find('.bankAccountInput').val(obj.value.split("/")[0]);
		}		
	}
	
	
	//户名二次重复校验
	var accountNameCheckTime = 1;//户名输入次数
	var accountNameFirst="";//第一次输入
	function checkAccountName(obj){
		if (accountNameCheckTime==1) {
			accountNameFirst=obj.value;//第一次输入
			accountNameCheckTime+=1;
			obj.value="";
		}else if (accountNameCheckTime==2) {	
			if (accountNameFirst!="" && accountNameFirst!=obj.value) {
				alertMsg.info("两次输入不一致，请重新输入！");				
				accountNameCheckTime=1;
				return;
			}else{
				$(obj).closest('tr').find('td:eq(10)').text(obj.value);
			}
		}		
	}
	
	
	//银行账号二次重复校验
	var bankAccountCheckTime = 1;//账号输入次数
	var bankAccountFirst="";//第一次输入
	function checkAccountBank(obj){
		if (bankAccountCheckTime==1) {
			bankAccountFirst=obj.value;//第一次输入
			bankAccountCheckTime+=1;
			obj.value="";
		}else if (bankAccountCheckTime==2) {	
			if (bankAccountFirst!="" && bankAccountFirst!=obj.value) {
				alertMsg.info("两次输入不一致，请重新输入！");				
				bankAccountCheckTime=1;
				return;
			}else{
				$(obj).closest('tr').find('td:eq(9)').text(obj.value);
			}
		}		
	}
	
	
	function newPlanChange(line) {
		var selectText = "";
	    $(line, navTab.getCurrentPanel()).find('option:selected').each(function() {
	    	selectText = $(this).text().trim() // 输出被选中的option的文本
	    });
	    
	    
	    var table = $("#infoTable", navTab.getCurrentPanel());
		var rows = table.find("tr");
		var surviWM = line.value;
		
		var payModeBoolean = false;//默认false表示，所有的收付费方式没有一个为银行转账（控制客户银行账号的展示与否）
		for (var n = 1; n < rows.length; n++) {
			//判断收付费方式中是否存在银行转账（制返盘）
			var payMode = $(rows[n]).find(".payModeClass").val();
			if(payMode == 2 && payModeBoolean == false){
				payModeBoolean = true;
			}
		}
		
		//此处控制客户银行账号的展示与否
		if(line.value == 1 && selectText.indexOf("约定银行")!=-1 && payModeBoolean){
			$("#_grBank").show();
		}else{
			$("#_grBank").hide();
		}
	    

		//如果是银行转账 则显示银行账户
		if (line.value == 1 && selectText.indexOf("约定银行")!=-1) {	
			//只有为约定银行转换的才能看到收付费方式，否则收付费不显示
			$(line).closest('tr').find(".payModeClass").attr("style", "display:black;");
			var payMode = $(line).closest('tr').find("select[name='payMode']", navTab.getCurrentPanel()).val();
			
			//payMode为2表示是银行转账（制返盘），否则为网上银行
			if(payMode==2){
				var bankCodeAccount = $(line).closest('tr').find("select[name='banklist']", navTab.getCurrentPanel()).val();
				$(line).parent().parent().find('td:eq(9)').text(bankCodeAccount.split("/")[1]);
				$(line).parent().parent().find('td:eq(10)').text(bankCodeAccount.split("/")[2]);
				$(line).parent().parent().find('td:eq(11)').text(bankCodeAccount.split("/")[0]);
				$(line).closest('tr').find("select[name='banklist']").attr("style", "display:black;");
				$(line).closest('tr').find(".accoNameInput").val("");
				$(line).closest('tr').find('.accoNameInput').val(obj.value.split("/")[2]);
				$(line).closest('tr').find('.bankCodeReadOnly').val(obj.value.split("/")[0]+"-"+obj.value.split("/")[3]);
				$(line).closest('tr').find(".bankCodeReadOnly").attr("style", "display:black;");
			}else{
				$(line).closest('tr').find(".bankAccountInput").attr("style", "display:black;");
				$(line).closest('tr').find(".banklistInput").attr("style", "display:black;");
			}
			$(line).closest('tr').find(".accoNameInput").attr("style", "display:black;");
		} else{
			//变更收付费方式默认选中值为1的内容
			$(line).closest('tr').find(".payModeClass option[value='1']", navTab.getCurrentPanel()).prop('selected', true);
			
			
			//隐藏收付费方式
			$(line).closest('tr').find(".payModeClass").attr("style", "display:none;");
			
			//隐藏将和银行相关的值全部重新赋值为空
			$(line).parent().parent().find('td:eq(9)').text("");
			$(line).parent().parent().find('td:eq(10)').text("");
			$(line).parent().parent().find('td:eq(11)').text("");
			$(line).closest('tr').find(".bankAccountInput").val("");
			$(line).closest('tr').find(".accoNameInput").val("");
			$(line).closest('tr').find(".bankCodeReadOnly").val("");
			$(line).closest('tr').find(".payModeClass").val("");
			
			
			//隐藏银行账号下拉选、银行账号输入框、户名输入框
			$(line).closest('tr').find("select[name='banklist']").attr("style", "display:none;");
			$(line).closest('tr').find(".bankAccountInput").attr("style", "display:none;");
			$(line).closest('tr').find(".accoNameInput").attr("style", "display:none;");
			$(line).closest('tr').find(".banklistInput").attr("style", "display:none;");
			$(line).closest('tr').find(".bankCodeReadOnly").attr("style", "display:none;");
		}
	}
	
	
</script>