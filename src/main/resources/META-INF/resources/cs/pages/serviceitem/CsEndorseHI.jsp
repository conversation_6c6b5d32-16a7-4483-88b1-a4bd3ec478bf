<!-- 投保人变更页面 -->
<%@ page contentType="text/html; charset=utf-8"%>
<%@ taglib uri="/struts-tags" prefix="s"%><%@include
	file="/udmpCommon.jsp"%>
<%@ taglib uri="http://www.git.com.cn/taglib/udmp/field" prefix="Field"%>
<%@ include file="/cs/pages/common/jsp/common_ctx.jsp"%>
<script type="text/javascript" src="${ctx}/cs/js/cs_common.js"></script>
<link rel="stylesheet" href="${ctx}/cs/css/public.css" type="text/css">
<form id="queryCsEndorseHIForm"
	action="${ctx}/cs/common/loadMainPage_PA_csSurveyAction.action"
	onsubmit="return divSearch(this,'csSurveyDiv')" method="post">
	<input type="hidden" id="customerId" value="${customerId}" /> <input
		type="hidden" id="changeId" value="${changeId}" /> <input
		type="hidden" id="acceptId_hi" value="${acceptId}" /> <input
		type="hidden" id="serviceCode_hi" value="HI" />
</form>
<!-- ********帮助菜单***********begin********* -->
<s:include value="/cs/pages/common/jsp/helpMenu.jsp"></s:include>
<!-- *********帮助菜单***********end********* -->

<div onmousedown="MM_changeProp('holderInfoCmDiv','display','none')">
<s:if test="queryFlag!=1">
<s:include value="entryProgressBar.jsp"></s:include>

</s:if>
	<div layoutH=150>
		<div  >
			<input type="hidden" id="queryFlag" value="${queryFlag }">
			<s:include value="customerInfo_list.jsp" />
			<!-- 			<div class="panel" style="display: none"> -->
			<!-- 				<h1>客户基本信息</h1> -->
			<!-- 				<div> -->
			<!-- 					<ul class="searchContent" style="height: auto;"> -->
			<!-- 						<li class="nowrap"><label>客户姓名</label> <input type="text" -->
			<!-- 							name="csCustomerVO.customerName" -->
			<%-- 							value="${csCustomerVO.customerName}" readonly="readonly" /></li> --%>
			<!-- 						<li class="nowrap"><label>出生日期</label> <input type="text" -->
			<!-- 							name="csCustomerVO.customerBirthday" -->
			<%-- 							value="<s:date format="yyyy-MM-dd" name="csCustomerVO.customerBirthday"></s:date>" --%>
			<!-- 							readonly="readonly" /></li> -->
			<!-- 					</ul> -->
			<!-- 					<ul class="searchContent" style="height: auto;"> -->
			<!-- 						<li class="nowrap"><label>证件类型</label> <input -->
			<!-- 							readonly="readonly" type="text" -->
			<!-- 							name="csCustomerVO.customerCertType" -->
			<%-- 							value="<Field:codeValue tableName="APP___PAS__DBUSER.T_CERTI_TYPE" value="${csCustomerVO.customerCertType}"/>" /> --%>
			<!-- 						</li> -->
			<!-- 						<li class="nowrap"><label>证件号码</label> <input type="text" -->
			<!-- 							name="customerVO.customerCertiCode" -->
			<%-- 							value="${csCustomerVO.customerCertiCode}" readonly="readonly" /></li> --%>
			<!-- 						<li class="nowrap"><label style="width: auto;">性别&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; -->
			<!-- 								<input type="text" name="csCustomerVO.customerGender" -->
			<%-- 								value="<Field:codeValue tableName="APP___PAS__DBUSER.T_GENDER" value="${csCustomerVO.customerGender}"/>" --%>
			<!-- 								readonly="readonly"> -->
			<!-- 						</label></li> -->
			<!-- 					</ul> -->
			<!-- 				</div> -->
			<!-- 			</div> -->

			<div>
				<div class="divfclass">
					<h1>
						<img src="${ctx}/cs/img/icon/tubiao.png" />客户相关保单列表
					</h1>
				</div>
				<div class="tabdivclass">
					<table class="list" id="" width="100%">
						<thead>
							<tr id="" align="center">
								<th>保单号</th>
								<th>客户角色</th>
								<th>保单状态</th>
								<th>保单生效日</th>
							</tr>
						</thead>
						<tbody>
							<s:iterator value="hiAddNofityVOs">
								<tr align="center" id="trpolicylist"
									<s:if test="queryFlag==1">disabled="disabled"</s:if>>
									<td>${policyCode}</td>
									<td>${customerRole}</td>
									<td><Field:codeValue
											tableName="APP___PAS__DBUSER.t_Liability_Status"
											value="${liabilityState}" /></td>
									<td><s:date format="yyyy-MM-dd" name="validateDate"></s:date></td>
								</tr>
							</s:iterator>
						</tbody>
					</table>
				</div>
			</div>
			<div>
			&nbsp;
			</div>
			
			<div>
				<div id="csSurveyDiv" ></div>
			</div>
		</div>

	</div>
	<s:include value="/cs/pages/serviceitem/serviceItemBottomBar.jsp"></s:include>
</div>
<script type="text/javascript">
	

	//退出按钮
	function exit() {
		navTab.closeCurrentTab();
	}
	//初始化
	$(document).ready(function() {
		var customerId = $("#customerId", navTab.getCurrentPanel()).val();
		var queryFlag = $("#queryFlag", navTab.getCurrentPanel()).val();
		var changeId = $("#changeId", navTab.getCurrentPanel()).val();
		var acceptId = $("#acceptId_hi", navTab.getCurrentPanel()).val();
		var serviceCode = $("#serviceCode_hi", navTab.getCurrentPanel()).val();
		var jsDiv = 'csSurveyDiv';
		var rel = $("#csSurveyDiv", navTab.getCurrentPanel());
		var type = "1";
		var customerRole = $("#trpolicylist", navTab.getCurrentPanel()).find("td:eq(1)")
				.text();
		var cusObject;//角色标识
		if (customerRole == '投保人') {
			cusObject = '1';
		} else if (customerRole == '被保人') {
			cusObject = '2';
		} else if (customerRole == '代理人') {//代理人
			cusObject = '3';
		}
		 rel.loadUrl("${ctx}/cs/common/loadMainPageHolder_PA_csNotificAction.action?customerId=" + customerId
						+ "&changeId="	+ changeId
						+ "&acceptId="	+ acceptId
						+ "&type="		+ type
						+ "&jsDiv="		+ jsDiv
						+ "&queryFlag="	+ queryFlag); 
	});
	//帮助部分动作
	function MM_changeProp(objId, theProp, theValue) {
		var obj = null;
		with (document) {
			if (getElementById) {
				obj = getElementById(objId);
			}
		}
		if (obj) {
			if (theValue == true || theValue == false) {
				eval("obj.style." + theProp + "=" + theValue);
			} else {
				eval("obj.style." + theProp + "='" + theValue + "'");
			}
		}
	};

	//下一步
	function next() {
		var val1 = $("#changeId", navTab.getCurrentPanel()).val();
		var val2 = $("#acceptId", navTab.getCurrentPanel()).val();
		var val3 = $("#customerId", navTab.getCurrentPanel()).val();
		var title = "受理信息录入";
		var tabid = "_aplPermit_M";
		var fresh = eval("true");
		var external = eval("false");
		var url = "${ctx}/cs/csEntry/showAcceptAndArapInfo_PA_csEntryAction.action?&menuId=${menuId}&changeId="
				+ val1 + "&acceptId=" + val2 + "&customerId=" + val3;
		navTab.openTab(tabid, url, {
			title : title,
			fresh : fresh,
			external : external
		});

	}

	
</script>