<!-- 退保页面 -->
<%@ page contentType="text/html; charset=utf-8"%>
<%@ taglib uri="/struts-tags" prefix="s"%><%@include file="/udmpCommon.jsp"%>
<%@ taglib uri="http://www.git.com.cn/taglib/udmp/field" prefix="Field"%>
<%@ include file="/cs/pages/common/jsp/common_ctx.jsp"%>
<link rel="stylesheet" href="${ctx}/cs/css/cs_common.css" type="text/css"> 
<link rel="stylesheet" href="${ctx}/cs/css/public.css" type="text/css"> 
<script type="text/javascript" src="${ctx}/cs/js/DomZoomZX.js"></script>
 <script>
	$(document).ready(function(){
        var D = DomZoomZX('input,select,textarea,td')
	})
</script>
<style type="text/css">
th,td {
	white-space: normal;
}

.searchBar li label {
	width: 105px;
}
</style>
<s:include value="/cs/pages/common/jsp/helpMenu.jsp"></s:include>
 <s:include value="csEndorseProgress.jsp" />
 <inupt type="hidden" id="queryFlagCEX" value="${queryFlag}"/>
<div class="divfclass"> 
	<h1>
			 <img src="images/tubiao.png" >退保
		</h1>
</div>					
<div class="" layoutH="140px" >
	<div class="pageContent">
		<form method="post"
			action="${ctx }/cs/serviceitem_ct/caluNormalPremium_PA_csEndorseCTAction.action"
			class="required-validate"
			onsubmit="return navTabSearch(this,'showPreiumDetail')"
			id="premAgreeSurrendForm">
			<input type="hidden" id="policyId" name="premAgreeSurrendVO.policyId" value="${policyId}" />
			<input  type="hidden" name="isCancerFlaga" id="isCancerFlaga" value="${isCancerFlaga}" />
			<input type="hidden" name="changeId" id="changeId" value="${changeId}" /> 
			<input type="hidden" name="acceptId" id="acceptId" value="${acceptId}" /> 
			<input type="hidden" name="customerId" id="customerId" value="${customerId}" /> 
			<input type="hidden" name="itemIds" id="itemIds" value="${itemIds}" />
		    <input type="hidden" name="saveTableInfo" id="saveTableInfo" value="${saveTableInfo}" />
				<%-- <s:include value="customerInfo.jsp" /> --%>
				<s:include value="customerInfo_list.jsp" />
				<s:if test="verifyFlag==1">
					<s:include value="verifyInfo_list.jsp" />
				</s:if>
				<s:if test="secondPHFlag == 1">
				<dt style="font-weight: 900; color: red;width: auto;">指定第二投保人:是</dt>
				</s:if>
			   <div class="divfclass">
								<h1>
							    <img src="images/tubiao.png" >变更前保单信息 
								</h1>
									
						</div>
					<!-- 150568 start -->
					<div class="tabdivclass" <s:if test=" multiFirstInsuredFlag!=1 ">style="display: none"</s:if>>
						<table class="list" id="tableinsured" width="20%" table_saveStatus="0">
							<thead>
								<tr id="" align="center">
									<th>选择</th>
									<th colName="customerId" style="display: none;">被保人客户id</th>
									<th colName="customerName" >被保险人</th>
									<th colName="busiItemIds" style="display: none;">险种IDs</th>
								</tr>
							</thead>    
							<tbody id="checkboxinsured">
								<s:iterator value="insuredListVOList" id="insuredListVOList" var="listinsured">
									<tr >
									    <!-- 选择 -->
										<td>
											<input type="checkbox" name="checkbox"  class="myClass"  data-busiItemIds="${busiItemIds}"
											onclick="selectInsured(this);"  <s:if test="queryFlag==1">disabled="true" </s:if> />
										</td>
										<!-- 被保人客户id -->
										<td style="display: none;" name="customerId">${customerId}</td>
										<!-- 被保险人 -->
										<td name="customerName">${customerName }</td>
										<!-- 险种IDs -->
										<td style="display: none;" name="busiItemIds">${busiItemIds }</td>
									</tr>
								</s:iterator>
							</tbody>
						</table>
					</div>
					<br/>
					<!-- 150568 end -->
					<div class="tabdivclass" >
						<table class="list" id="table1" width="100%" table_saveStatus="0">
							<thead>
								<tr id="" align="center">
									<th>选择</th>
									<th colName="itemId" style="display: none;">责任组id</th>
									<th colName="isMasterItem"  style="display: none;">是否是责任组</th>
									<th colName="busiItemId" style="display: none;">险种ID</th>
									<th colName="masterBusiItemId" style="display: none;">所属险种ID</th>
									<th colName="policyCode">保单号</th>
									<th colName="insuredNames" <s:if test=" multiFirstInsuredFlag!=1 ">style="display: none"</s:if>>被保险人</th><!-- 150568 险种被保人姓名 -->
									<th colName="busiPrdCode">险种代码</th>
									<th>险种名称</th>
									<th style="display: none;">责任组代码</th>
									<th>责任组名称</th>
									<th>生效日期</th>
									<th>险种状态</th>
									<th>保额</th>
									<th>保费</th>
									<th>已交保费</th>
									<th>下次缴费日</th>
									<th>保单年月</th>
									<th>是否在犹豫期内</th>
									<th colName="investCashValue" style="display: none;"><s:if test="busiCode928!=null&&busiCode928.length()>0">退保金</s:if><s:else>万能险现价</s:else></th>
									<!-- 13 -->
									<th colName="changeId" style="display: none;" >changeId</th>
									<th colName="policyChgId"  style="display: none;">policyChgId</th>
									<th colName="surrenderCause"  inputType="select" >退保原因</th>
									<th colName="agentHolderRelation"  inputType="select">投保人与业务员关系</th>
									<s:if test="isDepart == 1">
										<th colName="isDepart"  inputType="select">是否出境</th>
									</s:if>
									<th colName="isCancerFlag" style="display: none;" >是否保险金给付</th>
								</tr>
							</thead>    
							<tbody id="checkboxcheckedroles">
								<s:iterator value="listBfSurrender" id="listBfSurrender" var="list1">
									<tr <s:if test="checked==1">tr_saveStatus='1'</s:if><s:else>tr_saveStatus='0' </s:else> 
									 title="${(masterBusiItemId == busiItemId && isMasterItem==1)?'primaryInsurance':'additionalInsurance'}">
									    <!-- 选择 150568 add selectBusiItem() -->
										<td>
											<input type="checkbox" id="itemId${st.index }" 
														 <s:if test="checked==1">checked</s:if>
											class="myClass" value="${itemId}" onclick="selectItemItem(this);selectBusiItem();" 	<s:if test="queryFlag==1">disabled="true" </s:if> />
										</td>
										<!-- 责任组id -->
										<td style="display: none;">${itemId}</td>
										<!-- 是否是责任组 -->
										<td style="display: none;">${isMasterItem }</td>
										<!-- 险种ID 150568 add data-busiItemId-->
										<td style="display: none;" data-busiItemId='${busiItemId }' >${busiItemId }</td>
										<!-- 所属险种ID -->
										<td id="masterBusiItemId" style="display: none;">${masterBusiItemId }</td>
										<!-- 保单号 -->
										<td>${policyCode }</td>
										<!-- 150568 险种被保人姓名 -->
										<td <s:if test=" multiFirstInsuredFlag!=1 ">style="display: none"</s:if>>${insuredNames }</td>
										<!-- 险种代码 -->
										<td id="_busiProdCode">${busiProdCode }</td>
										<!-- 险种名称 -->
										<td><Field:codeValue tableName="APP___PAS__DBUSER.T_BUSINESS_PRODUCT"
												value="${busiPrdId}" /></td>
										<!-- 责任组代码 -->
										<td style="display: none;">${productCode }</td>
										<!-- 责任组名称 -->
										<td><Field:codeValue tableName="APP___PAS__DBUSER.T_PRODUCT_LIFE" value="${productCode}" /></td>
										<!-- 生效日期 -->
										<td><s:date name="validatePDay" format="yyyy-MM-dd" /></td>
										<!-- 险种状态 -->
										<td><Field:codeValue tableName="APP___PAS__DBUSER.T_LIABILITY_STATUS"
												value="${prodStatus}" /></td>
										<!-- 保额 -->
										<td>${productCost }</td>
										<!-- 保费 -->
										<td>${productFee }</td>
										<!-- 已交保费 -->
										<td>${prodInPremAf }</td>
										<!-- 下次缴费日 -->
										<td><s:date name="nextPayDay" format="yyyy-MM-dd" /></td>
										<!-- 保单年月 -->
										<td>${policyDur }</td>
										<!-- 是否在犹豫期内 -->
										<td><Field:codeValue tableName="APP___PAS__DBUSER.T_YES_NO"
												value="${isInHesitate}" /></td>
										<!-- 退保金/万能险现价 -->		
										<td style="display: none;">${investCashValue}</td>
										<td style="display: none;">${changeId }</td>
										<td style="display: none;">${policyChgId }</td>
										<!-- 退保原因 -->
										<td>
											<s:if test="queryFlag!=1">
												<Field:codeTable  id="surrenderCauseId" name="0" value="${surrenderCause}" tableName="APP___PAS__DBUSER.t_surrender_cause" cssClass="combox" nullOption="true"/>
											</s:if>
											<s:else>
												<Field:codeValue value="${surrenderCause}" tableName="APP___PAS__DBUSER.t_surrender_cause" />
											</s:else>
										</td>
										<!-- 投保人与业务员关系 -->
										<td>
											<s:if test="queryFlag!=1"> 
												<Field:codeTable  id="agentHolderRelationId" name="1"  value="${agentHolderRelation}" tableName="APP___PAS__DBUSER.T_AH_RELATION" cssClass="combox" nullOption="true"/>
											</s:if>
											<s:else>
												<Field:codeValue  value="${agentHolderRelation}" tableName="APP___PAS__DBUSER.T_AH_RELATION" />
											</s:else>
										</td>
										<!-- 是否出境 -->
										<s:if test="isDepart == 1">
											<td>
													<s:if test="queryFlag!=1">
														<Field:codeTable name="2"  value="${isDepart}" tableName="APP___PAS__DBUSER.T_YES_NO" cssClass="combox"/>
													</s:if>
													<s:else>
														<Field:codeValue  value="${isDepart}" tableName="APP___PAS__DBUSER.T_YES_NO" />
													</s:else>
											</td>
										</s:if>
										<!-- 是否保险金给付 -->
										<td style="display: none;">${isCancerFlag }</td>
									</tr>
								</s:iterator>
							</tbody>
						</table>
					</div>
					
					 <%--rm:93396  928养老保险_保全业务 start --%>
					<div id="_ctCode928"   class="pageFormContent" <s:if test="!((busiCode928!=null&&busiCode928!='')||(busiCodeSY!=null && busiCodeSY!=''))">style="display: none"</s:if> >
						<dl>
							<dt>特殊退保</dt>
							<dd>
								<s:if test="queryFlag==1">
									<Field:codeTable id="_specialFlag" name="specialFlag" tableName="APP___PAS__DBUSER.T_YES_NO" nullOption="false"  
								    	value="${specialFlag }"  cssClass="combox"  disabled="true" />
								</s:if>
								<s:else>
									<Field:codeTable id="_specialFlag" name="specialFlag" tableName="APP___PAS__DBUSER.T_YES_NO" nullOption="false"  
								    	value="${specialFlag }"  cssClass="combox" defaultValue="0"  />
								 </s:else>  
							</dd>
						</dl>
				   	</div>
				    <%--rm:93396  928养老保险_保全业务 end --%>
				    	
				 <div class="pageFormdiv" id="saveMesId"  style='<s:if test="queryFlag==1" >display:none</s:if>'>
					 <button id="save1" type="button" class="but_blue" >保存</button>
				 </div>
				
		</form>
	</div>
	
	<div id ='showCTDetailAfter'>
	    <s:if test="listSurrenderNew != null && listSurrenderNew.size()>0">
			<s:include value="/cs/pages/serviceitem/CsEndorseCT_afterInfo.jsp"></s:include>
		</s:if> 
	</div> 
	<s:include value="/cs/pages/serviceitem/serviceItemBottomBar.jsp"></s:include>
</div>
	
<script type="text/javascript">
	$(function(){
		
		//保存
		$("#save1",navTab.getCurrentPanel()).click(function(){
			calSaveChange();
		});
		
		//保全查询
		if($("#queryFlagCEX",navTab.getCurrentPanel()).val()==1){
			$("#table1 input",navTab.getCurrentPanel()).each(function(){
				$(this).attr("disabled","disabled");
			});
		}
		//150568 start
		var $table = $("#table1",navTab.getCurrentPanel());
		var busiProdCodeIndex = null;
		$table.find("th").each(function(i){
			if($(this).attr("colName") == 'busiPrdCode'){
				busiProdCodeIndex = i;
			}
		});
		//150568 end
	//投连险不能做任何操作
	$("#checkboxcheckedroles tr",navTab.getCurrentPanel()).each(function(i){
		var busiProdCode=$(this).find("td").eq(busiProdCodeIndex).text();//150568 modify 6 to busiProdCodeIndex
		if(busiProdCode == '00890000' || busiProdCode =='00888000' || busiProdCode =='00892000'){
			$(this).attr("title","TLX");
			$(this).find("td :checkbox").attr("disabled",true);
			$(this).find("td select").attr("disabled",true);
		}
	});
	
	
		//绑定主附加险选中规则-退保原因及关系关联  
		$("#checkboxcheckedroles tr[title='primaryInsurance'] select",navTab.getCurrentPanel()).change(function(){
			// 点击下拉列表时 参数为2 
			checkBoxChecked(2,0,true);
		});
		
		//页面刷新时 参数为1 
		checkBoxChecked(1,0,true);
		//150568 选择被保人复选框
		selectBusiItem();
	});
	//150568 start
	//选择被保人
	function selectInsured(obj){
		//选择被保人后，循环被保人险种选中,或取消
		var busiItemIds = $(obj).attr("data-busiItemIds");
		if(busiItemIds != null && busiItemIds != '' && typeof busiItemIds != 'undefined'){
			var busiArr = busiItemIds.split('-');
			for(var i=0;i<busiArr.length;i++){
				var busiItem = busiArr[i];
				if($("#table1").find("[data-busiItemId='"+busiItem+"']").length){
					var busiObj = $("#table1").find("[data-busiItemId='"+busiItem+"']").closest("tr").find("input#itemId");
					if($(obj).is(":checked")){//选中
						$(busiObj).attr("checked",true);
					}else{
						$(busiObj).attr("checked",false);
					}
					selectItemItem(busiObj);
				}
			}
			selectBusiItem();
		}
	}
	//选择险种，页面加载后执行一次
	function selectBusiItem(){
		//选择险种后，循环所有被保人，如被保人下所有险种都选中，则选中对应被保人
		var insuredTableTrs = $("#tableinsured",navTab.getCurrentPanel()).find("tbody tr");
		insuredTableTrs.each(function(){
			var insuredObj = $(this).find("input[name='checkbox']");
			var busiItemIds = $(insuredObj).attr("data-busiItemIds");
			if(busiItemIds != null && busiItemIds != '' && typeof busiItemIds != 'undefined'){
				var busiArr = busiItemIds.split('-');
				var isAllCheck = true;
				var existsBusi = false;
				for(var i=0;i<busiArr.length;i++){
					var busiItem = busiArr[i];
					if($("#table1").find("[data-busiItemId='"+busiItem+"']").length){
						existsBusi = true;
						var busiObj = $("#table1").find("[data-busiItemId='"+busiItem+"']").closest("tr").find("input#itemId");
						if(!$(busiObj).is(":checked")){
							isAllCheck = false;
						}
					}
				}
				if(isAllCheck && existsBusi){
					$(insuredObj).attr("checked",true);
				}else{
					$(insuredObj).attr("checked",false);
				}
			}
		});
	}
	//150568 end
	//跳到HC保全项页面
    function hcPageShow() {
		var acceptId=$("#acceptId",navTab.getCurrentPanel()).val();
		var changeId=$("#changeId",navTab.getCurrentPanel()).val();
		var customerId=$("#customerId",navTab.getCurrentPanel()).val();
		navTab.openTab("1",
				"${ctx}/cs/serviceitem_hc/loadHCPage_PA_csEndorseHCAction.action?acceptId="+
						acceptId+"&changeId="+changeId+"&customerId="+customerId+"&isCtToHc=1",
				{
					title : "红利领取形式变更",
					fresh : false,
					data : {}
				});
	}
	
	// 主险与附加险的退保原因 和投保人与业务员关系 绑定方法  
	function checkBoxChecked(checkResource,materBusID,ischecked){
		
		var beferTable = $("#table1",navTab.getCurrentPanel());
		
		// 主险前的复选框 不选中 
		if(materBusID!=0 && (!ischecked)){
			
			var beferFJTableTrs = $(beferTable).find("tbody tr[title='additionalInsurance']");
			// 循环所有的附加险  
			beferFJTableTrs.each(function(){
				
				var fjxmasterBusiItemId = $(this).find("td[id='masterBusiItemId']").text();
				
				if(fjxmasterBusiItemId==materBusID){
					$(this).find(".combox:eq(0) a").attr("disabled",false);
					$(this).find(".combox:eq(1) a").attr("disabled",false);
				}
				
			});
		}
		
		// 循环 主险 看那些主险被选中 
		var beferTableTrs = $(beferTable).find("tbody tr[title='primaryInsurance']");
		
		beferTableTrs.each(function(){
			// 判断选中的是哪个复选框 
			var checkedFlag = $(this).find("td input[type='checkbox']").is(':checked');
			
			var masterBusiItemId = $(this).find("td[id='masterBusiItemId']").text();
			
			// 如果主险被选中 则将该主险底下的附加险 的退保原因保持与主险一致 
			if(checkedFlag){
					var surrenderCauseHtml = $(this).find("td a[name='0']").html();
					var surrenderCauseVal = $(this).find("#surrenderCauseId option:selected").val();
					var surrenderCauseText = $(this).find("#surrenderCauseId option:selected").text();
					var agentHolderRelationHtml = $(this).find("td a[name='1']").html();
					var agentHolderRelationText = $(this).find("#agentHolderRelationId option:selected").text();
					var agentHolderRelationVal = $(this).find("#agentHolderRelationId option:selected").val();
					
					/** if(surrenderCauseHtml!="请选择"){
						$(this).find("td select[id='surrenderCauseId']").attr("disabled",true);
					} **/
					
					var beferFJTableTrs = $(beferTable).find("tbody tr[title='additionalInsurance']");
					// 循环所有的附加险  
					beferFJTableTrs.each(function(){
						var fjxmasterBusiItemId = $(this).find("td[id='masterBusiItemId']").text();
						
						if(fjxmasterBusiItemId==masterBusiItemId){
							// 页面刷新重新进入
							if(checkResource==1){
								// 循环某一个主险下的所有附加险 
								// 将此附加险的退保原因  和和业务员关系和主险保持一致
								$(this).find("#surrenderCauseId option:selected").text(surrenderCauseText);
								$(this).find("td select[id='surrenderCauseId']").attr("value",surrenderCauseVal);
								$(this).find("#agentHolderRelationId option:selected").text(agentHolderRelationText);
								$(this).find("td select[id='agentHolderRelationId']").attr("value",agentHolderRelationVal);
								
							}else{
								// 点击下拉列表
								// 将此附加险的退保原因  和和业务员关系和主险保持一致  
								$(this).find("td a[name='0']").text(surrenderCauseText);
								$(this).find("td select[id='surrenderCauseId']").attr("value",surrenderCauseVal);
								
								$(this).find("td a[name='1']").text(agentHolderRelationHtml);
								$(this).find("td select[id='agentHolderRelationId']").attr("value",agentHolderRelationVal);
								
							}
							
							if(surrenderCauseHtml!="请选择"){
								$(this).find(".combox:eq(0) a").attr("disabled",true);
							}else{
								$(this).find(".combox:eq(0) a").attr("disabled",false);
							}
							
							if(agentHolderRelationHtml!="请选择"){
								$(this).find(".combox:eq(1) a").attr("disabled",true);
							}else{
								$(this).find(".combox:eq(1) a").attr("disabled",false);
							}
							
								
						}
						
					});
			}
		});
		
	}

	
	//绑定主附加险选中规则-退保原因及关系关联
	function checkboxcheckedrolesfun(ischecked,dom,selBusiItemId){
		var mastervalue = $(dom).find("option:selected").val();
		var seleindex = $(dom).attr("name");
		var mastertext = $(dom).find("option:selected").text();
		$("#checkboxcheckedroles tr[title='additionalInsurance']").each(function(){
			var isCheck=$(this).find("td").eq(0).find("input:checkbox").is(":checked");
			if(selBusiItemId != null && selBusiItemId == $(this).find("td").eq(4).text() && isCheck){
			    if(ischecked){
					$(this).find(".combox:eq("+seleindex+") a").text(mastertext);
					$(this).find(".combox:eq("+seleindex+") a").attr("disabled",true);
					$(this).find(".combox:eq("+seleindex+") select").find("option[value='"+mastervalue+"']").attr("selected",true);
			     }else{
				    $(this).find(".combox:eq("+seleindex+") a").attr("disabled",false);
			    }
			}
		});
     }
	
	
	function isCK928(){//如果有928险种退保，选择是否特殊退保
		var isSpecial=false;
		var $trs=$("#table1",navTab.getCurrentPanel()).find("tbody tr");
	    $trs.each(function(){
	    	var bpCode=$(this).find("#_busiProdCode").text();
			var $checkIds=$(this).find("input:checkbox:checked");
			if($checkIds.size()>0&&(bpCode=='00928000'||bpCode=='00928100' || bpCode=='00Z01000'||bpCode=='00Z01100'
					|| bpCode=='00994000'||bpCode=='00995000'||bpCode=='00996000')){
				isSpecial=true;
			}	  
		});
	    if(isSpecial){
			$("#_ctCode928").show();
		}else{
			$("#_ctCode928").hide();
		}
	    return isSpecial;
	}
	
	//通过复选框的单击事件，来对退保标识进行赋值
	function selectItemItem(obj) {
		isCK928();//如果有928险种退保，选择是否特殊退保
		
		var $table = $("#table1",navTab.getCurrentPanel());
		var isMasterItemIndex = null;
		var busiItemIdIndex = null;
		var masterBusiItemIdIndex = null;
		var busiProdCode = null;
		var busiProdCodeIndex = null;//150568
		$table.find("th").each(function(i){
			if($(this).attr("colName") == 'isMasterItem'){
				isMasterItemIndex = i;
			} else if($(this).attr("colName") == 'busiItemId'){
				busiItemIdIndex = i;
			} else if($(this).attr("colName") == 'masterBusiItemId'){
				masterBusiItemIdIndex = i;
			}
			//150568 start
			else if($(this).attr("colName") == 'busiPrdCode'){
				busiProdCodeIndex = i;
			}
			//150568 end
		});
		//判断自己
		if($(obj).is(":checked")){//选中
			$(obj).parents("tr").attr("tr_saveStatus",'1');
		}else{
			$(obj).parents("tr").attr("tr_saveStatus",'0');
		}
		//判断所选险种是否是可选必须责任组，如果是必选责任组，则默认勾选同险种下的其它责任组；取消也是
			if($(obj).parents("tr").attr("title")=='primaryInsurance'){
				selBusiItemId = $(obj).parents("tr").find("td").eq(busiItemIdIndex).text();
				//下拉列表关联
				//$(obj).parents("tr").find("td select").each(function(i){
					checkBoxChecked(2,selBusiItemId,$(obj).parents("tr").find("td select").is(':checked'));
						//checkboxcheckedrolesfun($(obj).attr("checked"), this,selBusiItemId);
				//});
				//复选框和是否保存关联
				$("#table1 tr[title='additionalInsurance']",navTab.getCurrentPanel()).each(function(i){
					if(selBusiItemId != null && selBusiItemId == $(this).find("td").eq(masterBusiItemIdIndex).text()){
						busiProdCode = $(this).find("td").eq(busiProdCodeIndex).text();//150568 modify 6 to busiProdCodeIndex
						if($(obj).is(":checked")){
								$(this).find("td").eq(0).find("input:checkbox").attr("checked",true);
								if(!isSpecialBusiCode(busiProdCode)){
								$(this).find("td").eq(0).find("input:checkbox").attr("disabled",true);
								}
								$(this).attr("tr_saveStatus",'1');
						}else{
								$(this).find("td").eq(0).find("input:checkbox").attr("checked",false);
								if(!isSpecialBusiCode(busiProdCode)){
								$(this).find("td").eq(0).find("input:checkbox").attr("disabled",false);
								}
								$(this).attr("tr_saveStatus",'0');
						}
						
					}
				});
			}
		    //保存过一次以后，再进来，没有置灰，如果去除附加险勾选，及联去掉主险的勾。以防止主险单独退保
			if($(obj).parents("tr").attr("title")=='additionalInsurance'){
				selBusiItemId = $(obj).parents("tr").find("td").eq(masterBusiItemIdIndex).text();
				busiProdCode =  $(obj).parents("tr").find("td").eq(busiProdCodeIndex).text();//150568 modify 6 to busiProdCodeIndex
				//复选框和是否保存关联
				$("#table1 tr[title='primaryInsurance']",navTab.getCurrentPanel()).each(function(i){
					if(selBusiItemId != null && selBusiItemId == $(this).find("td").eq(busiItemIdIndex).text()){
						if(!$(obj).is(":checked")){
							if(!isSpecialBusiCode(busiProdCode)){
								$(this).find("td").eq(0).find("input:checkbox").attr("checked",false);
								$(this).attr("tr_saveStatus",'0');
							}
						}
					}
				});
			} 
		//处理tableJon标识 
	};
	
	function isSpecialBusiCode(busiProdCode){
		if(busiProdCode=='00563100'|| busiProdCode=='00958100'){
			return true;
		}else{
			return false;
		}
	}

	//计算正常退保金额
	function caluNormalPremium() {
		var itemIds = "";
		//判断是否选中
		var haveChecked = $("#table1 input[type='checkbox']:checked",
				navTab.getCurrentPanel()).size();
		if (haveChecked == 0) {
			alertMsg.info("请选择需要计算的信息！");
			return;
		} else {
			
			//判断主附加险
			//所有可选条目
			var allSelect=$("#table1 .myClass ",
					navTab.getCurrentPanel());
			//所有已选条目
			var isChecked=$("#table1 input[type='checkbox']:checked",
					navTab.getCurrentPanel());
			//如果可选条目大于已选条目
			if(allSelect.size()>isChecked.size()){
				//如果被退的是主险
				for(var a=0;a<isChecked.length;a++){
					if($(isChecked[a]).parents("tr").first().children().last().text()==""){
						//遍历所有可选条目，查找其附加险其附加险并未被选中
						for(var index=0;index<allSelect.length;index++){
							if($(allSelect[index]).parents("tr").first().children().last()
									.text()==$(isChecked[a]).parents("tr").first().children().eq(-3).text()
									&&$(allSelect[index]).parents("tr").first().
									children().first().children().first().attr("checked")!="checked"){
								alertMsg.info("选择主险退保的同时需要同时选择对应的附加险！");
								return;
							}
						}
					}
				}
			}
			
			$("#table1 input[type='checkbox']:checked",navTab.getCurrentPanel()).each(function() {
				if (itemIds == "") {
					itemIds = $(this).val();
				} else {
					itemIds = itemIds + "," + $(this).val();
				}
			});
			
		};
		
		//校验是否选择了退保原因 和 投保人与业务员关系
		var $table = $("#table1",navTab.getCurrentPanel());
		var surrenderCauseIndex;
		var agentHolderRelIndex;
		$table.find("th").each(function(i){
			if($(this).attr("colName") == 'surrenderCause'){
				surrenderCauseIndex = i;
			} else if($(this).attr("colName") == 'agentHolderRelation'){
				agentHolderRelIndex = i;
			}
		});
		
		var returnflay = 0; //阻断标记
		$("#table1 input[type='checkbox']:checked",navTab.getCurrentPanel()).each(function(i){
			if(surrenderCauseIndex != null && $(this).parents("tr").find("td").eq(surrenderCauseIndex).find("select").val() ==''){
				alertMsg.info("请选择退保原因！");
				returnflay = 1;
			}
			/*	if(agentHolderRelIndex != null && $(this).parents("tr").find("td").eq(agentHolderRelIndex).find("select").val() ==''){
				alertMsg.info("请选择投保人与业务员关系！");
				returnflay = 1;
			}*/
		});
		
		if(returnflay == 1){
			return;
		}
		
		var _table = $("#table1",navTab.getCurrentPanel());
		var _tableJs = _cs_tableToJson(_table);
		$("#saveTableInfo",navTab.getCurrentPanel()).attr('value', _tableJs);
		var action = "${ctx }/cs/serviceitem_ct/caluNormalPremium_PA_csEndorseCTAction.action";
		var onsubmit = "return navTabSearch(this,'showPreiumDetail');";
		$("#itemIds",navTab.getCurrentPanel()).attr('value', itemIds);
		$("#premAgreeSurrendForm",navTab.getCurrentPanel()).attr('action', action);
		$("#premAgreeSurrendForm",navTab.getCurrentPanel()).attr('onsubmit', onsubmit);
		$("#premAgreeSurrendForm",navTab.getCurrentPanel()).submit();

	}
	
	//计算并保存信息
	function calSaveChange(){
		var itemIds = "";
		//判断是否选中
		debugger;
		var haveChecked = $("#table1 input[type='checkbox']:checked",
				navTab.getCurrentPanel()).size();
		if (haveChecked == 0) {
			alertMsg.info("请选择需要计算的信息！");
			return;
		} else {
			//判断主附加险
			//所有可选条目
			var allSelect=$("#table1 .myClass ",
					navTab.getCurrentPanel());
			//所有已选条目
			var isChecked=$("#table1 input[type='checkbox']:checked",navTab.getCurrentPanel());
			//如果可选条目大于已选条目
			if(allSelect.size()>isChecked.size()){
				//如果被退的是主险
				for(var a=0;a<isChecked.length;a++){								
					if($(isChecked[a]).parents("tr").first().children().last().text()==""){
						//遍历所有可选条目，查找其附加险其附加险并未被选中
						for(var index=0;index<allSelect.length;index++){
							if($(allSelect[index]).parents("tr").first().children().last()
									.text()==$(isChecked[a]).parents("tr").first().children().eq(-3).text()
									&&$(allSelect[index]).parents("tr").first().
									children().first().children().first().attr("checked")!="checked"){
								alertMsg.info("选择主险退保的同时需要同时选择对应的附加险！");
								return;
							}
						}
					}
				}
			}
			
			$("#table1 input[type='checkbox']:checked",navTab.getCurrentPanel()).each(function() {
				if (itemIds == "") {
					itemIds = $(this).val();
				} else {
					itemIds = itemIds + "," + $(this).val();
					/* var busiProdCode=$(this).parents("tr").find("td").eq(6).text();
					var isCancerFlag=$(this).parents("tr").find("td").eq(20).text();
				if(isCancerFlag!=null&&isCancerFlag=="1"&&busiProdCode=="00389000"){
					alertMsg.info("389附加高额交通意外保险已经发生保险金给付，不允许退保。"+
					"如您强求退保，389附加高额交通意外保险退保时现价为0");
					} */
				}
			});
			
		};
		
		/** var $table = $("#table1",navTab.getCurrentPanel());
		var surrenderCauseIndex;
		var agentHolderRelIndex;
		$table.find("th").each(function(i){
			if($(this).attr("colName") == 'surrenderCause'){
				surrenderCauseIndex = i;
			} else if($(this).attr("colName") == 'agentHolderRelation'){
				agentHolderRelIndex = i;
			}
		}); **/
		
		var returnflay = 0; //阻断标记
		
		//校验是否选择了退保原因 和 投保人与业务员关系
		var beferTable = $("#table1",navTab.getCurrentPanel());
		var beferTableTrs = $(beferTable).find("tbody tr");
		beferTableTrs.each(function(){
			// 判断选中的是哪个复选框 
			var checkedFlag = $(this).find("td input[type='checkbox']").is(':checked');
			
			if(checkedFlag){
				// 退保原因的值 
				var surrenderCauseVal = $(this).find("td a[name='0']").html();
				if(surrenderCauseVal=="请选择" || surrenderCauseVal==null){
					alertMsg.info("请选择退保原因！");
					returnflay = 1;
				}
				// 与业务员关系的值 
				/*var agentHolderRelationVal =$(this).find("td a[name='1']").html();
				if(agentHolderRelationVal=="请选择" || agentHolderRelationVal==null ){
					alertMsg.info("请选择投保人与业务员关系！");
					returnflay = 1;
				}*/
			}
			
		});
		
		if(returnflay == 1){
			return;
		}
		var _table = $("#table1",navTab.getCurrentPanel());
		var _tableJs = _cs_tableToJson(_table);
		$("#saveTableInfo",navTab.getCurrentPanel()).attr('value', _tableJs);
 		var action = "${ctx }/cs/serviceitem_ct/saveSurrender_PA_csEndorseCTAction.action";
 		var onsubmit = "return navTabSearch(this,'showAFPage');";
		$("#itemIds",navTab.getCurrentPanel()).attr('value', itemIds);
		$("#premAgreeSurrendForm",navTab.getCurrentPanel()).attr('action', action);
		$("#premAgreeSurrendForm",navTab.getCurrentPanel()).attr('onsubmit', onsubmit);
		//150568 start
		var $table = $("#table1",navTab.getCurrentPanel());
		var busiProdCodeIndex = null;
		$table.find("th").each(function(i){
			if($(this).attr("colName") == 'busiPrdCode'){
				busiProdCodeIndex = i;
			}
		});
		//150568 end
		//（险种简称）已经发生保险金给付，不允许解除合同。如您强求解除合同，（险种简称）解除合同时现价为0。
		var  bxj=null;
		//（险种简称）已经发生保险金给付，不允许解除合同。阻断提示
		var shMsg=null;
		$("#table1 input[type='checkbox']:checked",
				navTab.getCurrentPanel()).each(function(){
					var busiCode=$(this).parent().parent().find("td").eq(busiProdCodeIndex).text();//150568 modify 6 to busiProdCodeIndex
					var isCancerFlag= $(this).parent().parent().find("td").last().text();
					if(isCancerFlag!=null&&isCancerFlag=="1"){
							if(busiCode=="00389000"){
								bxj="附加高额交通意外已经发生保险金给付，不允许解除合同。"+
										"如您强求解除合同，附加高额交通意外解除合同时现价为0";
						 	}else if(busiCode=="00958100"){
						 		bxj="康健华贵B已经发生保险金给付，不允许解除合同。"+
										"如您强求解除合同，康健华贵B解除合同时现价为0";
						 	}else if(busiCode=="00963000"){ 
						 		bxj="附加住院补贴已经发生保险金给付，不允许解除合同。"+
										"如您强求解除合同，附加住院补贴解除合同时现价为0";
						 	}else if(busiCode=="00976000"){
						 		bxj="附加心脑血管已经发生保险金给付，不允许解除合同。"+
										"如您强求解除合同，附加心脑血管解除合同时现价为0";
						 	}else if(busiCode=="00863000"){
						 		bxj="附加心脑血管A已经发生保险金给付，不允许解除合同。"+
								"如您强求解除合同，附加心脑血管解除合同时现价为0";
				 			}else if(busiCode=="00831000"){
						 		bxj="附加门急诊已经发生保险金给付，不允许解除合同。"+
								"如您强求解除合同，附加门急诊解除合同时现价为0";
				 			}else if(busiCode=="00581000"){
						 		bxj="附加自驾车意外已经发生保险金给付，不允许解除合同。"+
								"如您强求解除合同，附加自驾车意外解除合同时现价为0";
				 			}else if(busiCode=="00839000"){
				 				var ischecked1 = $("#checkboxcheckedroles tr[title='primaryInsurance'] :checkbox").attr("checked");
				 				if(ischecked1){
				 					bxj="附加心脑血管重症监护津贴已经发生保险金给付，不允许解除合同。"+
									"如您强求解除合同，附加心脑血管重症监护津贴解除合同时现价为0";
				 				}
				 			}else if(busiCode=="00844000"){
						 		bxj="附加恶性肿瘤疾病保险已经发生保险金给付，不允许解除合同。"+
								"如您强求解除合同，附加恶性肿瘤疾病保险解除合同时现价为0";
				 			}else if(busiCode=="00A15000"){
						 		bxj="附加恶性肿瘤A款疾病保险已经发生保险金给付，不允许解除合同。"+
								"如您强求解除合同,附加恶性肿瘤A款疾病保险解除合同时现价为0";
				 			}else if(busiCode=="00557000"){
				 				shMsg="上海医保卡重疾已经发生保险金给付，不允许解除合同。";
				 			}else if(busiCode=="00558000"){
				 				shMsg="上海医保卡住院自费医疗已经发生保险金给付，不允许解除合同。";
				 			}else if(busiCode=="00842000"){
				 				shMsg="上海医保账户医疗已经发生保险金给付，不允许解除合同。";
				 			}else if(busiCode=="00843000"){
				 				shMsg="上海医保账户意外医疗已经发生保险金给付，不允许解除合同。";
				 			}
				     }
		});
		//上海医保卡阻断提示
		if(shMsg!=null){
			alertMsg.error(shMsg);
			return;
		}
		
			
		//非阻断提示
		if(bxj!=null){
			alertMsg.confirm(bxj,
					{
						okCall : function(){
							check808Relation();
						}
					});
		}else{
			check808Relation();
		}
		
		
	}
	
	function checkNewRule(){
		debugger;
		var acceptId=$("#acceptId",navTab.getCurrentPanel()).val();
		var flg = false;
		$.ajax({
			url:"${ctx}/cs/csAccept/checkNewRuleCT_PA_csEntryAction.action",
			type:"post",
			dataType:'text',
			async: false,
			data:"acceptId=" + acceptId,
			success:function(data){
				console.log(data);
				var json = DWZ.jsonEval(data);
				
				console.log(json);
				if(json.statusCode == '300'){
					alertMsg.error(json.message);
					flg = true;
					
				}
			}
		});
		return flg;
	}
	
	function check808Relation(){
		var policyCode = "";
		var busiItemId = "";
		$("#table1 input[type='checkbox']:checked",navTab.getCurrentPanel()).each(function(){
			policyCode = policyCode + $(this).parent().parent().find("td").eq(5).text() + ",";
			busiItemId = busiItemId + $(this).parent().parent().find("td").eq(3).text() + ",";
		});
		$.ajax({
			url:"${ctx}/cs/csAccept/checkRelationFor808_PA_csEntryAction.action",
			type:"post",
			dataType:'text',
			data:"policyCode=" + policyCode +"&busiItemId=" + busiItemId,
			success:function(data){
				console.log(data);
				var json = jQuery.parseJSON(data);
				console.log(json);
				if(json.statusCode == 200){
					alertMsg.confirm(json.message,
							{
								okCall : function(){
									saveSubmitAjax();
								}
							});
				}else{
					saveSubmitAjax();
				}
			}
		});
	}
	
	function saveSubmitAjax(){
		debugger;
		var isCode928=isCK928();
		var isSYFlag = isSY();//如果有928险种退保，选择是否特殊退保
		if(isCode928&&$("#_specialFlag").val()==''){
			alertMsg.error("请选择是否特殊退保！");
			return;
		}
		if(isSYFlag){
			alertMsg.error("税延产品需要退保全部产品,请对所有产品进行退保操作！");
			return;
		}
		$.ajax({
			type : "post",
			url : "${ctx }/cs/serviceitem_ct/saveSurrender_PA_csEndorseCTAction.action",
			data : $("#premAgreeSurrendForm", navTab.getCurrentPanel()).serialize(),
			success : function(data) {
				var json = DWZ.jsonEval(data);
				if (json.statusCode == DWZ.statusCode.error) {
					alertMsg.error(json.message);
				} else if (json.statusCode == DWZ.statusCode.timeout) {
					DWZ.loadLogin();
				}else{
					alertMsg.correct("保存成功！");
					$('#showCTDetailAfter', navTab.getCurrentPanel()).html(data).initUI();
					 navTab.reload();
				}
			},
		});
	}
	
	//根据页面上录入变更信息记录新的变更信息
	function saveChange() {
		//判断是否选中
		var haveChecked = $("#table1 input[type='checkbox']:checked",
				navTab.getCurrentPanel()).size();
		if (haveChecked == 0) {
			alertMsg.info("未选择公司解约的险种，请确认！");
			return;
		}else {
			//判断主附加险
			//所有可选条目
			var allSelect=$("#table1 .myClass ",
					navTab.getCurrentPanel());
			//所有已选条目
			var isChecked=$("#table1 input[type='checkbox']:checked", navTab.getCurrentPanel());
			//如果可选条目大于已选条目
			if(allSelect.size()>isChecked.size()){
				//如果被退的是主险
				for(var a=0;a<isChecked.length;a++){
					if($(isChecked[a]).parents("tr").first().children().last().text()==""){
						//遍历所有可选条目，查找其附加险其附加险并未被选中
						for(var index=0;index<allSelect.length;index++){
							if($(allSelect[index]).parents("tr").first().children().last()
									.text()==$(isChecked[a]).parents("tr").first().children().eq(-3).text()
									&&$(allSelect[index]).parents("tr").first().
									children().first().children().first().attr("checked")!="checked"){
								alertMsg.info("选择主险退保的同时需要同时选择对应的附加险！");
								return;
							}
						}
					}
				}
			}
		}

		alertMsg.confirm("请确认是否需要保存录入信息？",{
			okCall:function(){
		//把选中的数据传入到后台
		var $dataTable = $("#table1", navTab.getCurrentPanel());
		var $trs = $dataTable.find("tbody tr");
		$trs.each(function() {
			var $tds = $(this).find("td");
			
			if ($tds.eq(0).children().attr("checked") == "checked") {
				$(this).attr("tr_saveStatus", "1");
			}else{
				$(this).attr("tr_saveStatus", "0");
			}

		});
 
		var _table = $("#norPremDetailTable",navTab.getCurrentPanel());
		var _tableJs = _cs_tableToJson(_table);
		$("#saveTableInfo",navTab.getCurrentPanel()).attr('value', _tableJs);
		var action = "${ctx }/cs/serviceitem_ct/saveSurrender_PA_csEndorseCTAction.action";
		var onsubmit = "return navTabSearch(this,'showAFPage');";
		$("#premAgreeSurrendForm",navTab.getCurrentPanel()).attr('action', action);
		$("#premAgreeSurrendForm",navTab.getCurrentPanel()).attr('onsubmit', onsubmit);
		$("#showAFPage", navTab.getCurrentPanel()).show();
		$("#premAgreeSurrendForm",navTab.getCurrentPanel()).submit();
		}});

	};
	/* $(function(){
		var haveChecked = $("#table1 input[type='checkbox']:checked",
				navTab.getCurrentPanel()).size();
		if (haveChecked > 0 && ${queryFlag != 1 }){//在查询页面显示时不做提示
			caluNormalPremium();
		}
	}); */
	function isSY(){//如果有928险种退保，选择是否特殊退保
		var $trs=$("#table1",navTab.getCurrentPanel()).find("tbody tr");
		var flag = false;
		$trs.each(function(){
			debugger;
			var bpCode=$(this).find("#_busiProdCode").text();
			var $checkIds=$(this).find("input:checkbox:checked");
			if($checkIds.size() == 0 && (bpCode=='00994000'||bpCode=='00995000'||bpCode=='00996000')){
				flag = true; 
			}
		});
		return flag;
	}
	
</script>