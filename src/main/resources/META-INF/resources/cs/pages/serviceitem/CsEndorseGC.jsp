<%@ page language="java" contentType="text/html; charset=utf-8"
	pageEncoding="utf-8" import="java.util.*,java.text.*"%>
<%@ taglib uri="/struts-tags" prefix="s"%><%@include file="/udmpCommon.jsp"%>
<%@ taglib uri="http://www.git.com.cn/taglib/udmp/field" prefix="Field"%>
<%@ include file="/cs/pages/common/jsp/common_ctx.jsp"%>
<script type="text/javascript" src="${ctx}/cs/js/cs_common.js"></script>
<link rel="stylesheet" href="${ctx}/cs/css/public.css" type="text/css"> 

<!-- ********帮助菜单***********begin********* --> 
<s:include value="/cs/pages/common/jsp/helpMenu.jsp"></s:include>
<!-- *********帮助菜单***********end********* -->
<!-- 步骤标识 -->
<s:include value="csEndorseProgress.jsp" />

<div class="pageFormInfoContent" layoutH="170">

	<input type="hidden"  name="specialAccountFlag"  id="specialAccountFlag" value="${csEndorseGCVO.csEndorseGCndVOs[0].specialAccountFlag}"/>
			
	<div class="divfclass">
		<h1>
			<img src="${ctx}/cs/img/icon/tubiao.png">生存金领取形式变更
		</h1>
	</div>
		<div class="pageContent">
		
				<div class="pageFormInfoContent">
				<!--客户的5项基本信息（姓名、出生日期、证件类型、证件号码、性别） -->
			    <s:include value="customerInfo_list.jsp" />
					<!--客户id-->
					<input type="hidden" id="customerId" name="customerId"
						value="${customerId}" />
					<!--保全变更Id-->
					<input type="hidden" id="changeId" name="changeId"
						value="${changeId}" />
					<!--保全受理号 -->
					<input type="hidden" id="acceptId" name="acceptId"
						value="${acceptId}" />
					<!--客户id-->
					<input type="hidden" id="insuredId" name="insuredId"
						value="${csCustomerVO.insuredId}" />
					<!--保全变更Id-->
					<input type="hidden" id="insuredName" name="insuredName"
						value="${csCustomerVO.insuredName}" />
				    <!--判断是否为自行领取-->
					<input type="hidden" id="isShow" name="isShow"
						value="${isShow}" />
				    <!--判断是否保存-->
					<input type="hidden" id="isSaved" name="isSaved"
						value="${isSaved}" />
				</div>

			<!--险种列表展示 -->
			   <div class="divfclass">
					<h1>
						<img src="cs/img/icon/tubiao.png">保单险种列表信息
					</h1>
				</div>
				<!-- <h1>保单险种列表信息</h1> -->
				<div class="main_FormContent">
					<!--json字符串 -->
					<input id="jsons" name="jsons" type="hidden" value="" />
					<table id="aaTableInform" name="aaTableInform" class="list"
						width="100%">
						<thead>
							<tr>
								<th colName="policyCode">保单号</th>
								<th colName="busiProdCode">险种代码</th>
								<th colName="busiPrdId" style="display: none"></th>
								<th>险种名称</th>
								<th>险种标识</th>
								<th colName="amount">基本保额</th>
								<th colName="customerId" style="display: none"></th>
								<th colName="changeId" style="display: none"></th>
								<th colName="policyChgId" style="display: none"></th>
								<th colName="acceptId" style="display: none"></th>
								<th colName="productId" style="display: none"></th>
								<th colName="busiItemId" style="display: none"></th>
								<th colName="oldSurvivalMode">生存金领取形式</th>
								<th colName="oldSurvivalWMode">收付费方式</th>
								<th colName="oldBankCode">银行代码/名称</th>
								<th colName="oldBankAccount">银行账号</th>
								<th colName="oldAccoName">户名</th>
							</tr>
						</thead>
						<tbody align="center" id="aaTableInformTbody">
							<s:iterator value="csEndorseGCVO.csEndorseGCndVOs" status="st">
								<tr tr_saveStatus="1">
									<td><input type="radio"  style="float:left" name="listId" value="${busiItemId}" onclick="selectMainBusiness(this)"/>
										${policyCode }
									</td>
									<td>${busiProdCode }</td>
									<td style="display: none">${busiPrdId }</td>
									<td><Field:codeValue
											tableName="APP___PAS__DBUSER.T_BUSINESS_PRODUCT"
											value="${busiPrdId}" /></td>
									<td>
										<s:if test="coverPeriodType==0">长期险</s:if>
										<s:else>短期险</s:else>
									</td>
									<td>${amount}</td>
									<%-- <td><Field:codeValue
											tableName="APP___PAS__DBUSER.T_SURVIVAL_MODE"
											value="${oldSurvivalMode}" /></td> --%>
									<%-- <td>
									<s:if test="oldSurvivalMode==1&&oldSurvivalWMode!=null">
									<Field:codeValue
											tableName="APP___PAS__DBUSER.T_SURVIVAL_W_MODE"
											value="${oldSurvivalWMode}" />
									</s:if>
									<s:else>
									   <Field:codeValue
											tableName="APP___PAS__DBUSER.T_SURVIVAL_MODE"
											value="${oldSurvivalMode}" />
									</s:else>
									</td>
									<td>${issueBankName }</td>
									<td>${bankCode }</td>
									<td>${bankAccount }</td>
									<td>${accoName }</td>
									<td>
										<select onchange="newChanglq(this)">
											<option>请选择</option>
										     <s:iterator value="bonusModes" var="plan" >
	                                                 <!-- <option value="1" <s:if test="newSurvivalMode == 1"> selected </s:if>>支取</option>
	                                                 <option value="2" <s:if test="newSurvivalMode == 2"> selected </s:if>>累积生息</option>
	                                                 <option value="3" <s:if test="newSurvivalMode == 3"> selected </s:if>>抵缴保费</option> -->
	                                                  <s:if test="#plan==1"><option value="1" <s:if test="newSurvivalMode == 1"> selected </s:if>>支取</option></s:if>
                                                      <s:if test="#plan==2"><option value="2" <s:if test="newSurvivalMode == 2"> selected </s:if>>累积生息</option></s:if>
                                                       <s:if test="#plan==3"><option value="3" <s:if test="newSurvivalMode == 3"> selected </s:if>>抵缴保费</option></s:if>
	                                          </s:iterator>
                                         </select>	
									</td> --%>
									<td style="display: none">${customerId }</td>
									<td style="display: none">${changeId }</td>
									<td style="display: none">${policyChgId }</td>
									<td style="display: none">${acceptId }</td>
									<td style="display: none">${productId }</td>
									<td style="display: none">${busiItemId }</td>
									<td>
									<s:if test="oldSurvivalMode==2 || oldSurvivalMode==4">
										<Field:codeValue tableName="APP___PAS__DBUSER.T_SURVIVAL_MODE" value="${oldSurvivalWMode}" />
									</s:if>
									<s:elseif test="oldSurvivalMode==1 && oldSurvivalWMode==1">
										现金领取-需办理保全项目领取
									</s:elseif>
									<s:elseif test="oldSurvivalMode==1 && (oldSurvivalWMode==2 || oldSurvivalWMode==3)">
										现金领取-约定银行转账
									</s:elseif>
									<td><s:if test="oldSurvivalMode==1 && (oldSurvivalWMode==2 || oldSurvivalWMode==3)">
										<s:if test="oldSurvivalWMode == 2">银行转账（制返盘）</s:if>
		                                <s:if test="oldSurvivalWMode == 3">网上银行</s:if>
									</s:if><s:else>--</s:else></td>
									<s:else>--</s:else></td>
									<td><s:if test="bankCode!=null && bankCode != ''">
										${bankCode}/${issueBankName}
									</s:if><s:else>--</s:else></td>
									<td><s:if test="bankAccount!=null && bankAccount != ''">
										${bankAccount }
									</s:if><s:else>--</s:else></td>
									<td><s:if test="accoName!=null && accoName != ''">
										${accoName }
									</s:if><s:else>--</s:else></td>
								</tr>
							</s:iterator>
						</tbody>
					</table>
							<!-- <div class="pageFormdiv">
								<button type="button" onclick="saveNewMsg()" class="but_blue">保存</button>
							</div> -->
				</div>


			<!-- 変更信息錄入 -->
			<form action="" id="formBgMsg">
					 <div class="divfclass">
						<h1>
							<img src="cs/img/icon/tubiao.png">变更信息录入
						</h1>
					 </div>
					<div class="panelPageFormContent" id="formBgMsg1">
							<!--变更信息的json字符串 -->
						    <input id="hcMSG" name="hcMSG" type="hidden" value="" />
						   
						    <div class="tabdivclass">
							<table id="hlGetTable" name="hlGetTable" class="list" width="100%">
								<thead>
									<tr>
										<th colName="newSurvivalMode" inputType="select"
													style="width: 150px">生存金领取形式</th>
										<th colName="newSurvivalWMode" inputType="select">收付费方式</th>
										<th colName="bankAccountShow">银行账号</th>
										<th colName="accoNameShow">户名</th>
										<th colName="bankCodeShow">银行代码/名称</th>
										<th colName="policyCode"  style="display: none">保单号</th>
										<th colName="busiProdCode"  style="display: none">险种代码</th>
										<th colName="busiPrdId" style="display: none">busiPrdId</th>
										<th colName="accountId" style="display: none">账号id</th>
										<th colName="customerId" style="display: none">领取人ID</th>
										<th colName="acceptId" style="display: none">acceptId</th>
										<th colName="itmeId" style="display: none">itemId</th>
										<th colName="changeId" style="display: none">changeId</th>
										<th colName="amount" style="display: none">amount</th>
										<th colName="policyPrdFlag" style="display: none">policyPrdFlag</th>
										<th colName="busiItemId" style="display: none">busiItemId</th>
										<!-- <th colName="isNeedUpdate" style="display: none">isNeedUpdate</th> -->
										<th colName="banklist" style="display: none">银行代码/账号</th>
										<th colName="specialAccountFlag" style="display: none">个人养老金保单标识</th>
										<th colName="bankAccount" style="display: none">银行账号</th>
										<th colName="accoName" style="display: none">户名</th>
										<th colName="bankCode" style="display: none">银行代码</th>
										<th colName="issueBankName" style="display: none">银行名称</th>
										<th colName="correspondentNo" style="display: none">联行号</th>
									</tr>
								</thead>
								<tbody align="center" id="policyBankMsg">
									<s:iterator value="csEndorseGCVO.csEndorseGCndVOs" status="st" var='itemVO'>
										<tr align="center" tr_saveStatus="1">
											<td>
												<select onchange="newChanglq(this)">
													<option>请选择</option>
													<option value="1" <s:if test="newSurvivalMode == 1 && newSurvivalWMode == 1"> selected </s:if>> 现金领取-需办理保全项目领取</option>
													<option value="0" <s:if test="newSurvivalMode == 1 && (newSurvivalWMode == 2 || newSurvivalWMode == 3)"> selected </s:if>> 现金领取-约定银行转账</option>
													<option value="2" <s:if test="newSurvivalMode == 2"> selected </s:if>>累积生息</option>
													<option value="3" <s:if test="newSurvivalMode == 3"> selected </s:if>>抵缴保费</option>
													<option value="4" <s:if test="newSurvivalMode == 4"> selected </s:if>>转万能账户</option>													 
		                                         </select>	
											</td>
											<td id="payMode">
												<Field:codeTable nullOption="true" name="payMode" id="payModeSelect" tableName="APP___PAS__DBUSER.T_SURVIVAL_W_MODE"
															value="${newSurvivalWMode}" onChange="newModeChange(this)" whereClause="mode_code != 1"></Field:codeTable>
											</td>
											<td>
												<select id="banklistNew" name="banklistNew"  <s:if test="newSurvivalWMode != 2 ">style="display: none" </s:if>  onchange="getBankCode(this)"></select>
												<div class="banklistInput">
											    	<input name="banklistInput"<s:if test="newSurvivalWMode != 3 ">style="display: none"</s:if> class="bankAccountInput"  onchange="checkAccountBank(this)" value=""/>
											    	<font style="color: red; font-size: 11px;" id="checkAccountCodeDIV"></font></div>
											</td>
											<td>
												<input type="text" name="accoNameInput" <s:if test="newSurvivalWMode == 2 ">disabled=true</s:if> onchange="checkAccountBank(this)" value="" />
												<font style="color: red; font-size: 11px;" ></font>
											</td>
											<td>
												<input style="width: 280px;" type="text" name="bankCodeReadOnly" value="${bankCode}" <s:if test="newSurvivalWMode != 2">style="display: none"</s:if> readOnly = true
												value="<Field:codeValue tableName="APP___PAS__DBUSER.T_BANK" value="${bankCode}"/>"/>
												
												<div name="banklistFind" <s:if test="newSurvivalWMode != 3 ">style="display: none"</s:if>>
											    	<input class="inputOrg1" name="org1.orgNum" value="" type="hidden"/><!-- 联行号 -->
											    	<input class="inputCreator" name= "org1.creator" value="" type="hidden"/><!-- 联行号对应的银行代码 -->
											   		<input class="required" name="org1.orgName" type="text" postField="keyword" readonly="readonly"
									suggestFields="orgNum,orgName,creator" suggestUrl="cs/pages/csEntry/districtBankCodeQuery.jsp" lookupGroup="org1"/>
													<a class="btnLook" href="${ctx}/cs/csEntry/selectReturnNumber_PA_csEntryAction.action" 
													lookupGroup="org1" style="float: right;">查找带回</a>
											    </div>
											</td>

											<td style="display: none">${policyCode }</td>
											<td style="display: none">${busiProdCode }</td>
											<td style="display: none">${busiPrdId }</td>
											<td style="display: none" id="_accountId">${accountId}</td>
											<td style="display: none">${customerId}</td>
											<td style="display: none">${acceptId}</td>
											<td style="display: none">${itemId}</td>
											<td style="display: none">${changeId}</td>
											<td style="display: none">${amount}</td>
											<td style="display: none">${policyPrdFlag}</td>
											<td style="display: none" id="busiItemId">${busiItemId}</td>
											<%-- <td style="display: none"><input type="text" id="isNeedUpdate" name = "isNeedUpdate"
												value="${isNeedUpdate}" /></td> --%>
											<td name="banklist" style="display: none"><select
											<s:if test="survivalWMode!=2||survivalWMode!=3">disabled=true</s:if>
										    name="banklist" onchange="getBankCode(this)"></select>
										    </td>
										    <td style="display: none">${specialAccountFlag}</td>
										    <td style="display: none">${bankAccount}</td>
										    <td style="display: none">${accoName}</td>
										    <td style="display: none">${bankCode}</td>
										    <td style="display: none">${issueBankName}</td>
										    <td style="display: none" class="correspondentNoTd" name="correspondentNo">${correspondentNo}</td>
										</tr>
									</s:iterator>
								</tbody>
							</table>
							</div>
						<!-- 銀行信息 -->
						<%-- <div id="bankAccount" style="display: none">
							<s:include value="/cs/pages/common/jsp/CsBankAccountsave.jsp"></s:include>
						</div> --%>
						
						<div class="pageFormdiv">
							<button class="but_blue" type="button" onclick="saveHCMsg()">保存</button>
						</div>
						 <!-- 銀行信息 -->
						<div id="bankAccount" style="display: none">
							<s:include value="/cs/pages/common/jsp/CsBankAccountpubsave.jsp"></s:include>
						</div>
					</div>
			</form>

			<!-- 应领未领红利 -->
				 <div class="divfclass">
					<h1>
						<img src="cs/img/icon/tubiao.png">应领未领生存金年金信息
					</h1>
				</div>
				<div class="tabdivclass">
					<table id="baseInfo" class="list" width="100%">
						<thead>
							<tr>
								<th>保单号</th>
								<th>险种代码</th>
								<th>应付项目</th>
								<th>应付日期</th>
								<th>应付金额</th>
								<th>生调标识</th>
								<th>是否参与本次变更</th>
							</tr>
						</thead>
						<tbody>
							<s:iterator value="csPayDueVOList">
							<tr align="center"
								<s:if test="queryFlag==1">disabled="disabled"</s:if>>
								<td>${policyCode }</td>
								<td>${busiProdCode }</td>
								<td>年金</td>
								<td><s:date name="payDueDate" format="yyyy-MM-dd" /></td>
								<td>${feeAmount }</td>
								<td>
								<s:if test="survivalInvestFlag==1">生调</s:if>
								<s:else>非生调</s:else>
								</td>
								<td>参与</td>
							</tr>
							</s:iterator>
						</tbody>
					</table>
				</div>
			<!-- </div> -->

			<!-- 变更后的信息 -->
			<div id="isShowGCMsg" style="display: none"> 
				<div class="divfclass">
					<h1>
						<img src="cs/img/icon/tubiao.png">变更后信息
					</h1>
				</div>
				<div class="tabdivclass"> 
					<table class="list" width="100%" id='tableAfterMsg'>
						<thead>
							<tr>
								<th>保单号</th>
								<th>险种代码</th>
								<th>险种名称</th>
								<th>基本保额</th>
								<th>生存金领取形式</th>
								<th>收付费方式</th>
								<th>银行代码/名称</th>
								<th>银行账号</th>
								<th>户名</th>
							</tr>
						</thead>
						<tbody align="center" id="changeBankMsg">
							<s:iterator value="csEndorseGCVOAF.csEndorseGCndVOs" status="st">
								<tr align="center">
									<td>${policyCode }</td>
									<td>${busiProdCode }</td>
									<td><Field:codeValue
											tableName="APP___PAS__DBUSER.T_BUSINESS_PRODUCT"
											value="${busiPrdId}" /></td>
									<td>${amount}</td>
									<td>
									<s:if test="newSurvivalMode==2 || newSurvivalMode==4">
										<Field:codeValue tableName="APP___PAS__DBUSER.T_SURVIVAL_MODE" value="${newSurvivalMode}" />
									</s:if>
									<s:elseif test="newSurvivalMode==1 && (newSurvivalWMode==2 || newSurvivalWMode==3)">
										现金领取-约定银行转账
									</s:elseif>
									<s:elseif test="newSurvivalMode==1">
										现金领取-需办理保全项目领取
									</s:elseif>
									<s:else>--</s:else></td>
									<td><s:if test="newSurvivalMode==1 && newSurvivalWMode==2">
	                                     	银行转账（制返盘）
									</s:if>
									<s:elseif test="newSurvivalMode==1 && newSurvivalWMode==3">
		                                 	网上银行
									</s:elseif><s:else>--</s:else></td>
									<td><s:if test="bankCode!=null||issueBankName!=null"> ${bankCode}/${issueBankName}</s:if></td>
									<td>${bankAccount }</td>
									<td>${accoName }</td>
								</tr>
							</s:iterator>
						</tbody>
					</table>
				</div>
			</div>
		</div>
</div>
    <s:if test="queryFlag!=1">
	<s:include value="/cs/pages/serviceitem/serviceItemBottomBar.jsp"></s:include>
	</s:if>
<script type="text/javascript">


$(function(){
	var isShow = $("#isShow", navTab.getCurrentPanel()).val();
	if(isShow != '1'){
		$("#formBgMsg", navTab.getCurrentPanel()).hide();
		$("#formBgMsg1", navTab.getCurrentPanel()).hide();
		$("#bankAccount", navTab.getCurrentPanel()).hide();
		$("#infoTable", navTab.getCurrentPanel()).attr("style", false);
	}
	
	
	var isSaved = $("#isSaved",navTab.getCurrentPanel()).val();
	if(isSaved == '1'){
		$("#isShowGCMsg",navTab.getCurrentPanel()).show();
	}
	
	//修改GC初始化领取形式为银行转账和网上支付时候将客户银行账号显示 renxiaodi
	var $tr = $("#policyBankMsg", navTab.getCurrentPanel()).find("tr");
	for (var k = 0 ; k < $tr.length; k++ ) {
		var payMode = $("#policyBankMsg", navTab.getCurrentPanel()).find("tr:eq("+k+")").find('td:eq(1)').find("select[name='payMode']", navTab.getCurrentPanel()).val();
		if (payMode == 2) {
			$("#bankAccount", navTab.getCurrentPanel()).show();
			$("select[name='banklist']").removeAttr("disabled");
		} else {
			$("#bankAccount", navTab.getCurrentPanel()).hide();
			$("select[name='banklist']").attr("disabled","true");
			$("#infoTable", navTab.getCurrentPanel()).attr("disabled", false);
		}
	}
	
	var busiItemId = $("input[name='listId']:checked").attr();
	var $tr = $("#policyBankMsg", navTab.getCurrentPanel()).find("tr");
	if(busiItemId == undefined){
		for (var k = 0 ; k < $tr.length; k++ ) {
			$("#policyBankMsg", navTab.getCurrentPanel()).find("tr:eq("+k+")").hide();
		}
	}
});


function selectMainBusiness(){
	debugger;
	var busiItemId = $("input[name='listId']:checked").val();
	var line="";
	var $tr = $("#policyBankMsg", navTab.getCurrentPanel()).find("tr");
	var optionsLength = $("select[name='banklist']").get(0).options.length;
	for (var k = 0 ; k < $tr.length; k++ ) {
		var busiItemId1 = $("#policyBankMsg", navTab.getCurrentPanel()).find("td[id='busiItemId']").text();
		var $select = $("#policyBankMsg", navTab.getCurrentPanel()).find("tr:eq("+k+")").find('td:eq(1)').find("select[id='payModeSelect']")
		if(busiItemId!=null && busiItemId1 == busiItemId){
			$("#policyBankMsg", navTab.getCurrentPanel()).find("tr:eq("+k+")").show();
			line=k;
		}else{
			$("#policyBankMsg", navTab.getCurrentPanel()).find("tr:eq("+k+")").hide();
		}
		$("select[name='banklist']").get(k).options[0].selected = true;
		$("select[name='banklist']").attr("disabled",true);
		$("#infoTable", navTab.getCurrentPanel()).attr("disabled", false);
		newModeChange($select[0]);
		
	}
	// 变更后信息展示
	debugger;
	var busiItemIdAF = $("input[name='listId']:checked").val()||"";
	if (busiItemIdAF !="" ) {
		var _jsons = "";
		var $jsonsText = $("input[name='hcMSG']",navTab.getCurrentPanel());
		var $table = $("#hlGetTable", navTab.getCurrentPanel());
		_jsons += _cs_tableToJson($table);
		$jsonsText.val(_jsons);
		
		$.ajax({
			url : "${ctx}/cs/serviceitem_gc/getAFInfo_PA_csEndorseGCAction.action?busiItemId=" + busiItemId,
			type : "post",
			dataType : "html",
			data : "jsonString=" + $("#hcMSG", navTab.getCurrentPanel()).val(),
			cache : false,
			success : function(response) {
				debugger;
				var json = DWZ.jsonEval(response);
				if (undefined == json.statusCode) {
					$("#changeBankMsg", navTab.getCurrentPanel()).html("");
					$("#changeBankMsg", navTab.getCurrentPanel()).html(response);
					$("#isShowGCMsg", navTab.getCurrentPanel()).show();
				} else {
					$("#isShowGCMsg", navTab.getCurrentPanel()).hide();
				}
			}
		});
	} else {
		$("#isShowGCMsg", navTab.getCurrentPanel()).hide();
	}
	
}



//点击支取形式变更后的状态
function newModeChange(line) {
	
	$("th[colName='newSurvivalWMode']").css("display",'');
	$("td[id='payMode']").css("display",'');
	$("th[colName='bankAccountShow']").css("display",'');
	$("th[colName='accoNameShow']").css("display",'');
	//$("th[colName='bankCodeShow']").css("display",'');
	
	$(line).parent().parent().find("td:eq(2)").css("display",'')
	$(line).parent().parent().find("td:eq(3)").css("display",'')
	$(line).parent().parent().find("td:eq(4)").css("display",'')
	$(line).parent().parent().find("td:eq(16)").css("display",'')
	//选择“银行转账（制返盘）”时，显示“银行代码/账号”列和“客户银行账号”模块；其他情况不显示此两项信息；
	console.log(line);
	if (line.value == 2) {
		$("#bankAccount", navTab.getCurrentPanel()).show();
		$("select[name='banklist']").removeAttr("disabled");
		$("#panelPageFormContentkhyhdiv", navTab.getCurrentPanel()).show();
		//$("select[name='banklist']").get(0).options[1].selected = true;
		$("input[name='banklistInput']").css("display","none");
		$("select[name='banklistNew']").css("display",'');
		$("input[name='accoNameInput']").attr("disabled",true);
		$("input[name='bankCodeReadOnly']").css("display",'');
		$("input[name='bankCodeReadOnly']").attr("disabled",true);
		$("div[name='banklistFind']").css("display","none");
		$("th[colName='bankCodeShow']").text("银行代码/名称");
	} else if(line.value == 3){
		$("#bankAccount", navTab.getCurrentPanel()).hide();
		$("select[name='banklist']").attr("disabled","true");
		$("#infoTable", navTab.getCurrentPanel()).attr("disabled", false);
		//$("select[name='banklist']").get(0).options[0].selected = true;
		$("input[name='banklistInput']").css("display",'');
		$("select[name='banklistNew']").css("display","none");
		$("input[name='accoNameInput']").attr("disabled",false);
		$("input[name='bankCodeReadOnly']").css("display","none");
		$("div[name='banklistFind']").css("display",'');
		$("th[colName='bankCodeShow']").text("开户行");
	} else {
		//$("th[colName='newSurvivalWMode']").css("display","none");
		//$("td[id='payMode']").css("display","none");
		$("th[colName='bankAccountShow']").css("display","none");
		$("th[colName='accoNameShow']").css("display","none");
		$("th[colName='bankCodeShow']").css("display","none");
		$(line).parent().parent().find("td:eq(2)").css("display","none")
		$(line).parent().parent().find("td:eq(3)").css("display","none")
		$(line).parent().parent().find("td:eq(4)").css("display","none")
		//$(line).parent().parent().find("td:eq(16)").css("display","none")
	}
	$("th[colName='banklist']").css("display","none");
	$("td[name='banklist']").css("display","none");
}

//点击领取形式变更后的状态
function newChanglq(line) {
	if (line.value == 0) {
		$("th[colName='newSurvivalWMode']").css("display","block");
		$("td[id='payMode']").css("display","block");
		$("th[colName='bankAccountShow']").css("display",'');
		$("th[colName='accoNameShow']").css("display",'');
		$("th[colName='bankCodeShow']").css("display",'');
	    //$("th[colName='banklist']").css("display",'');
		$(line).parent().parent().find("td:eq(2)").css("display",'');
		$(line).parent().parent().find("td:eq(3)").css("display",'');
		$(line).parent().parent().find("td:eq(4)").css("display",'');
		//$(line).parent().parent().find("td:eq(16)").css("display",'');
		$("#bankAccount", navTab.getCurrentPanel()).show();
	} else {
		$("th[colName='newSurvivalWMode']").css("display","none");
		$("td[id='payMode']").css("display","none");
		$("th[colName='bankAccountShow']").css("display","none");
		$("th[colName='accoNameShow']").css("display","none");
		$("th[colName='bankCodeShow']").css("display","none");
		$("th[colName='banklist']").css("display","none");
		$(line).parent().parent().find("td:eq(2)").css("display","none");
		$(line).parent().parent().find("td:eq(3)").css("display","none");
		$(line).parent().parent().find("td:eq(4)").css("display","none");
		$(line).parent().parent().find("td:eq(16)").css("display","none");
		$("#bankAccount", navTab.getCurrentPanel()).hide();
	}
}


//保单险种列表展示列表保存按钮
function saveNewMsg() {
	
	alertMsg
			.confirm(
					"确认保存以上信息？",
					{
						okCall : function() {
							var _jsons = "";
							var $jsonsText = $("input[name='jsons']",
									navTab.getCurrentPanel());
							var $table = $("#aaTableInform", navTab
									.getCurrentPanel());
							/* alert($table.html());  */
							_jsons += _cs_tableToJson($table);
							/* alert(_jsons); */
							$jsonsText.val(_jsons);
							$
									.ajax({
										url : "${ctx}/cs/serviceitem_gc/saveGCSurvivalModeMsg_PA_csEndorseGCAction.action",
										type : "post",
										dataType : 'html',
										data : "jsonString="
												+ $("#jsons", navTab.getCurrentPanel()).val(),
										cache : false,
										contentType:'application/x-www-form-urlencoded; charset=UTF-8',
										success : function(response) {
											alertMsg.correct("信息保存成功");
										    var json = DWZ
													.jsonEval(response);
											if (undefined == json.statusCode) {
												$("#changeBankMsg", navTab.getCurrentPanel()).html("");
												$("#changeBankMsg", navTab.getCurrentPanel()).html(response);
												$("#isShowGCMsg",navTab.getCurrentPanel()).show();
											} else {
												alertMsg.error(json.message);
											} 

										}
									});
						},
						cancelCall : function() {
						}
					});

}

//红利领取信息保存按钮
function saveHCMsg() {
	debugger;
	var $table = $("#policyBankMsg", navTab.getCurrentPanel());
	var rows = $table.find("tr");
	
	for (var k = 0; k < rows.length; k++) {
		var payMode = $(rows[k]).find("td:eq(1)").find("select[name='payMode']").val();
		var bonusMode = $(rows[k]).find("td:eq(0)").find("select").val();
		if(bonusMode == '请选择'){
			alertMsg.info("请选择生存金领取形式！");
			return false;
		}else if(bonusMode == 0){
			//$(rows[k]).find("td:eq(0)").find("select").val(1);
			if(payMode == 2){
				var bankList = $(rows[k]).find("td:eq(2)").find("select[name='banklistNew']").val();
				$(rows[k]).find("td:eq(18)").text(bankList.split("/")[1]);
				$(rows[k]).find("td:eq(19)").text(bankList.split("/")[2]);
				$(rows[k]).find("td:eq(20)").text(bankList.split("/")[0]);
				$(rows[k]).find("td:eq(21)").text(bankList.split("/")[4]);
				$(rows[k]).find("#_accountId").text(bankList.split("/")[3]);
			}else if (payMode == 3){
				var bankAccount = $(rows[k]).find("td:eq(2)").find("input[name='banklistInput']").val();
				var bankCode = $(rows[k]).find("td:eq(4)").find(".inputCreator", navTab.getCurrentPanel()).val();
				var inputOrg1 = $(rows[k]).find("td:eq(4)").find(".inputOrg1", navTab.getCurrentPanel()).val();
				var accoName = $(rows[k]).find("td:eq(3)").find("input[name='accoNameInput']").val();
				$(rows[k]).find("td:eq(18)").text(bankAccount);
				$(rows[k]).find("td:eq(19)").text(accoName);
				$(rows[k]).find("td:eq(20)").text(bankCode);
				$(rows[k]).find("td:eq(22)").text(inputOrg1);
				
			}
		} else if (bonusMode == 1){
			//$(rows[k]).find("td:eq(1)").find("select[name='payMode']").val(1);
		}else{
			//$(rows[k]).find("td:eq(1)").find("select[name='payMode']").val("");
		}
		
	}
		
		alertMsg
				.confirm(
						"确认保存以上信息？",
						{
							okCall : function() {
								var _jsons = "";
								var $jsonsText = $("input[name='jsons']",
										navTab.getCurrentPanel());
								var $table = $("#hlGetTable", navTab
										.getCurrentPanel());
								_jsons += _cs_tableToJson($table);
								$jsonsText.val(_jsons);
								/* alert($table.html());  */
								$
										.ajax({
											url : "${ctx}/cs/serviceitem_gc/saveGCSurvivalWMode_PA_csEndorseGCAction.action",
											type : "post",
											dataType : 'html',
											data : "jsonString="
													+ encodeURI($("#jsons", navTab.getCurrentPanel()).val()),
											cache : false,
											success : function(response) {
												alertMsg.correct("信息保存成功");
											    var json = DWZ
														.jsonEval(response);
												if (undefined == json.statusCode) {
													$("#changeBankMsg", navTab.getCurrentPanel()).html("");
													$("#changeBankMsg", navTab.getCurrentPanel()).html(response);
													$("#isShowGCMsg",navTab.getCurrentPanel()).show();
												} else {
													alertMsg.error(json.message);
												} 

											}
										});
							},
							cancelCall : function() {
							}
						});

	}





	$(function() {
		loadBankInfo();
	});

	function loadBankInfo() {
		var table = $("#bankAccountTable", navTab.getCurrentPanel());
		var rows = table.find("tr");
		/* alert(table.html()); */
		var str = "";
		var str1 = "";
		//循环下拉框 
		var bankinfo = $("select[name='banklist']");
		var bankinfo1 = $("select[name='banklistNew']");

		var first = "";
		var first1 = "";
		for (var i = 0; i < bankinfo.length; i++) {
			str = "<option value=''>请选择</option>";
			var obj = bankinfo[i];
			var obj1 = bankinfo1[i];
			for (var n = 1; n < rows.length; n++) {
				var bankName = $(rows[n]).find("td").eq(4).text();
				var bankCode = $(rows[n]).find("td").eq(5).text();
				var bankAccount = $(rows[n]).find("td").eq(6).find("input").val();
				var accoName = $(rows[n]).find("td").eq(7).text();
				var accountId = $(rows[n]).find("td").eq(10).text();
				str += "<option value='"+bankCode + "/"+bankAccount  + "/"+accoName+"/"+accountId+"/"+bankName+ "'>" + bankCode + "/"+ bankAccount +"</option>";
				str1 += "<option value='"+bankCode + "/"+bankAccount  + "/"+accoName+"/"+accountId+"/"+bankName+"'>" +bankAccount+"</option>";
				if (n === 1) {
					first = bankCode + "/"+bankAccount + "/"+accountId + "/"+accoName +"/"+bankName;
					first1 = bankCode + "/"+bankAccount + "/"+accountId + "/"+accoName +"/"+bankName;
				}
			}
			$(obj).html(str);
			var code = $(obj).prev().val();
			//让当前值选中
			$("select[name='banklist']").val(first);
			$("select[name='banklist']").selectedIndex = 1;
			$(obj1).html(str1);
			var code1 = $(obj1).prev().val();
			//让当前值选中
			$("select[name='banklistNew']").val(first1);
			$("select[name='banklistNew']").selectedIndex = 1;
			getBankCode($("select[name='banklist']").get(0));
			getBankCode($("select[name='banklistNew']").get(0));
		}
	}

	
	function getBankCode(obj) {
		debugger;
		//给银行账号一列赋值
		//$(obj).parent().next().text(obj.value.split("/")[1]);
		//$(obj).parent().next().next().next().text(obj.value.split("/")[0]);
		if(obj.value == "" ||obj.value == null){
			$(obj).parent().parent().find('td:eq(3)').children("input").children().val("");//户名
			$(obj).parent().parent().find("input[name='banklistInput']").val("");
			$(obj).parent().parent().find('td:eq(4)').val("");
		}else{
			$(obj).parent().parent().find('td:eq(3)').children("input").val(obj.value.split("/")[2]);
			$(obj).parent().parent().find("input[name='banklistInput']").val(obj.value.split("/")[1]);
			$(obj).parent().parent().find('td:eq(4)').val(obj.value.split("/")[0]);
			$(obj).parent().parent().find('td:eq(4)').children("input").val(obj.value.split("/")[4]);
			//$(obj).parent().parent().find('td:eq(8)').text(obj.value.split("/")[3]);
		}
		
		
	}

	function setBank(obj) {

		if (obj.value == 2) {
			$(obj).parent().next().removeAttr("disabled");
		} else {
			$(obj).parent().next().attr("disabled", "disabled");
			$(obj).parent().next().find("select").get(0).options[0].selected = true;
			$(obj).parent().next().next().text('');
		}
	}
	
	//银行账号二次重复校验
	var bankAccountCheckTime = 1;//账号输入次数
	var bankAccountFirst="";//第一次输入
	function checkAccountBank(obj){
		if (bankAccountCheckTime==1) {
			bankAccountFirst=obj.value;//第一次输入
			bankAccountCheckTime+=1;
			obj.value="";
			$(obj).parent().children("font").html('请再次输入');
		}else if (bankAccountCheckTime==2) {	
			if (bankAccountFirst!="" && bankAccountFirst!=obj.value) {
				$(obj).parent().children("font").attr("class", "error");
				$(obj).parent().children("font").html("");
				alertMsg.info("两次输入不一致，请重新输入！");	
				$(obj).parent().children("font").val("");
				obj.value="";
				bankAccountCheckTime=1;
				return;
			}else{
				$(obj).parent().children("font").html("");
				bankAccountCheckTime=1;
			}
		}		
	}
</script>

