
<!-- 加保页面 -->
<%@ page contentType="text/html; charset=utf-8"%>
<%@ taglib uri="/struts-tags" prefix="s"%><%@include file="/udmpCommon.jsp"%>
<%@ taglib uri="http://www.git.com.cn/taglib/udmp/field" prefix="Field" %>
<%@ include file="/cs/pages/common/jsp/common_ctx.jsp" %>
<script type = "text/javascript" src="${ctx}/cs/js/cs_common.js"></script>
<link rel="stylesheet" href="${ctx}/cs/css/public.css" type="text/css">
<style type="text/css">
th,td {
	white-space:normal;
}
.searchBar li label{
	width: 90px;
}
</style>
<!-- ********帮助菜单***********begin********* -->
<s:include value="/cs/pages/common/jsp/helpMenu.jsp"></s:include>
<!-- *********帮助菜单***********end********* -->

<div onmousedown="MM_changeProp('premAddCmDiv','display','none')"  class="backgroundCollor" style="height: 620px; overflow: auto;" layOutH='40'>
<s:if test="queryFlag!=1">
<s:include value="entryProgressBar.jsp"></s:include>
</s:if>
	<div id="shtab">
		<div >
			<!--第一个页签-->
			<div >
			<div >
			   
			   
			   
			    <div  id="">
			    <s:include value="customerInfo_list.jsp" />
				    <form method="post" action="${ctx}/cs/serviceitem_pa/savePremiumAdd_PA_csEndorsePAAction.action" 
				       class="required-validate" onsubmit ="return navTabSearch(this,'preminumAddNew');" id= "premiunmAddForm" style="width:100%">
				       <div class="divfclass">
									<h1 >
									  <img src="cs/img/icon/tubiao.png">保单险种列表信息
									</h1>
									</div>
				         		<div>
				         		<input id="customerId" type="hidden" name="customerId" value="${customerId}"/>
					            <input id="_cs_pa_changeId" type="hidden" name="changeId" value="${changeId }"/>
					            <input id="acceptId" type="hidden" name="acceptId" value="${acceptId }"/>
					            <input id="queryFlag" type="hidden" name="queryFlag" value="${queryFlag}"/>
					            <input type="hidden" name="iswavid" value="${policyHolderVO.iswavid}" id="iswavid"/>
					            <input type="hidden" name="policyHolderId" value="${policyHolderVO.customerId}" id="policyHolderId"/>
					            <input type="hidden" name="insuredId" value="${policyInsurdVO.customerId}" id="insuredId"/>
					            
					            	
					            
									<div>
										<div class="tabdivclass">
										<table class="list" id="preminumAddTable"　 table_saveStatus="1" width="100%">
											<thead >										
												<tr id="" align="center">	
												    <th style = "display:none" colName="changeId">changeId</th>
												    <th style = "display:none" colName="policyId">policyId</th>
												    <th style = "display:none" colName="busiItemId">busiItemId</th>	
												    <th style = "display:none" colName="policyChgId">policyChgId</th>	
												    <th style = "display:none" colName="acceptId">acceptId</th>	
												    <th style = "display:none" colName="isPer">isPer</th>
												    <th style = "display:none" colName="rateAdjustProductFlag">rateAdjustProductFlag</th>								   
													<th colName="policyCode">保单号</th>
													<th colName="productCodeSys">险种代码</th>
													<th colName="productAbbrName">险种名称</th>
													<th colName="insuredNames" >被保险人</th><!-- 151299 险种被保人姓名 -->
													<th colName="amout">基本保额/份</th>
													<th colName="perAmout">每份保额</th>
													<th>保费</th>
													<th>生效日期</th>
													<th>下次缴费日期</th>
													<th colName="perPreMinumAdd" inputType="input">加保额度/份</th>	
													<th>注意事项</th>							
												</tr>
											</thead>
											<tbody align="center" addRole="appendTo"  addTableGroup="preminumAddGroup" >
												<s:iterator value="preminumAddVOS" id="qr" status="in" var="premAdd">
													<tr align="center" id="showTr" tr_saveStatus="1">	
													    <td style ="display: none"><s:property value="changeId"/></td>	
													    <td style ="display: none"><s:property value="policyId"/></td>	
													    <td style ="display:none"><s:property value="busiItemId"/></td>		
													    <td style ="display:none"><s:property value="policyChgId"/></td>	
													    <td style ="display:none"><s:property value="acceptId"/></td>	
													    <td style ="display:none"><s:property value="isPer"/></td>
													    <td style ="display:none"><s:property value="rateAdjustProductFlag"/></td>																	  
														<td><s:property value="policyCode"/></td>
														<td><s:property value="productCodeSys"/></td>														
														<td> ${productAbbrName } </td>
														<!-- 151299 险种被保人姓名 -->
														<td >${insuredNames }</td>
														<td><s:property  value="amout"/></td>
														<td><s:property  value="perAmout"/></td>
														<td><s:property  value="stdPremAf"/></td>
														<td><s:date name="validateDate" format="yyyy/MM/dd"/></td>
														<td><s:date name="payDueDate" format="yyyy/MM/dd"/></td>
														<td id="_addFlag">	
														<!-- null不可以做 -->
								
														<s:if test="yesOrNO ==null||yesOrNO==0">
																<input disabled="disabled" type="text" id="extraSA${in.index }" name="perPreMinumAdd" value="${premAdd.perPreMinumAdd }" onblur="isPAJudge(this);"
																size="10" class="number" minlength="0" maxlength="10" onmouseover="overs('${in.index }');"/>
																<input type="hidden" id="isPreminumAdd${in.index }" name="isPreminumAdd" value="${premAdd.isPreminumAdd}"/> 
														</s:if>
														 <s:else >		
														 
																<input onkeyup="value=value.replace(/[^\d]/g,'')" 
									   								onbeforepaste="clipboardData.setData('text',clipboardData.getData('text').replace(/[^\d]/g,''))"
																	type="text" id="extraSA${in.index }" name="perPreMinumAdd" value="${premAdd.perPreMinumAdd }" <s:if test="queryFlag==1">disabled="disabled"</s:if>
																	size="10" class="number" minlength="0" maxlength="10" onmouseover="overs('${in.index }');"/>
															<input type="hidden" id="isPreminumAdd${in.index }" name="isPreminumAdd" value="${premAdd.isPreminumAdd}"/> 
														</s:else> 
														
														</td>
														<td>
															<s:if test="calcAmount!=null&&calcAmount>0">*注：公司建议意外险产品加保额度为${premAdd.calcAmount }，请与客户确认意愿后保存</s:if>
														</td>
													</tr>
												</s:iterator>
										 </tbody>
									</table>
								</div>					
							</div>
							
							 <!-- 按钮 -->
							 <s:if test="queryFlag!=1">
								<div class="formBarButton" >
									<button type="button" class="but_blue" onclick="save('premiunmAddForm','preminumAddNew')">保存</button>
								</div>
							</s:if>
								<!-- 按钮结束 -->   
						</div>
						<input type="hidden" id="jsonString" name="jsonString"/>
	　　　　　　　　　　		<input type="hidden" id="ids" name="ids"/>
				    </form>
		
				    <!-- 告知信息变更-->
				    <div id="csSurveyDiv" class="unitBox" ></div>					     
           			<!-- 151299 start -->
					<s:if test=" multiInsuredFlag==1 ">
					<div class="divfclass" style="max-width:50%;">
						<h1>
							<img src="cs/img/icon/tubiao.png">被保人社保状态
						</h1>
						<div class="divfclass" >
						<form id="customerSocialSecuForm" action="${ctx}/cs/serviceitem_so/saveSocialSecuList_PA_csEndorseSOAction.action" method="post" class="pageForm required-validate"
							  onsubmit="return navTabSearch(this,'addSubBusiProdNew')">
							<input type="hidden" id="changeId" name="csCustomerSocialSecuVO.changeId" value="${csCustomerSocialSecuVO.changeId }"> 
							<input type="hidden" id="acceptId" name="csCustomerSocialSecuVO.acceptId" value="${csCustomerSocialSecuVO.acceptId }"> 
							<input type="hidden" id="isSaved" name="csCustomerSocialSecuVO.isSaved" value="${csCustomerSocialSecuVO.isSaved }">
							<input type="hidden" id="newSocialSecu" name="csCustomerSocialSecuVO.newSocialSecu" value="0">
							<input type="hidden" name="changeId" value="${csCustomerSocialSecuVO.changeId }"> 
							<input type="hidden" name="acceptId" value="${csCustomerSocialSecuVO.acceptId }">
							<input type="hidden" id="resetFlag" name="csCustomerSocialSecuVO.resetFlag" value="${csCustomerSocialSecuVO.resetFlag}">
							<input type="hidden" id="multiInsuredFlag" value="${multiInsuredFlag }"> 
						</form>
							<table class="list" id="csCustomerSocialSecuVOList" table_saveStatus="1">
								<thead>
									<tr>
										<th colName="customerId" inputType="input">被保险人</th>
										<th colName="socialSecu" inputType="input">当前社保状态</th>
										<th colName="newSocialSecu" inputType="select">变更后社保状态</th>
										<th colName="changeId" inputType="input" style="display: none">changeId</th>
										<th colName="acceptId" inputType="input" style="display: none">acceptId</th>
									</tr>
								</thead>
								<tbody id="csCustomerSocialSecuVOListBody">
									<s:iterator value="csCustomerSocialSecuVOList" var="sr"
										status="st1">
										<tr tr_saveStatus="1">
											<td><input type="hidden" id="customerId" name="csCustomerSocialSecuVO.customerId" value="<s:property value="customerId"/>"/>${csCustomerVO.customerName}</td>
											<td><input type="hidden" id="oldSocialSecu" value="${socialSecu}"><Field:codeValue tableName="APP___PAS__DBUSER.T_YES_NO" value="${socialSecu}"/></td>
											<td>
												<s:select list="#{0:'否', 1:'是'}" id="newSocialSecu" name="csCustomerSocialSecuVO.newSocialSecu" listKey="key"
															listValue="value" value="newSocialSecu">
												</s:select>
											</td>
											<td style="display: none"><input type="hidden" id="changeId" name="csCustomerSocialSecuVO.changeId" value="${changeId }"> </td>
											<td style="display: none"><input type="hidden" id="acceptId" name="csCustomerSocialSecuVO.acceptId" value="${acceptId }"> </td>
										</tr>
									</s:iterator>
								</tbody>
							</table>
						
						</div>
						<div  style="float:left; margin-left:10%;">
							<table style="width: 100%">
								<tr>
									<td style="width: 120px">
										<button class="but_blue" type="button"
											onclick="_saveSociSecuList()">社保状态保存</button>
									</td>
									<td style="width: 120px">
										<button class="but_blue" type="button" onclick="_rollBackSociSecuList()">社保状态重置</button>
									</td>
								</tr>
							</table>
						</div>
					</div>
					</s:if>
					<s:else>
					<!-- 151299 end -->
          			<div class="divfclass">
          				 <div class="divfclass">
							<h1 >
							  <img src="cs/img/icon/tubiao.png">被保人社保状态
							</h1>
						</div>
				       	<div class="pageFormContent">
				       		<dl>
								<dt>当前状态</dt>
								<dd>
								<s:select list="#{0:'否', 1:'是',2:''}" id="oldSociSecu"   name="policyInsurdVO.sociSecu" disabled="true"
									listKey="key" listValue="value" value="policyInsurdVO.sociSecu">
								</s:select>
								</dd>
							</dl>
							<dl>
								<dt>变更后状态</dt>
								<dd>
								<s:select list="#{0:'否', 1:'是'}"  id="newSociSecu" name="policyInsurdVO.newSociSecu" listKey="key"
									listValue="value" value="policyInsurdVO.newSociSecu" >
								</s:select>
								</dd>
							</dl>
							
							<dl style="width: 120px;">
								<dt></dt>
								<dd>
									<button class="but_blue" type="button" onclick="_saveSociSecu()">社保状态保存</button>
								</dd>
							</dl>
							<dl style="width: 120px;">
								<dt></dt>
								<dd>
									<button class="but_blue" type="button" onclick="_rollBackSociSecu()">社保状态重置</button>
								</dd>
							</dl>
				       	</div>
				       	
          			</div>
          			<!-- 151299 start -->
					</s:else>
					<br/>
					<!-- 151299 end -->
          			<div id= "preminumAddNew" class="unitBox" >
          				<s:include value="/cs/pages/serviceitem/CsEndorsePA_new.jsp"></s:include>
          			</div>
          			<div id="operationCheck" style="display:block">						
           			</div>
				
		        </div>
		    </div>
		    <s:if test="queryFlag!=1">
		    <s:include value="/cs/pages/serviceitem/serviceItemBottomBar.jsp"></s:include>
		    </s:if>
		  </div>
		</div>
		<div class="tabsFooter">
			<div class="tabsFooterContent"></div>
		</div>
	</div>
</div>

<script type="text/javascript">

	function _saveSociSecu(){
		debugger;		
		var newSociSecu=$("#newSociSecu", navTab.getCurrentPanel()).val();
		var changeId=$("#_cs_pa_changeId", navTab.getCurrentPanel()).val();
		var acceptId=$("#acceptId", navTab.getCurrentPanel()).val();
		
		$.ajax({
			type : "post",
			url : "${ctx }/cs/serviceitem_pa/checkSociSecu_PA_csEndorsePAAction.action?changeId="+changeId+"&acceptId="+acceptId,
			data : "policyInsurdVO.newSociSecu="+newSociSecu,
			success : function(resData) {
				debugger;
				var json = DWZ.jsonEval(resData);
				if(json.message!=null&&json.message!=''){
					alertMsg.confirm(json.message,
					{
						okCall : function() {						
							_submitSociSecu('save');
						},
						cancelCall : function() {
							
						}
					}); 
				}else{
					_submitSociSecu('save');
				}						 
			}
		});
	}
	//151299 start
	function _saveSociSecuList(){
		debugger;		
		var changeId=$("#_cs_pa_changeId", navTab.getCurrentPanel()).val();
		var acceptId=$("#acceptId", navTab.getCurrentPanel()).val();
		var $jsons = $("#csCustomerSocialSecuVOList",navTab.getCurrentPanel());
   		var _jsons = _cs_tableToJson($jsons);
	   	_jsons = eval('('+ _jsons + ')');
	   	_jsons = JSON.stringify(_jsons);
		var sendData = "jsonSocialSecuVOList="+ _jsons;
		$.ajax({
			type : "post",
			url : "${ctx }/cs/serviceitem_pa/checkSociSecuList_PA_csEndorsePAAction.action?changeId="+changeId+"&acceptId="+acceptId,
			data : sendData,
			success : function(resData) {
				debugger;
				var json = DWZ.jsonEval(resData);
				if(json.message!=null&&json.message!=''){
					alertMsg.confirm(json.message,
					{
						okCall : function() {						
							_submitSociSecuList('save');
						},
						cancelCall : function() {
							
						}
					}); 
				}else{
					_submitSociSecuList('save');
				}						 
			}
		});
		
	}
	function _submitSociSecuList(flag){	
		var changeId=$("#_cs_pa_changeId", navTab.getCurrentPanel()).val();
		var acceptId=$("#acceptId", navTab.getCurrentPanel()).val();
		var $jsons = $("#csCustomerSocialSecuVOList",navTab.getCurrentPanel());
   		var _jsons = _cs_tableToJson($jsons);
	   	_jsons = eval('('+ _jsons + ')');
	   	_jsons = JSON.stringify(_jsons);
		var sendData = "jsonSocialSecuVOList="+ _jsons;
		$.ajax({
			type : "post",
			url : "${ctx }/cs/serviceitem_pa/saveSociSecuList_PA_csEndorsePAAction.action?changeId="+changeId+"&acceptId="+acceptId,
			data : sendData,
			success : function(resData) {
				debugger;
				$("#preminumAddNew", navTab.getCurrentPanel()).html(resData).initUI();
			}
		});
	}
	function _rollBackSociSecuList(){		
		var changeId=$("#_cs_pa_changeId", navTab.getCurrentPanel()).val();
		var acceptId=$("#acceptId", navTab.getCurrentPanel()).val();
		var sameCustomers = $("#csCustomerSocialSecuVOListBody").find("select[id='newSocialSecu']");
		sameCustomers.each(function(){
			$(this).val($(this).closest("tr").find("input#oldSocialSecu").val());
		});
		var $jsons = $("#csCustomerSocialSecuVOList",navTab.getCurrentPanel());
   		var _jsons = _cs_tableToJson($jsons);
	   	_jsons = eval('('+ _jsons + ')');
	   	_jsons = JSON.stringify(_jsons);
		var sendData = "jsonSocialSecuVOList="+ _jsons;
		$.ajax({
			type : "post",
			url : "${ctx }/cs/serviceitem_pa/saveSociSecuList_PA_csEndorsePAAction.action?changeId="+changeId+"&acceptId="+acceptId,
			data : sendData,
			success : function(resData) {
				debugger;
				$("#preminumAddNew", navTab.getCurrentPanel()).html(resData).initUI();
			}
		});
		
	}
	//151299 end
	function _submitSociSecu(){		
		var changeId=$("#_cs_pa_changeId", navTab.getCurrentPanel()).val();
		var acceptId=$("#acceptId", navTab.getCurrentPanel()).val();
		var newSociSecu=$("#newSociSecu", navTab.getCurrentPanel()).val();
		
		$.ajax({
			type : "post",
			url : "${ctx }/cs/serviceitem_pa/saveSociSecu_PA_csEndorsePAAction.action?changeId="+changeId+"&acceptId="+acceptId,
			data : "policyInsurdVO.newSociSecu="+newSociSecu,
			success : function(resData) {
				debugger;
				$("#preminumAddNew", navTab.getCurrentPanel()).html(resData).initUI();
			}
		});
	}
	
	

	function _rollBackSociSecu(){		
		var changeId=$("#_cs_pa_changeId", navTab.getCurrentPanel()).val();
		var acceptId=$("#acceptId", navTab.getCurrentPanel()).val();
		var oldSociSecu=$("#oldSociSecu", navTab.getCurrentPanel()).val();	
		
		$.ajax({
			type : "post",
			url : "${ctx }/cs/serviceitem_pa/saveSociSecu_PA_csEndorsePAAction.action?changeId="+changeId+"&acceptId="+acceptId,
			data :  "policyInsurdVO.newSociSecu="+oldSociSecu,
			success : function(resData) {
				var soci=$("#oldSociSecu", navTab.getCurrentPanel()).val();
				$("#newSociSecu", navTab.getCurrentPanel()).val(soci);
				$("#preminumAddNew", navTab.getCurrentPanel()).html(resData).initUI();
			}
		});
		
	}

	function overs(id){
		debugger;
		var strId = "#isPreminumAdd"+id;
		//console.info("+++++++++"+$(strId).val() );
		if($(strId).val() != 'true'){
			$("#extraSA"+id, navTab.getCurrentPanel()).attr('readonly',true);
		}
	}
	
	function isPAJudge(obj){
		var perPreMinumAdd=$(obj).parent().parent().find("#_addFlag").find("input:text").val();
	
		if(perPreMinumAdd != null && perPreMinumAdd != ''){
			var policyName=$(obj).parent().parent().find("td:eq(9)").html();
			alertMsg.err(policyName+"不能受理加保，请确认。");
			$(obj).parent().parent().find("#_addFlag").find("input:text").val(null);
			
		}
	}
	
	
	
	//保存 加保信息 
	function save(formId, boxId) {
		
		var $table = $("#preminumAddTable", navTab.getCurrentPanel());
		var _jsons = _cs_tableToJson($table);
		$("#jsonString", navTab.getCurrentPanel()).val(_jsons);
		
		var flag=0;
		$("#preminumAddTable", navTab.getCurrentPanel()).find("tbody").find("tr").each(
				function() {					
					var $td = $(this).find("#_addFlag");								
					var premAdd = $td.find("input:text").val();
					if(premAdd !=null && premAdd !='' && premAdd != 0){
						flag=1;									
					}							
		});
		
		if(flag == 0){
			var policyCode = $("#preminumAddTable", navTab.getCurrentPanel()).find("tbody").find("tr").eq(0).find("td").eq(7).text() ;
			alertMsg.info(policyCode+"未选择加保或者加保险种，不能受理加保，请确认。");
			return false;
		}
		
		$.ajax({
			type : "post",
			url : "${ctx }/cs/serviceitem_pa/checkErrRule_PA_csEndorsePAAction.action",
			data : $("#"+formId, navTab.getCurrentPanel()).serialize(),
			success : function(data) {
				var json = DWZ.jsonEval(data);
				if (json.statusCode == DWZ.statusCode.error) {
					alertMsg.error(json.message);
				} else if (json.statusCode == DWZ.statusCode.timeout) {
					DWZ.loadLogin();
				}else{
					if(json.statusCode=="333"){//非阻断
						alertMsg .confirm(json.message,
								{
									okCall : function() {
									savePAinfo(formId, boxId);
									},
									cancleCall : function() {

									}
								});
					}else{
						savePAinfo(formId, boxId);
					}
				}
			}
		});
	};
	
	//保存加保信息
	function savePAinfo(formId, boxId) {
		var flag = "";
		$("#preminumAddTable", navTab.getCurrentPanel()).find("tbody").find("tr").each(function() {
			var $tds = $(this).find("td");
			var rateAdjustProductFlag = $tds.eq(6).text();

			if (rateAdjustProductFlag == "1") {
				flag = "1";
			}
		});
		
		if (flag == "1") {
			alertMsg.confirm("本险种保单年度内申请过续保险种转换，如确认提交申请，本公司将取消下一保单年度转换险种的申请，如有需要，请重新申请转换。",
							{
								okCall : function() {
									_PAformSubmit();
								},
								cancleCall : function() {
								}
							});
		} else {
			_PAformSubmit();
		}

	}

	function _PAformSubmit(){
		$("#operationCheck", navTab.getCurrentPanel()).css("display","none");
		var $table = $("#preminumAddTable", navTab.getCurrentPanel());
		var _jsons = _cs_tableToJson($table);
		var changeId=$("#_cs_pa_changeId", navTab.getCurrentPanel()).val();
		var acceptId=$("#acceptId", navTab.getCurrentPanel()).val();
		var customerId = $("#customerId", navTab.getCurrentPanel()).val();
		
		$.ajax({
			type : "post",
			url : "${ctx}/cs/serviceitem_pa/savePremiumAdd_PA_csEndorsePAAction.action?changeId="+changeId+"&acceptId="+acceptId+"&customerId="+customerId,
			data : "jsonString="+_jsons,
			success : function(resData) {
				debugger;
			 	$("#preminumAddNew",navTab.getCurrentPanel()).html(resData).initUI();
			 	
			 	var warnMsg=$("#_warnMsg",navTab.getCurrentPanel()).val();
			 	if(warnMsg!=null&&warnMsg.length>0){//保存后，限额非阻断提示
			 		alertMsg.warn(warnMsg);
			 	}
			}
		});
	}
	
	
	//帮助部分动作
	function MM_changeProp(objId, theProp, theValue) {
		var obj = null;
		with (document) {
			if (getElementById) {
				obj = getElementById(objId);
			}
		}
		if (obj) {
			if (theValue == true || theValue == false) {
				eval("obj.style." + theProp + "=" + theValue);
			} else {
				eval("obj.style." + theProp + "='" + theValue + "'");
			}
		}
	};

	/**
	初始化页面 变更前数据加载
	 **/
	//初始化
	$(document)
			.ready(
					function() {
						var customerId = $("#customerId",
								navTab.getCurrentPanel()).val();
						var changeId = $("#_cs_pa_changeId",
								navTab.getCurrentPanel()).val();
						var acceptId = $("#acceptId", navTab.getCurrentPanel())
								.val();
						var jsDiv = 'csSurveyDiv';
						var rel = $("#csSurveyDiv", navTab.getCurrentPanel());
						var type = "1";
						/* rel.loadUrl("${ctx}/cs/common/loadMainPage_PA_csSurveyAction.action?customerId="
										+ customerId
										+ "&changeId="
										+ changeId
										+ "&acceptId="
										+ acceptId
										+ "&type="
										+ type + "&jsDiv=" + jsDiv); */

						var policyHolderId = $("#policyHolderId",
								navTab.getCurrentPanel()).val();
						var policyInsuredId = $("#insuredId",
								navTab.getCurrentPanel()).val();
						var iswavid = $("#iswavid", navTab.getCurrentPanel())
								.val();

						var queryFlag = $("#queryFlag",
								navTab.getCurrentPanel()).val();

						var operationType = '${operationType}';
						//根据需求，.当保单下不存在投保人豁免责任时，不显示投保人健康告知
						//3.即使存在投保人豁免责任，当投保人被保人为同一人时，只填写一次被保人健康告知。
						if (iswavid == 0
								|| (policyHolderId == policyInsuredId && iswavid != 0)) {
							//只有被保人的健康告知
							var isHolderNeed = 1;
							rel
									.loadUrl("${ctx}/cs/common/loadMainPage_PA_csNotificAction.action?customerId="
											+ policyInsuredId
											+ "&changeId="
											+ changeId
											+ "&acceptId="
											+ acceptId
											+ "&queryFlag="
											+ queryFlag
											+ "&isHolderNeed="
											+ isHolderNeed
											+ "&holderCusObject=1");
						} else {
							var isHolderNeed = 2;
							//投保人/被保人健康告知
							rel
									.loadUrl("${ctx}/cs/common/loadMainPage_PA_csNotificAction.action?customerId="
											+ policyHolderId
											+ "&changeId="
											+ changeId
											+ "&acceptId="
											+ acceptId
											+ "&queryFlag="
											+ queryFlag
											+ "&isHolderNeed="
											+ isHolderNeed);
							//被保人健康告知
							//rel.loadUrl("${ctx}/cs/common/loadMainPage_PA_csSurveyAction.action?customerId="+policyInsuredId+"&changeId="+changeId+"&acceptId="+acceptId+"&queryFlag="+queryFlag);
						}
					});
</script>


	
