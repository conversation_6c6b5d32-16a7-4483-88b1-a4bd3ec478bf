<!-- 减额缴清、险种转换 主页面-->
<%@ page language="java" contentType="text/html; charset=utf-8" pageEncoding="utf-8"%>
<%@ taglib uri="http://www.git.com.cn/taglib/udmp/field" prefix="Field"%>
<%@ taglib uri="/struts-tags" prefix="s"%><%@include file="/udmpCommon.jsp"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<%@ include file="/cs/pages/common/jsp/common_ctx.jsp"%>
<script type="text/javascript" src="${ctx}/cs/js/cs_common.js"></script>
<script type ="text/javascript"
	src="${ctx }/udmp/plugins/ribbon/jquery.asyncorgtree.js">
</script>
<link rel="stylesheet" href="${ctx }/cs/css/cs_common.css" type="text/css">
 <script type="text/javascript">
	$(document).ready(function() {
		//alert($("#queryFlag").val());
		_cs_initMultipleBox();
		$("#updateEndInfo", navTab.getCurrentPanel()).hide();
	});
</script> <script type="text/javascript">
	//帮助部分动作
	function MM_changeProp(objId, theProp, theValue) {
		var obj = null;
		with (document) {
			if (getElementById) {
				obj = getElementById(objId);
			}
		}
		if (obj) {
			if (theValue == true || theValue == false) {
				eval("obj.style." + theProp + "=" + theValue);
			} else {
				eval("obj.style." + theProp + "='" + theValue + "'");
			}
		}
	};
</script>
<style type="text/css">
th,td {
	white-space: normal;
}

.searchBar li label {
	width: 90px;
}
</style>
<script>
$(function(){
	var objs = $("#ReducedPaidUpBFTable",navTab.getCurrentPanel()).find("tbody tr td[name='radioTd']").find("input");
	if(objs.size() > 0){
		// 需求变更首个默认选中
		obj = objs.eq(0);
		obj.attr("checked",'2');
		getBusiItem(obj);
	}
});
function checkMseeage(){
		
		var radio = $(".myClass:checked", navTab.getCurrentPanel());		
		if (radio.length == 0) {
			alertMsg.error("请选择险种 ");
		} else {
			var busiItemId = radio.parents("tr").find("td").eq(2).text();
			var policyChgId = radio.parents("tr").find("td").eq(3).text();
			
			//console.info("policyChgId" + policyChgId + "busiItemId" + busiItemId);
			$.ajax({
				url : "${ctx}/cs/serviceitem_pu/checkWarnInfo_PA_csEndorsePUAction.action",
				type : "post",
				dataType : 'text',
				data : "policyChgId=" + policyChgId + "&busiItemId=" + busiItemId,
				cache : false,
				success : function(data) {

					if (data.indexOf("{") == 0) {
						var json = jQuery.parseJSON(data);
						if (json.statusCode != 200) {

							alertMsg.confirm(json.message + " 是否继续？", {
								okCall : function() {
									reducedPaid();
								}
							});

						}
					} else {
						reducedPaid();
					}
				}
			});
		}
	}

	/* 减额交清   */
	function reducedPaid() {	
		// 清空上次险种转换的信息
		$("#_printDiv", navTab.getCurrentPanel()).html("");
		$(".myClass", navTab.getCurrentPanel()).each(function(){
			if($(this).is(':checked')){
				$(this).parent("td").parent("tr").attr("tr_saveStatus","1");
			}else{
				$(this).parent("td").parent("tr").attr("tr_saveStatus","0");
			}
		});
		
		var radio = $(".myClass:checked", navTab.getCurrentPanel());
		var _businessPrdId = radio.val();		
		var $obj = $("#reducedPaidUpForm", navTab.getCurrentPanel());
		var _jsons = "";
		_jsons += _cs_tableToJson($("#ReducedPaidUpBFTable"),navTab.getCurrentPanel());
// 		alert(_jsons);
		//var oldBusiInfoStr = _jsons;
		
		$("#oldBusiInfoStr",navTab.getCurrentPanel()).val(_jsons);
		
		action = "${ctx}/cs/serviceitem_pu/reducedPaid_PA_csEndorsePUAction.action?businessPrdId=" + _businessPrdId ;
		$obj.attr('action', action);
		$("#reducedPaidUpForm", navTab.getCurrentPanel()).submit();
	}
	
	//为隐含域 赋值
	function getBusiItem(obj) {
		$("#policyChgId").attr('value', $(obj).parents("tr").find("#_policyChgId").text().trim());
		$("#policyId").attr('value', $(obj).parents("tr").find("#_policyId").text().trim());
		$("#busiItemId").attr('value', $(obj).parents("tr").find("#_busiItemId").text().trim());

		 
		var cashValue = $(obj).parents("tr").find("#_cashValue").text().trim() - 0;
		var finalBonus = $(obj).parents("tr").find("#_finalBonus").text().trim() - 0;
		var busiCV=cashValue + finalBonus;
		$("#busiCashValue", navTab.getCurrentPanel()).attr('value',busiCV );
	}
</script>

<!-- 帮助菜单 -->
 <s:include value="/cs/pages/common/jsp/helpMenu.jsp"></s:include>

<!-- 步骤标识 -->
<s:include value="csEndorseProgress.jsp" />

<div class="panelPageFormContent">
	<div class="divfclass">
		<h1>
			<img src="${ctx}/cs/img/icon/tubiao.png">减额缴清/险种转换
		</h1>
	</div>

	
	<div class="pageContent" layoutH="150">
		<div class="panelPageFormContent">		
		<!-- 客户信息 -->
		<s:include value="customerInfo_list.jsp"></s:include>
		</div>

		<div class="main_tabdiv">
			<!-- 变更前信息 -->
			<form id="reducedPaidUpForm" action="${ctx}/cs/serviceitem_pu/reducedPaid_PA_csEndorsePUAction.action" class="" method="post"
				onsubmit="return divSearch(this,'reducedChangeNew');">

				<div class="panelPageFormContent">
					<input type="hidden" name="customerId" value="${customerId}"> 
					<input type="hidden" name="changeId" value="${changeId }" id="changeId"> 
					<input type="hidden" name="acceptId" value="${acceptId }" id="acceptId"> 
					<input type="hidden" id="policyChgId" name="policyChgId"> 
					<input type="hidden" id="policyId" name="policyId"> 
					<input type="hidden" id="busiItemId" name="busiItemId"> 
					<input type="hidden" id="queryFlag" value="${queryFlag}"> 
					<input type="hidden" id="busiCashValue" name='busiCashValue'>
					<input type="hidden" id="oldBusiInfoStr" name='oldBusiInfoStr'>
					<div class="divfclass">
						<h1>
							<img src="${ctx}/cs/img/icon/tubiao.png">变更前信息
						</h1>
					</div>

					<div class="tabdivclass" <s:if test="queryFlag==1">disabled="disabled"</s:if>>
						<table class="list" id="ReducedPaidUpBFTable" width="100%">
							<thead>
								<tr <s:if test="queryFlag==1">disabled="disabled"</s:if>>
									<th style="display: none" colName="changeId">changeId</th>
									<th style="display: none" colName="policyId">policyId</th>
									<th style="display: none" colName="busiItemId">busiItemId</th>
									<th style="display: none" colName="policyChgId">policyChgId</th>
									<th>选择</th>
									<th colName="productCodeSys">险种代码</th>
									<th>保单号</th>
									<th>险种名称</th>
									<th>保额</th>
									<th>每份保额</th>
									<th>保费</th>
									<th>下次缴费日</th>
									<th>险种状态</th>
									<th colName="cashValue">现价</th>
									<th colName="finalBonus">终了红利</th>
								</tr>
							</thead>
							<tbody align="center">
								<s:iterator value="reducePaidUpChangeBFVOs" id="qr" status="st">
									<tr align="center" id="showTr" tr_saveStatus='0' <s:if test="queryFlag==1">disabled="disabled"</s:if>>
										<td style="display: none" id="_changeId"><s:property value="changeId" /></td>
										<td style="display: none" id="_policyId"><s:property value="policyId" /></td>
										<td style="display: none" id="_busiItemId"><s:property value="busiItemId" /></td>
										<td style="display: none" id="_policyChgId"><s:property value="policyChgId" /></td>
										<td name='radioTd'>
										<s:if test="yesOrNO==1"><input onclick="getBusiItem(this)" type="radio" name="product" name="productItem" class="myClass"
											value="${businessPrdId}" />
										</s:if>
										</td>
										<td>${productCodeSys}</td>
										<td>${policyCode}</td>
										<td><Field:codeValue tableName="APP___PAS__DBUSER.T_BUSINESS_PRODUCT" value="${businessPrdId}" /></td>
										<td>${amout}</td>
										<td><s:if test="perAmout != 0">
												<s:property value="perAmout" />
											</s:if></td>
										<td><s:property value="totalPremAf" /></td>
										<td><s:date name="payDueDate" format="yyyy-MM-dd" /></td>
										<td><Field:codeValue tableName="APP___PAS__DBUSER.T_LIABILITY_STATUS" value="${liabilityState}" /></td>
										<td id="_cashValue"><s:property value="cashValue" /></td>
										<td id="_finalBonus"><s:property value="finalBonus" /></td>
									</tr>
								</s:iterator>
							</tbody>
						</table>
					</div>
					<!-- 按钮 -->
					<div class="pageFormdiv" <s:if test="queryFlag==1">style="display:none"</s:if>>
						<button type="button" class="but_blue" onclick="checkMseeage()">减额缴清</button>
					</div>
				</div>
			</form>
		</div>

		<!-- 减额缴清、险种转换页面 -->
		<div id="reducedChangeNew" class="main_tabdiv">		   
			<s:if test="(isSaved!=null&&isSaved==1)||queryFlag==1">
				<s:include value="CsEndorsePU_reduced.jsp"></s:include>
			</s:if>
		</div>
		
 		<div id="_printDiv"></div>


		<!-- 受理完成和下一步按钮 -->
		<s:include value="/cs/pages/serviceitem/serviceItemBottomBar.jsp"></s:include>

	</div>
	
</div>