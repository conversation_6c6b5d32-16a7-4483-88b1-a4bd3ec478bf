<%@ page language="java" contentType="text/html; charset=utf-8" pageEncoding="utf-8"%>
<%@ taglib uri="/struts-tags" prefix="s"%><%@include file="/udmpCommon.jsp"%>
<%@ include file="/cs/pages/common/jsp/common_ctx.jsp" %>
<script type="text/javascript" src="cs/pages/common/js/returnAcceptAndPay.js"></script>
<%@ taglib uri="http://www.git.com.cn/taglib/udmp/field" prefix="Field"%>
<script type="text/javascript" src="${ctx}/cs/js/cs_common.js"></script>

<script type="text/javascript">
	
	//保存
	function saveCsEndorseCP() {
		var solutionPayDate = $("#solutionPayDate", navTab.getCurrentPanel()).val();
		var stopDate = $("#stopDate", navTab.getCurrentPanel()).val();
		if (solutionPayDate == null || solutionPayDate == ''){
			alertMsg.warn("请填写解付日期！");
			return false;
		}
		var solutionPayCause = $("#solutionPayCause", navTab.getCurrentPanel()).val();
		if (solutionPayCause == null || solutionPayCause == ''){
			alertMsg.warn("选择解付原因");
			return false;
		}
// 		new Date(solutionPayDate.replace(/\-/g, "\/"));  
// 		new Date(stopDate.replace(/\-/g, "\/"));
		/* if (new Date(solutionPayDate.replace(/\-/g, "\/")) < new Date(stopDate.replace(/\-/g, "\/"))){
			alertMsg.warn("录入的解付日期早于止付日期，请重新录入解付日期。");
			return false;
		} */
		var $form = $("#savePolicySolutionPay");
		var policyCHGId=$(".policyCHGId");
		var solutionPayCause = $("select[name='cpItemPolicyPledgeVO.solutionPayCause']");
		var policyCHGIds="";
		var solutionPayCauses="";
		for(var a=0;a<policyCHGId.length;a++){
			policyCHGIds=policyCHGIds+policyCHGId[a].value+",";
		}
		policyCHGIds=policyCHGIds.substring(0, policyCHGIds.length - 1);
		$("#policyCHGIds").val(policyCHGIds);
		for(var a=0;a<solutionPayCause.length;a++){
			solutionPayCauses=solutionPayCauses+solutionPayCause[a].value+",";
		}
		solutionPayCauses=solutionPayCauses.substring(0,solutionPayCauses.length-1);
		$("#solutionPayCause").val(solutionPayCauses);
		var action = "${ctx}/cs/serviceitem_cp/saveCsEndorseCP_PA_csEndorseCPAction.action";
		 var onsubmit = "return validateCallback(this);"; 
		$form.attr("action", action);
		 $form.attr('onsubmit', onsubmit); 
		$form.submit();
		
		//alertMsg.correct("保存成功！"); 
	}
	
	function cpItemPolicyPledgeNextStep() {
		var $form = $("#savePolicySolutionPay");
		var action = "${ctx}/cs/serviceitem_cp/cpItemPolicyPledgeNextStep_PA_csEndorseCPAction.action";
		$form.attr("action", action);
		$form.submit();
	}
	//上一步
	function upToCsEntry(){
		 $("#gotoCsEntry").click();
	}
</script>

<!-- ********帮助菜单***********begin********* -->
<s:include value="/cs/pages/common/jsp/helpMenu.jsp"></s:include>
<!-- *********帮助菜单***********end********* -->
<!-- 步骤标识 -->
<s:if test="queryFlag!=1">
	<s:include value="entryProgressBar.jsp"></s:include>
</s:if>

<div layoutH="140">
		<div class="divfclass">
			<h1>
				<img src="cs/img/icon/tubiao.png">保单质押第三方解付
			</h1>
		</div>
		

	<div class="pageContent" >
		<s:include value="customerInfo_list.jsp" />
		
		<form action="" id="savePolicySolutionPay" method="post"
			onsubmit="return validateCallback(this);">
			<!-- 隐藏域传值 -->
			<input type="hidden" name="acceptId" value="${acceptId}"> <input
				type="hidden" name="changeId" value="${changeId}"> <input
				type="hidden" name="customerId" value="${customerId}"><input
				type="hidden" name="cpItemPolicyPledgeVO.solutionPayCauses" value=""
				id="solutionPayCauses"><input type="hidden"
				name="cpItemPolicyPledgeVO.policyCHGIds" value="" id="policyCHGIds">
			<div class="pageFormInfoContent">

				<dl>
					<dt>解付日期</dt>
					<dd>
						<s:if test="queryFlag==1">
							<input type="text" style="width: 120px;" readonly="readonly"
								value="<s:date format="yyyy-MM-dd" name="cpItemPolicyPledgeVO.solutionPayDate"/>" />
							<%-- value="${cpItemPolicyPledgeVO.solutionPayDate}" readonly="readonly"/> --%>
						</s:if>
						<s:else>
							<input type="expandDateYMDRO" id="solutionPayDate"
								style="width: 120px;"
								name="cpItemPolicyPledgeVO.solutionPayDate"
								value="<s:date format="yyyy-MM-dd" name="cpItemPolicyPledgeVO.solutionPayDate"/>" />
						</s:else>
					</dd>
				</dl>
			</div>

	<s:iterator value="CpItemPolicyPledgeVO.cpPolicyInfoVOList">
		<div class="divfclass">
			<h1>
				<img src="cs/img/icon/tubiao.png">保单详情
			</h1>
		</div>
		<div class="pageFormInfoContent">
			<dl>
				<dt>保单号</dt>
				<dd>
					<input type="text" value="${policyCode }" readonly="readonly" />
				</dd>
			</dl>
			<dl>
				<dt>保单生效日</dt>
				<dd>
					<input type="text"
						value="<s:date format="yyyy-MM-dd" name="validateDate"/>"
						readonly="readonly" />
				</dd>
			</dl>
			<dl>
				<dt>保险止期</dt>
				<dd>
					<input type="text"
						value="<s:date format="yyyy-MM-dd" name="expiryDate"/>"
						readonly="readonly" />
				</dd>
			</dl>
			<dl>
				<dt>止付申请提交日期</dt>
				<dd>
					<input type="text" id="stopDate"
						value="<s:date format="yyyy-MM-dd" name="stopDate"/>"
						readonly="readonly" />
				</dd>
			</dl>
			<dl>
				<dt>投保人姓名</dt>
				<dd>
					<input type="text" value="${name}" readonly="readonly" />
				</dd>
			</dl>
			<dl>
				<dt>投保人证件类型</dt>
				<dd>
					<input type="text"
						value="<Field:codeValue tableName="APP___PAS__DBUSER.T_CERTI_TYPE" value="${certiType}" />"
						readonly="readonly" />
				</dd>
			</dl>
			<dl>
				<dt>投保人证件号</dt>
				<dd>
					<input type="text" value="${certiCode}" readonly="readonly" />
				</dd>
			</dl>
			<dl>
				<dt>
					解除原因<font style="color: red;">*</font>
				</dt>
				<dd>
					<!-- 隐藏域传值(保单变更id，policyCHGId) -->
					<input type="hidden" class="policyCHGId" value="${policyCHGId}">
					<s:if test="queryFlag==1">
						<input type="text"
							value="<Field:codeValue tableName="APP___PAS__DBUSER.T_SOLUTION_PAY_CAUSE" 
									value="${cpItemPolicyPledgeVO.solutionPayCause}" />"
							readonly="readonly" />
					</s:if>
					<s:else>
						<Field:codeTable cssClass="combox" nullOption="true"
							name="cpItemPolicyPledgeVO.solutionPayCause"
							tableName="APP___PAS__DBUSER.T_SOLUTION_PAY_CAUSE"
							id="solutionPayCause" />
					</s:else>
				</dd>
			</dl>
			<dl>
				<dt>质押对象</dt>
				<dd>
					<input type="text"
						value="<Field:codeValue tableName="APP___PAS__DBUSER.T_PLEDGE_ORG_TYPE" 
									value="${csPolicyPledgeThirdStopVO.pledgeOrgType}" />"
						readonly="readonly" />
				</dd>
			</dl>
			<dl>
				<dt>质押第三方名称</dt>
				<dd>
					<input type="text" name=""
						value="${csPolicyPledgeThirdStopVO.pledgeThirdParty }"
						readonly="readonly" />
				</dd>
			</dl>
			<dl>
				<dt>组织机构代码</dt>
				<dd>
					<input type="text" name=""
						value="${csPolicyPledgeThirdStopVO.partyCode }"
						readonly="readonly" />
				</dd>
			</dl>
			<dl>
				<dt>经办人姓名</dt>
				<dd>
					<input type="text" name=""
						value="${csPolicyPledgeThirdStopVO.transactorName }"
						readonly="readonly" />
				</dd>
			</dl>
			<dl>
				<dt>经办人联系电话</dt>
				<dd>
					<input type="text" name=""
						value="${csPolicyPledgeThirdStopVO.transactorPhone }"
						readonly="readonly" />
				</dd>
			</dl>
			<dl>
				<dt>经办人证件类型</dt>
				<dd>
					<input type="text"
						value="<Field:codeValue tableName="APP___PAS__DBUSER.T_CERTI_TYPE" 
									value="${csPolicyPledgeThirdStopVO.transactorCertiType}" />"
						readonly="readonly" />
				</dd>
			</dl>
			<dl>
				<dt>经办人证件号</dt>
				<dd>
					<input type="text" name=""
						value="${csPolicyPledgeThirdStopVO.transactorCertiCode }"
						readonly="readonly" />
				</dd>
			</dl>
		</div>
		<div class="divfclass">
			<h1>
				<img src="cs/img/icon/tubiao.png">被保险人信息
			</h1>
		</div>
		<div class="tabdivclass">
			<table class="list" width="100%">
				<thead>
					<tr>
						<th>姓名</th>
						<th>证件类型</th>
						<th>证件号码</th>
						<th>出生日期</th>
						<th>性别</th>
					</tr>
				</thead>
				<tbody>
					<s:iterator value="insureds">
						<tr align="center">
							<td>${name}</td>
							<td><Field:codeValue
									tableName="APP___PAS__DBUSER.T_CERTI_TYPE" value="${certiType}" /></td>
							<td>${certiCode}</td>
							<td><s:date format="yyyy-MM-dd" name="birthDay" /></td>
							<td><Field:codeValue tableName="APP___PAS__DBUSER.T_GENDER"
									value="${gender}" /></td>
						</tr>
					</s:iterator>
				</tbody>
			</table>
		</div>

		<div class="divfclass">
			<h1>
				<img src="cs/img/icon/tubiao.png">受益人信息
			</h1>
		</div>
		<div class="tabdivclass">
			<table class="list" width="100%">
				<thead>
					<tr>
						<th>险种代码</th>
						<th>被保险人</th>
						<th>被保险人证件类型</th>
						<th>被保险人证件号</th>
						<th>受益人姓名</th>
						<th>受益人证件类型</th>
						<th>受益人证件号码</th>
						<th>受益人出生日期</th>
						<th>受益人性别</th>
						<th>受益顺序</th>
						<th>受益份额</th>
					</tr>
				</thead>
				<tbody>
					<s:iterator value="benes">
						<tr align="center">
							<td>${productCode}</td>
							<td>${protectedName}</td>
							<td><Field:codeValue
									tableName="APP___PAS__DBUSER.T_CERTI_TYPE"
									value="${protectedCertiType}" /></td>
							<td>${protectedCertiCode}</td>
							<td>${name}</td>
							<td><Field:codeValue
									tableName="APP___PAS__DBUSER.T_CERTI_TYPE" value="${certiType}" /></td>
							<td>${certiCode}</td>
							<td><s:date format="yyyy-MM-dd" name="birthDay" /></td>
							<td><Field:codeValue tableName="APP___PAS__DBUSER.T_GENDER"
									value="${gender}" /></td>
							<td>${shareOrder}</td>
							<td>${shareRate}</td>
						</tr>
					</s:iterator>
				</tbody>
			</table>
		</div>

	</s:iterator>
	</form>
	<s:if test="queryFlag!=1">
		<div class="formBarButton">
			<button type="button" class="but_blue" onclick="saveCsEndorseCP(); ">保存</button>
		</div>
	</s:if>
	</div>
</div>
<s:include value="/cs/pages/serviceitem/serviceItemBottomBar.jsp"></s:include>