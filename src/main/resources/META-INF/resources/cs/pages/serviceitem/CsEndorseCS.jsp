<%@ page language="java" contentType="text/html; charset=utf-8"
	pageEncoding="utf-8" import="java.util.*,java.text.*"%>
<%@ taglib uri="/struts-tags" prefix="s"%><%@include file="/udmpCommon.jsp"%>
<%@ taglib uri="http://www.git.com.cn/taglib/udmp/field" prefix="Field"%>
<%@ include file="/cs/pages/common/jsp/common_ctx.jsp" %>
<script type="text/javascript" src="${ctx}/cs/js/cs_common.js"></script>
<link rel="stylesheet" href="${ctx }/cs/css/cs_common.css" type="text/css">
<link rel="stylesheet" href="${ctx}/cs/css/public.css" type="text/css">

<script type="text/javascript">
	//帮助部分动作
	function MM_changeProp(objId, theProp, theValue) {
		var obj = null;
		with (document) {
			if (getElementById) {
				obj = getElementById(objId);
			}
		}
		if (obj) {
			if (theValue == true || theValue == false) {
				eval("obj.style." + theProp + "=" + theValue);
			} else {
				eval("obj.style." + theProp + "='" + theValue + "'");
			}
		}
	};

	//保存质押。
	function savePolicyPledge() {
		var $form = $("#savePolicyPledge");
		var action = "${ctx}/cs/serviceitem_cs/savePolicyPledge_PA_csEndorseCSAction.action";
		var onsubmit = "return validateCallback(this);";
		$form.attr("action", action);
		if(checkTel()){
			$form.attr('onsubmit', onsubmit);
			$form.submit();
			}
	}

	//下一步
	function policyPledgeNextStep() {
		var $form = $("#savePolicyPledge");
		var action = "${ctx}/cs/serviceitem_cs/nextStep_PA_csEndorseCSAction.action";
		var onsubmit = "return navTabSearch(this);";
		$form.attr("action", action);
		$form.attr('onsubmit', onsubmit);
		$form.submit();
		
	}
	//上一步
	function upToCsEntry(){
		 $("#gotoCsEntry").click();
	}
</script>
<script type="text/javascript">
	$(document).ready(function() {
		csHelpMenu();
	});

	function checkTel(){
		var mobile=/^1[3|5|8]\d{9}$/,phone=/^0\d{2,3}-?\d{7,8}$/;
		var tel=document.getElementById("jbrlxdh").value;
	    if( mobile.test(tel)||phone.test(tel)){
	    	return true;
	    }else if(tel==null||tel==""){
			alertMsg.error("必填项信息未完整录入，不能受理保单质押第三方止付，请确认。");
			return false;
	    }else{
	    	alertMsg.error("请输入正确的电话号码！");
	    	return false;
	    };
	}

</script>

<!-- ********帮助菜单***********begin********* -->
<s:include value="/cs/pages/common/jsp/helpMenu.jsp"></s:include>
<!-- *********帮助菜单***********end********* -->

<!-- 步骤标识 -->
<s:if test="queryFlag!=1">
<s:include value="csEndorseProgress.jsp"></s:include>
</s:if>

<div class="backgroundCollor"  layOutH='140' >
	<div>
		
	
		<div class="pageFormInfoContent">
		<s:include value="customerInfo_list.jsp" />
			<form action="" id="savePolicyPledge" method="post"
				onsubmit="return validateCallback(this);">
				<div >
					<!-- 隐藏域传值 -->
					<input type="hidden" name="acceptId" value="${acceptId}"> <input
						type="hidden" name="changeId" value="${changeId}"> <input
						type="hidden" name="customerId" value="${customerId}">
					 <input type="hidden" name="csPolicyPledgeThirdStopVO.acceptCode" value="${csPolicyPledgeThirdStopVO.acceptCode}"> 


						<div class="divfclass">
							<h1>
								<img src="${ctx}/cs/img/icon/tubiao.png"">第三方信息
							</h1>
						</div>				
	
					<div class="pageFormInfoContent">
						<dl>
							<dt><font color="red">*</font>质押对象</dt>
							<dd><s:if test="queryFlag==1">
									<input type="text"
									value="<Field:codeValue tableName="APP___PAS__DBUSER.T_PLEDGE_ORG_TYPE" 
									value="${csPolicyPledgeThirdStopVO.pledgeOrgType}" />"
									readonly="readonly" />
								</s:if>
								<s:else>
								<Field:codeTable cssClass="combox" nullOption="true"
									name="csPolicyPledgeThirdStopVO.pledgeOrgType"  
									tableName="APP___PAS__DBUSER.T_PLEDGE_ORG_TYPE" 
									value="${csPolicyPledgeThirdStopVO.pledgeOrgType}" />
								</s:else>
							</dd>
						</dl>
						<dl>
							<dt><font color="red">*</font>质押第三方名称</dt>
							<dd>
								<input type="text" value="${csPolicyPledgeThirdStopVO.pledgeThirdParty}"
									name="csPolicyPledgeThirdStopVO.pledgeThirdParty" <s:if test="queryFlag==1">readonly="true"</s:if>
									 />
							</dd>
						</dl>
						<dl>
							<dt><font color="red">*</font>组织机构代码</dt>
							<dd>
								<input type="text"  value="${csPolicyPledgeThirdStopVO.partyCode}"
								   name="csPolicyPledgeThirdStopVO.partyCode" <s:if test="queryFlag==1">readonly="true"</s:if>
									 />
							</dd>
						</dl>
						<dl>
							<dt><font color="red">*</font>经办人姓名</dt>
							<dd>
								<input type="text" value="${csPolicyPledgeThirdStopVO.transactorName}"
									name="csPolicyPledgeThirdStopVO.transactorName" <s:if test="queryFlag==1">readonly="true"</s:if>
									 />
							</dd>
						</dl>
						<dl>
							<dt><font color="red">*</font>经办人联系电话</dt>
							<dd>
							<!--type="expandPhone"  type="expandMobile" class="phone required"-->
								<input  value ="${csPolicyPledgeThirdStopVO.transactorPhone }" onblur="checkTel()" id="jbrlxdh"
									name="csPolicyPledgeThirdStopVO.transactorPhone" <s:if test="queryFlag==1">readonly="true"</s:if>
									 />
							</dd>
						</dl>
						<dl>
							<dt><font color="red">*</font>经办人证件类型</dt>
							<dd>
								<s:if test="queryFlag==1">
									<input type="text"
									value="<Field:codeValue tableName="APP___PAS__DBUSER.T_CERTI_TYPE" 
									value="${csPolicyPledgeThirdStopVO.transactorCertiType}" />"
									readonly="readonly" />
								</s:if>
								<s:else>
								<Field:codeTable cssClass="combox" nullOption="true" 
									name="csPolicyPledgeThirdStopVO.transactorCertiType"
									tableName="APP___PAS__DBUSER.T_CERTI_TYPE" value="${csPolicyPledgeThirdStopVO.transactorCertiType }" />
								</s:else>
							</dd>
						</dl>
						<dl>
							<dt><font color="red">*</font>经办人证件号</dt>
							<dd>
								<input type="text"  value="${csPolicyPledgeThirdStopVO.transactorCertiCode }"	
									name="csPolicyPledgeThirdStopVO.transactorCertiCode" <s:if test="queryFlag==1">readonly="true"</s:if>
									 />
							</dd>
						</dl>
						
		<div class="formBarButton">	
			<s:if test="queryFlag != 1">
				<button type="button" class="but_blue" onclick="savePolicyPledge();">保存</button>
			</s:if>
		</div>
						
						
						
					</div>
				</div>
			</form>

			<s:iterator value="csPolicyPledgeThirdStopVO.csPolicyInfoVOList">
				<div >
				
				<div class="divfclass">
							<h1>
								<img src="${ctx}/cs/img/icon/tubiao.png"">保单详情
							</h1>
						</div>
				
					<div class="pageFormInfoContent">
						<dl>
							<dt>保单号</dt>
							<dd>
								<input type="text" value="${policyCode }" readonly="readonly" />
							</dd>
						</dl>
						<dl>
							<dt>保单生效日</dt>
							<dd>
								<input type="text"
									value="<s:date format="yyyy-MM-dd" name="validateDate"/>"
									readonly="readonly" />
							</dd>
						</dl>
						<dl>
							<dt>保险止期</dt>
							<dd>
								<input type="text"
									value="<s:date format="yyyy-MM-dd" name="expiryDate"/>"
									readonly="readonly" />
							</dd>
						</dl>
						<dl>
							<dt>现金价值</dt>
							<dd>
								<input type="text" value="${cashPrice}" readonly="readonly" />
							</dd>
						</dl>
						<dl>
							<s:if test="firstGetDate!=null">
							<dt>首次领取日期</dt>
							<dd>
								<input type="text"
									value="<s:date format="yyyy-MM-dd" name="firstGetDate"/>"
									readonly="readonly" />
							</dd>
							</s:if>
							<s:else>
							<dt>首次领取日期</dt>
							<dd>
								<input type="text"
									value="<s:date format="yyyy-MM-dd" name="maturityDate"/>"
									readonly="readonly" />
							</dd>
							</s:else>
						</dl>
						<dl>
							<dt>投保人姓名</dt>
							<dd>
								<input type="text" value="${name}" readonly="readonly" />
							</dd>
						</dl>
						<dl>
							<dt>投保人证件类型</dt>
							<dd>
								<input type="text"
									value="<Field:codeValue tableName="APP___PAS__DBUSER.T_CERTI_TYPE" value="${certiType}" />"
									readonly="readonly" />
							</dd>
						</dl>
						<dl>
							<dt>投保人证件号</dt>
							<dd>
								<input type="text" value="${certiCode}" readonly="readonly" />
							</dd>
						</dl>
						<div >
						
							<div class="divfclass">
							<h1>
								<img src="${ctx}/cs/img/icon/tubiao.png"">被保险人信息
							</h1>
						</div>
						
							<div class="tabdivclass">
								<table class="list" width="100%">
									<thead>
										<tr>
											<th>姓名</th>
											<th>证件类型</th>
											<th>证件号码</th>
											<th>出生日期</th>
											<th>性别</th>
										</tr>
									</thead>
									<tbody>
										<s:iterator value="insureds">
											<tr align="center">
												<td>${name}</td>
												<td><Field:codeValue tableName="APP___PAS__DBUSER.T_CERTI_TYPE"
														value="${certiType}" /></td>
												<td>${certiCode}</td>
												<td><s:date format="yyyy-MM-dd" name="birthDay" /></td>
												<td><Field:codeValue tableName="APP___PAS__DBUSER.T_GENDER"
														value="${gender}" /></td>
											</tr>
										</s:iterator>
									</tbody>
								</table>
							</div>
						</div>
						<div  >
						
							<div class="divfclass">
							<h1>
								<img src="${ctx}/cs/img/icon/tubiao.png"">受益人信息
							</h1>
						</div>
							
							<div class="tabdivclass">
								<table class="list" width="100%">
									<thead>
										<tr>
											<th>险种代码</th>
											<th>被保险人</th>
											<th>被保险人证件类型</th>
											<th>被保险人证件号</th>
											<th>受益人姓名</th>
											<th>受益人证件类型</th>
											<th>受益人证件号码</th>
											<th>受益人出生日期</th>
											<th>受益人性别</th>
											<th>受益顺序</th>
											<th>受益份额</th>
										</tr>
									</thead>
									<tbody>
										<s:iterator value="benes">
											<tr align="center">
												<td>${productCode}</td>
												<td>${protectedName}</td>
												<td><Field:codeValue tableName="APP___PAS__DBUSER.T_CERTI_TYPE"
														value="${protectedCertiType}" /></td>
												<td>${protectedCertiCode}</td>
												<td>${name}</td>
												<td><Field:codeValue tableName="APP___PAS__DBUSER.T_CERTI_TYPE"
														value="${certiType}" /></td>
												<td>${certiCode}</td>
												<td><s:date format="yyyy-MM-dd" name="birthDay" /></td>
												<td><Field:codeValue tableName="APP___PAS__DBUSER.T_GENDER"
														value="${gender}" /></td>
												<td>${shareOrder}</td>
												<td>${shareRate}</td>
											</tr>
										</s:iterator>
									</tbody>
								</table>
							</div>
						</div>
					</div>
				</div>
			</s:iterator>
		</div>
	</div>
	
</div>

<s:include value="/cs/pages/serviceitem/serviceItemBottomBar.jsp"></s:include>






