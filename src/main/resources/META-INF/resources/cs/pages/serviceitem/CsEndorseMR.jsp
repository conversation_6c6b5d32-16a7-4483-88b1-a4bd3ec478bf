<%@ page language="java" contentType="text/html; charset=utf-8"
	pageEncoding="utf-8"%>
<%@ taglib uri="/struts-tags" prefix="s"%><%@include
	file="/udmpCommon.jsp"%>
<!-- 引入点击下一步返回保全录入的js -->
<%-- <s:set var="ctx">${pageContext.request.contextPath}</s:set> --%>
<%@ include file="/cs/pages/common/jsp/common_ctx.jsp"%>
<script type="text/javascript"
	src="cs/pages/common/js/returnAcceptAndPay.js"></script>
<%@ taglib uri="http://www.git.com.cn/taglib/udmp/field" prefix="Field"%>
<script type="text/javascript" src="${ctx}/cs/js/cs_common.js"></script>
<link rel="stylesheet" href="${ctx }/cs/css/cs_common.css"
	type="text/css">
<link rel="stylesheet" href="${ctx }/cs/css/public.css" type="text/css">
<script type="text/javascript" src="${ctx}/cs/js/DomZoomZX.js"></script>
<script>
	$(document).ready(function(){
        var D = DomZoomZX('input,select,textarea,td')
	})
</script>
<input type="hidden" name="menuId" id="menuId" value="${menuId}">
<input type="hidden" name="itemFlag" id="itemFlag" value="mainProd">
<input type="hidden" name="itemName" id="itemName" value="主险续保">
<!-- 帮助菜单 -->
<!-- ********帮助菜单***********begin********* -->
<s:include value="/cs/pages/common/jsp/helpMenu.jsp"></s:include>
<!-- *********帮助菜单***********end********* -->
<%-- 进度条 --%>
<s:include value="csEndorseProgress.jsp" />


<div  class = "backgroundCollor" layoutH="140">

	<s:include value="customerInfo_list.jsp" />

	<div class="divfclass">
		<h1>
			<img src="cs/img/icon/tubiao.png">主险续保
		</h1>
	</div>
	<!-- validateCallback  -->
	<form id="mainProdRenewalForm"
		action="${ctx}/cs/serviceitem_mr/mainProdRenewal_PA_csEndorseMRAction.action?flag=1"
		class="pageForm required-validate" method="post"
		onsubmit="return navTabSearch(this);">
		<input type="hidden" id="customerId" name="customerId"
			value="${customerId}" /> <input type="hidden" id="acceptId"
			name="acceptId" value="${acceptId}" /> <input type="hidden"
			id="changeId" name="changeId" value="${changeId}" />
			<input type="hidden"
			id="totalPremAf" name="totalPremAf"/>
	
		
		<div class="divfclass">
			<h1>
				<img src="${ctx}/cs/img/icon/tubiao.png" />查询结果
			</h1>
		</div>
		<div class="tabdivclass">
			<table class="list" width="100%">
				<thead>
					<tr>
						<th>保单号</th>
						<th>险种代码</th>
						<th>险种名称</th>
						<th>基本保额</th>
						<th>累计红利保额</th>
						<th>满期终了红利</th>
						<th>有效保额</th>
						<th>保费</th>
						<th>险种状态</th>
					</tr>
				</thead>
				<tbody>
					<s:iterator value="mrMainProdRenewalVOs" status="st" id="BFList">
						<tr align="center">
							<td>${policyCode }</td>
							<td>${busiProdCode }</td>
							<td><Field:codeValue
									tableName="APP___PAS__DBUSER.T_BUSINESS_PRODUCT"
									value="${busiPrdId}" /></td>
							<td>${basicAmount }</td>
							<td>${summationBonusAmount }</td>
							<td>${fillExpectBonus }</td>
							<td id="${busiProdCode }">${validAmount }</td>
							<td>${totalPremAf }</td>
							<td><Field:codeValue
									tableName="APP___PAS__DBUSER.T_LIABILITY_STATUS"
									value="${liabilityState}" /></td>
						</tr>
					</s:iterator>
				</tbody>
			</table>
		</div>
		<div class="pageFormbut">
			<button <s:if test="queryFlag==1">style="display:none"</s:if>
				type="button" class="but_blue" onclick="mainProdRenewal()">续保</button>
		</div>
	</form>
	<div id="updateEndDiv">
		<div class="divfclass">
			<h1>
				<img src="${ctx}/cs/img/icon/tubiao.png" />变更后信息
			</h1>
		</div>
		<div class="tabdivclass">
			<table class="list" width="100%" id="decreaseAFDetail">
				<thead>
					<tr>
						<th>保单号</th>
						<th>险种代码</th>
						<th>险种名称</th>
						<th>基本保额</th>
						<th>保费</th>
						<th>生效日期</th>
						<th>终止日期</th>
					</tr>
				</thead>
				<tbody id="allInfoContent">
					<s:iterator value="mrMainProdRenewalVONews" status="AFst"
						id="AFList">
						<s:if test="basicAmount!=0">
							<tr align="center">
								<td>${policyCode }</td>
								<td>${busiProdCode }</td>
								<td><Field:codeValue
										tableName="APP___PAS__DBUSER.T_BUSINESS_PRODUCT"
										value="${busiPrdId}" /></td>
								<td>${basicAmount}</td>
								<td>${totalPremAf}</td>
								<td><s:date name="validateDate" format="yyyy-MM-dd"></s:date></td>
								<td><s:date name="expiryDate" format="yyyy-MM-dd"></s:date></td>
							</tr>
						</s:if>
					</s:iterator>
				</tbody>
			</table>
		</div>
	</div>
</div>
<s:include value="/cs/pages/serviceitem/serviceItemBottomBar.jsp"></s:include>

<script type="text/javascript">
	$(document).ready(function() {
		csHelpMenu();
	});
	//续保操作
	function mainProdRenewal() {
		var $form = $("#mainProdRenewalForm", navTab.getCurrentPanel());
		var totalPremAf = $("#00609000", navTab.getCurrentPanel()).text();
		$("#totalPremAf", navTab.getCurrentPanel()).val(totalPremAf);
		$form.submit();
	}
	//上一步
	function upToCsEntry() {
		alertMsg
				.confirm(
						"请确认是否需要保存变更的信息？",
						{
							okCall : function() {
								//保存变更信息---返回保全录入主页面
								var onsubmit = "return validateCallback(this,reusltAjaxDone)";
								$("#mainProdRenewalForm",
										navTab.getCurrentPanel()).attr(
										"onsubmit", onsubmit);
								var action = "${ctx}/cs/serviceitem_mr/mainProdRenewal_PA_mrMainProdRenewalAction.action?flag=2";
								$("#mainProdRenewalForm",
										navTab.getCurrentPanel()).attr(
										"action", action);
								mainProdRenewal();
							},
							cancelCall : function() {
								$("#gotoCsEntry").click();
							}
						});
	}
	//回调函数，跳转页面
	function reusltAjaxDone(json) {
		DWZ.ajaxDone(json);
		if (json.statusCode == DWZ.statusCode.ok) {
			$("#gotoCsEntry").click();
		} else {
			alertMsg.error("系统异常!");
		}
	}
	//下一步
	function next() {
		var val1 = $("#changeId").val();
		var val2 = $("#acceptId").val();
		var val3 = $("#customerId").val();
		var title = "受理信息录入";
		var tabid = "_aplPermit_M";
		var fresh = eval("true");
		var external = eval("false");
		var url = "${ctx}/cs/csEntry/showAcceptAndArapInfo_PA_csEntryAction.action?&menuId=${menuId}&changeId="
				+ val1 + "&acceptId=" + val2 + "&customerId=" + val3;
		navTab.openTab(tabid, url, {
			title : title,
			fresh : fresh,
			external : external
		});

	}

	
</script>