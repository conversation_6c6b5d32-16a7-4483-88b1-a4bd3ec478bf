<%@ page language="java" contentType="text/html; charset=utf-8"
	pageEncoding="utf-8" import="java.util.*,java.text.*"%>
<%@ taglib uri="/struts-tags" prefix="s"%><%@include
	file="/udmpCommon.jsp"%>
<%@ taglib uri="http://www.git.com.cn/taglib/udmp/field" prefix="Field"%>
<%@ include file="/cs/pages/common/jsp/common_ctx.jsp"%>
<script type="text/javascript" src="${ctx}/cs/js/cs_common.js"></script>
<link rel="stylesheet" href="${ctx }/cs/css/cs_common.css" type="text/css"/>

<!-- 帮助菜单-->
<s:include value="/cs/pages/common/jsp/helpMenu.jsp"></s:include>
<!-- 步骤标识 -->
<s:include value="csEndorseProgress.jsp" />

<div class="pageContent" style="background-color: white" layoutH="50px">
		<input type="hidden"  id="changeId"  value="${changeId }"/> 
		<input type="hidden"  id="acceptId"  value="${acceptId }"/> 
		
		<div class="divfclass">
			<h1>
				<img src="${ctx}/cs/img/icon/tubiao.png">领取方式变更
			</h1>
		</div>
		<div class="pageContent" layoutH="180">
		
		<s:include value="customerInfo_list.jsp" />

		<!-- 约定下一年的保单周年日领取频率 -->
		<s:if test="oldCsPreConPayList!=null&&oldCsPreConPayList.size()>0">
			<div class="divfclass">
				<h1>
					<img src="${ctx}/cs/img/icon/tubiao.png">保单险种信息列表
				</h1>
			</div>

			<div class="tabdivclass">
				<table class="list" width="100%" id="_precontPayList">
					<thead>
						<tr>
							<th colName='precontId' inputType="checkbox">选择</th>
							<th colName="policyChgId" style="display: none">policyChgId</th>
							<th>保单号</th>
							<th>险种代码</th>
							<th>险种名称</th>
							<th>责任名称</th>
							<th>下一保单年度领取频率</th>
							<th>下一保单年度领取标准</th>
							<th>下一保单年度开始领取日期</th>
						</tr>
						</thead>
						<tbody>
							<s:iterator value="oldCsPreConPayList">
								<tr tr_saveStatus="1">
									<td><input name="precontId" value="${precontId }" type="checkbox"/></td>
									<td style="display: none" >${policyChgId }</td>
									<td>${policyCode }</td>
									<td>${busiProdCode }</td>
									<td>${busiProdName }</td>
									<td>${liabName }</td>
									<td><Field:codeValue tableName="APP___PAS__DBUSER.T_PAY_TYPE" value="${newPlanFreq}" /></td>
									<td>${newInstalmentAmount }</td>
									<td><s:date name="precontTime" format="yyyy-MM-dd" /></td>
								</tr>
							</s:iterator>
						</tbody>
				</table>
			</div>

			<div class="pageFormdiv" <s:if test="queryFlag == 1">style="display: none"</s:if>>
				<button class="but_blue" type="button" onclick="_cancelPrecont()">取消约定变更</button>
			</div>
		</s:if>
		<!-- 保单险种列表信息 -->
			<div class="divfclass">
				<h1>
					<img src="${ctx}/cs/img/icon/tubiao.png">保单险种列表信息
				</h1>
			</div>
			<div class="main_FormContent">
				<input id="jsons" name="jsons" type="hidden" value="" />
				<table id="gmTableInform" name="gmTableInform" class="list" width="100%" >
				<input style="display: none" name="isShowInAm" id="isShowInAm" value="${isShowInAm }"/>
					<thead>
						<tr>
							<th style="width:54px;" colName="logId" inputType="checkbox" <s:if test="queryFlag == 1">style="display: none"</s:if>>选择
								<br/><input  id="_ckAll"  type="checkbox" class="selectChange" />
							</th>
							<th colName="policyCode">保单号</th>
							<th colName="busiProdCode">险种代码</th>
							<th colName="busiProdId">险种名称</th>
							<th>责任名称</th>
							<th colName="amount">基本保额/份</th>
							<th colName="planFreq">领取方式</th>
							<s:if test="isShowInAm!=null && isShowInAm!=0">
								<th colName="instalmentAmount">领取标准</th>
							</s:if>
							<th colName="beginDate">开始领取日期</th>							
							<th colName="changePlanFreq" inputType="select" style="width: 150px">变更后领取方式</th>
							<s:if test="(busiCode928!=null&&busiCode928.length()>0) || (busiCodeSY!=null&&busiCodeSY.length()>0)" >
								<th colName="newPlanFreq" inputType="select">变更后领取频率</th>
							</s:if>
							<th colName="policyChgId" style="display: none">policyChgId</th>
							<th colName="changeId" style="display: none">changeId</th>
							<th colName="planId" style="display: none">planId</th>
							<th colName="acceptId" style="display: none">acceptId</th>
							<th colName="payDueDate" style="display: none">payDueDate</th>
							<th colName="policyPrdFlag" style="display: none">policyPrdFlag</th>
						</tr>
					</thead>
					<tbody>
						<s:iterator value="oldCsEndorseGMVOList" status="st"
							id="gm">
							<tr align="center" tr_saveStatus="1" class="instalment"  <s:if test="queryFlag==1">disabled="disabled"</s:if>>
								<td <s:if test="queryFlag == 1">style="display: none"</s:if>>
									<input type="checkbox" class="logId" name="logId" value="${logId}" onclick="checkItem()"
									 <s:if test="csEndorseGBSaveed.csEndorseGBndVOList.size()>0" >checked="true"</s:if>>
								</td>
								<td>${policyCode}</td>
								<td>${busiProdCode }</td>
								<td><Field:codeValue tableName="APP___PAS__DBUSER.T_BUSINESS_PRODUCT" value="${busiProdId}" /></td>
								<td>${liabName}</td>
								<td>${amount}/${unit}</td>
								
								<%--领取方式（变更前 ）--%>
								<td>
									<s:if test='oldPayType=="Y"'>保证${oldPayValue}年终身领取</s:if>
									<s:elseif test='oldPayType=="F"'>固定期限${oldPayValue}年领取</s:elseif>
									<s:elseif test='oldPayType=="V"'>保证返还账户价值终身领取</s:elseif>
									<s:elseif test='oldPayType=="O"'>一次性领取</s:elseif>
									<s:else>
									<Field:codeValue tableName="APP___PAS__DBUSER.T_PAY_TYPE" value="${planFreq}" />
									</s:else>
								</td>
								
								<s:if test="isShowInAm!=null &&  isShowInAm!=0">
									<td>${instalmentAmount}</td>
								</s:if>
								<td><s:date name="beginDate" format="yyyy-MM-dd" /></td>
								
								<%--变更后领取方式 --%>
								<td>
									<s:if test="busiCodeSY!=null&&busiCodeSY.length()>0">
										<%--税延产品 --%>
										<select id="changePlanFreq" onchange="syPlanFreq(this)">
											<option value="F-15" <s:if test='newPayType=="F" and newPayValue=="15"'>selected</s:if>>固定期限15年领取</option>
											<option value="F-20" <s:if test='newPayType=="F" and newPayValue=="20"'>selected</s:if>>固定期限20年领取</option>
											<option value="V-999" <s:if test='newPayType=="V" and newPayValue=="999"'>selected</s:if>>终身领取</option>
											<option value="O-1" <s:if test='newPayType=="O" and newPayValue=="1"'>selected</s:if>>一次性领取</option>
										</select>
									</s:if>
									<%-- <s:elseif test="specialAccountFlag!=null&&specialAccountFlag==1&& busiCode4911!='********'">
										专户保单
										<select id="changePlanFreq" onchange="syPlanFreq(this)">
											<option value="Y-20" <s:if test='newPayType=="Y" and newPayValue=="20"'>selected</s:if>>保证20年终身领取</option>
											<option value="F-10" <s:if test='newPayType=="F" and newPayValue=="10"'>selected</s:if>>固定期限10年领取</option>
											<option value="F-15" <s:if test='newPayType=="F" and newPayValue=="15"'>selected</s:if>>固定期限15年领取</option>
											<option value="F-20" <s:if test='newPayType=="F" and newPayValue=="20"'>selected</s:if>>固定期限20年领取</option>
											<option value="V-999" <s:if test='newPayType=="V" and newPayValue=="999"'>selected</s:if>>保证返还账户价值终身领取</option>
											<option value="O-1" <s:if test='newPayType=="O" and newPayValue=="1"'>selected</s:if>>一次性领取</option>
										</select>
									</s:elseif> --%>
									<s:elseif test="busiCode928!=null&&busiCode928.length()>0">
										<%--Z01/Z011 --%>
										<s:if test="busiCodeZ01!=null&&busiCodeZ01.length()>0">
											<select id="changePlanFreq" onchange="syPlanFreq(this)">
											<option value="V-999" <s:if test='newPayType=="V" and newPayValue=="999"'>selected</s:if>>保证返还账户价值终身领取</option>
											<option value="F-10" <s:if test='newPayType=="F" and newPayValue=="10"'>selected</s:if>>固定期限10年领取</option>
											<option value="F-15" <s:if test='newPayType=="F" and newPayValue=="15"'>selected</s:if>>固定期限15年领取</option>
											<option value="F-20" <s:if test='newPayType=="F" and newPayValue=="20"'>selected</s:if>>固定期限20年领取</option>
											<s:if test="busiProdCode=='00Z01100'">
											<option value="O-1"  <s:if test='newPayType=="O"'>selected</s:if>>一次性领取</option>
											</s:if>
											</select>
										</s:if>
										<s:else>
										<%--928 --%>
											<select id="changePlanFreq">
												<option value="Y-20" <s:if test='newPayType=="Y" and newPayValue=="20"'>selected</s:if>>保证20年终身领取</option>
												<option value="F-10" <s:if test='newPayType=="F" and newPayValue=="10"'>selected</s:if>>固定期限10年领取</option>
												<option value="F-15" <s:if test='newPayType=="F" and newPayValue=="15"'>selected</s:if>>固定期限15年领取</option>
												<option value="F-20" <s:if test='newPayType=="F" and newPayValue=="20"'>selected</s:if>>固定期限20年领取</option>
												<option value="V-999" <s:if test='newPayType=="V" and newPayValue=="999"'>selected</s:if>>保证返还账户价值终身领取</option>
											</select>
										</s:else>
									</s:elseif>
									<s:elseif test="busiProdCode=='00455000'|| busiProdCode=='00459000'|| busiProdCode=='********' || busiProdCode=='00933100' || busiProdCode == '00598000' || busiProdCode == '00598100'
													|| busiProdCode == '00S09000' || busiProdCode == '00S09100'">
										<select id="changePlanFreq">
												<option value="1" <s:if test="newPlanFreq==1 ">selected</s:if>>年领</option>
												<option value="4" <s:if test="newPlanFreq==4 ">selected</s:if>>月领</option>
										</select>
									</s:elseif>
									<s:else>
								    	<select id="changePlanFreq">
										<option>请选择</option>
										     <s:iterator value="planFreqs" var="plan" >
	                                                 <s:if test="#plan==1 && #plan != planFreq"><option value="1" <s:if test="newPlanFreq == 1"> selected </s:if>>年领</option></s:if>
	                                                 <s:if test="#plan==4 && #plan != planFreq"><option value="4" <s:if test="newPlanFreq == 4"> selected </s:if>>月领</option></s:if>
	                                                 <s:if test="#plan==5 && #plan != planFreq"><option value="5" <s:if test="newPlanFreq == 5"> selected </s:if>>趸领</option></s:if>
	                                           </s:iterator>
	                                      </select>
									</s:else>
								</td>
								
								
								<%--变更后领取频率 --%>
								<s:if test="((busiCode928!=null&&busiCode928.length()>0) || 
										(busiCodeSY!=null&&busiCodeSY.length()>0)) && (busiProdCode!='********')" >
									<td>
										<select id="newPlanFreq">
											<option value="1" <s:if test="newPlanFreq==1 ">selected</s:if>>年领</option>
											<option value="4" <s:if test="newPlanFreq==4 ">selected</s:if>>月领</option>
										</select>
									</td> 
								</s:if>
								 
								<td style="display: none">${policyCHGId }</td>
								<td style="display: none">${changeId }</td>
								<td style="display: none">${planId }</td>
								<td style="display: none">${acceptId }</td>
								<td style="display: none"><s:date name="payDueDate" format="yyyy-MM-dd" /></td>
								<td style="display: none">${policyPrdFlag }</td>
							</tr>
						</s:iterator>
					</tbody>
				</table>
				<div class="pageFormdiv" <s:if test="queryFlag == 1">style="display: none"</s:if>>
					<button class="but_blue" type="button" onclick="saveCsPayPlan()">保存</button>
			    </div>	
			</div>
					
			<!-- 变更后信息 -->
			<div class="pageContent" id="changeBankMsg">
				<s:include value="/cs/pages/serviceitem/CsEndorseGM_saveResult.jsp"/>
			</div>
		
	</div>
	<s:include value="/cs/pages/serviceitem/serviceItemBottomBar.jsp"></s:include>
</div>
<script type="text/javascript">
function syPlanFreq(obj){
	var planFreq = $(obj).val().charAt(0);
		debugger;
	if(planFreq == 'O'){
		$(obj).parent().parent().find("select[id='newPlanFreq']").append("<option value='5'>一次性领取</option>");
		$(obj).parent().parent().find("select[id='newPlanFreq']").val(5);
		$(obj).parent().parent().find("select[id='newPlanFreq']").attr("disabled","disabled");
	}else{
		$(obj).parent().parent().find("select[id='newPlanFreq']").find("option[value='5']").remove();
		$(obj).parent().parent().find("select[id='newPlanFreq']").attr("disabled",false);
	}
}

function newGMChange(line){
	
	var table = $("#gmTableInform", navTab.getCurrentPanel());
	var rows = table.find("tr");
	
	for (var n = 1; n < rows.length; n++) {
		var policyPrdFlag = $(rows[n]).find("td").eq(13).text();
		if(policyPrdFlag==1){
			if (line.value == 01) {
				$("select[name='changeGM']").find("option[value='01']").attr("selected", true);
			}
			if (line.value == 02) {
				$("select[name='changeGM']").find("option[value='02']").attr("selected", true);
			}
			if (line.value == 03) {
				$("select[name='changeGM']").find("option[value='03']").attr("selected", true);
			}
			if (line.value == 04) {
				$("select[name='changeGM']").find("option[value='04']").attr("selected", true);
			}
			if (line.value == 05) {
				$("select[name='changeGM']").find("option[value='05']").attr("selected", true);
			}
			if (line.value == 06) {
				$("select[name='changeGM']").find("option[value='06']").attr("selected", true);
			}
			
		}
	}
}

	//保存
	function saveCsPayPlan() {
		
		var $checkIds = $("#gmTableInform", navTab.getCurrentPanel()).find("input:checkbox:checked");
		if ($checkIds.size() <= 0) {	 
			alertMsg.error("请选择要保存的信息！");
			return false;
		}
		
		var changePlanFreq = $('#changePlanFreq option:selected', navTab.getCurrentPanel()).text();
		if(changePlanFreq == '请选择'){
			alertMsg.error("必填项信息未完整录入，不能受理领取方式变更，请确认。");
			return false;
		}
		alertMsg.confirm(
						"确认保存以上信息？",
						{okCall : function() {
								var _jsons = "";
								var $jsonsText = $("input[name='jsons']",navTab.getCurrentPanel());
								var $table = $("#gmTableInform", navTab.getCurrentPanel());
								
								
								$table.find("tbody tr").each(function(){
									debugger;
									var $checkIds = $(this).find("input:checkbox:checked");
									if($checkIds.size()<=0){
										$(this).removeAttr("tr_saveStatus");
									}									  
								});
								
								/*   alert($table.html());  */
								_jsons = _cs_tableToJson($table);
								/*  alert(_jsons); */
								$jsonsText.val(_jsons);
								$.ajax({
											url : "${ctx}/cs/serviceitem_gm/saveCsPayPlan_PA_csEndorseGMAction.action",
											type : "post",
											dataType : 'html',
											data : "jsonString="
													+ $("#jsons", navTab.getCurrentPanel()).val(),
											cache : false,
											success : function(response) {
												alertMsg.correct("信息保存成功");
												var json = DWZ
														.jsonEval(response);
												if (undefined == json.statusCode) {
													$("#changeBankMsg", navTab.getCurrentPanel()).html(response).initUI();
												} else {
													alertMsg.error(json.message);
															
												}

											}
										});
							},
							cancelCall : function() {
							}
						});
	}
	
	$(document).ready(function() {
		csHelpMenu();
		
		checkItem();		
		syChange();
	});	
	
	$("#_ckAll", navTab.getCurrentPanel()).click(function() {
		var flag = $(this).is(':checked');
		
		$("input.logId","table#gmTableInform").each(function() {								
			$(this).attr("checked", flag);			
		});
	});
	
	
	function checkItem(){
		debugger;
		var isAll=true;
		$("input.logId","table#gmTableInform").each(function() {								
			if($(this).is(':checked')==false){
				isAll=false;
				return;
			}
		});		
		$("#_ckAll").attr("checked", isAll);
	}	
	function syChange(){
		debugger;
		var table = $("#gmTableInform", navTab.getCurrentPanel());
		var rows = table.find("tr");
		for (var n = 1; n < rows.length; n++) {
			var planFreq = $(rows[n]).find("td").find("select[id='changePlanFreq']").val().charAt(0);
			if(planFreq == 'O'){
				$(rows[n]).find("td").find("select[id='newPlanFreq']").append("<option value='5'>一次性领取</option>");
				$(rows[n]).find("td").find("select[id='newPlanFreq']").val(5);
				$(rows[n]).find("td").find("select[id='newPlanFreq']").attr("disabled","disabled");
			}
		}
	}	
	
</script>
<script type="text/javascript">
	$(document).ready(function() {
		debugger;
		csHelpMenu();
		
	});	
	
    //取消约定变更
	function _cancelPrecont(){
    	debugger;
		var $checkIds = $("#_precontPayList", navTab.getCurrentPanel()).find("input:checkbox:checked");
		if ($checkIds.size() <= 0) {	 
			alertMsg.error("请勾选要取消的约定变更任务！");
			return false;
		}
		
		var $table = $("#_precontPayList", navTab.getCurrentPanel());
		 
		$table.find("tbody tr").each(function(){
			debugger;
			var $checkIds = $(this).find("input:checkbox:checked");
			if($checkIds.size()<=0){
				$(this).removeAttr("tr_saveStatus");
			}									  
		});
		 
		var _jsons = _cs_tableToJson($table);
		var changeId=$("#changeId", navTab.getCurrentPanel()).val();
		var acceptId=$("#acceptId", navTab.getCurrentPanel()).val();
		$.ajax({
			url : "${ctx}/cs/serviceitem_gm/cancelPrecontInfo_PA_csEndorseGMAction.action?changeId="+changeId+"&acceptId="+acceptId,
			type : "post",
			dataType : 'html',
			data : "jsonString="+ _jsons,
			cache : false,
			success : function(response) {
				$("#changeBankMsg", navTab.getCurrentPanel()).html(response).initUI();
			}
		});
		
	}
	 
	
</script>