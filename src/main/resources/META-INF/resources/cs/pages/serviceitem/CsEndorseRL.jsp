<%@ page language="java" contentType="text/html; charset=utf-8"
	pageEncoding="utf-8"%>
<%@ taglib uri="/struts-tags" prefix="s"%><%@include file="/udmpCommon.jsp"%>
<!-- 引入点击下一步返回保全录入的js -->
<%@ include file="/cs/pages/common/jsp/common_ctx.jsp" %>
<script type="text/javascript" src="${ctx }/cs/pages/common/js/returnAcceptAndPay.js"></script>
<%@ taglib uri="http://www.git.com.cn/taglib/udmp/field" prefix="Field"%>
<script type="text/javascript" src="${ctx}/cs/js/cs_common.js"></script>
<link rel="stylesheet" href="${ctx }/cs/css/cs_common.css" type="text/css">
<link rel="stylesheet" href="${ctx}/cs/css/public.css" type="text/css">
<script type="text/javascript" src="${ctx}/cs/js/DomZoomZX.js"></script>
<script>
	 $(document).ready(function(){
        var D = DomZoomZX('input,select,textarea,td')
	}) 
</script>

<s:include value="/cs/pages/common/jsp/helpMenu.jsp"></s:include>
<s:include value="csEndorseProgress.jsp" />

<div class="pageContent"  id="csEndorseRL${fromCheck}" layoutH="140">
	<!-- 客户信息 -->
	<s:include value="customerInfo_list.jsp" />
	
	<!-- 保全受理实名查验 -->
	<s:if test="verifyFlag==1">
		<s:include value="verifyInfo_list.jsp" />
	</s:if>
	
	<form id="policyLoanRevivalForm"
		action="${ctx}/cs/serviceitem_rl/saveRevivalInfo_PA_csEndorseRLAction.action"
		class="pageForm required-validate" method="post"
		onsubmit="return navTabSearch(this,'PolicyLoanRevivalDiv')">

		<input type="hidden" name="changeId" id="changeId" value="${changeId}" />
		<input type="hidden" name="acceptId" id="acceptId" value="${acceptId}" />
		<input type="hidden" name="customerId" id="customerId" value="${customerId}" />
		<input type="hidden" id="csDifOrgCfgFlag" name="csDifOrgCfgFlag"  value="${csDifOrgCfgFlag}">

		<div class="divfclass">
			<h1>
				<img src="images/tubiao.png">保单原贷款信息
			</h1>
		</div>

		<div class="tabdivclass">
			<table class="list" width="100%" >
				<thead>
					<tr>
						<th>保单号</th>
						<th colName="insuredNames" >被保险人</th><!-- 151299 险种被保人姓名 -->
						<th>险种代码</th>
						<th>险种名称</th>
						<th style="width:80px;">原贷款本金</th>
						<th>原贷款起期</th>
						<th style="width:80px;">原贷款起息日</th>
						<th>原贷款止期</th>
						<th>初始利率</th>
						<th>初始利息</th>
						<th style="width:60px;">逾期利率/二阶段利率</th>
						<th style="width:60px;">逾期利息/二阶段利息</th>
						<th>三阶段利率</th>
						<th>三阶段利息</th>
						<th>贷款本息和</th>						
					</tr>
				</thead>
				<tbody id="allTrContent">
					<s:iterator value="policyRevivalVO" status="st">
						<tr align="center" tr_saveStatus="1">		
							<!-- 保单号 -->					 
							<td>${policyCode}</td>		
							<!-- 151299 险种被保人姓名 -->
							<td >${insuredNames }</td>
							<!-- 险种代码 -->					 
							<td>${productItem}</td>
							<!-- 险种名称 -->
							<td>${productNameSys }</td>
							<!-- 原贷款本金 -->
							<td>${loanPrincipal}</td>
							<!-- 原贷款起期 -->
							<td><s:date format="yyyy-MM-dd" name="loanStartDate" /></td>
							<!-- 原贷款起息日 -->
							<td><s:date format="yyyy-MM-dd" name="interestStartDate" /></td>
							<!-- 原贷款止期 -->
							<td><s:date format="yyyy-MM-dd" name="loanEndDate" /></td>
							<!-- 初始利率 -->
							<td>${initialInterestRate.stripTrailingZeros().toPlainString() }%</td>
							<!-- 初始利息 -->
							<td>${initialInterest}</td>
							<!-- 逾期利率/二阶段利率 -->
							<td>
								<s:if test="prductFlag==1">
								 ${secondInterestRate.stripTrailingZeros().toPlainString() }
								</s:if> <s:else>
								${overdueInterestRate.stripTrailingZeros().toPlainString() }
								</s:else>%
							</td>
							<!-- 逾期利息/二阶段利息 -->
							<td><s:if test="prductFlag==1">
								 ${secondInterest }
								</s:if> <s:else>
								${overdueInterest}
								</s:else>
							</td>
							<!--  三阶段利率-->
							<td><s:if test="prductFlag==1">
									${thiredInterestRate}%
								</s:if>
								<s:else> 
									--
								</s:else>
							</td>
							<!-- 三阶段利息 -->
							<td><s:if test="prductFlag==1">
									${thiredInterest }
								</s:if>
								<s:else> 
									--
								</s:else>
							</td>
							<!-- 贷款本息和 -->
							<td>${lastInterestCapital }</td>
						</tr>
					</s:iterator>
				</tbody>
			</table>
		</div>


		<div class="divfclass">
			<h1>
				<img src="images/tubiao.png" >保单续贷信息录入
				<font color='red' style="margin-left:800px;">
				 	${isAutoPCL?"自动续贷业务":"" }
				</font>
			</h1>
		</div>
		 <!-- 151299 start -->
		<s:if test="is485TwoInsured == 1">
		<div class="tabdivclass" >
		<br/>
			<input type="hidden" id="is485TwoInsured" value="${is485TwoInsured}"/>
			<table id="policyInsuredList485" table_saveStatus="1">
				<thead>
					<tr style="display: none">
						<th colName="policyCode" inputType="input">保单号</th>
						<th colName="a" ></th>
						<th colName="checkedCustomerId" inputType="input">选中的被保人</th>
						<th colName="b"></th>
						<th colName="customerIdFS" inputType="input">第一被保人</th>
						<th colName="c"></th>
						<th colName="customerIdSE" inputType="input">第二被保人</th>
						<th colName="d"></th>
						<th colName="deathDate" inputType="input">死亡日期</th>
					</tr>
				</thead>
				<tbody>
					<s:iterator value="policyInsuredList485VOList" var="sr"
						status="st1">
						<tr tr_saveStatus="1">
							<td><input type="hidden" id="policyCode" name="policyInsuredList485VOList.policyCode" value="${policyCode}"/>保单号：${policyCode}</td>
							<td>&nbsp;&nbsp;&nbsp;</td>
							<td style="display: none;"><input type="hidden" id="checkedCustomerId" name="policyInsuredList485VOList.checkedCustomerId" value="${checkedCustomerId}"></td>
							<td>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</td>
							<td><input id="customerIdFS" value="${customerIdFS}" 
								${(checkedCustomerId==null?"":checkedCustomerId.toString()).equals(customerIdFS==null?"":customerIdFS.toString())?'checked':'' } 
								${queryFlag==1?'disabled':'' } onclick="checkedInsured(this);reloadEnterDiv();"
								type="checkbox" name="policyInsuredList485VOList.customerIdFS" /><text>如被保险人${customerNameFS}已死亡，请勾选</text></td>
							<td>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</td>
							<td><input id="customerIdSE" value="${customerIdSE}" 
								${(checkedCustomerId==null?"":checkedCustomerId.toString()).equals(customerIdSE==null?"":customerIdSE.toString())?'checked':'' } 
								${queryFlag==1?'disabled':'' } onclick="checkedInsured(this);reloadEnterDiv();"
								type="checkbox" name="policyInsuredList485VOList.customerIdSE" /><text>如被保险人${customerNameSE}已死亡，请勾选</text></td>
							<td>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</td>
							<td>死亡日期<input id="deathDate" type="expandDateYMD" name="policyInsuredList485VOList.deathDate" ${queryFlag==1?'disabled':'' }
								onpropertychange="checkedInsuredDeath(this)" value="<s:date format="yyyy-MM-dd" name="deathDate"></s:date>" /></td>
						</tr>
					</s:iterator>
				</tbody>
			</table>
		<br/>
		</div>
		</s:if>
		<!-- 151299 end -->
			<div class="tabdivclass" id="RL_enterDiv">
				<!-- 151299 列表单独提出来 -->
				<s:include value="CsEndorseRL_enter.jsp" />
				</div>
				
				<!-- 110633 add div -->
				<div class="pageFormContent">
				<div class="tabdivclass">
					<table class="list"  id="policyCodeLoabTable">
						<thead>
							<tr>
								<th colName="policyCode">保单号</th>
								<th colName="isAuthorized" >被保险人已进行转授权</th>
								<th colName="firAuthAcceptCode"  >首次转授权受理号</th>
							</tr>
						</thead>
						<tbody id="allTrContent">
							<s:iterator value="policyLoanVOIsAuthorized" var="vo" status="st" id="BFList">
								<tr align="center" tr_saveStatus="1">
									<td>${policyCode}</td>
									<!-- 险种代码 -->
									<td>
									<s:if test="isAuthorized!=1">
											<input id="isAuthorized"  type="checkbox" name="isAuthorized" 
										 ${isAuthorized==1?'checked':'' }/>
									</s:if>
									<s:elseif test="isAuthorized==1 && firAuthAcceptCode==''">
											<input id="isAuthorized"  type="checkbox" name="isAuthorized" 
										 ${isAuthorized==1?'checked':'' }/>
									</s:elseif>
									<s:else>
									   是<input id="isAuthorizedCheckFlag" type="hidden" value="true"/>
									</s:else>
										
									</td>
									<td>
										<input id="firAuthAcceptCode" disabled  type="text" name="firAuthAcceptCode" 
											   value="${firAuthAcceptCode }" />
									</td>										
								</tr>
							</s:iterator>
						</tbody>
					</table>
				</div>
				</div>

		<div id="deduLoanFlag" style="display: ${deduLoanFlag==null?'none':'block'}; color: red">
			<div class="divfclass">
				<h1>
				
					<input id="deduLoanFlagCheckBox" type="checkbox"  <s:if test="deduLoanFlag!=null&&deduLoanFlag==1">checked="checked"</s:if> onclick="deduWarnMessage()"/>
					投保人及被保险人已经知悉并确认，如本次保单贷款/保单贷款续贷产品具有年金/生存金/满期金等给付责任，并且在本次保单质押贷款未清偿期间保单有年金/生存金/满期金，
					则年金/生存金/满期金将自动、优先对本次保单贷款/保单贷款续贷进行抵扣。<br/>抵扣后如贷款本息和未全部偿还，剩余贷款本息和将继续计息直至全部偿还；
					抵扣后如有年金/生存金/满期金余额，生存金受益人可领取余额部分，投保人及被保险人知晓并同意本次保单贷款/保单贷款续贷申请。
				</h1>
			</div>
		</div>
		<!-- 新增是否自动续贷标识，适用自动清偿续贷批处理2017-11-7-------------begin--------------- -->
				<div class="divfclass">
					<h1>
						<input id="autoRenew" ${queryFlag==1?'disabled':'' } 
						type="checkbox" name="autoRenew" ${isAutoRenew==1?'checked':'' } />是否自动续贷
					</h1>
				</div>
					
					<div id="accountInfoForRFandRL">
					<div class="divfclass">
						<h1>
							<img src="images/tubiao.png" >自动续贷/清偿账户信息
						</h1>
					</div>
					 
						<div class="pageFormContent">
							<div class="divfclass">
								<h1>
									<input id="isNewAccount" ${isNewAccount==0?'checked':'' } ${queryFlag==1?'disabled':'' }
										onclick="showOrhideAccountInfo(this)" type="checkbox" name="isNewAccount"/>&nbsp;&nbsp;自动续贷/清偿使用本次贷款/续贷账户信息
								</h1>
							</div>
						</div>
						<!-- 如果未选择 自动续贷/清偿使用本次贷款/续贷账户信息 则展示此项-->
						<div id="accountInfoForBank"  style="display:${isNewAccount==0?'none':'block'}" >
							<table style="margin-left:50px; width: 90%; border-right-width:0px; border-left-width:1px;" class="list" >
								<thead>
									<tr>
										<th>收付方式</th>
										<th  data-width="250px">开户银行</th>
										<th>银行账户</th>
										<th>户名</th>
									</tr>
								</thead>
								<tbody>
									<tr>
										<td align="center">
											<select class="combox" disabled><option value="32">银行转账</option></select>
										</td>
										<td align="center" >
										<input id = "orgcodeid" type="hidden" value="<s:property value="#session.appUser.organCode"/>" />
										<input  type="text" id="bankCode" class="autocomplete textLeft"
														data-width="280px" style="width: 70px;"
														data-showValue="bankInfo" data-mode="2"
														data-disCurrentValFlag="1"
														data-tableName="APP___PAS__DBUSER.T_BANK A"
														data-tableCol="BANK_CODE"
														value="${csBankAccountVO.bankCode }"
														data-tableDesc="BANK_CODE||' - '||BANK_NAME"
														data-separator=" - " data-like="1" data-orderBy="BANK_CODE"
														data-view="BANK_NAME" data-view="TYPE"/> 

										<input type="text" id="bankInfo" class="textRight" readOnly style="width: 200px;"/>
										</td>
										<td align="center">
											<input type="text" id="bankAccount" maxsize="25" value="${csBankAccountVO.bankAccount }"
													onkeyup="addTrimForAccount(this);" onblur="accountTwiceCheck();" ${queryFlag==1?'disabled':'' }/>
											<input type="text" style="display: none" id="bankAccountTwiceCheck" />
											<input type="text" style="display: none" id="bankAccountIsChange" />
										</td>
										<td align="center"><input type="text" id="accoName" value="${csBankAccountVO.accoName }"  ${queryFlag==1?'disabled':'' } /></td>
									</tr>
								</tbody>
							</table>
						</div>
					
				</div>


				<div class="pageFormContent" style="height: 75px;">
					<div>
						<dl>
							<dt style="color: red">贷款原因：</dt>
							<!-- 149610 贷款原因录入功能优化需求 modify by cuiqi_wb 2023-12-07 -->
							<dd>
								<Field:codeTable tableName="APP___PAS__DBUSER.T_LOAN_REASON" name="loanReason" id="loanReason" nullOption="true"
									value="${loanReason}" whereClause="code not in ('1','2','3','99')" />
							</dd>
						</dl>
					</div>


					<div>
						<%--  149610 贷款原因录入功能优化需求 modify by cuiqi_wb 2023-12-07 
						
						<dl>
							<dt style="color: red">原因描述：</dt>
							<dd>
								<textarea style="width: 350px" name="reasonDescription"
								 maxlength="166" rows="3" cols="80" id="reasonDescription">${reasonDescription}</textarea>
							</dd>
						</dl> --%>
					</div>
				</div>
				<!-- 110633 end -->
				<!-- 新增是否自动续贷标识，适用自动清偿续贷批处理2017-11-7-------------end--------------- -->


				<div class="pageFormdiv" <s:if test="queryFlag==1">style="display:none"</s:if>>
					<button type="button" class="but_blue" onclick="_checkRLFormData();">保存</button>
				</div>

			 
			<input type="hidden" id="jsons" name="jsons">
			<input type="hidden" id="queryFlag"name="queryFlag" vlaue="${queryFlag}"/>
			<input type="hidden" name="miniAmount" id="miniAmount" value="${miniAmount}"/>
			
	</form>
	<div id="PolicyLoanRevivalDiv" class="unitBox">
		<s:include value="/cs/pages/serviceitem/CsEndorseRL_after.jsp" />
	</div>
	
	<s:include value="/cs/pages/serviceitem/serviceItemBottomBar.jsp"></s:include>
</div>


<script type="text/javascript">
$(function() {
	var sessionorgcode = $("#orgcodeid", navTab.getCurrentPanel()).val();
	if(sessionorgcode.length>=4){
		var org_code = sessionorgcode.substring(0,4);
		$("#bankCode", navTab.getCurrentPanel()).attr("data-whereClause","IS_CREDIT_CARD='0' AND IS_BANK_DISK='1' "+
				"AND EXISTS (SELECT 1 FROM DEV_PAS.t_Bank_Orgen_Rel B WHERE A.BANK_CODE=B.BANK_CODE AND "+
				"EXISTS(SELECT ORGAN_CODE FROM (SELECT C.ORGAN_CODE FROM DEV_PAS.T_UDMP_ORG_REL C WHERE C.ORGAN_CODE='860000' OR C.ORGAN_CODE LIKE "+"'"+org_code+"%'"+") E "+ 
						"WHERE 1=1 AND B.ORGAN_CODE = E.ORGAN_CODE))");
		
	}else{
		$("#bankCode", navTab.getCurrentPanel()).attr("data-whereClause","IS_CREDIT_CARD='0' AND IS_BANK_DISK='1' "+
				"AND EXISTS (SELECT 1 FROM DEV_PAS.t_Bank_Orgen_Rel B WHERE A.BANK_CODE=B.BANK_CODE AND "+
						"EXISTS(SELECT ORGAN_CODE FROM (SELECT C.ORGAN_CODE FROM DEV_PAS.T_UDMP_ORG_REL C WHERE C.ORGAN_CODE='860000' ) E "+ 
								"WHERE 1=1 AND B.ORGAN_CODE = E.ORGAN_CODE))");

	}
		
});

//扩展String 的trim，全部去空
String.prototype.trim = function(){
	return this.replace(/\s/g, '');//去除全部空格
}
var $currentScope = navTab.getCurrentPanel();//获取当前域
//定义当前域对象：如果是来源复核详情页面，设置自定义域
if("${fromCheck}"==1){
	$currentScope = $("#csEndorseRL1");//获取自定义域
}
	
	//账户录入二次校验方法
	function accountTwiceCheck(){
		var $bankAccountTwiceCheck = $("#bankAccountTwiceCheck", $currentScope);
		var $bankAccount = $("#bankAccount", $currentScope);
		var $bankAccountIsChange = $("#bankAccountIsChange", $currentScope);
		var bankAccountTwiceCheck = $bankAccountTwiceCheck.val(); 
		var bankAccount = $bankAccount.val();
		var bankAccountIsChange = $bankAccountIsChange.val(); 
		if(bankAccount==bankAccountIsChange){//如果没有改变则不进行校验
			return false;
		}
		if(bankAccount!="" && !checkAccount(bankAccount)){//如果输入不合法则中断并清除提示之
			alertMsg.error("输入账户不合法！");
			$bankAccount.val("");//清空值
			$bankAccountTwiceCheck.val("");
			$bankAccountIsChange.val("");
			return false;
		}
		if(bankAccount!="" && bankAccountTwiceCheck==""){//如果二次校验框没有值，则再次输入
			$bankAccount.val("");//清空值
			$bankAccountTwiceCheck.val(bankAccount);
			$bankAccountIsChange.val("");
			alertMsg.error("请再次输入账号信息！");
		}else if(bankAccount!="" && bankAccountTwiceCheck!=""){//二次校验框有值了
			if(bankAccount == bankAccountTwiceCheck){//如果相同则校验通过，只清空二次校验框，并且为bankAccountIsChange赋值，用于判断是否改变
				$bankAccountTwiceCheck.val("");
				$bankAccountIsChange.val(bankAccount);
				return true;
			}else{//则校验不通过，清空两个值
				$bankAccount.val("");//清空值
				$bankAccountTwiceCheck.val("");
				$bankAccountIsChange.val("");
				alertMsg.error("两次输入账户不同！");
				return false;
			}
		}else{
			// do nothing
		}
	}
	
	//检查银行账户合法性
	function checkAccount(bankAccount){
		 var bankAccountTrim = bankAccount.trim();
		 var length = bankAccountTrim.length;
		 if(length<12 || length>25){
             return false;
         }
		 var reg = /^[0-9]+$/;
		 if(!reg.test(bankAccountTrim)){
			 return false;
		 }
		 return true;
	}
	
	//为账户添加空格，keyup时触发
	function addTrimForAccount(dom){
		$input = $(dom);
		var initVal = $input.val().replace(/\s+/g, "");
        if (initVal.length > 0) {
            $input.val(initVal.replace(/\s/g, '').replace(/(^\d{3}|\d{4}\B)/g,"$1 ").substr(0,25));
        }
	}
	
	//隐藏或显示账户信息
	function showOrhideAccountInfo(dom){
		if($(dom).attr("checked")){
			$("#accountInfoForBank",$currentScope).hide();
		}else{
			$("#accountInfoForBank",$currentScope).show();
		}
	}

</script>

<script type="text/javascript">

$(function(){
	_showDeduLoanFlag();
})

function _showDeduLoanFlag(){
	var deduShowFlag = false;
	var $trs=$("#policyRLTable tbody tr",navTab.getCurrentPanel());	
	$trs.each(function (){
		 var isHaveAnnuityOrSurvival=$(this).find("#isHaveAnnuityOrSurvival").text();
		 var busiLoanAmount=$(this).find("#_busiLoanAmount input").val();
		 if(isHaveAnnuityOrSurvival == "1" && eval(busiLoanAmount) > 0){
			deduShowFlag = true;
		}
	});
	if(deduShowFlag){
		$("#deduLoanFlag",navTab.getCurrentPanel()).show();
		$("#deduLoanFlagCheckBox",navTab.getCurrentPanel()).prop("checked",true);
	}else {
		$("#deduLoanFlag",navTab.getCurrentPanel()).hide();
	}
}

//151299 start
/* $(function() {
	_initRLPage();
}); */
function checkedInsured(obj){
	var customerid = $(obj).val();
	var sameCustomers = $("#policyInsuredList485").find("input[value="+customerid+"]");
	sameCustomers.each(function(){
		if($(obj).is(":checked")){//选中
			$(this).attr("checked",true);
			if($(this).closest("tr").find("input#deathDate").val()!=$(obj).closest("tr").find("input#deathDate").val()){
				$(this).closest("tr").find("input#deathDate").val($(obj).closest("tr").find("input#deathDate").val());
			}
		}else{
			$(this).attr("checked",false);
			$(this).closest("tr").find("input#checkedCustomerId").val("");
		}
	});
}
function checkedInsuredDeath(obj){
	var dat = $(obj).val();
	if(dat != null  && dat != undefined){
		debugger;
		var customerids = $(obj).closest("tr").find("input[checked=checked]");
		customerids.each(function(){
			checkedInsured(this);
		});
	}
}
function reloadEnterDiv(){
	var changeId =$("#changeId",$currentScope).val();
    var acceptId =$("#acceptId",$currentScope).val();
    var sendData = "changeId=" + changeId + "&acceptId="  + acceptId;
  	var policys = $("#policyInsuredList485").find("tr");
	policys.each(function(){
		var ischeck = false;
		if($(this).find("#customerIdFS").is(":checked")){
			$(this).find("#checkedCustomerId").val($(this).find("#customerIdFS").val());
			ischeck = true;
		}
		if($(this).find("#customerIdSE").is(":checked")){
			$(this).find("#checkedCustomerId").val($(this).find("#customerIdSE").val());
			ischeck = true;
		}
		if(!ischeck){
			$(this).find("#checkedCustomerId").val("");
		}
	});
	var $jsons485 = $("#policyInsuredList485",navTab.getCurrentPanel());
	var _jsons485 = _cs_tableToJson($jsons485);
   	_jsons485 = eval('('+ _jsons485 + ')');
   	_jsons485 = JSON.stringify(_jsons485);
 	sendData += "&jsonString485="+_jsons485;
 	var rL_enterDiv = $('div#RL_enterDiv');
 	rL_enterDiv.loadUrl("cs/serviceitem_rl/loadRLenterPage_PA_csEndorseRLAction.action?"+sendData);
}
function check485Insureds(){
	var policys = $("#policyInsuredList485").find("tr");
	var res = true;
	policys.each(function(){
		if($(this).find("#customerIdFS").is(":checked") && $(this).find("#customerIdSE").is(":checked")){//两个被保人同时选中
			res = false;
			return;
		}
	});
	if(!res){
		alertMsg.error("两个被保险人均为死亡，则不可申请保单贷款，请确认。");
	}
	
  	var policys = $("#policyInsuredList485").find("tr");
  	var flag = "";
	policys.each(function(){
		var deathDate = $(this).find("input#deathDate").val();
		if(($(this).find("#customerIdFS").is(":checked") || $(this).find("#customerIdSE").is(":checked")) 
				&& (deathDate == '' || deathDate == null || deathDate == undefined)){
			flag = "1";
			res = false;
			return;
		}
		if((deathDate != '' && deathDate != null && deathDate != undefined)
				&& (!$(this).find("#customerIdFS").is(":checked") && !$(this).find("#customerIdSE").is(":checked"))){
			flag = "2";
			res = false;
			return;
		}
		
	});
	if(flag == "1"){
		alertMsg.error("未录入死亡日期，请确认！");
	}else if(flag == "2"){
		alertMsg.error("未勾选死亡的被保险人，请确认！");
	}
	return res;
}
/* function _initRLPage(){	
	$("#policyRLTable tbody tr",navTab.getCurrentPanel()).each(function (){
		var masterBusiItemId=$(this).find("#masterBusiItemId").text();		
		var isShowMaster=$(this).find("#isShowMaster").text();//附加险是否可单独贷款
		var isJoinRF=$(this).find("#isJoinRF").text();//是否参与清偿处理
		if((isJoinRF!=null&&isJoinRF.trim()=="1")||(masterBusiItemId!=""&&isShowMaster!="1")){
			//附加险
			$(this).find("#_policyLoanLtAmount").text("");//续贷金额(险种组)
			$(this).find("#revivalLimitAmount").text("");//续贷限额(险种组)		 
		}
	});	
} */
//151299 end


function _clearRLData(masterId){
	if(masterId==""){
		return;
	}
	
	$("#policyRLTable tbody tr",navTab.getCurrentPanel()).each(function (){		 		
		 var masterBusiItemId=$(this).find("#masterBusiItemId").text();
		 if(masterBusiItemId==""){
			 masterBusiItemId=$(this).find("#busiItemId").text();
		 }
		 
		 if(masterBusiItemId!=""&&masterBusiItemId==masterId){
			$(this).find("#_policyLoanLtAmount input").val("");//实领金额(险种组)
			$(this).find("#_busiLoanAmount input").val("");//贷款金额(险种组)
		 }
	});	
}

function  _checkpolicyLoan(obj){
	debugger;
	var $trs=$("#policyRLTable tbody tr",navTab.getCurrentPanel());	
	var $currTr=$(obj).parents("tr");//当前行	
	
	var _masterId=$currTr.find("#masterBusiItemId").text();
	if(_masterId==""){
		_masterId=	$currTr.find("#busiItemId").text();
	}
	
	var policyLoanLtAmount=$(obj).val();//续贷金额(险种组)
	
	if (obj.id == 'busiLoanAmount') {
		var errFlag = "0";
		// 续贷金额(险种)
		var busiLoanAmount = $(obj).val();
		// 续贷限额(险种)
		var revivalAmount = $currTr.find("#revivalAmount").text();
		
		if (busiLoanAmount != "" && (isNaN(busiLoanAmount) || busiLoanAmount < 0)) {
			alertMsg.error("续贷金额需大于0!");
			$(obj).val("");
			errFlag = "1";
		}
		if (errFlag == "0" && eval(busiLoanAmount) > eval(revivalAmount)) {
			var policyCode = $currTr.find("#policyCode").text();
			var productItem = $currTr.find("#_productItem").text();
			alertMsg.error("保单" + policyCode + "险种" + productItem + "续贷金额高于续贷限额!");
			$(obj).val("");
			errFlag = "1";
		}
		var sumLoanAmount = 0;// 续贷金额(险种组)
		$trs.each(function (){
			debugger;
			var busiLoanAmount = $(this).find("#_busiLoanAmount input").val();// 续贷金额(险种)
			var masterBusiItemId = $(this).find("#masterBusiItemId").text();
			if (masterBusiItemId == "") {
				masterBusiItemId = $(this).find("#busiItemId").text();
			}
			if (busiLoanAmount != '' && masterBusiItemId != '' && masterBusiItemId == _masterId) {
				sumLoanAmount = parseFloat(sumLoanAmount) + parseFloat(busiLoanAmount);
			}
		});
		$trs.each(function (){
			debugger;
			var busiItemId = $(this).find("#busiItemId").text();
			if (busiItemId != '' && busiItemId == _masterId) {
				$(this).find("#_policyLoanLtAmount input").val(sumLoanAmount);
			}
		});
		policyLoanLtAmount = sumLoanAmount;//续贷金额(险种组) */
		if (errFlag == "1") {
			return;
		}
	}
	var revivalLimitAmount=$currTr.find("#revivalLimitAmount").text();//续贷限额(险种组)	
	if(policyLoanLtAmount!=""&&(isNaN(policyLoanLtAmount)||policyLoanLtAmount<=0)){
		alertMsg.error("续贷金额需大于0!");
		_clearRLData(_masterId);
		return false;
	} else if (policyLoanLtAmount == "") {
		_clearRLData(_masterId);
		return false;
	}
	
	if(eval(policyLoanLtAmount)>eval(revivalLimitAmount)){
		var policyCode=$currTr.find("#policyCode").text();
		alertMsg.error("保单"+policyCode+"续贷金额高于续贷限额!");	
		_clearRLData(_masterId);
		return;
	}
	
 	/**rm:174444 续贷金额拆分*/	
	$trs.each(function (){
		 debugger;	 
		 $(this).attr("tr_saveStatus","0");
		 
		 var isJoinRF=$(this).find("#isJoinRF").text();//是否参与清偿处理
		 if(isJoinRF==null||isJoinRF==""||isJoinRF.trim()=="0"){
			 var masterBusiItemId=$(this).find("#masterBusiItemId").text();
			 if(masterBusiItemId==""){
				 masterBusiItemId=$(this).find("#busiItemId").text();
			 }
			 if(masterBusiItemId!=''&&masterBusiItemId==_masterId){//同一主险
				 $(this).attr("tr_saveStatus","1");
			 }
		 }
	});
 	
	if (obj.id == 'busiLoanAmount') {
		//判断是否显示抵扣信息
		_showDeduLoanFlag();
		return;
	}
	var _jsons = _cs_tableToJson($("#policyRLTable",navTab.getCurrentPanel()));
	var sendData="tableJson=" + _jsons 
				+"&masterBusiItemId="+_masterId
				+"&masterLoanAmount="+policyLoanLtAmount;
	var rootPath= getRootPath();
	
	$.ajax({
		type : 'post',
		dataType : 'html',
		url : rootPath+'/cs/serviceitem_rl/assignLoanAmount_PA_csEndorseRLAction.action',
		cache : false,
		data : sendData,
		success : function(data) {
			debugger;
			var json = DWZ.jsonEval(data);			
			for(var i=0;i<json.length;i++){
				var obj=json[i];
				 
				$trs.each(function (){
					 debugger;	 
					 if($(this).find("#busiItemId").text()==obj.busiItemId&&obj.busiLoanAmount!=null){
						 $(this).find("#_busiLoanAmount input").val(obj.busiLoanAmount.toFixed(2));
						 return false;
					 }
				}); 
			}
			
			//判断是否显示抵扣信息
			_showDeduLoanFlag();
		}
	});
	
	
}



function _checkRLAmount(){
	debugger;
	var $trs=$("#policyRLTable tbody tr ",navTab.getCurrentPanel());
	var miniAmount = $("#miniAmount",navTab.getCurrentPanel()).val();
	
	var errMsg="";//保单贷款信息未录入
	var errMsg1="";//保单贷款金额不得为0元
	var errMsg2="";//保单贷款金额不得低于0元
	var warnMsg="";//保单贷款金额不得低于最低标准

	var policyCodeArr=[];
	$trs.each(function (){
		var policyCode=$(this).find("#policyCode").text();
		if($.inArray(policyCode,policyCodeArr)<0){
			policyCodeArr.push(policyCode);			
		}
	});
	
	for(var i=0;i<policyCodeArr.length;i++){		
		var sumLoanAmount=0;
		var isNull=true;
		$trs.each(function (){			
			debugger;
			var policyCode=$(this).find("#policyCode").text();			
			if(policyCodeArr[i]==policyCode){								
				var busiLoanAmount=$(this).find("#_busiLoanAmount input").val();
				if(busiLoanAmount!=""){
					sumLoanAmount=parseFloat(sumLoanAmount)+parseFloat(busiLoanAmount);	
					isNull=false;
				} 
			}			
		});
		
		 
		var pCode=policyCodeArr[i];
		
		if(isNull&&errMsg.indexOf(pCode)<0){			
			errMsg+=pCode+"、";			 		
		}else if(eval(sumLoanAmount)==0&&errMsg1.indexOf(pCode)<0){			
			errMsg1+=pCode+"、";			 		
		}else if(eval(sumLoanAmount)<0&&errMsg2.indexOf(pCode)<0){
			errMsg2+=pCode+"、";
		}else if(eval(sumLoanAmount)<parseFloat(miniAmount)&&warnMsg.indexOf(pCode)<0){
			warnMsg+=pCode+"、";
		}		
	}	
   
   	
    if(errMsg!=""&&errMsg.length>0){
    	errMsg=errMsg.substring(0,errMsg.length-1);
    	errMsg="保单"+errMsg+"贷款金额未录入，请核实。<br/>";
    }
    
    if(errMsg1!=""&&errMsg1.length>0){
    	errMsg1=errMsg1.substring(0,errMsg1.length-1);
    	errMsg1="保单"+errMsg1+"贷款金额不得为0元，请核实。<br/>";
    }
    
    if(errMsg2!=""&&errMsg2.length>0){
    	errMsg2=errMsg2.substring(0,errMsg2.length-1);
    	errMsg2="保单"+errMsg2+"贷款金额不得低于0元，请核实。<br/>";
    }
    
    var  reErrMsg=errMsg+errMsg1+errMsg2;
    if(reErrMsg!=""&&reErrMsg.length>0){
    	alertMsg.error(reErrMsg);
    	return false;
    }
    
    if(warnMsg!=""&&warnMsg.length>0){
    	warnMsg=warnMsg.substring(0,errMsg2.length-1);
    	warnMsg="保单"+warnMsg+"贷款金额低于"+miniAmount+"元，请核实。如客户仍需按此金额办理贷款，请向客户充分告知贷款影响，并详细登记贷款原因";
    	
    	alertMsg.confirm(warnMsg,
				{ okCall: function(){
					return true;//校验通过
				},
				cancelCall : function() {
					return false;
				}
			});
    }
    
   return true;//校验通过
    
}

function _checkRLAccount(){
	   var isNewAccountCheck = $("#isNewAccount",$currentScope).attr("checked");
	   var isNewAccount = isNewAccountCheck?"0":"1";//1表示新的，0表示旧的
	   //如果未勾选自动续贷/清偿使用本次贷款/续贷账户信息 
	   if(isNewAccount=="1"){
		   var bankCode = $("#bankCode",$currentScope).val();
		   var bankAccount = $("#bankAccount",$currentScope).val();
		   	   bankAccount = bankAccount.trim();
		   var accoName = $("#accoName",$currentScope).val();
		   if(bankCode==""){
			   alertMsg.error("请选择开户银行！");
		       return false;
		   }
		   if(bankAccount==""){
			   alertMsg.error("请填写银行账户！");
		       return false;
		   }
		   if(accoName==""){
			   alertMsg.error("请填写户名！");
		       return false; 
		   }
		   var customerName=$("#customerName",$currentScope).val();
		   if(customerName!=accoName){
				alertMsg.confirm("录入的户名不是客户名，请确认！",
					{ okCall: function(){
						return true;
					},
					cancelCall : function() {
						return false;
					}
				});
			} 
	   } 
	   
	   return true;
}

function _checkAGAmount(){
	debugger;
	/* 生存金和贷款本息和同时结算 校验：当应领生存金大于贷款本息和，系统需阻断提示“应领生存金大于贷款本息和，请直接进行年金满期金领取或客户账户领取。”*/ 
	if('${csAcceptChangeVO.balanceFlag}'==1){		
		var policyCodeArr=[];
		var ckResult="";
		
		var $trs=$("#policyRLTable tbody tr ",navTab.getCurrentPanel());
		$trs.each(function (){
			var policyCode=$(this).find("#policyCode").text();
			if($.inArray(policyCode,policyCodeArr)<0){
				policyCodeArr.push(policyCode);			
			}
		});
		
		for(var i=0;i<policyCodeArr.length;i++){	
			var sumSurMoney=0;//生存金
			var sumlastIntCapital=0;//原贷款本息合计
			
			$trs.each(function (){
				debugger;
				var policyCode=$(this).find("#policyCode").text();
				if(policyCodeArr[i]==policyCode){		
					var surMoney=$(this).find("#survivalMoney").text();
					var lastIntCapital=$(this).find("#lastInterestCapital").text();
					if(surMoney!=""){
						sumSurMoney=parseFloat(sumSurMoney)+parseFloat(surMoney);
					}
					if(lastIntCapital!=""){
						sumlastIntCapital=parseFloat(sumlastIntCapital)+parseFloat(lastIntCapital);
					}
				}
			});
			
			if(eval(sumSurMoney)>eval(sumlastIntCapital)){
				ckResult+=policyCode+"应领生存金大于贷款本息和，请直接进行年金满期金领取或客户账户领取。";
			}
		}	
		
		if(ckResult!=""&&ckResult.length>0){
			alertMsg.error(ckResult);
			return false;
		}
	}
	
	return true;
}

function _checkRLWarnMsg(){
	debugger;
	//如果存在保单贷款续贷止期现价不够清偿贷款本息和则提示错误信息（提示不阻断）
	
	var policyCodeArr=[];
	var $trs=$("#_rlAfterList tbody tr ",navTab.getCurrentPanel());
	$trs.each(function (){
		var policyCode=$(this).find("#policyCode").text();
		if($.inArray(policyCode,policyCodeArr)<0){
			policyCodeArr.push(policyCode);			
		}
	});
	
	
	var ckWarnMsg="";
	for(var i=0;i<policyCodeArr.length;i++){	
		var sumEndCV=0;//贷款止期现价
		var sumIntCapital=0;//贷款预计到期本息合计
		
		$trs.each(function (){
			var policyCode=$(this).find("#policyCode").text();
			if(policyCodeArr[i]==policyCode){		
				var _intCapital=$(this).find("#_intCapital").text();
				var loanEndDateCV=$(this).find("#loanEndDateCV").text();
				if(loanEndDateCV!=""){
					sumEndCV=parseFloat(sumEndCV)+parseFloat(loanEndDateCV);
				}
				if(_intCapital!=""){
					sumIntCapital=parseFloat(sumIntCapital)+parseFloat(_intCapital);
				}
			}
		});
		
		//保单贷款续贷止期现价不够清偿贷款本息和
		if(eval(sumIntCapital)>eval(sumEndCV)){
			ckWarnMsg+="保单"+policyCode+"的贷款到期本息和："+sumIntCapital+"，贷款止期保单现价（含生存金）："+sumEndCV+"，到期时现价不够清偿贷款本息和。\n";
		}
	}
	
	if(ckWarnMsg!=""&&ckWarnMsg.length>0){		
		alertMsg.warn(ckWarnMsg);	
	}
	return true;
}

function _checkRLFormData(){
	//151299 start
	var is485TwoInsured = $("#is485TwoInsured").val();
	if(is485TwoInsured == '1' && check485Insureds()!=true){
 		return false;
 	}
	//151299 end
	if(!_checkRLAmount()){
		return false;
	}
	
	if(!_checkRLAccount()){
		return false;
	}
	
	if(!_checkAGAmount()){
		return false;
	}
	
	var loanReason = $("#loanReason",$currentScope).val();
	/* var reasonDescription = $("#reasonDescription",$currentScope).val(); */
	if(loanReason=='' || loanReason == null || loanReason == undefined){
		alertMsg.error('请录入贷款原因');
		return false;
	}
	 
	/*
	 149610 贷款原因录入功能优化需求 modify by cuiqi_wb 2023-12-07 
	if(loanReason == '99' && (reasonDescription=='' || reasonDescription == null || reasonDescription == undefined)){
		alertMsg.error('请录入原因描述');
		return false;
	}
	 
	if((reasonDescription!='' && reasonDescription != null && reasonDescription != undefined) && reasonDescription.length > 166){
		alertMsg.error('原因描述最多输入166字');
		return false;
	} */
	
	var _sumAmount=0;//保单续贷金额需大于0
	$("#policyRLTable tbody tr",navTab.getCurrentPanel()).each(function (){
		debugger;
		var isJoinRF=$(this).find("#isJoinRF").text();
		if(isJoinRF.trim()!="1"){
			var _loanAmount=$(this).find("input[name='policyLoanLtAmount']").val();			
			if(_loanAmount!=""&&eval(_loanAmount)>=0){
				_sumAmount+=parseFloat(_loanAmount);
			}
		}
		
	});
	
	if(_sumAmount<=0){
		alertMsg.error('续贷金额不大于0，应办理贷款清偿!');
		$("#policyRLTable tbody tr",navTab.getCurrentPanel()).each(function (){		 		
			$(this).find("#_policyLoanLtAmount input").val("");//实领金额(险种组)
			$(this).find("#_busiLoanAmount input").val("");//贷款金额(险种组)
		});	
		return false;
	}
	
	alertMsg.confirm("请确认是否需要保存录入的信息!", {
		okCall : function() {
			_submitRLData();
		},
		cancelCall : function() {
			return false;
		}
	}); 	
	
}


function _initRLFormData(){	
	debugger;	
	$("#policyRLTable tbody tr",navTab.getCurrentPanel()).each(function (){
		debugger;
		$(this).attr("tr_saveStatus","1");
	});
	
	var _jsons = _cs_tableToJsonRL($("#policyRLTable",$currentScope));
	_jsons = eval('('+ _jsons + ')');
	_jsons = JSON.stringify(_jsons);
	
 	var changeId =$("#changeId",$currentScope).val();
    var acceptId =$("#acceptId",$currentScope).val();
    var customerId =$("#customerId",$currentScope).val();
	var autoRenewCheck = $("#autoRenew",$currentScope).attr("checked");
	var isAutoRenew = autoRenewCheck?"1":"0";
	var isNewAccountCheck = $("#isNewAccount",$currentScope).attr("checked");
	var isNewAccount = isNewAccountCheck?"0":"1";//1表示使用，0表示不使用
	var isAuthorizedCheck = $("#isAuthorized",$currentScope).attr("checked");
	var isAuthFlag = $("#isAuthorizedCheckFlag",$currentScope).val();
	 if(isAuthFlag == 'true'){
		 isAuthorizedCheck = true;
	}
	var isAuthorized = isAuthorizedCheck?"1":"0";
	var loanReason = $("#loanReason",$currentScope).val();
	var reasonDescription = $("#reasonDescription",$currentScope).val();
	console.info(reasonDescription);
	
	var policyCodeList="";
	$("#policyCodeLoabTable tbody tr",navTab.getCurrentPanel()).each(function() {
		debugger;
		var $checkIds=$(this).find("input:checkbox:checked");
		var policycode1 = $(this).find("td:eq(0)").text(); 
		if($checkIds.size()>0){
			policyCodeList = policycode1+";"+policyCodeList;
		}
	}); 
	var deduLoanFlagShow = $("#deduLoanFlag").css('display');
	var deduLoanFlag = null;
	if(deduLoanFlagShow == "block"){
		var isDeduLoanFlag = $("#deduLoanFlagCheckBox",navTab.getCurrentPanel()).attr("checked");
		 if(isDeduLoanFlag == 'checked'){
			 deduLoanFlag = "1";
		  }else{
			 deduLoanFlag = "0";
		  }
	}
	
	var sendData = "tableJson="+ _jsons
	 	+ "&changeId=" + changeId 
	 	+ "&acceptId="  + acceptId
	 	+ "&customerId="+customerId
	 	+ "&isNewAccount=" + isNewAccount 
		+ "&isAutoRenew=" + isAutoRenew 
		+ "&isAuthorized=" + isAuthorized 
		+ "&loanReason="+loanReason
		+ "&reasonDescription="+encodeURI(encodeURI(reasonDescription))
		+ "&policyCodeList="+policyCodeList
		+ "&deduLoanFlag=" + deduLoanFlag;
	
	//151299 start
	  var is485TwoInsured = $("#is485TwoInsured").val();
	  if(is485TwoInsured == '1'){
		  	var policys = $("#policyInsuredList485").find("tr");
			policys.each(function(){
				var ischeck = false;
				if($(this).find("#customerIdFS").is(":checked")){
					$(this).find("#checkedCustomerId").val($(this).find("#customerIdFS").val());
					ischeck = true;
				}
				if($(this).find("#customerIdSE").is(":checked")){
					$(this).find("#checkedCustomerId").val($(this).find("#customerIdSE").val());
					ischeck = true;
				}
				if(!ischeck){
					$(this).find("#checkedCustomerId").val("");
				}
			});
	   		var $jsons485 = $("#policyInsuredList485",navTab.getCurrentPanel());
	   		var _jsons485 = _cs_tableToJson($jsons485);
		   	_jsons485 = eval('('+ _jsons485 + ')');
		   	_jsons485 = JSON.stringify(_jsons485);
	  		sendData += "&jsonString485="+_jsons485;
	  }
	  //151299 end
	
	if(isNewAccount=="1"){
		 var bankCode = $("#bankCode",$currentScope).val();
		 var bankAccount = $("#bankAccount",$currentScope).val().trim();		   	   
		 var accoName = $("#accoName",$currentScope).val();
		 sendData += "&csBankAccountVO.bankCode=" + bankCode 
		 			+ "&csBankAccountVO.bankAccount=" + bankAccount 
		 			+ "&csBankAccountVO.accoName=" +  encodeURI(encodeURI(accoName));
	} 
	
	return sendData;
    
}


function _submitRLData(){
	debugger;
	var rootPath= getRootPath();
	var _sendData=_initRLFormData();//获取要提交的信息
	
	$.ajax({
		type : 'post',		
		dataType : 'html',
		url : rootPath+'/cs/serviceitem_rl/saveRevivalInfo_PA_csEndorseRLAction.action',
		cache : false,
		data : _sendData,
		success : function(data) {
			var json = DWZ.jsonEval(data);
			if (json.statusCode == 300) {
				alertMsg.error(json.message);
			}
			if (json.statusCode==DWZ.statusCode.error){
				if (json.message) alertMsg.error(json.message);
			} else {
				 $("#PolicyLoanRevivalDiv",$currentScope).html(data).initUI();
				 _checkRLWarnMsg();
			}
		},
		error : function() {
			alertMsg.error("保存失败！");
		}
	});
	
}
function deduWarnMessage(){
	if($("#deduLoanFlagCheckBox",navTab.getCurrentPanel()).attr('checked') != 'checked' && $("#csDifOrgCfgFlag",navTab.getCurrentPanel()).val() != '1'){
		$("#deduLoanFlagCheckBox",navTab.getCurrentPanel()).prop("checked",true);
		alertMsg.error("根据产品条款中关于未还款项的约定，同意此内容，才能办理贷款/续贷。"); 
	}
	
}
</script>
<script type="text/javascript">
function _cs_tableToJsonRL(table){
	debugger;
	var $table = $(table);
	var _thColNames = new Array();
	var _inputTypes = new Array();
	$table.find("thead th").each(function(i){
		_thColNames[i]=$(this).attr("colName")||'';
		_inputTypes[i] = $(this).attr("inputType")||'';
	});
	
	var $trs = $table.find("tbody tr[tr_saveStatus='1']");
	if($trs.size()==0){
		return "[]";
	}
	$trs=$("#policyRLTable tbody tr",navTab.getCurrentPanel());	
	var _jsons = "[";
	$trs.each(function(){
		var $tds = $(this).find("td");
		_jsons+="{";
		var _splitInputTypes;
		$tds.each(function(j){
			var _tdVal='';
			if(_inputTypes[j] != null){
				_splitInputTypes = _inputTypes[j].split('>');
				for(var k = 0 ; k < _splitInputTypes.length ; k++){
					if(_splitInputTypes[k]=='td'||_splitInputTypes[k]==null||_splitInputTypes[k]==''||_splitInputTypes[k]=='undefined'){
						_tdVal = $(this).text();
					}else if(_splitInputTypes[k]=='radio'){
						_tdVal = $(this).find("input:radio:checked").val()||'';
					}else if(_splitInputTypes[k]=='checkbox'){
						var checkeds = $(this).find("input:checkbox:checked");
						checkeds.each(function(){
							_tdVal += $(this).val()+'/';
						});
						if(checkeds.length>0){
							_tdVal = _tdVal.substring(0,_tdVal.length-1);
						}
					}else if(_splitInputTypes[k]=='combox'){
						var $a = $(this).find(".select a");
						var _text = $a.text();
						var $select = $(this).find(".select select");
						_tdVal = $select.find("option").filter(function(){
							return $(this).text()==_text;
						}).val();
					}else{
						_tdVal = $(this).find(_splitInputTypes[k]).val();
					}
					_tdVal = $.trim(_tdVal);
					if(_thColNames[j]!='' && _tdVal != 'undefined' && _tdVal != undefined){
						_jsons+="'"+_thColNames[j]+"':'"+_tdVal+"',";	
					}
				}
			} else {
				_tdVal = $(this).find(_inputTypes[j]).val();
				_tdVal = $.trim(_tdVal);
				if(_thColNames[j]!='' && _tdVal != 'undefined' && _tdVal != undefined){
					_jsons+="'"+_thColNames[j]+"':'"+_tdVal+"',";	
				}
			}
			
		});
		_jsons = _jsons.substring(0,_jsons.length-1)+"},";
	});
	_jsons = _jsons.substring(0,_jsons.length-1)+"]";
	return _jsons;
}
</script>

