
<!-- 万能险基本保额减少-->
<%@ page contentType="text/html; charset=utf-8"%>
<%@ taglib uri="/struts-tags" prefix="s"%><%@include
	file="/udmpCommon.jsp"%>
<%@ taglib uri="http://www.git.com.cn/taglib/udmp/field" prefix="Field"%>
<%@ include file="/cs/pages/common/jsp/common_ctx.jsp"%>
<link rel="stylesheet" href="${ctx}/cs/css/public.css" type="text/css">
<script type="text/javascript" src="${ctx}/cs/js/cs_common.js"></script>
<script type="text/javascript" src="${ctx}/cs/js/DomZoomZX.js"></script>
<script>
	$(document).ready(function(){
        var D = DomZoomZX('input,select,textarea,td')
	})
</script>
<div class="pageContent" layoutH="36px">

	<s:include value="/cs/pages/common/jsp/helpMenu.jsp"></s:include>
	<s:include value="csEndorseProgress.jsp" />
	<div class="divfclass">
		<h1>
			<img src="images/tubiao.png">万能险、投连险基本保额减少
		</h1>
	</div>

	<div class="pageContent" layoutH="30px">
		
		<!--客户的5项基本信息（姓名、出生日期、证件类型、证件号码、性别） -->
	    <s:include value="customerInfo_list.jsp" />
	    		
		<form method="post" action="" class="required-validate"
			id="decreaseBFForm" onsubmit="return navTabSearch(this)"
			style="width: 100%">
			<div class="panel" style="margin: 10px;">
				<input id="customerId" type="hidden" name="customerId"
					value="${customerId}"> <input id="changeId" type="hidden"
					name="changeId" value="${changeId }"> <input id="acceptId"
					type="hidden" name="acceptId" value="${acceptId }">
				<div class="divfclass">
					<h1>
						<img src="cs/img/icon/tubiao.png">变更前信息
					</h1>
				</div>
				<div>
					<table class="list" width="100%" table_saveStatus="1"
						id="decreaseBFDetailTable">
						<thead>
							<tr>
								<th style="display: none" colName="changeId">changeId</th>
								<th style="display: none" colName="policyId">policyId</th>
								<th style="display: none" colName="busiItemId">busiItemId</th>
								<th style="display: none" colName="policyChgId">policyChgId</th>
								<th style="display: none" colName="isPer">isPer</th>
								<th>保单号</th>
								<th colName="busiProdCode">险种代码</th>
								<th colName="busiPrdId">险种名称id</th>
								<th>险种名称</th>
								<th colName="amountOrUnit">保额/份数</th>
								<th>每份保额</th>
								<th>保费</th>
								<th>下次缴费日</th>
								<th>保单年度</th>
								<th>险种状态</th>
								<th>月度风险保费（试算）</th>
								<s:iterator value="csEndorseCBVOs" status="st" id="BFList">
									<s:if test="itemFlag==20004">
										<th>账户代码</th>
										<th>账户名称</th>
										<th>账户价值</th>
										<th>账户单位数</th>
									</s:if>
								</s:iterator>
								<th colName="afAmount" inputType="input">变更后保额</th>
							</tr>
						</thead>
						<tbody>
							<s:iterator value="csEndorseCBVOs" status="st" id="BFList">
								<tr align="center" tr_saveStatus='1'>
									<td style="display: none"><s:property value="changeId" /></td>
									<td style="display: none"><s:property value="policyId" /></td>
									<td style="display: none"><s:property value="busiItemId" /></td>
									<td style="display: none"><s:property value="policyChgId" /></td>
									<td style="display: none"><s:property value="isPer" /></td>
									<td><s:property value="policyCode" /></td>
									<td><s:property value="busiProdCode" /></td>
									<td><s:property value="busiPrdId" /></td>
									<td><Field:codeValue
											tableName="APP___PAS__DBUSER.T_BUSINESS_PRODUCT"
											value="${busiPrdId}" /></td>
									<td id="amountOrUnit"><s:property value="amountOrUnit" /></td>
									<td id="amount"><s:property value="amount" /></td>
									<%-- <td><s:property value="amount" /></td> --%>
									<td><s:property value="totalPremAf" /></td>
									<td><s:date name="payDueDate" format="yyyy-MM-dd" /></td>
									<td><s:property value="policyYear" /></td>
									<td><Field:codeValue
											tableName="APP___PAS__DBUSER.T_LIABILITY_STATUS"
											value="${liabilityState}" /></td>
									<td><s:property value="mounthRiskPrem" /></td>
									<s:if test="itemFlag==20004">
										<td><s:property value="accountCode" /></td>
										<!-- 账户代码 -->
										<td><s:property value="fundName" /></td>
										<!-- 账户名称 -->
										<td><s:property value="interestCapital" /></td>
										<!-- 账户价值 -->
										<td><s:property value="accumUnits" /></td>
										<!-- 账户单位数 -->
									</s:if>
									<td><s:if test="queryFlag!=1">
											<s:if test="ItemFlag==20003||ItemFlag==20004">
												<input type="text" id="afAmount" name="afAmount"
													class="number" value="${afAmount }" />
											</s:if>
										</s:if> 
										<s:elseif test="queryFlag==1">
											<s:if test="ItemFlag==20003||ItemFlag==20004">
												<s:iterator value="csEndorseCBCAVOnew" status="AFst" id="AFList">
													<s:property value="amountOrUnit" />
												</s:iterator>
											</s:if>
											<s:else>--</s:else>
										</s:elseif>
									</td>
									<td style="display: none"><input id="itemFlag"
										value="${itemFlag }" /></td>
								</tr>
							</s:iterator>
						</tbody>
					</table>
					<div></div>
					<!-- <div class="subBar" style="margin-right: 20px;float:right"> -->
					<table style="width: 100%">
						<tbody>
							<tr>
								<td></td>
								<td style="width: 150px">
									<div <s:if test="queryFlag==1">style="display:none"</s:if>
										class="buttonContent" style="margin-top: 5px">
										<div class="buttonContent">
											<button type="button" class="but_blue"
												onclick="beforeSubmit()">减少基本保额</button>
										</div>
									</div>
								</td>
								<td></td>
							</tr>
						</tbody>
					</table>
				</div>

			</div>
		</form>

		<!-- 变更后的信息 -->
		<div id="decreaseAmountAfter" class="unitBox">
			<s:include value="/cs/pages/serviceitem/CsEndorseCB_after.jsp"></s:include>
		</div>
		<s:include value="/cs/pages/serviceitem/serviceItemBottomBar.jsp"></s:include>
	</div>
</div>
<script>
	function beforeSubmit() {
		var amount = $("#afAmount", navTab.getCurrentPanel()).val();
		var amountOrUnit = $("#afAmount", navTab.getCurrentPanel()).parent().parent().find("td:eq(9)")
				.text();
		var itemFlag = $("#itemFlag", navTab.getCurrentPanel()).val();
		var reg = /^[1-9]*[1-9][0-9]*$/;
		var msg = "";
		if (amount != null && amount != "") {
			
			
		   /*if (amount < 10000) {
				alertMsg.error(msg + "减少后的保险金额至少保留10000元");
				return false;
			} */
			if(amount != 0){
				if (!reg.test(amount)) {
					alertMsg.error(msg + "只能是正整数！");
					return false;
				}
			}
			
			if (amount - amountOrUnit > 0) {
				alertMsg.error(msg + "变更后保额不能大于等于原保额，请重新填写");
				return false;
			}
			/* if(amount - amountOrUnit == 0){
				alertMsg.error(msg + "变更的保额不能为0，请重新填写");
				return false;
			} */
		}
		
		if ("" == amount) {
			alertMsg.error('请输入变更保额/份数！');
		} else {
			save('decreaseBFForm', 'decreaseAmountAfter');
		}
	}
	//保存信息
	function save(formId, boxId) {

		var _customerId = $("#customerId", navTab.getCurrentPanel()).val();
		var _acceptId = $("#acceptId", navTab.getCurrentPanel()).val();
		alertMsg.confirm("请确认是否需要保存录入的信息",{
					okCall : function() {
								var _jsons = "";
								var $table = $("#decreaseBFForm", navTab
										.getCurrentPanel());
								_jsons = _cs_tableToJson($table);
								var _jsonsDetail = "";
								var $tableDetail = $("#" + formId, navTab
										.getCurrentPanel());
								_jsonsDetail = _cs_tableToJson($tableDetail);
								$Div = $("#" + formId, navTab.getCurrentPanel());
								$
										.ajax({
											type : 'get',
											url : '${ctx}/cs/serviceitem_cb/saveCsEndorseCBAfter_PA_csEndorseCBAction.action?customerId='
													+ _customerId
													+ "&acceptId=" + _acceptId,
											cache : false,
											data : "jsonString=" + _jsons
													+ '&jsonStringDetail='
													+ _jsonsDetail,
											success : function(data) {
												$form = $("#" + formId, navTab
														.getCurrentPanel());
												$(
														"#" + boxId,
														navTab
																.getCurrentPanel())
														.show();
												$("#decreaseBFDetailForm", navTab.getCurrentPanel())
														.submit();
												alertMsg.correct("保存成功！");
												
												var flag = $("#flag", navTab.getCurrentPanel()).val();
												if(flag=='true'){
													alertMsg.warn("此保险项目操作将取消保单存在的未生效的基本保额约定变更记录");
												}

											},
											error : function() {
												alertMsg.error("保存失败！");
											}
										});
							},
							cancelCall : function() {
							}

						});

	};
</script>