<!-- 公司解约页面 -->
<%@ page contentType="text/html; charset=utf-8"%>
<%@ taglib uri="/struts-tags" prefix="s"%><%@include file="/udmpCommon.jsp"%>
<%@ taglib uri="http://www.git.com.cn/taglib/udmp/field" prefix="Field"%>
<%@ include file="/cs/pages/common/jsp/common_ctx.jsp" %>
<link rel="stylesheet" href="${ctx}/cs/css/public.css" type="text/css">
<script type="text/javascript" src="${ctx}/cs/js/DomZoomZX.js"></script>
<script>
	$(document).ready(function(){
        var D = DomZoomZX('input,select,textarea,td')
	})
</script>
<style type="text/css">
th,td {
	white-space: normal;
}

.searchBar li label {
	width: 105px;
}
</style>
<!-- ********帮助菜单***********begin********* -->
<s:include value="/cs/pages/common/jsp/helpMenu.jsp"></s:include>
<!-- *********帮助菜单***********end********* -->
 <s:include value="csEndorseProgress.jsp" />
 <inupt type="hidden" id="queryFlagCEX" value="${queryFlag}"/>
<div class="divfclass">
					<h1>
					  <img src="images/tubiao.png" >公司解约
					</h1>
				</div>
<div  layoutH="140px">
	<div class="">
<!-- 		<div class="pageContent">
 -->			<form method="post"
				action="${ctx }/cs/serviceitem_ea/caluNormalPremium_PA_csEndorseEAAction.action"
				class="required-validate"
				onsubmit="return navTabSearch(this,'showPreiumDetail')"
				id="premAgreeSurrendForm">
				<input type="hidden" id="policyId"
					name="policyId" value="${policyId}" /> <input
					type="hidden" name="changeId" id="changeId" value="${changeId}" />
				<input type="hidden" name="acceptId" id="acceptId"
					value="${acceptId}" /> <input type="hidden" name="customerId"
					id="customerId" value="${customerId}" /> <input type="hidden"
					name="itemIds" id="itemIds" value="${itemIds}" /><input
					type="hidden" name="saveTableInfo" id="saveTableInfo"
					value="${saveTableInfo}" />
				<s:include value="customerInfo_list.jsp" />
				<div class="divfclass">
					<h1>
					  <img src="images/tubiao.png" >变更前保单信息
					</h1>
				</div>
				
					<!-- 150568 start -->
					<div class="tabdivclass" <s:if test=" multiFirstInsuredFlag!=1 ">style="display: none"</s:if> >
						<table class="list" id="tableinsured" width="20%" table_saveStatus="0">
							<thead>
								<tr id="" align="center">
									<th>选择</th>
									<th colName="customerId" style="display: none;">被保人客户id</th>
									<th colName="customerName" >被保险人</th>
									<th colName="busiItemIds" style="display: none;">险种IDs</th>
								</tr>
							</thead>    
							<tbody id="checkboxinsured">
								<s:iterator value="insuredListVOList" id="insuredListVOList" var="listinsured">
									<tr >
									    <!-- 选择 -->
										<td>
											<input type="checkbox" name="checkbox"  class="myClass"  data-busiItemIds="${busiItemIds}"
											onclick="selectInsured(this);"  <s:if test="queryFlag==1">disabled="true" </s:if> />
										</td>
										<!-- 被保人客户id -->
										<td style="display: none;" name="customerId">${customerId}</td>
										<!-- 被保险人 -->
										<td name="customerName">${customerName }</td>
										<!-- 险种IDs -->
										<td style="display: none;" name="busiItemIds">${busiItemIds }</td>
									</tr>
								</s:iterator>
							</tbody>
						</table>
					</div>
					<br/>
					<!-- 150568 end -->
						<div class="tabdivclass">
							<table class="list" id="table1" width="200%" table_saveStatus="1">
								<thead>
									<tr id="" align="center">
										<th>选择</th>
										<th style="display: none;" colName="itemId">责任组id</th>
										<th style="display: none;" colName="isMasterItem" >是否是必选责任组</th>
										<th style="display: none;" colName="busiItemId">险种ID</th>
										<th style="display: none;" colName="masterBusiItemId">所属险种ID</th>
										<th style="display: none;" colName="acceptId">acceptId</th>									
										<th style="display: none;" colName="changeId" >changeId</th>
										<th style="display: none;" colName="policyChgId" >policyChgId</th>
										<th colName="policyCode">保单号</th>
										<th colName="insuredNames" <s:if test=" multiFirstInsuredFlag!=1 ">style="display: none"</s:if>>被保险人</th><!-- 150568 险种被保人姓名 -->
										<th style="display: none;" colName="policyId">保单id</th>
										<th colName="busiProdCode">险种代码</th>
										<th>险种名称</th>
										<th colName="productCode" style="display: none;">责任组代码</th>
										<th>责任组名称</th>
										<th>生效日期</th>
										<th>险种状态</th>
										<th>保额</th>
										<th>保费</th>
										<th>已交保费</th>
										<th style="display: none;"><s:if test="busiCode928!=null&&busiCode928.length()>0">退保金</s:if><s:else>万能险现价</s:else></th>
										<!-- RM101239 add -->
					                    <th style="display: none;">终止日期</th>
					                    <th style="display: none;">交费期间</th>
										<th>下次缴费日</th>
										<th>保单年月</th>
										<th>是否在犹豫期内</th>
										<th style="display: none;" colName="actualReturnPremium">现价</th>
					<!-- 13 -->
										<th colName="standAmount" style="display: none;">基本保额现价</th>
										<!-- 16 -->
										<th colName="adjustStandAmount"   inputType="input" style="display: none;">调整后基本保额现价</th>
										<th colName="bonusAmount"  style="display: none;" >红利保额现价</th>
										<th colName="adjustBonusAmount"   inputType="input" style="display: none;" >调整后红利保额现价</th>
										<th colName="endBonus" style="display: none;">终了红利</th>
										<th colName="adjustEndBonus"inputType="input" style="display: none;">调整后终了红利</th>
										<th colName="investCashValue" style="display: none;"><s:if test="busiCode928!=null&&busiCode928.length()>0">退保金</s:if><s:else>投连万能现价</s:else></th>
										<th colName="adjustInvestCashValue"   inputType="input" style="display: none;"><s:if test="busiCode928!=null&&busiCode928.length()>0">调整后退保金</s:if><s:else>调整后投连万能现价</s:else></th>
										<s:if test="isDepart == 1">
											<th colName="isDepart"  inputType="select">是否出境</th>
										</s:if>
										<th colName="companySurrenderCause"  inputType="select" >解约原因</th>
										<th colName="agentHolderRelation"  inputType="select">投保人与业务员关系</th>
										<th style="display: none;" colName="cashValue">现价</th>
										<th style="display: none;" colName="sumcushBigDecimal">现金红利及利息</th>
										<th style="display: none;" colName="extraPremAf">健康/职业加费应退金额</th>
										<th style="display: none;" colName="extraPremJ">健康加费应退金额</th>
										<th style="display: none;" colName="extraPremH">职业加费应退金额</th>
										<th style="display: none;" colName="policyFee">保单管理费</th>
										<th style="display: none;" colName="riskFee">风险保费</th>
										<th style="display: none;" colName="ctCashFee">退保费用</th>
										<th style="display: none;" colName="prodInPremAf">已缴保费</th>
										<th style="display: none;" colName="endInterest">终了结算利息</th>
										<th style="display: none;" colName="loan">贷款本息合计</th>
										<th style="display: none;" colName="loanCapital">贷款本金</th>
										<th style="display: none;" colName="loanInterest">贷款利息</th>
							            <th style="display: none;" colName="advanced">自垫本息合计</th>
										<th style="display: none;" colName="aplCapital">自垫本金</th>
										<th style="display: none;" colName="aplInterest">自垫利息</th>
										<th style="display: none;" colName="calProfit">累积生息账户本息合计</th>
										<th style="display: none;" colName="revivilFee">应领未领生存金/年金/养老金</th>
										<th style="display: none;" colName="profitGap">利差账户金额</th>
										<th style="display: none;" colName="nominalFee">工本费</th>
										<th style="display: none;" colName="afterAmount">保全申请提交日后的调整项</th>
										<!-- 15 -->
										<th colName="actualReturnAmount" style="display: none;">正常退保总保费</th>
									    <th colName="chgActualReturnPremium" style="display: none;">调整后解约退费</th>
									    <th style="display: none;" colName="isOrNoClaimPolicyYear" title="isOrNoClaimPolicyYear" ></th>
									   <th colName="isCancerFlag" title="isCancerFlag" style="display: none;" >是否保险金给付</th>
									    <!--需求分析任务 #71049公司解约原因增加贷款终止选项需求 -->
									   <th colName="contractMasterState" title="contractMasterState" style="display: none;" >是否贷款终止</th>
									   <th colName="adjustMoney" title="adjustMoney" style="display: none;" >解约金额</th>
									</tr>
								</thead>
								<tbody id="checkboxcheckedroles">
									<s:iterator value="listBfSurrender" id="listBfSurrender">
										<tr  <s:if test="checked==1">tr_saveStatus='1'</s:if><s:else>tr_saveStatus='0' </s:else>
										 title="${(masterBusiItemId == busiItemId && isMasterItem==1)?'primaryInsurance':'additionalInsurance' }">
											<!-- 选择  150568 add selectBusiItem() -->
											<td><input type="checkbox" id="itemId${st.index }"
												class="myClass" value="${itemId}"
												onclick="selectItem(this);selectBusiItem();" 
													<s:if test="checked==1">
														checked
													</s:if> /></td>
											<td style="display: none;">${itemId}</td>
											<td style="display: none;">${isMasterItem }</td>
											<!-- 险种ID 150568 add data-busiItemId-->
											<td style="display: none;" data-busiItemId='${busiItemId }' >${busiItemId }</td>
											<td style="display: none;" id="masterBusiItemId">${masterBusiItemId }</td>
											<td style="display: none;">${acceptId }</td>									
											<td style="display: none;">${changeId }</td>
											<td style="display: none;">${policyChgId }</td>
											<!-- 保单号 -->
											<td>${policyCode }</td>
											<!-- 150568 险种被保人姓名 -->
											<td <s:if test=" multiFirstInsuredFlag!=1 ">style="display: none"</s:if>>${insuredNames }</td>
											<td style="display: none;">${policyId }</td>
											<!-- 险种代码 -->
											<td id="_busiProdCode">${busiProdCode }</td>
											<!-- 险种名称 -->
											<td title="busiProdName"><Field:codeValue tableName="APP___PAS__DBUSER.T_BUSINESS_PRODUCT"
													value="${busiPrdId}" /></td>
											<td style="display: none;">${productCode }</td>
											<!-- 责任组名称 -->
											<td><Field:codeValue tableName="APP___PAS__DBUSER.T_PRODUCT_LIFE" value="${productCode}" /></td>
											<!-- 生效日期 -->
											<td><s:date name="validatePDay" format="yyyy-MM-dd" /></td>
											<!-- 险种状态 -->
											<td><Field:codeValue tableName="APP___PAS__DBUSER.T_LIABILITY_STATUS" value="${prodStatus}" /></td>
											<!-- 保额 -->
											<td>${productCost }</td>
											<!-- 保费 -->
											<td title="productFee" >${productFee }</td>
											<!-- 已交保费 -->
											<td id="paidInPremAf">${prodInPremAf }</td>
											<td style="display: none;">${investCashValue}</td>
											<!-- RM101239 add 终止日期-->
						                    <td style="display: none;"><s:date name="expiryDate" format="yyyy-MM-dd" /></td>
						                    <!-- RM101239 add 交费期间-->
						                    <td style="display: none;">${chargeDesc }</td>
						                    <!-- 下次缴费日 -->
											<td><s:date name="nextPayDay" format="yyyy-MM-dd" /></td>
											<!-- 保单年月 -->
											<td>${policyDur }</td>
											<!-- 是否在犹豫期内 -->
											<td title="isInHesitate" ><Field:codeValue tableName="APP___PAS__DBUSER.T_YES_NO"
												value="${isInHesitate}" /></td>
											<!-- 现价 -->
											<td style="display: none;" id="cashValue" title="cashValue" >${cashValue}</td>
											<td id="actualReturnPremium" title="basicPremCashValue" style="display: none;">${standAmount }</td>
											<td id="adjustFeeAmount" style="display: none;"><input type="text"
												id="adjustFeeAmounts" name="adjustFeeAmount" size="7"
												<s:if test="adjustStandAmount==null">value="${standAmount}" </s:if>
												<s:else>value="${adjustStandAmount}" </s:else>
												onchange="changedFeeAmount(this,${standAmount},' 调整后基本保额现价')"/></td>
											<td style="display: none;">${bonusAmount}</td>
											<td id="adjustBonusAmount" style="display: none;"><input type="text"
												id="adjustBonusAmount" name="adjustBonusAmount" size="7"
												<s:if test="adjustBonusAmount==null">value="${bonusAmount}" </s:if>
												<s:else>value="${adjustBonusAmount}" </s:else>
												 onchange="changedFeeAmount(this,${bonusAmount},' 调整后红利保额现价')"  /></td>
											<td style="display: none;">${endBonus}</td>
											<!--调整后终了红利--><!--调整后终了红利取值字段修改 接口需求任务 #50256 -->
											<td id="adjustEndBonus" style="display: none;"><input type="text"
												id="adjustEndBonus" name="adjustEndBonus" size="7"
												<s:if test="adjustEndBonus==null">value="${endBonus}" </s:if>
												<s:else>value="${adjustEndBonus}" </s:else>
												onchange="changedFeeAmount(this,${endBonus},' 调整后终了红利')"/></td>
											<td title="investCashValue" style="display: none;">${investCashValue}</td>
											<td title="adjustInvestCashValue" style="display: none;"><input type="text"
												id="adjustInvestCashValue" name="adjustInvestCashValue" size="7"
												<s:if test="adjustInvestCashValue==null">value="${investCashValue}" </s:if>
												<s:else>value="${adjustInvestCashValue}"</s:else>
												 onchange="changedFeeAmount(this,${investCashValue},' 调整后万能险现价')"/></td>
											<!--是否出境 -->
											<s:if test="isDepart == 1">
											<td>
													<s:if test="queryFlag!=1">
														<Field:codeTable name="2"  value="${isDepart}" tableName="APP___PAS__DBUSER.T_YES_NO" cssClass="combox"/>
													</s:if>
													<s:else>
														<Field:codeTable   name=""  disabled="true" value="${isDepart}" tableName="APP___PAS__DBUSER.T_YES_NO" cssClass="combox"/>
													</s:else> 
											</td>
										    </s:if>
											<!--解约原因-->		
											<td>
											<s:if test="queryFlag!=1">
												<!-- #104_35562 -->
												<Field:codeTable id="surrenderCauseId" name="0"  value="${surrenderCause}" tableName="APP___PAS__DBUSER.T_SURRENDER_CAUSE" whereClause="surrender_cause in ('400','401','402','403','404','405','406','407','999')" cssClass="combox" nullOption="true"/>
											</s:if>
											<s:else>
												<Field:codeTable  name=""  disabled="true" value="${surrenderCause}" tableName="APP___PAS__DBUSER.T_SURRENDER_CAUSE" cssClass="combox" nullOption="true"/>
											</s:else>
											</td>
											<!--投保人与业务员关系-->	
											<td>
												<s:if test="queryFlag!=1">
													<Field:codeTable   id="agentHolderRelationId" name="1"  value="${agentHolderRelation}" tableName="APP___PAS__DBUSER.T_AH_RELATION" cssClass="combox" nullOption="true"/>
												</s:if>
												<s:else>
													<Field:codeTable   name=""  disabled="true" value="${agentHolderRelation}" tableName="APP___PAS__DBUSER.T_AH_RELATION" cssClass="combox" nullOption="true"/>
												</s:else>
											</td>
											
											<td style="display: none;">${cashValue}</td>
											<td style="display: none;">${sumcushBigDecimal }</td>
											<td style="display: none;">${extraPremAf}</td>
											<td style="display: none;">${extraPremJ}</td>
											<td style="display: none;">${extraPremH}</td>
											<td style="display: none;">${policyFee}</td>
											<td style="display: none;">${riskFee}</td>
											<td style="display: none;">${ctCashFee}</td>
											<td style="display: none;" title="allAmount">${prodInPremAf}</td>
											<td style="display: none;">${endInterest}</td>
											<td style="display: none;">${loan}</td>
											<td style="display: none;">${loanCapital}</td>
											<td style="display: none;">${loanInterest}</td>
											<td style="display: none;">${advanced}</td>
											<td style="display: none;">${aplCapital}</td>
											<td style="display: none;">${aplInterest}</td>
											<td style="display: none;">${calProfit}</td>
											<td style="display: none;">${revivilFee}</td>
											<td style="display: none;">${profitGap}</td>
											<td style="display: none;">${nominalFee}</td>
											<!--保全申请提交日后的调整项 -->
											<td style="display: none;">${afterAmount}</td>
											<td style="display: none;">${actualReturnPremium}</td>
											<td title="chgActualReturnPremium" style="display: none;">${chgActualReturnPremium}</td>
											<td style="display: none;" title="isOrNoClaimPolicyYear" >${isOrNoClaimPolicyYear }</td>
											<td style="display: none;" >${isCancerFlag }</td>
											<td style="display: none;" title="contractMasterState" >${contractMasterState}</td>
											<td style="display: none;" title="adjustMoney" ></td>
										</tr>
									</s:iterator>
								</tbody>
							</table>
				</div>
				<!-- RM101239 add-->
<%-- 				<div class="divfclass">
					<h1>
					  	实际已交保费：${totalPrem }
					</h1>
				</div> --%>
				<%--rm:93396  928养老保险_保全业务 start --%>
				<div id="_ctCode928"   class="pageFormContent"  <s:if test="!((busiCode928!=null&&busiCode928!='')||(busiCodeSY!=null && busiCodeSY!=''))">style="display: none"</s:if>>
					<dl>
						<dt>特殊退保</dt>
						<dd>
							<s:if test="queryFlag==1">
								<Field:codeTable id="_specialFlag" name="specialFlag" tableName="APP___PAS__DBUSER.T_YES_NO" nullOption="false"
							    value="${specialFlag }"  cssClass="combox" disabled="true"/>
							 </s:if>
							 <%-- <s:elseif test="busiCodeSY!=null && busiCodeSY!=''">
								<select   id="_specialFlag" name="specialFlag" style="pointer-events:none;"  >
									<option value="1"  selected="selected" >是</option>
								</select>
							 </s:elseif> --%>
							<s:else>
								<Field:codeTable id="_specialFlag" name="specialFlag" tableName="APP___PAS__DBUSER.T_YES_NO"  nullOption="false"
							    value="${specialFlag }"  cssClass="combox"  defaultValue="0"/>
							</s:else>
						</dd>
					</dl>
			   	</div>
			    <%--rm:93396  928养老保险_保全业务 end --%>
				
				<div class="pageFormdiv" id="saveMesId"  style='<s:if test="queryFlag==1" >display:none</s:if>'>
						 <button id="save1" type="button" class="but_blue" onclick="beforecCaluNormalPremium()">保存</button>
			   </div>
				
			</form>
			
		<div id="showAFPage">
		<s:if test="listSurrenderNew != null && listSurrenderNew.size()>0">
			<s:include value="/cs/pages/serviceitem/CsEndorseEA_afterInfo.jsp"></s:include>
		</s:if>
		</div>
	<!-- 	</div> -->
	</div>
			<s:include value="/cs/pages/serviceitem/serviceItemBottomBar.jsp"></s:include>	
	</div>
	
<script type="text/javascript">
function isCK928(){//如果有928险种退保，选择是否特殊退保
	debugger;
	var isSpecial=false;
	var $trs=$("#table1",navTab.getCurrentPanel()).find("tbody tr");
    $trs.each(function(){
    	debugger;
    	var bpCode=$(this).find("#_busiProdCode").text();
		var $checkIds=$(this).find("input:checkbox:checked");
		if($checkIds.size()>0&&(bpCode=='00928000'||bpCode=='00928100' || bpCode=='00Z01000'||bpCode=='00Z01100' ||
				bpCode=='00994000'||bpCode=='00995000'||bpCode=='00996000')){
			isSpecial=true;
		}	  
	});
    if(isSpecial){
		$("#_ctCode928").show();
	}else{
		$("#_ctCode928").hide();
	}
    return isSpecial;
}
function isSY(){//如果有928险种退保，选择是否特殊退保
	var $trs=$("#table1",navTab.getCurrentPanel()).find("tbody tr");
	var flag = false;
	$trs.each(function(){
		debugger;
		var bpCode=$(this).find("#_busiProdCode").text();
		var $checkIds=$(this).find("input:checkbox:checked");
		if($checkIds.size() == 0 && (bpCode=='00994000'||bpCode=='00995000'||bpCode=='00996000')){
			flag = true; 
		}
	});
	return flag;
}
$(function(){
//保全查询
   if($("#queryFlagCEX",navTab.getCurrentPanel()).val()==1){
	$("#table1 input",navTab.getCurrentPanel()).each(function(){
		$(this).attr("disabled","disabled");
	});
	
}
 	
//下拉框点击方法绑定主附加险选中规则-退保原因及关系关联
$("#table1 tr[title='primaryInsurance'] select").change(function(){
	/** var ischecked = $("#table1 tr[title='primaryInsurance'] :checkbox").attr("checked");
	selBusiItemId = $(this).parents("tr").find("td").eq(3).text();
	checkboxcheckedrolesfun(ischecked,this,selBusiItemId); **/
	// 点击下拉列表时 参数为2 
	checkBoxChecked(2,0,true);
	
});


/**   主险前面的复选框选中方法
$("#table1 tr[title='primaryInsurance'] :checkbox").change(function(){
	var ischecked = $(this).attr("checked");
	selBusiItemId = $(this).parents("tr").find("td").eq(3).text();
	$("#table1 tr[title='primaryInsurance'] select").each(function(){
		checkboxcheckedrolesfun(ischecked,this,selBusiItemId);
	});;
}); **/ 

//页面刷新时 参数为1 
checkBoxChecked(1,0,true);
//150568 选择被保人复选框
selectBusiItem();
});
//150568 start
//选择被保人
function selectInsured(obj){
	//选择被保人后，循环被保人险种选中,或取消
	var busiItemIds = $(obj).attr("data-busiItemIds");
	if(busiItemIds != null && busiItemIds != '' && typeof busiItemIds != 'undefined'){
		var busiArr = busiItemIds.split('-');
		for(var i=0;i<busiArr.length;i++){
			var busiItem = busiArr[i];
			if($("#table1").find("[data-busiItemId='"+busiItem+"']").length){
				var busiObj = $("#table1").find("[data-busiItemId='"+busiItem+"']").closest("tr").find("input#itemId");
				if($(obj).is(":checked")){//选中
					$(busiObj).attr("checked",true);
				}else{
					$(busiObj).attr("checked",false);
				}
				selectItem(busiObj);
			}
		}
		selectBusiItem();
	}
}
//选择险种，页面加载后执行一次
function selectBusiItem(){
	//选择险种后，循环所有被保人，如被保人下所有险种都选中，则选中对应被保人
	var insuredTableTrs = $("#tableinsured",navTab.getCurrentPanel()).find("tbody tr");
	insuredTableTrs.each(function(){
		var insuredObj = $(this).find("input[name='checkbox']");
		var busiItemIds = $(insuredObj).attr("data-busiItemIds");
		if(busiItemIds != null && busiItemIds != '' && typeof busiItemIds != 'undefined'){
			var busiArr = busiItemIds.split('-');
			var isAllCheck = true;
			var existsBusi = false;
			for(var i=0;i<busiArr.length;i++){
				var busiItem = busiArr[i];
				if($("#table1").find("[data-busiItemId='"+busiItem+"']").length){
					existsBusi = true;
					var busiObj = $("#table1").find("[data-busiItemId='"+busiItem+"']").closest("tr").find("input#itemId");
					if(!$(busiObj).is(":checked")){
						isAllCheck = false;
					}
				}
			}
			if(isAllCheck && existsBusi){
				$(insuredObj).attr("checked",true);
			}else{
				$(insuredObj).attr("checked",false);
			}
		}
	});
}
//150568 end

// 主险与附加险的退保原因 和投保人与业务员关系 绑定方法  
function checkBoxChecked(checkResource,materBusID,ischecked){
	var beferTable = $("#table1",navTab.getCurrentPanel());
	// 主险前的复选框 不选中 
	if(materBusID!=0 && (!ischecked)){
		var beferFJTableTrs = $(beferTable).find("tbody tr[title='additionalInsurance']");
		// 循环所有的附加险  
		beferFJTableTrs.each(function(){
			var fjxmasterBusiItemId = $(this).find("td[id='masterBusiItemId']").text();
			
			if(fjxmasterBusiItemId==materBusID){
				$(this).find(".combox:eq(1) a").attr("disabled",false);
				$(this).find(".combox:eq(2) a").attr("disabled",false);
			
			}
		});
	}
	// 循环 主险 看那些主险被选中 
	var beferTableTrs = $(beferTable).find("tbody tr[title='primaryInsurance']");
	
	beferTableTrs.each(function(){
		// 判断选中的是哪个复选框 
		var checkedFlag = $(this).find("td input[type='checkbox']").is(':checked');
		
		var masterBusiItemId = $(this).find("td[id='masterBusiItemId']").text();
		
		// 如果主险被选中 则将该主险底下的附加险 的退保原因保持与主险一致 
		if(checkedFlag){
			//	var surrenderCauseHtml = $(this).find("td a[name='0']").html();
				var surrenderCauseVal = $(this).find("#surrenderCauseId option:selected").val();
				var surrenderCauseText = $(this).find("#surrenderCauseId option:selected").text();
			//	var agentHolderRelationHtml = $(this).find("td a[name='1']").html();
				var agentHolderRelationText = $(this).find("#agentHolderRelationId option:selected").text();
				var agentHolderRelationVal = $(this).find("#agentHolderRelationId option:selected").val();
				
				var beferFJTableTrs = $(beferTable).find("tbody tr[title='additionalInsurance']");
				// 循环所有的附加险  
				beferFJTableTrs.each(function(){
					var fjxmasterBusiItemId = $(this).find("td[id='masterBusiItemId']").text();
					
					if(fjxmasterBusiItemId==masterBusiItemId){
						// 页面刷新重新进入
						if(checkResource==1){
							// 循环某一个主险下的所有附加险 
							// 将此附加险的退保原因  和和业务员关系和主险保持一致
							$(this).find("#surrenderCauseId option:selected").text(surrenderCauseText);
							$(this).find("td select[id='surrenderCauseId']").attr("value",surrenderCauseVal);
							$(this).find("#agentHolderRelationId option:selected").text(agentHolderRelationText);
							$(this).find("td select[id='agentHolderRelationId']").attr("value",agentHolderRelationVal);
							
						}else{
							// 点击下拉列表
							// 将此附加险的退保原因  和和业务员关系和主险保持一致  
							$(this).find("td a[name='0']").text(surrenderCauseText);
							$(this).find("td select[id='surrenderCauseId']").attr("value",surrenderCauseVal);
							$(this).find("td a[name='1']").text(agentHolderRelationText);
							$(this).find("td select[id='agentHolderRelationId']").attr("value",agentHolderRelationVal);
							
						}
						
						if(surrenderCauseText!="请选择"){
							$(this).find(".combox:eq(1) a").attr("disabled",true);
							$(this).find("td a[name='0']").attr("disabled",true);
						}else{
							$(this).find(".combox:eq(1) a").attr("disabled",false);
						}
						
						if(agentHolderRelationText!="请选择"){
							$(this).find(".combox:eq(2) a").attr("disabled",true);
						}else{
							$(this).find(".combox:eq(2) a").attr("disabled",false);
						}
					}
				});
		}
	});
	
}

//绑定主附加险选中规则-退保原因及关系关联
function checkboxcheckedrolesfun(ischecked,dom,selBusiItemId){
		var mastervalue = $(dom).find("option:selected").val();
		var seleindex = $(dom).attr("name");
		var mastertext = $(dom).find("option:selected").text();
		$("#checkboxcheckedroles tr[title='additionalInsurance']").each(function(){
			var isCheck=$(this).find("td").eq(0).find("input:checkbox").is(":checked");
			if(selBusiItemId != null && selBusiItemId == $(this).find("td").eq(4).text() && isCheck){
			    if(ischecked){
					$(this).find(".combox:eq("+seleindex+") a").text(mastertext);
					$(this).find(".combox:eq("+seleindex+") a").attr("disabled",true);
					$(this).find(".combox:eq("+seleindex+") select").find("option[value='"+mastervalue+"']").attr("selected",true);
			     }else{
				    $(this).find(".combox:eq("+seleindex+") a").attr("disabled",false);
			    }
			}
		});
     }

 function valueValidationComm1(objInput,BFSA ,msgstr){
		var value = parseFloat(objInput.value);
		var rel=/^[0-9]+\.?[0-9]{0,9}$/; 
		if(!rel.test(value)){
			alertMsg.error(msgstr+'只能大于等于0')
			//给文本框赋值
			$(objInput).val(BFSA);
		}
		if(value <0){
			alertMsg.error(msgstr+'只能大于等于0')
			//给文本框赋值
			$(objInput).val(BFSA);
		}
	}

	function valueValidationComm(objInput,BFSA ,msgstr){
		var objValue=$(objInput).val();
		if(objValue != 0 && objValue != BFSA){
			alertMsg.error(msgstr+'只能是0或者是原始金额！');
			//给文本框赋值
			$(objInput).val(BFSA);
		}
	}

//调整后的解约总退费
function changedFeeAmount(objInput,BFSA ,msgstr){
	 var objInputName=$(objInput).attr("name");
	if(objInputName=='adjustEndBonus' || objInputName=='adjustBonusAmount'){
		valueValidationComm(objInput,BFSA ,msgstr);
	}
    if(objInputName=='adjustFeeAmount' || objInputName=="adjustInvestCashValue"){
		valueValidationComm1(objInput,BFSA ,msgstr);
	} 
    var isInHesitate=($(objInput).parents("td").parent("tr").find("td[title='isInHesitate']").text());//犹豫期

	
	var $table = $("#table1",navTab.getCurrentPanel());
	var adjustFeeAmountIndex = null; //调整后解约退费
	var chgActualReturnPremiumIndex = null;//实退金额
	var adjustStandAmountIndex = null; //调整后基本保额现金价值
	var adjustBonusAmountIndex = null; //调整后累计红利保额现金价值（包括调整红利保额现金价值）
	var adjustEndBonusIndex = null;//调整后终了红利
	var actualReturnAmountIndext=null;//正常退保总金额
	var standAmountIndext    =null      //基本保额现价
	var bonusAmountIndext=null	//红利保额现价 	 		 		
	var endBonusIndext=null //终了红利
	var investCashValueIndext=null;//投连万能现价
	var adjustInvestCashValueIndext=null;//调整后投连万能现价
	$table.find("th").each(function(i){
		if($(this).attr("colName") == 'adjustFeeAmount'){
			adjustFeeAmountIndex = i;
		} else if($(this).attr("colName") == 'loan'){
			loanIndex = i;
		} else if($(this).attr("colName") == 'advanced'){
			advancedIndex = i;
		}else if($(this).attr("colName") == 'profitGap'){
			profitGapIndex = i;
		}else if($(this).attr("colName") == 'revivilFee'){
			revivilFeeIndex = i;
		}else if($(this).attr("colName") == 'chgActualReturnPremium'){
			chgActualReturnPremiumIndex = i;
		}else if($(this).attr("colName") == 'adjustStandAmount'){//调整后基本保额现金价值
			adjustStandAmountIndex=i;
		}else if($(this).attr("colName") == 'adjustBonusAmount'){//调整后累计红利保额现金价值
			adjustBonusAmountIndex=i;
		}else if($(this).attr("colName") == 'adjustEndBonus'){//调整后终了红利理
			adjustEndBonusIndex=i;
		}else if($(this).attr("colName") == 'actualReturnAmount'){//正常退保总金额
			actualReturnAmountIndext=i;
		}else if($(this).attr("colName") == 'standAmount'){//基本保额现价 
			standAmountIndext=i;
		}else if($(this).attr("colName") == 'bonusAmount'){//红利保额现价
			bonusAmountIndext=i
		}else if($(this).attr("colName") == 'endBonus'){//终了红利
			endBonusIndext=i;
		}else if($(this).attr("colName") == 'investCashValue'){
			investCashValueIndext=i;
		}else if($(this).attr("colName")=='adjustInvestCashValue'){
			adjustInvestCashValueIndext=i;
		}
	});
	
	
	var chgActualReturnPremium = 0;
	var thisVal = $(objInput).val();
	var $thisTr = $(objInput).parents("tr");
	var adjustFeeAmount = $thisTr.find("td").eq(actualReturnAmountIndext).text();;//正常退保总金额
	var chgActualReturnPremium = 0;//实退金额
	var adjustStandAmount = 0; //调整后基本保额现金价值
	var adjustBonusAmount = 0; //调整后累计红利保额现金价值（包括调整红利保额现金价值）
	var adjustEndBonus = 0;//调整后终了红利
	var actualReturnAmount =0;//正常退保总金额
	var adjustInvestCashValue=0;//万能现价
		
	if(thisVal != null && thisVal!='' && adjustFeeAmount !=''){
		 //计算基本保额现价 前和后的的差值
		 if(standAmountIndext != null && adjustStandAmountIndex != null){
			 adjustStandAmount =  $thisTr.find("td").eq(standAmountIndext).text() -$thisTr.find("td").eq(adjustStandAmountIndex).find("input").val();
			}
		 
		 //变更后的红利保额现价 adjustStandAmount
		 
		 if(bonusAmountIndext !=null && adjustBonusAmountIndex !=null){
			 adjustBonusAmount=$thisTr.find("td").eq(bonusAmountIndext).text()-$thisTr.find("td").eq(adjustBonusAmountIndex).find("input").val();
		 }
		 //变更后的终了红利、 
		 if(endBonusIndext !=null && adjustEndBonusIndex !=null){
			 adjustEndBonus=$thisTr.find("td").eq(endBonusIndext).text()-$thisTr.find("td").eq(adjustEndBonusIndex).find("input").val();
		 }
		 //变更后的万能险
		 if(investCashValueIndext !=null && adjustInvestCashValueIndext!=null){
			 adjustInvestCashValue=$thisTr.find("td").eq(investCashValueIndext).text()-$thisTr.find("td").eq(adjustInvestCashValueIndext).find("input").val();
		 }
		 
	}
	var finalCash = (adjustFeeAmount-adjustStandAmount-adjustBonusAmount-adjustEndBonus-adjustInvestCashValue);
	//如果是犹豫期内 可以调基本保额现价 比如保费1000 现价调500 则退的金额为500 =保费当做现价 chgbasicPrem
	if(adjustStandAmount<0 && isInHesitate!=null && isInHesitate==1 ){
		var finalCash=-adjustStandAmount;
	}
	$thisTr.find("td").eq(chgActualReturnPremiumIndex).text(finalCash.toFixed(2));
};



//通过复选框的单击事件，来对退保标识进行赋值
function selectItem(obj){
	isCK928();
	
	var $table = $("#table1",navTab.getCurrentPanel());
	var isMasterItemIndex = null;
	var busiItemIdIndex = null;
	var masterBusiItemIdIndex = null;
	var busiProdCode = null;
	var busiProdCodeIndex = null;//150568
	$table.find("th").each(function(i){
		if($(this).attr("colName") == 'isMasterItem'){
			isMasterItemIndex = i;
		} else if($(this).attr("colName") == 'busiItemId'){
			busiItemIdIndex = i;
		} else if($(this).attr("colName") == 'masterBusiItemId'){
			masterBusiItemIdIndex = i;
		}
		//150568 start
		else if($(this).attr("colName") == 'busiProdCode'){
			busiProdCodeIndex = i;
		}
		//150568 end
	});
	//判断自己
	if($(obj).is(":checked")){//选中
		$(obj).parents("tr").attr("tr_saveStatus",'1');
	}else{
		$(obj).parents("tr").attr("tr_saveStatus",'0');
	}
	//判断所选险种是否是可选必须责任组，如果是必选责任组，则默认勾选同险种下的其它责任组；取消也是
		if($(obj).parents("tr").attr("title")=='primaryInsurance'){
			selBusiItemId = $(obj).parents("tr").find("td").eq(busiItemIdIndex).text();
			checkBoxChecked(2,selBusiItemId,$(obj).is(":checked"));
			//下拉列表关联
			//$(obj).parents("tr").find("td select").each(function(i){
			//		checkboxcheckedrolesfun($(obj).attr("checked"), this,selBusiItemId);
			//});
			
			//复选框和是否保存关联
			$("#table1 tr[title='additionalInsurance']").each(function(i){
				if(selBusiItemId != null && selBusiItemId == $(this).find("td").eq(masterBusiItemIdIndex).text()){
					busiProdCode = $(this).find("td").eq(busiProdCodeIndex).text();//150568 modify 10 to busiProdCodeIndex
					if($(obj).is(":checked")){
							$(this).find("td").eq(0).find("input:checkbox").attr("checked",true);
							if(!isSpecialBusiCode(busiProdCode)){
							$(this).find("td").eq(0).find("input:checkbox").attr("disabled",true);
							}
							$(this).attr("tr_saveStatus",'1');
					}else{
							$(this).find("td").eq(0).find("input:checkbox").attr("checked",false);
							if(!isSpecialBusiCode(busiProdCode)){
							$(this).find("td").eq(0).find("input:checkbox").attr("disabled",false);
							}
							$(this).attr("tr_saveStatus",'0');
					}
				}
			});
		}
	    //保存过一次以后，再进来，没有置灰，如果去除附加险勾选，及联去掉险的。
		if($(obj).parents("tr").attr("title")=='additionalInsurance'){
			selBusiItemId = $(obj).parents("tr").find("td").eq(masterBusiItemIdIndex).text();
			busiProdCode =  $(obj).parents("tr").find("td").eq(busiProdCodeIndex).text();//150568 modify 10 to busiProdCodeIndex
			//复选框和是否保存关联
			$("#table1 tr[title='primaryInsurance']").each(function(i){
				if(selBusiItemId != null && selBusiItemId == $(this).find("td").eq(busiItemIdIndex).text()){
					if(!$(obj).is(":checked")){
						if(!isSpecialBusiCode(busiProdCode)){
						$(this).find("td").eq(0).find("input:checkbox").attr("checked",false);
						$(this).attr("tr_saveStatus",'0');
					}
					}
				}
			});
		} 
	
};

function isSpecialBusiCode(busiProdCode){
	if(busiProdCode=='00563100'|| busiProdCode=='00958100'){
		return true;
	}else{
		return false;
	}
}

//计算正常退保金额
function caluNormalPremium() {
	var itemIds="";
	//判断是否选中
	var haveChecked = $("#table1 input[type='checkbox']:checked",
			navTab.getCurrentPanel()).size();
	if (haveChecked == 0) {
		alertMsg.info("请选择需要计算的信息！");
		return;
	}else{
		//判断主险附加险，可选必选责任组
		var checkMessage=checkBusiAndProduct();
		if(checkMessage!=null){
			alertMsg.info(checkMessage);
			return null;
		}
		
		$("#table1 input[type='checkbox']:checked").each(function(){
			if(itemIds==""){
				itemIds = $(this).val();
			}else{
				itemIds = itemIds +","+ $(this).val();
			}
		});
	};
	
	$("#itemIds").attr('value',itemIds);
	
	$("#premAgreeSurrendForm").submit();
}

function beforecCaluNormalPremium (){
	//添加	实际退保付费金额大于MAX（保单累计所交保费，保单现价）时，系统要做出非阻断提示:” XXXX（多个保单以“，”隔开）实际退保金额大于MAX（保单累计所交保费，保单现价），请确认！”。
	//选中的行进行校验，保单累计所交保费，保单现价较大者与实际退保金额比较
	var maxFlag = false;
	$("#table1 input[type='checkbox']:checked",navTab.getCurrentPanel()).each(function(){
		var allAmount = $(this).parents("tr").find("td[title='allAmount']").text()
		var cashValue = $(this).parents("tr").find("td[title='cashValue']").text()
		var chgActualReturnPremium = $(this).parents("tr").find("td[title='chgActualReturnPremium']").text()
		if(parseFloat(chgActualReturnPremium)>parseFloat(cashValue) && 
				parseFloat(chgActualReturnPremium)>parseFloat(allAmount)){//大于其中较大者
			maxFlag = true;
			return false;
		}
	});
	if(maxFlag){
		var $table = $("#table1",navTab.getCurrentPanel());
		var policycode="";
		$table.find("tbody").find("tr").each(function(i){
			policycode=policycode+$(this).find("td").eq(8).html()+",";
				
		});
		if(policycode.length>0){
			policycode=policycode.substring(0,policycode.length-1);
		}
		alertMsg.confirm(policycode+"实际退保金额大于MAX（保单累计所交保费，保单现价），请确认", {
						okCall : function() {
							_CSEA_saveChange();
						}
					});
	}else{
		_CSEA_saveChange();
	}
}

	//根据页面上录入变更信息记录新的变更信息
	function _CSEA_saveChange() {
			
		//判断是否选中
		var haveChecked = $("#table1 input[type='checkbox']:checked",
				navTab.getCurrentPanel()).size();
		//需求分析任务 #71049公司解约原因增加贷款终止选项需求:判断页面展示险种个数start
		var haveCount = $("#table1 input[type='checkbox']",
				navTab.getCurrentPanel()).size();
		//需求分析任务 #71049公司解约原因增加贷款终止选项需求:判断页面展示险种个数end
		if (haveChecked == 0) {
			alertMsg.info("未选择公司解约的险种，请确认！");
			return;
		} else {
			
			//校验必填项
			//校验是否选择了退保原因 和 投保人与业务员关系
			var $table = $("#table1",navTab.getCurrentPanel());
			var surrenderCauseIndex;
			var agentHolderRelIndex;
			var adjustFeeAmountIndex;
			var busiProdCodeIndex;//150568
			$table.find("th").each(function(i){
				
				if($(this).attr("colName") == 'companySurrenderCause'){
					surrenderCauseIndex = i;
				} else if($(this).attr("colName") == 'agentHolderRelation'){
					agentHolderRelIndex = i;
				}else if($(this).attr("colName") == 'adjustFeeAmount'){
					adjustFeeAmountIndex = i;
				}
				//150568 start
				else if($(this).attr("colName") == 'busiProdCode'){
					busiProdCodeIndex = i;
				}
				//150568 end
			});
			
			var returnflay = 0; //阻断标记
			//所有可选条目
			var allSelect=$("#table1 .myClass ",
					navTab.getCurrentPanel());
			//所有已选条目
			var isChecked=$("#table1 input[type='checkbox']:checked",
					navTab.getCurrentPanel());
			$("#table1 input[type='checkbox']:checked",navTab.getCurrentPanel()).each(function(i){
				if(adjustFeeAmountIndex != null && $(this).parents("tr").find("td").eq(adjustFeeAmountIndex).find("input").val() ==''){
					alertMsg.info('请选录入"调整后解约退费"金额！');
					returnflay = 1;
				}
				if(surrenderCauseIndex != null && $(this).parents("tr").find("td").eq(surrenderCauseIndex).find("select").val() ==''){
					alertMsg.info("请选择退保原因！");
					returnflay = 1;
				}
              /*if(agentHolderRelIndex != null && $(this).parents("tr").find("td").eq(agentHolderRelIndex).find("select").val() ==''){
					alertMsg.info("请选择投保人与业务员关系！");
					returnflay = 1;
				} */
				/**需求分析任务 #71049公司解约原因增加贷款终止选项需求:start*/
				 var policyLoanSurrenderCause=$(this).parents("tr").find("td").eq(surrenderCauseIndex).find("select").val();
				 if(null!=policyLoanSurrenderCause&&policyLoanSurrenderCause=='407'){
					//整单退保
					if(haveCount==haveChecked){
						var contractMasterState = $(this).parents("tr").find("td[title='contractMasterState']").text();
						if(contractMasterState=='0'){
							alertMsg.info("保单不处于贷款终止状态，请重新选择解约原因。");
							returnflay = 1;	
						}
						
					}else{
						var contractMasterState = $(this).parents("tr").find("td[title='contractMasterState']").text();
						if(contractMasterState=='1'){
							alertMsg.info("本次非整单解约，请重新选择解约原因。");
							returnflay = 1;	
						}
				  } 
			  }
				 /**需求分析任务 #71049公司解约原因增加贷款终止选项需求:end*/
				var isOrNoClaimPolicyYear = $(this).parents("tr").find("td[title='isOrNoClaimPolicyYear']").text();
				var busiProdName=$(this).parents("tr").find("td[title='busiProdName']").text()
				if(allSelect.size()>isChecked.size()){
					if(isOrNoClaimPolicyYear == "1"){
						//alertMsg.info(busiProdName + "已经发生保险金给付，不允许解除合同。")
						alertMsg.error(busiProdName + "已经发生保险金给付，不得申请解除本合同。");
						//alert(busiProdName + "============" + isOrNoClaimPolicyYear);
						returnflay = 1;
					}
				}
					
				
			});
			

			if(returnflay == 1){
				return;
			}
			
			
		}
		
		var checkMessage=checkBusiAndProduct();
		if(checkMessage!=null){
			alertMsg.info(checkMessage);
			return null;
		}
		
		alertMsg.confirm("请确认是否需要保存录入信息？",{
			okCall:function(){
		//把选中的数据传入到后台
		var $dataTable = $("#table1", navTab.getCurrentPanel());
		var $trs = $dataTable.find("tbody tr");
		$trs.each(function() {
			var $tds = $(this).find("td");
			
			if ($tds.eq(0).children().attr("checked") == "checked") {
				$(this).attr("tr_saveStatus", "1");
			}

		});
		var _table = $("#table1");
		var _tableJs = _cs_tableToJson(_table);
		$("#saveTableInfo").attr('value',_tableJs);
		var action ="${ctx }/cs/serviceitem_ea/saveSurrender_PA_csEndorseEAAction.action";
		var onsubmit ="return navTabSearch(this,'showAFPage');";
 		$("#premAgreeSurrendForm").attr('action',action);
 		$("#premAgreeSurrendForm").attr('onsubmit',onsubmit);
 		
		//（险种简称）已经发生保险金给付，不允许解除合同。如您强求解除合同，（险种简称）解除合同时现价为0。
		var  bxj=null;
		//（险种简称）已经发生保险金给付，不允许解除合同。阻断提示
		var shMsg=null;
		$("#table1 input[type='checkbox']:checked",
				navTab.getCurrentPanel()).each(function(){
					var busiCode=$(this).parent().parent().find("td").eq(busiProdCodeIndex).text();//150568 modify 10 to busiProdCodeIndex
					var isCancerFlag= $(this).parent().parent().find("td").eq(-3).text();
					
					if(isCancerFlag!=null&&isCancerFlag=="1"){
							if(busiCode=="00389000"){
								bxj="附加高额交通意外已经发生保险金给付，不允许解除合同。"+
										"如您强求解除合同，附加高额交通意外解除合同时现价为0";
						 	}else if(busiCode=="00958100"){
						 		bxj="康健华贵B已经发生保险金给付，不允许解除合同。"+
										"如您强求解除合同，康健华贵B解除合同时现价为0";
						 	}else if(busiCode=="00963000"){ 
						 		bxj="附加住院补贴已经发生保险金给付，不允许解除合同。"+
										"如您强求解除合同，附加住院补贴解除合同时现价为0";
						 	}else if(busiCode=="00976000"){
						 		bxj="附加心脑血管已经发生保险金给付，不允许解除合同。"+
										"如您强求解除合同，附加心脑血管解除合同时现价为0";
						 	}else if(busiCode=="00831000"){
						 		bxj="附加门急诊已经发生保险金给付，不允许解除合同。"+
								"如您强求解除合同，附加门急诊解除合同时现价为0";
				 			}else if(busiCode=="00581000"){
						 		bxj="附加自驾车意外已经发生保险金给付，不允许解除合同。"+
								"如您强求解除合同，附加自驾车意外解除合同时现价为0";
				 			}else if(busiCode=="00A15000"){
						 		bxj="附加恶性肿瘤A款疾病保险已经发生保险金给付，不允许退保。"+
								"如您强求退保,附加恶性肿瘤A款疾病保险退保时现价为0";
				 			}else if(busiCode=="00557000"){
				 				shMsg="上海医保卡重疾已经发生保险金给付，不允许解除合同。";
				 			}else if(busiCode=="00558000"){
				 				shMsg="上海医保卡住院自费医疗已经发生保险金给付，不允许解除合同。";
				 			}else if(busiCode=="00842000"){
				 				shMsg="上海医保账户医疗已经发生保险金给付，不允许解除合同。";
				 			}else if(busiCode=="00843000"){
				 				shMsg="上海医保账户意外医疗已经发生保险金给付，不允许解除合同。";
				 			}
				     }
		});
		//上海医保卡阻断提示
		if(shMsg!=null){
			alertMsg.error(shMsg);
			return;
		}
		//非阻断提示
		if(bxj!=null){
			alertMsg.confirm(bxj,
					{
						okCall : function(){
							check808Relation();
						}
					});
		}else{
			check808Relation();
		}
		}}); 
	};
	
	function check808Relation(){
		var policyCode = "";
		var busiItemId = "";
		$("#table1 input[type='checkbox']:checked",navTab.getCurrentPanel()).each(function(){
			policyCode = policyCode + $(this).parent().parent().find("td").eq(8).text() + ",";
			busiItemId = busiItemId + $(this).parent().parent().find("td").eq(3).text() + ",";
		});
		$.ajax({
			url:"${ctx}/cs/csAccept/checkRelationFor808_PA_csEntryAction.action",
			type:"post",
			dataType:'text',
			data:"policyCode=" + policyCode +"&busiItemId=" + busiItemId,
			success:function(data){
				console.log(data);
				var json = jQuery.parseJSON(data);
				console.log(json);
				if(json.statusCode == 200){
					alertMsg.confirm(json.message,
							{
								okCall : function(){
									saveSubmitAjax();
								}
							});
				}else{
					saveSubmitAjax();
				}
			}
		});
	}
	
	function saveSubmitAjax(){
		debugger;
		var isCode928=isCK928();
		if(isCode928&&$("#_specialFlag").val()==''){
			alertMsg.error("请选择是否特殊退保！");
			return;
		}
		var isSYFlag = isSY();//如果有928险种退保，选择是否特殊退保
		if(isSYFlag){
			alertMsg.error("税延产品需要退保全部产品,请对所有产品进行退保操作！");
			return;
		}
		var action ="${ctx }/cs/serviceitem_ea/saveSurrender_PA_csEndorseEAAction.action";
		var onsubmit ="return navTabSearch(this,'showAFPage');";
 		$("#premAgreeSurrendForm").attr('action',action);
 		$("#premAgreeSurrendForm").attr('onsubmit',onsubmit);
		
		$.ajax({
			type : "post",
			url : action,
			data : $("#premAgreeSurrendForm", navTab.getCurrentPanel()).serialize(),
			success : function(data) {
				var json = DWZ.jsonEval(data);
				if (json.statusCode == DWZ.statusCode.error) {
					alertMsg.error(json.message);
				} else if (json.statusCode == DWZ.statusCode.timeout) {
					DWZ.loadLogin();
				}else{
					alertMsg.correct("保存成功！");
					$('#showAFPage', navTab.getCurrentPanel()).html(data).initUI();
					 navTab.reload();
				}
			},
		});
	}
	
	function checkBusiAndProduct() {
		//判断主险附加险，可选必选责任组
		//所有可选条目
		var allSelect=$("#table1 .myClass ",
				navTab.getCurrentPanel());
		//所有已选条目
		var isChecked=$("#table1 input[type='checkbox']:checked",
				navTab.getCurrentPanel());
		//如果可选条目大于已选条目
		if(allSelect.size()>isChecked.size()){
			for(var a=0;a<isChecked.length;a++){
				//如果被退的是主险
// 					alert($(allSelect[index]).parents("tr").first().children().eq(25).text());
				if($(isChecked[a]).parents("tr").first().children().eq(25).text()==""){
					//遍历所有可选条目，查找其附加险其附加险并未被选中
					for(var index=0;index<allSelect.length;index++){
					
// 						alert($(isChecked[a]).parents("tr").first().children().eq(22).text());
// 						alert($(allSelect[index]).parents("tr").first().
// 								children().first().children().first().attr("checked")!="checked"); 
						
						if($(allSelect[index]).parents("tr").first().children().eq(25)
								.text()==$(isChecked[a]).parents("tr").first().children().eq(22).text()
								&&$(allSelect[index]).parents("tr").first().
								children().first().children().first().attr("checked")!="checked"){
							return "选择主险公司解约的同时需要选择对应的附加险！";
						}
					}
				}
				//如果被退的是必选责任组
				if($(isChecked[a]).parents("tr").first().children().eq(26).text()==1){
					//遍历所有可选条目，查找与其同一险种下的责任组（不管可选必选）且这些责任组并未被选中。
					for(var index=0;index<allSelect.length;index++){
/* 						alert($(allSelect[index]).parents("tr").first().children().eq(22)
								.text());
						alert($(isChecked[a]).parents("tr").first().children().eq(22).text());
						alert($(allSelect[index]).parents("tr").first().
								children().first().children().first().attr("checked")!="checked"); */
						if($(allSelect[index]).parents("tr").first().children().eq(22)
								.text()==$(isChecked[a]).parents("tr").first().children().eq(22).text()
								&&$(allSelect[index]).parents("tr").first().
								children().first().children().first().attr("checked")!="checked"){
							return "选择必选责任组公司解约的同时需要选择该险种下的所有责任组！";
						}
					}
				}
			}
		}
		return null;
	}
	
</script>