<%@ page language="java" contentType="text/html; charset=utf-8"
	pageEncoding="utf-8"%>
<%@ taglib uri="http://www.git.com.cn/taglib/udmp/field" prefix="Field"%>
<%@ taglib uri="/struts-tags" prefix="s"%><%@include file="/udmpCommon.jsp"%>
<%@ include file="/cs/pages/common/jsp/common_ctx.jsp"%><script
	type="text/javascript" src="${ctx}/cs/js/cs_common.js"></script>
<link rel="stylesheet" href="${ctx}/cs/css/cs_common.css" type="text/css">
<link rel="stylesheet" href="${ctx}/cs/css/public.css" type="text/css">
<script
	src="${pageContext.request.contextPath}/udmp/plugins/ribbon/jquery.asyncorgtree.js"
	type="text/javascript"></script>
<script type="text/javascript" src="${ctx}/cs/js/DomZoomZX.js"></script>
<script>
	$(document).ready(function(){
        var D = DomZoomZX('input,select,textarea,td')
	})
</script>
<script type="text/javascript">
	function changePolicyLoss() {
		/* var lossType = $("#lossType option:selected", navTab.getCurrentPanel())
				.val(); */
		/* var lossReason = $("#lossReason option:selected",
				navTab.getCurrentPanel()).val(); */
		var lossDate = $("#lossDate", navTab.getCurrentPanel()).val();
		var lossReason = $("#lossReason", navTab.getCurrentPanel()).val();
		var lostType = $("#lostType", navTab.getCurrentPanel()).val();
		
		$("#t", navTab.getCurrentPanel()).show();
		if (lostType == "" || lossReason == "" || lossDate == "") {
			alertMsg.error("必填项信息未完整录入，不能受理保单挂失，请确认。");
		} else {
			var  rootPath= getRootPath();
			$.ajax({
				type : "POST",
				url : rootPath+"/cs/serviceitem_pl/saveChange_PA_csEndorsePLAction.action?",
				async : false,
				success : function(data) {
					$("#changeLossMsg", navTab.getCurrentPanel()).html(data)
							.initUI();
				}
			});
		}
	}

	//帮助部分动作
	function MM_changeProp(objId, theProp, theValue) {
		var obj = null;
		with (document) {
			if (getElementById) {
				obj = getElementById(objId);
			}
		}
		if (obj) {
			if (theValue == true || theValue == false) {
				eval("obj.style." + theProp + "=" + theValue);
			} else {
				eval("obj.style." + theProp + "='" + theValue + "'");
			}
		}
	};

	function upToCsEntry_pl() {
		//系统提示是否保存变更信息，确认--保存，取消--清除
		alertMsg.confirm("请确认是否需要保存录入的信息？", {
			okCall : function() {
				$("#gotoCsEntry", navTab.getCurrentPanel()).click();
			},
			cancelCall : function() {
				//不保存--返回保全录入页面
				$("#gotoCsEntry", navTab.getCurrentPanel()).click();
			}
		});
	};

	function nextStep_basicRemark_pl(acceptId) {

		$("#gotoCsEntry", navTab.getCurrentPanel()).click();
	}

	function csEndorsePL_query(formId) {
		var $form = $("#" + formId, navTab.getCurrentPanel());
		$form.submit();
		alertMsg.correct("保单挂失修改状态成功");
		
	}
	function checkquery(formId) {
		var customerId = $("#customerIdt", navTab.getCurrentPanel()).val();
		/* var lossType = $("#lossType option:selected", navTab.getCurrentPanel())
				.val(); */
		/* var lossReason = $("#lossReason option:selected",
				navTab.getCurrentPanel()).val(); */
				var lossReason = $("#lossReason", navTab.getCurrentPanel()).val();
				var lostType = $("#lostType", navTab.getCurrentPanel()).val();	
		var lossDate = $("#lossDate", navTab.getCurrentPanel()).val();
		if (customerId == '') {
			alertMsg.error("无挂失数据。不能受理。");
			return false;
		} else if (lostType == "" || lossReason == "" || lossDate == "") {
			alertMsg.error("必填项信息未完整录入，不能受理保单挂失，请确认。");
			return false;
		} else {
			csEndorsePL_query(formId);
		}
	}

	//下一步
	function next() {
		var  rootPath= getRootPath();
		var val1 = $("#changeId").val();
		var val2 = $("#acceptId").val();
		var val3 = $("#customerIdt").val();
		var title = "受理信息录入";
		var tabid = "_aplPermit_M";
		var fresh = eval("true");
		var external = eval("false");
		var url = rootPath+"/cs/csEntry/showAcceptAndArapInfo_PA_csEntryAction.action?&menuId=${menuId}&changeId="
				+ val1 + "&acceptId=" + val2 + "&customerIdt=" + val3;
		navTab.openTab(tabid, url, {
			title : title,
			fresh : fresh,
			external : external
		});

	}
</script>
<!-- ********帮助菜单***********begin********* -->
<s:include value="/cs/pages/common/jsp/helpMenu.jsp"></s:include>
<!-- *********帮助菜单***********end********* -->
	<div class="pageContent" layoutH="36px">
		<s:include value="customerInfo_list.jsp" />

		<form id="policyLossForm"
			action="${ctx}/cs/serviceitem_pl/saveChange_PA_csEndorsePLAction.action?leftFlag=0&menuId=${menuId}"
			method="post" class="pageForm required-validate"
			onsubmit="return navTabSearch(this);">
			<!-- onsubmit="return validateCallback(this);" -->
			<div class="panel" style="margin: 10px;">
				<h1>保单险种列表信息</h1>
				<div class="pageFormContent">
					<table class="list" width="100%">
						<thead>
							<tr>
								<th>序号</th>
								<th>保单号</th>
								<th>险种代码</th>
								<th>险种名称</th>
								<th>保额</th>
								<th>保费</th>
								<th>生效日期</th>
								<th>保单状态</th>
							</tr>
						</thead>
						<tbody align="center">
							<s:iterator value="policyLoanVOs" status="st">
								<tr align="center" tr_saveStatus="1">
									<td><s:property value="#st.index+1" /></td>
									<td>${policyCode}</td>
									<td>${busiprcode}</td>
									<td><Field:codeValue tableName="APP___PAS__DBUSER.T_BUSINESS_PRODUCT"
											value="${busipid}" /></td>
									<td>${amount}</td>
									<td>${premium }</td>
									<td><s:date format="yyyy-MM-dd" name="validateDate"></s:date></td>
									<td><Field:codeValue tableName="APP___PAS__DBUSER.T_LIABILITY_STATUS"
											value="${policyState}" /></td>
								</tr>
							</s:iterator>
						</tbody>
					</table>
				</div>
			</div>


			<div class="panel" style="margin: 10px;">
				<h1>保单挂失信息</h1>
				<div class="pageFormContent">
					<!--保全申请号 -->
					<input type="hidden" id="customerIdt" name="customerId"
						value="${customerId}" />
					<!--保全申请号 -->
					<input type="hidden" id="changeId" name="changeId"
						value="${changeId}" />
					<!--保全受理号 -->
					<input type="hidden" id="acceptId" name="acceptId"
						value="${acceptId}" />

					<dl>
						<dt>
							挂失类型<span style="color: red">*</span>
						</dt>
						<dd >
							<s:if test="queryFlag==1">
								<Field:codeTable id="lostType" name="policyLoseVO.lostType"
									disabled="true" nullOption="true" cssClass="combox"
									tableName="APP___PAS__DBUSER.T_LOSE_TYPE" whereClause=""
									value="${policyLoseVO.lostType}"></Field:codeTable>
							</s:if>
							<s:else>
								<Field:codeTable id="lostType" name="policyLoseVO.lostType"
									nullOption="true" cssClass="combox" tableName="APP___PAS__DBUSER.T_LOSE_TYPE"
									whereClause="" value="${policyLoseVO.lostType}"></Field:codeTable>
							</s:else>
						</dd>
					</dl>
					<dl>
						<dt>
							挂失原因<span style="color: red">*</span>
						</dt>
						<dd >
							<s:if test="queryFlag==1">
								<Field:codeTable id="lossReason" name="policyLoseVO.lostCause"
									disabled="true" nullOption="true" cssClass="combox"
									tableName="APP___PAS__DBUSER.T_LOSE_CAUSE" whereClause=""
									value="${policyLoseVO.lostCause}"></Field:codeTable>
							</s:if>
							<s:else>
								<Field:codeTable id="lossReason" name="policyLoseVO.lostCause"
									nullOption="true" cssClass="combox" tableName="APP___PAS__DBUSER.T_LOSE_CAUSE"
									whereClause="" value="${policyLoseVO.lostCause}"></Field:codeTable>
							</s:else>
						</dd>
					</dl>
					<dl>
						<dt>
							丢失日期<span style="color: red">*</span>
						</dt>
						<dd >
							<s:if test="queryFlag==1">
								<input class="required" name="policyLoseVO.lostDate"
									disabled="disabled" required="required" type="expandDateYMD"
									value="<s:date format="yyyy-MM-dd" name="policyLoseVO.lostDate"></s:date>"
									style="width: 100px" id="lossDate" />
							</s:if>
							<s:else>
								<input class="required" name="policyLoseVO.lostDate"
									required="required" type="expandDateYMD" style="width: 100px"
									value="<s:date format="yyyy-MM-dd" name="policyLoseVO.lostDate"></s:date>"
									id="lossDate" />
							</s:else>
						</dd>
					</dl>
					<table <s:if test="queryFlag==1">style="display:none"</s:if><s:else>style="width:100%;padding-top:10px;" </s:else>>
				        <tbody>
					        <tr>
					            <td></td>
					            <td style="width:100px">
					                <!-- <div class="button" >
										<div class="buttonContent">
											<button type="button" class="but_blue" onclick="checkquery('policyLossForm');">保存</button>
										</div>
									</div> -->
									<div class="pageFormbut">
										<button type="button" class="but_blue" onclick="checkquery('policyLossForm');">保存</button>
									</div>
					            </td>
					            <td></td>
					        </tr>
				    	 </tbody>
				    </table>
				</div>
			</div>
		</form>
		<%--  <div class="panel" style="margin:10px;margin-top:0px;">
			<h1>变更后保单险种列表信息</h1>
			<div class="pageFormContent">
				<table class="list" width="100%">
					<thead>
						<tr>
							<th>序号</th>
							<th>保单号</th>
							<th>险种代码</th>
							<th>险种名称</th>
							<th>保额</th>
							<th>保费</th>
							<th>生效日期</th>
							<th>保单状态</th>
						</tr>
					</thead>
					<tbody align="center">
						<s:iterator value="policyLoanVOss" status="st">
							<tr align="center" tr_saveStatus="1">
								<td><s:property value="#st.index+1" /></td>
								<td>${policyCode}</td>
								<td>${busiprcode}</td>
								<td><Field:codeValue tableName="APP___PAS__DBUSER.T_BUSINESS_PRODUCT"
										value="${busipid}" /></td>
								<td>${amount}</td>
								<td>${premium}</td>
								<td><s:date format="yyyy-MM-dd" name="validateDate"></s:date></td>
								<td><Field:codeValue tableName="APP___PAS__DBUSER.T_LIABILITY_STATUS"
										value="${policyState}" /></td>
							</tr>
						</s:iterator>
					</tbody>
				</table>
			</div>
		</div> --%>

		<s:include value="/cs/pages/serviceitem/serviceItemBottomBar.jsp"></s:include>
	</div>




