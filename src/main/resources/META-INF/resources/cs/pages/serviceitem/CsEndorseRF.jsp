<%@ page language="java" pageEncoding="utf-8"%>
<%@ taglib uri="/struts-tags" prefix="s"%><%@include file="/udmpCommon.jsp"%>
<%@ taglib uri="http://www.git.com.cn/taglib/udmp/field" prefix="Field"%>
<%@ include file="/cs/pages/common/jsp/common_ctx.jsp" %>
<script type = "text/javascript" src="${ctx}/cs/js/cs_common.js"></script>
<link rel="stylesheet" href="${ctx}/cs/css/public.css" type="text/css">
<script type="text/javascript" src="${ctx}/cs/js/DomZoomZX.js"></script>
<script>
	$(document).ready(function(){
        var D = DomZoomZX('input,select,textarea,td')
	})
</script>
<script type="text/javascript">
	function payOffAll(obj) {
		//获取这个表格
		var $dataTable = $("#policyLoadTable", navTab.getCurrentPanel());
		//所有的行
		var $trs = $dataTable.find("tbody tr");
		if(obj.checked==true){
			var total=0;
			$trs.each(function() {
				if(eval($(this).find("td:eq(9)").text())>=0){
					$(this).find("td:eq(20)").children().val($(this).find("td:eq(9)").text());
					$(this).find("td:eq(20)").children().attr("readonly","readonly");
					$(this).find("td:eq(21)").text((eval($(this).find("td:eq(9)").text())+eval($(this).find("td:eq(13)").text())+eval($(this).find("td:eq(15)").text())+eval($(this).find("td:eq(17)").text())+eval($(this).find("td:eq(19)").text())).toFixed(4));
					total+=eval($(this).find("td:eq(21)").text());
				}
			});
			if (!isNaN(total)){
				$("#policySum", navTab.getCurrentPanel()).val((total).toFixed(4));
			}else{
				$("#policySum", navTab.getCurrentPanel()).val(0);
			}
		}else{
			$trs.each(function() {
				$(this).find("td:eq(20)").children().val(0);
				$(this).find("td:eq(20)").children().removeAttr("readonly");
				$(this).find("td:eq(21)").text(0);
			});
			$("#policySum", navTab.getCurrentPanel()).val(0);
		}
		
		
	}
	function changeNum(obj){
		//获取这个表格
		var $dataTable = $("#policyLoadTable", navTab.getCurrentPanel());
		//所有的行
		var $trs = $dataTable.find("tbody tr");
		//贷款本金
		var Amount =$(obj).parent().parent().find("td:eq(9)").text()-0;
		var cash1=$(obj).parent().parent().find("td:eq(13)").text()-0;
		var cash2=$(obj).parent().parent().find("td:eq(15)").text()-0;
		var cash3=$(obj).parent().parent().find("td:eq(17)").text()-0;
		var cash4=$(obj).parent().parent().find("td:eq(19)").text()-0;
		//清偿金额
		var realAmount = $(obj).val()-0;
		if(isNaN(realAmount)){
			alertMsg.info("输入非法字符！");
			$(obj).val(0);
			realAmount=0;
			
		}else{
		var rate=realAmount/Amount;
		if(Amount<realAmount||realAmount<=0){
			//A.金额非法
			if(realAmount>0){
				alertMsg.info("还款金额大于贷款本金，请做修改！");
			}
			if(realAmount<0){
				alertMsg.info("还款金额不能为负，请做修改！");
			}
			//1.还款金额设为空
			$(obj).val(0);
			$(obj).parent().parent().find("td:eq(21)").text(0);
			var total=0;
			$trs.each(function() {
				if(eval($(this).find("td:eq(20)").children().val())>=0){
					total+=eval($(this).find("td:eq(21)").text());
				}
			});
			
			if (!isNaN(total)){
				$("#policySum", navTab.getCurrentPanel()).val((total).toFixed(4));
			}else{
				$("#policySum", navTab.getCurrentPanel()).val(0);
			}
		}else{
			//B.金额合法
			$(obj).parent().parent().find("td:eq(21)").text((eval(realAmount)+(eval(cash1)+eval(cash2)+eval(cash3)+eval(cash4))*rate).toFixed(4));
			//所有领取金额总和
			var sumtotal=0;
			$trs.each(function() {
				if(eval($(this).find("td:eq(20)").children().val())>=0){
					sumtotal+=eval($(this).find("td:eq(21)").text());
				}
			});
			if (!isNaN(sumtotal)){
				$("#policySum", navTab.getCurrentPanel()).val((sumtotal).toFixed(4));
			}else{
				$("#policySum", navTab.getCurrentPanel()).val(0);
			}
		}
		}
	}
	
</script>
<s:include value="/cs/pages/common/jsp/helpMenu.jsp"></s:include>
<s:include value="csEndorseProgress.jsp" />
<div layoutH="140">
<input type="hidden" name="changeId" id="changeId" value="${changeId}" />
<input type="hidden" name="acceptId" id="acceptId" value="${acceptId}" />
<input type="hidden" name="customerId" id="customerId" value="${customerId}" />
	<form id="policyLoanPayOffForm"
		action="${ctx}/cs/serviceitem_rf/loadLoanPayOff_PA_csEndorseRFAction.action"
		class="pageForm required-validate" method="post"
		onsubmit="return navTabSearch(this,'PolicyLoanPayOffDiv')">
		<s:include value="customerInfo_list.jsp" />
		<div class="panel" style="margin: 10px;  display: none">
			<h1>客户信息</h1>
			<div class="pageFormContent">
				<dl>
					<dt>姓名</dt>
					<dd>
						<input name="loanPayOffVO.cusName" type="text"
							value="${csCustomerVO.customerName}" disabled="disabled" />
					</dd>
				</dl>
				<dl>
					<dt>性别</dt>
					<dd>
						<input type="text" style="display: none" name="customerGender"
							value="${csCustomerVO.customerGender }" /> <input type="text"
							readonly="readonly"
							value='<Field:codeValue tableName="APP___PAS__DBUSER.T_GENDER"  value="${csCustomerVO.customerGender}"/>'>
					</dd>
				</dl>
				<dl>
					<dt>生日</dt>
					<dd>
						<input type="text" readonly="readonly" name="customerBirthday"
							value="<s:date format="yyyy-MM-dd" name="csCustomerVO.customerBirthday"></s:date>">
					</dd>
				</dl>
				<dl>
					<dt>证件类型</dt>
					<dd>
						<input type="text" style="display: none" name="customerCertType"
							value="${csCustomerVO.customerCertType }" /> <input type="text"
							readonly="readonly"
							value='<Field:codeValue   tableName="APP___PAS__DBUSER.T_CERTI_TYPE"  value="${csCustomerVO.customerCertType}"/>'>
					</dd>
				</dl>
				<dl>
					<dt>证件号码</dt>
					<dd>
						<input type="text" style="display: none" name="customerCertiCode"
							value="${csCustomerVO.customerCertiCode }"> <input
							type="text" readonly="readonly"
							value="${csCustomerVO.customerCertiCode}">
					</dd>
				</dl>
			</div>
		</div>
		<div class="divfclass">
			<h1>
				<img src="images/tubiao.png" >保单贷款信息<font style="display: none">CsEndorseRF</font>
				<font color='red' style="margin-left:800px;">
				 	${isAutoPCL?"自动清偿业务":"" }
				</font>
			</h1>
		</div>
			<div>
			<div class="pageFormContent">
				<table class="list" width="100%" id="policyLoadTable" table_saveStatus="1">
					<thead>
						<tr>
						    <th colName="changeId" style="display: none">changeId</th>
						    <th colName="acceptId" style="display: none">acceptId</th>
						    <th colName="policyChgId" style="display: none">policyChgId</th>
							<th colName="policyId" style="display: none">policyId</th>
							<th colName="productItemID" style="display: none">BusiItemId</th>
							<th colName="masterBusiItemId" style="display: none">masterBusiItemId</th>
							<th colName="policyCode">保单号</th>
							<th colName="productItem">险种代码</th>
							<th>险种名称</th>
							<th colName="loanPrincipal">贷款本金</th>
							<th colName="loanStartDateString">贷款起期</th>
							<th colName="loanStartDateOld">贷款起息日</th>
							<th colName="loanEndDateString">原贷款止期</th>
							<th colName="initialInterestRate">初始利率</th>
							<th colName="initialInterest">初始利息</th>
							<th colName="overdueInterestRate">逾期利率</th>
							<th colName="overdueInterest">逾期利息</th>
							<th colName="secondInterestRate">二阶段利率</th>
							<th colName="secondInterest">二阶段利息</th>
							<th colName="thiredInterestRate">三阶段利率</th>
							<th colName="thiredInterest">三阶段利息</th>
							<th colName="busiLoanAmount" inputType="input">清偿本金/全部清偿
							<input type="checkbox" id="result"  onclick="payOffAll(this);" disabled checked="checked" /> </th>
							<!-- <th colName="payOffCapital">清偿金额</th> -->
							<s:if test="csAcceptChangeVO.balanceFlag == 1 ">
								<th>到期应清偿本息和</th>
								<th colName="survivalMoney" >领取金额</th>
								<th colName="payOffCapital" >实际清偿金额</th>
								
								<th colName="surMoneyFromAcc" style="display: none">领取金额源于累积生息账户</th>
								<th colName="surMoneyFromSur" style="display: none">领取金额源于未领生存金</th>
								<th colName="busiItemId" style="display: none">险种Id</th>
								<th colName="surMoneyAccountId" style="display: none">累积生息账户Id</th>
							</s:if>
							<s:else>
								<th colName="payOffCapital">到期应清偿本息和</th>
							</s:else>
							<th colName="customerId" style="display: none">customerId</th>
							<th colName="productName" style="display: none"></th>
						</tr>
					</thead>
					<tbody id="allTrContent">
						<s:iterator value="policyRevivalVO" status="st">
							<tr align="center" tr_saveStatus="1">
                                <td style="display: none"><s:property value="changeId" /></td>
                                <td style="display: none"><s:property value="acceptId" /></td>
                                <td style="display: none"><s:property value="policyChgId" /></td>
								<td style="display: none"><s:property value="policyId" /></td>
								<td style="display: none"><s:property value="productItemID " /></td>
								<td style="display: none"><s:property value="masterBusiItemId" /></td>
								<td>${policyCode}</td>
								<td>${productItem}</td>
								<td><Field:codeValue tableName="APP___PAS__DBUSER.T_BUSINESS_PRODUCT" value="${productName}" /></td>
								<td>${loanPrincipal}</td>
								<td><s:date format="yyyy-MM-dd" name="loanStartDate" /></td>
								<!--贷款起息日  -->
								<td><s:date format="yyyy-MM-dd" name="loanStartDate" /></td>
								<!-- 止期 -->
								<td><s:date format="yyyy-MM-dd" name="loanEndDate" /></td>
								<td>${initialInterestRate}</td>
								<td>${initialInterest}</td>
								<td>${overdueInterestRate}</td>
								<td>${overdueInterest}</td>
								<td>${secondInterestRate}</td>
								<td>${secondInterest}</td>
								<td>${thiredInterestRate}</td>
								<td>${thiredInterest}</td>
								<td><input class="number" onchange="changeNum(this)" id="payOffCash" name="busiLoanAmount" 
									value="${loanPrincipal}" style="width: 80px" type="text" readonly /></td>
								<!-- <td>${payOffCapital}</td> -->
								<!-- 领取金额  - 新增2017-10-10 -->
								<s:if test="csAcceptChangeVO.balanceFlag == 1 ">
									<td>${payOffCapital}</td>
									<td title="survivalMoney">${survivalMoney}</td>
									<td title="payOffCapital">${payOffCapital-survivalMoney}</td>
									
									<td title="surMoneyFromAcc" style="display: none">${surMoneyFromAcc}</td>
									<td title="surMoneyFromSur" style="display: none">${surMoneyFromSur}</td>
									<td title="busiItemId" style="display: none">${busiItemId}</td>
									<td title="surMoneyAccountId" style="display: none">${surMoneyAccountId}</td>
								</s:if>
								<s:else>
									<td title="payOffCapital">${payOffCapital}</td>
								</s:else>
								<td style="display: none">${customerId}</td>
								<td style="display: none">${productName}</td>
							</tr>
						</s:iterator>
					</tbody>
				</table>
				
				<s:if test="queryFlag!=1">
					<div class="subBar">
						<table style="width:100%;padding-top:5px;">
					        <tbody>
						        <tr>
						            <td></td>
						            <td style="width:100px">
						            	<div class="pageFormdiv" >
							                <div class="buttonContent">
												<button type="button" class="but_blue" onclick="policyLoanSaveForRF('policyLoanPayOffForm','PolicyLoanPayOffDiv');">保存</button>
											</div>
										</div>
						            </td>
						            <td></td>
						        </tr>
					    	 </tbody>
					    </table>
				   </div>
			   </s:if>
		    <input type="hidden" id="jsons" name="jsons">
		    <dl>
			<dt>本次补退费合计：</dt>
			<dd>
				<input id="policySum" type="text" name="amount" value="${amount}" readonly="readonly"/>
			</dd>
		</dl>
	</div>	
</div>
	</form>
		
	<div style="clear: both;"></div>
	<br />
	<div id="PolicyLoanPayOffDiv" class="unitBox">
		<s:if test="loanList.get(0)!=null">
			<s:include value="/cs/pages/serviceitem/CsEndorseRF_after.jsp"></s:include>
		</s:if>
	</div>
</div>
<s:if test="queryFlag!=1">
<s:include value="/cs/pages/serviceitem/serviceItemBottomBar.jsp"></s:include>
</s:if>
<script type="text/javascript">
$(function(){//页面加载完毕，计算总额
	//获取这个表格
	var $dataTable = $("#policyLoadTable", navTab.getCurrentPanel());
	//所有的行
	var $trs = $dataTable.find("tbody tr");
	var total=0;
	$trs.each(function() {
		if(eval($(this).find("td:eq(9)").text())>=0){
			total += eval($(this).find("td[title='payOffCapital']").text());
		}
	});
	if (!isNaN(total)){
		$("#policySum", navTab.getCurrentPanel()).val((total).toFixed(2));
	}else{
		$("#policySum", navTab.getCurrentPanel()).val(0);
	}
	
});

//下一步
function next() {
	var val1 = $("#changeId").val();
	var val2 = $("#acceptId").val();
	var val3 = $("#customerId").val();
	var title = "受理信息录入";
	var tabid = "_aplPermit_M";
	var fresh = eval("true");
	var external = eval("false");
	var  rootPath= getRootPath();
	var url = rootPath+"/cs/csEntry/showAcceptAndArapInfo_PA_csEntryAction.action?&menuId=${menuId}&changeId="
			+ val1 + "&acceptId=" + val2 + "&customerId=" + val3;
	navTab.openTab(tabid, url, {
		title : title,
		fresh : fresh,
		external : external
	});

}
	function policyLoanSaveForRF(formId, boxId) {
		var flag=0;
		var policyCode="";
		 var totalAmount = $("#policySum", navTab.getCurrentPanel()).val();
			 if(totalAmount == null || totalAmount ==""){
			       alertMsg.info("必填项信息未完整录入，不能受理贷款清偿，请确认！");
			       return false;
			   } 
	//获取这个表格
	var $dataTable = $("#policyLoadTable", navTab.getCurrentPanel());
	/* alert($dataTable.html()); */
	var $trs = $dataTable.find("tbody tr");
	$trs.each(function() {
		if(eval($(this).find("td:eq(5)").text())==null && eval($(this).find("td:eq(20)").find("input").val())==null){
					 flag=1;
					 if(policyCode==""){
						 policyCode=$(this).find("td:eq(6)").text();
					 }else{
						 policyCode=policyCode+","+$(this).find("td:eq(6)").text();
					 }
		 
		}
		
	
	});	
	/* if(flag==1){
		 alertMsg.info("保单"+policyCode+"未选择清偿");
	       return false;
	} */
	
	
	/* 对于有生存金抵扣时添加校验，与需求确认，以保单为单位进行生存金计算，某个保单的生存金总额不能大于此保单贷款本息和，续贷相关校验亦同 ----begin---- */
	if('${csAcceptChangeVO.balanceFlag}'==1){//如果是生存金抵扣的话
		var isTooLargeForSurveyMoney = false;//定义是否应当提示：应领生存金大于贷款本息和
		var hadCalcuPolicy = "";
		$trs.each(function() {
			/* 对于有生存金抵扣时添加校验----begin---- */
				var currentPolicyCode = $(this).find("td:eq(6)").text();
				var currentProductItem = $(this).find("td:eq(7)").text();
				if(hadCalcuPolicy.indexOf(currentPolicyCode)>=0){//如果已经计算过了
					return true;//跳过本次循环
				}
				hadCalcuPolicy += currentPolicyCode+",";
				var currentPolicyTotalPay = 0;
				var $currentPolicyHaveProducts = $dataTable.find("td:contains('"+currentPolicyCode+"')");
				$currentPolicyHaveProducts.each(function(index,domEle){//循环当前保单下的险种
					var payStr = $(domEle).parents("tr").find("td[title='payOffCapital']").text();
					currentPolicyTotalPay +=  parseFloat(payStr);
				});
				//如果有一个不符合条件就终止判断
				if(currentPolicyTotalPay<0){
					isTooLargeForSurveyMoney = true;
					return false;
				}
			/* 对于有生存金抵扣时添加校验 ----end---- */
		});
		// Msg:应领生存金大于贷款本息和，请直接进行年金满期金领取或客户账户领取。
		if(isTooLargeForSurveyMoney){
			alertMsg.error("应领生存金大于贷款本息和，请直接进行年金满期金领取或客户账户领取。");
		    return false;
		}
	}
	/* 对于有生存金抵扣时添加校验，与需求确认，以保单为单位进行生存金计算，某个保单的生存金总额不能大于此保单贷款本息和，续贷相关校验亦同 ----end---- */
	
	
			
	alertMsg.confirm("请确认是否需要保存录入的信息!", {
		okCall : function() {
	    var changeId =$("#changeId", navTab.getCurrentPanel()).val();
	    var acceptId =$("#acceptId", navTab.getCurrentPanel()).val();
	    var customerId =$("#customerId", navTab.getCurrentPanel()).val();
	    var $jsons = $("#policyLoadTable", navTab.getCurrentPanel());
		var _jsons = _cs_tableToJson($jsons);
		_jsons = eval('('+ _jsons + ')');
		_jsons = JSON.stringify(_jsons);
		var  rootPath= getRootPath();
		var json="";
		$.ajax({
					type : 'post',
					dataType : 'html',
					url : rootPath+'/cs/serviceitem_rf/policyLoanPayOffSave_PA_csEndorseRFAction.action',
					cache : false,
					data : "jsonString="+ _jsons+ "&changeId=" + changeId+ "&acceptId=" + acceptId+"&customerId="+customerId,
					success : function(data) {
						if(data.indexOf("{")==0){
							json=jQuery.parseJSON(data);	
							if(json.statusCode!=200){
								alertMsg.confirm(
										json.message+"请确认是否需要保存录入的信息"	,	
								{
									okCall : function() { 
									$.ajax(
									{
										url : rootPath+'/cs/serviceitem_rf/checkMassage_PA_csEndorseRFAction.action',
										type : "post",
										dataType : 'html',
										data : "jsonString="+ _jsons+ "&changeId=" + changeId+ "&acceptId=" + acceptId+"&customerId="+customerId,
										cache : false,
										success : function(data){
											var json = DWZ.jsonEval(data);	
											if (json.statusCode == 300) {
												alertMsg.error(json.message);
											}
											if (json.statusCode==DWZ.statusCode.error){
												if (json.message) alertMsg.error(json.message);
											} else {
												 $("#PolicyLoanPayOffDiv", navTab.getCurrentPanel()).html(data).initUI();
											}
										},
										error : function() {
											alertMsg.error("保存失败！");
										}
									});//ajaxend
									}
									
									
								});//确认继续end 
							}
						}else{
						
						var json = DWZ.jsonEval(data);
						if (json.statusCode == 300) {
							alertMsg.error(json.message);
						}
						if (json.statusCode==DWZ.statusCode.error){
							if (json.message) alertMsg.error(json.message);
						} else {
							 $("#PolicyLoanPayOffDiv", navTab.getCurrentPanel()).html(data).initUI();
						}
						}
					},
					error : function() {
						alertMsg.error("保存失败！");
					}
				});
		},
		cancelCall : function() {
			return false;
			}
	});  
		}
</script>
