<%@ page contentType="text/html; charset=utf-8"%>
<%@ taglib uri="/struts-tags" prefix="s"%><%@include file="/udmpCommon.jsp"%>
<%@ taglib uri="http://www.git.com.cn/taglib/udmp/field" prefix="Field"%>
<%@ include file="/cs/pages/common/jsp/common_ctx.jsp"%>
<script type="text/javascript" src="${ctx}/cs/js/cs_common.js"></script>
<link rel="stylesheet" href="${ctx}/cs/css/cs_common.css" type="text/css">
<link rel="stylesheet" href="${ctx}/cs/css/public.css" type="text/css">
<script type="text/javascript" src="${ctx}/cs/js/DomZoomZX.js"></script>
<script>
	$(document).ready(function(){
        var D = DomZoomZX('input,select,textarea,td')
	})
</script>
<!-- ********帮助菜单***********begin********* -->
<s:include value="/cs/pages/common/jsp/helpMenu.jsp"></s:include>
<!-- *********帮助菜单***********end********* -->
<!-- 步骤标识 -->
<s:include value="csEndorseProgress.jsp" />

<div class="pageFormInfoContent" id="policyBFDetailPanel_rg" layoutH="170">
	<div class="divfclass">
		<h1>
			<img src="${ctx}/cs/img/icon/tubiao.png">生存保险金追回
		</h1>
	</div>
	<div class="pageContent" >


		<!-- 客户资料 -->
		<div class="pageFormInfoContent">
<%-- 			<s:include value="customerInfo.jsp" /> --%>
			<!--客户的5项基本信息（姓名、出生日期、证件类型、证件号码、性别） -->
			<s:include value="customerInfo_list.jsp" />
		</div>
		<div class="main_tabdiv">
			<div class="pageFormInfoContent">
				<div class="divfclass">
					<h1>
						<img src="${ctx}/cs/img/icon/tubiao.png">保单险种列表
					</h1>
				</div>
				<div class="tabdivclass">
					<table class="list" width="100%" id="policyBFDetailTable_rg">
						<thead>
							<tr>
								<th style="display: none" colName="changeId">changeId</th>
								<th style="display: none" colName="acceptId">acceptId</th>
								<th style="display: none" colName="policyId">policyId</th>
								<th style="display: none" colName="policyChgId">policyChgId</th>
								<th style="display: none" colName="busiItemId">busiItemId</th>
								<th style="display: none" colName="liveStatus">liveStatus</th>
								<th style="display: none" colName="deathDate">deathDate</th>
								<th colName="policyCode">保单号</th>
								<th colName="busiProdCode">险种代码</th>
								<th style="display: none" colName="busiPrdId">busiItemId</th>
								<th>险种名称</th>
								<th>生效日期</th>
								<th>交至日期</th>
								<th colName="totalPrem">总保费</th>
							</tr>
						</thead>
						<tbody id="policyBFListbody">
							<s:iterator value="policyBFList" status="st" id="policyBFList">
								<tr align="center" tr_saveStatus='1'>
									<td style="display: none"><s:property value="changeId" /></td>
									<td style="display: none"><s:property value="acceptId" /></td>
									<td style="display: none"><s:property value="policyId" /></td>
									<td style="display: none"><s:property value="policyChgId" /></td>
									<td style="display: none"><s:property value="busiItemId" /></td>
									<td style="display: none" id="td_liveStatus"><s:property value="liveStatus" /></td>
									<td style="display: none" id="td_deathDate"><s:date format="yyyy-MM-dd" name="deathDate" /></td>
									<td id="td_policyCode">${policyCode}</td>
									<td>${busiProdCode}</td>
									<td style="display: none"><s:property value="busiPrdId" /></td>
									<td><Field:codeValue tableName="APP___PAS__DBUSER.T_BUSINESS_PRODUCT" value="${busiPrdId}" /></td>
									<td><s:date format="yyyy-MM-dd" name="validateDate" /></td>
									<td><s:date format="yyyy-MM-dd" name="payDuDate" /></td>
									<td>${totalPrem}</td>
								</tr>
							</s:iterator>
						</tbody>
					</table>
				</div>
				<div class="pageFormInfoContent">
					<s:if test="queryFlag!=1">
						<dl>
							<dt>追回原因</dt>
							<dd>
								<Field:codeTable id="reitrieveCause" name="outputVO.reitrieveCause" nullOption="true" 
									tableName="APP___PAS__DBUSER.T_CS_RETRIEVE_CAUSE" whereClause="" onChange="reitrieveCauseChg('change')"
									defaultValue="1" value="${outputVO.reitrieveCause}"></Field:codeTable>
							</dd>
						</dl>
						<dl>
							<dt>追至日期</dt>
							<dd>
								<input class="date" type="expandDateYMD" id="reitrieveDate" name="outputVO.reitrieveDate"
									value="<s:date name="outputVO.reitrieveDate" format="yyyy-MM-dd"/>" 
									<s:if test="outputVO.reitrieveCause==1"> disabled="disabled"</s:if> />
								<a id="reitrieveDateSelector" class="inputDateButton" href="javascript:;" 
									<s:if test="outputVO.reitrieveCause==1"> disabled="disabled"</s:if>>选择</a>
							</dd>
						</dl>
					</s:if>
					<s:else>
						<dl>
							<dt>追回原因 </dt>
							<dd>
								<input disabled="disabled" value="<Field:codeValue tableName="APP___PAS__DBUSER.T_CS_RETRIEVE_CAUSE" value="${outputVO.reitrieveCause}" />" />
							</dd>
						</dl>
						<dl>
							<dt>追至日期</dt>
							<dd>
								<input type="text" disabled="disabled" value="<s:date name="outputVO.reitrieveDate" format="yyyy-MM-dd"/>" />
							</dd>
						</dl>
					</s:else>
				</div>
				<s:if test="queryFlag!=1">
				<div class="pageFormdiv">
					<button class="but_blue" type="button" onclick="policyReitrieveSave();">保存</button>
				</div>
				<div class="pageFormdiv">
					<button class="but_blue" type="button" onclick="survivalBenefitInformation();">生存给付信息查询</button>
				</div>
				</s:if>
			</div>
		</div>

		<div class="main_tabdiv">
			<div id="policyAFListDiv" class="pageFormInfoContent">
				<s:include value="CsEndorseRG_after.jsp"></s:include>
			</div>
		</div>
	</div>
</div>
<s:if test="queryFlag!=1">
		<s:include value="/cs/pages/serviceitem/serviceItemBottomBar.jsp"></s:include>
</s:if>
<script>
$(document).ready(function(){
	reitrieveCauseChg("init");
});
function setProductName(tdObj, pname) {
	var obj = document.createElement("Field:codeValue");
	obj.setAttribute("tableName","T_BUSINESS_PRODUCT");
	obj.setAttribute("value",pname);
	tdObj.appendChild(obj);
}
//保存 4454
function policyReitrieveSave() {
	var line = $("#policyBFListbody").find("tr").size();
	var policyCodes = "";
	//追回原因
	var reitrieveCause = $("#reitrieveCause", navTab.getCurrentPanel()).val();
	//追至日期
	var  reitrieveDate = $("#reitrieveDate", navTab.getCurrentPanel()).val();
	for(var i= 0;i < line;i++){
		//被保人生存状态
		var liveStatus = $("#policyBFListbody", navTab.getCurrentPanel()).find("tr:eq("+i+")").find("td[id='td_liveStatus']").html();
		//保单号
		var policyCode = $("#policyBFListbody", navTab.getCurrentPanel()).find("tr:eq("+i+")").find("td[id='td_policyCode']").html();
		if(!reitrieveCause){
			alertMsg.info("请选择追回原因");
			return;
		}
		if(!reitrieveDate){
			alertMsg.info("请选择追至日期");
			return;
		}
		if(liveStatus==2){
			if (policyCodes.indexOf(policyCode) < 0) {
				if (policyCodes == "") {
					policyCodes = policyCode;
				} else {
					policyCodes = policyCodes + "," + policyCode;
				}
			}
		}
	}
	var alertStr = "";
	if(policyCodes != ""){
		alertStr = "保单号为"+policyCodes+"的被保人生存状态为\"死亡\"，请确认是否需要保存录入的信息!";
	}else{
		alertStr = "请确认是否需要保存录入的信息!"
	}
	alertMsg.confirm(alertStr, {
		okCall : function(){			
			var jsons_table = $("#policyBFDetailTable_rg");
			var _tableJs = _cs_tableToJson(jsons_table);
			if (_tableJs == null || _tableJs == '') {
				alertMsg.info("没有保存的保单信息，请核对后保存！");
				return false;
			}
			var $panel = $("#policyBFDetailPanel_rg", navTab.getCurrentPanel());
			$("#jsonString", $panel).val(_tableJs);
			var $box = $("#policyAFListDiv", navTab.getCurrentPanel());
			var $form = $("#retriveInfoForm", navTab.getCurrentPanel());
			//$form.submit();
			function callback() {
				$box.find("[layoutH]").layoutH();
			}
			
			$.ajax({
				type : $form.attr("method") || 'POST',
				url : $form.attr("action")+"?outputVO.reitrieveCause="+reitrieveCause+"&outputVO.reitrieveDate="+reitrieveDate,
				data : $form.serializeArray(),
		 		async: false, 
				success : function(response) {
					var json = DWZ.jsonEval(response);
					if (json.statusCode == DWZ.statusCode.error) {
						if (json.message && alertMsg){
							alertMsg.error(json.message);
							return ;
						}
							
					} else if (json.statusCode == DWZ.statusCode.timeout) {
						// if(alertMsg) alertMsg.error(json.message ||
						// DWZ.msg("sessionTimout"), {okCall:DWZ.loadLogin});
						// else
						// 注掉提示信息 ，直接弹出登录窗口 Liandong
						DWZ.loadLogin();
					} else {
						// 增加session过期处理 LiAnDong
						if (response.indexOf("\"statusCode\":\"301\"") > 0) {
							// $this.html(json.message).initUI();
						} else {
							$box.html(response).initUI();
							alertMsg.info("保存成功！");
							// $box.find("")
						}
						if ($.isFunction(callback))
							callback(response);
					}
				},
				error : DWZ.ajaxError
			});
			
		}
	});
}
function reitrieveCauseChg(type) {
	var line = $("#policyBFListbody", navTab.getCurrentPanel()).find("tr").size();
	//追回原因
	var reitrieveCause = $("#reitrieveCause", navTab.getCurrentPanel()).val()||"";
	//追至日期
	var  reitrieveDate = $("#reitrieveDate", navTab.getCurrentPanel()).val();
	for (var i= 0;i < line;i++) {
		if (reitrieveCause == "1") {
			//被保人生存状态
			var _liveStatus = $("#policyBFListbody", navTab.getCurrentPanel()).find("tr:eq("+i+")").find("td[id='td_liveStatus']").text();
			var _deathDate = $("#policyBFListbody", navTab.getCurrentPanel()).find("tr:eq("+i+")").find("td[id='td_deathDate']").text();
			if (type != null && type == "change" && _liveStatus == "1") {//1生存，2死亡
				alertMsg.info("被保险人未死亡，请确认被保险人状态！");
				return;
			} else {
				if (_deathDate != null && _deathDate != "null" && _deathDate != "") {
					$("#reitrieveDate", navTab.getCurrentPanel()).val(_deathDate);
					$("#reitrieveDate", navTab.getCurrentPanel()).attr("disabled", "true");
					$("#reitrieveDateSelector", navTab.getCurrentPanel()).attr("disabled", "true");
				}
			}
		} else {
			$("#reitrieveDate", navTab.getCurrentPanel()).removeAttr("disabled");
			$("#reitrieveDateSelector", navTab.getCurrentPanel()).removeAttr("disabled");
		}
	}
}
</script>
