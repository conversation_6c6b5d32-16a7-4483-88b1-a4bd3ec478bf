<%@ page language="java" contentType="text/html; charset=utf-8"
	pageEncoding="utf-8"%>
<%@ taglib uri="/struts-tags" prefix="s"%><%@include file="/udmpCommon.jsp"%>
<%@ taglib uri="http://www.git.com.cn/taglib/udmp/field" prefix="Field"%>
<%@ include file="/cs/pages/common/jsp/common_ctx.jsp" %>
<script type="text/javascript" src="${ctx}/cs/js/cs_common.js"></script>
<link rel="stylesheet" href="${ctx }/cs/css/cs_common.css" type="text/css">
<link rel="stylesheet" href="${ctx }/cs/css/public.css" type="text/css">
<script type="text/javascript" src="${ctx}/cs/js/DomZoomZX.js"></script>
<script>
	$(document).ready(function(){
        var D = DomZoomZX('input,select,textarea,td')
	})
</script>
<script>
	$(function(){
		var readOnly = $("#newPayMode").find("option:selected").attr("isReadOnly");
		//设置有只读属性的险种为只读
		if(readOnly!=null ){
			if(1==readOnly){
				$("select[name='newPayMode']").attr("disabled",true);
			}
		}
		//add by huangcc_wb 20160812 start
		setPaymentType();
		//end huangcc_wb
	});
	
	//add by huangcc_wb 20160812 start
	function setPaymentType(){
		
		//如果是附加险下拉列表同主险 则设置为只读
		$("select[name='newPayMode']").each(function(){
			if(null!=$(this).attr("defaultValueOrigin")&&2==$(this).attr("defaultValueOrigin")){
				$(this).attr("disabled",true);
			}
		});
		
		//如果是附加险下拉列表同主险 则设置为只读
		$("select[name='newPayTerm']").each(function(){
			
			if(null!=$(this).attr("defaultValueOrigin")&&2==$(this).attr("defaultValueOrigin")){
				$(this).attr("disabled",true);
			}
		});
		//end huangcc_wb
	}
	
	function PayModeChangeBack() {
		alertMsg.confirm("请确认是否需要保存录入的信息？", {
			okCall : function() {
				$("#payModeChangeForm").submit();
			}
		});
	}

	//保存变更后交费方式及期限
	function payModeChangeSave() {
		
		var changeId = $("#changeId",navTab.getCurrentPanel()).val();
		var acceptId = $("#acceptId", navTab.getCurrentPanel()).val();
		var customerId = $("#customerId", navTab.getCurrentPanel()).val();
	    var $dataTable = $("#payModeInfoTable", navTab.getCurrentPanel());
		var $trs = $dataTable.find("tbody tr");

		var flage=false;		//主险和附加险变更标示  均未变更
		for(var i=0;i<$trs.length;i++){		
			
			//初始交费方式和交费期限
			var initialPayMode = $($trs[i]).find("td:eq(19)").text();		/**lizhao_wb  根据需求添加td：附属险种 取值+1**/
			var initialPayTerm = $($trs[i]).find("td:eq(20)").text();
			//新的交费方式和交费期限
			var newPayMode = $($trs[i]).find("td:eq(16)").find('select').val();	
			var newPayTerm;
			var policyYear = $($trs[i]).find("td:eq(21)").text();
// 			alert($($trs[i]).find("td:eq(17)").next().val());
			if ($($trs[i]).find("td:eq(4)").text() == "00909000" || $($trs[i]).find("td:eq(4)").text() == "00907000" ) {
				newPayTerm = $($trs[i]).find("td:eq(17)").find("input[name=newPayTerm]").val();
				$($trs[i]).find("td:eq(17)").next().text(newPayTerm);
			} else {
				newPayTerm = $($trs[i]).find("td:eq(17)").find('select').val();		
			}
			
			console.info(" ---   "+newPayTerm);
			var beforChangePeriodText = $($trs[i]).find("td select[name='newPayMode']").find("option:selected").text();
		 	$($trs[i]).find("td[name='beforChangePeriod']").text(beforChangePeriodText);
			
			
		 	if(newPayTerm == "" && newPayMode == "" ){
				alertMsg.error("必填项信息未完整录入，请确认。");
		    	return false;
			}
			
			/*		if(newPayMode == "" && newPayTerm== ""){
			alertMsg.info("必填项信息未完整录入，请确认。");
		    return false;
		} */
			
			if(initialPayMode == newPayMode && initialPayTerm == newPayTerm){
				flage=flage||false;
			}
			else{
				flage=flage||true;
			}
			debugger;
			//交费方式相同时，比较交费期限大小		by lizhao_wb
			/*alert("initialPayMode变更前缴费方式:"+initialPayMode);
			alert("initialPayTerm: 变更前交费期限"+initialPayTerm);
			alert("newPayMode: 	        变更后缴费方式"+newPayMode);
			alert("newPayTerm：	        变更后交费期限"+newPayTerm);
			alert("policyYear ：已缴保费年数"+policyYear);*/
			debugger;
			if(newPayMode == ""){				
				if(newPayTerm != "" && newPayTerm == initialPayTerm){// 变更后缴费方式未选择，且变更后的缴费期限与原来一直，应该阻断。
					alertMsg.error("交费方式和交费期限均未变更，请确认");
					return false;
				}else if(newPayTerm != "" && newPayTerm != initialPayTerm){
					// 如果不进行变更的话，就默认为原来的缴费方式initialPayMode。
					newPayMode = initialPayMode;
				}			
			}
			if(newPayTerm == ""){
				if(newPayMode != "" && newPayMode == initialPayMode){
					alertMsg.error("交费方式和交费期限均未变更，请确认");
					return false;
				}else if(newPayMode != "" && newPayMode != initialPayMode){
					// 如果不进行变更的话，就默认为原来的交费期限initialPayTerm。
					newPayTerm = initialPayTerm;
				}				
			}				

			debugger;
			if(initialPayMode == newPayMode){//变更前后缴费方式相同					
				if(newPayMode == '5'){// 变更后的缴费方式，如果是年交。
					debugger;
					if(parseFloat(newPayTerm) <= parseFloat(policyYear)){
						alertMsg.error("变更后的交费期限不能小于变更前已缴保费年数，请确认。");
						return false;
					}
				}else if(newPayMode == '2'){// 变更后的缴费方式，月缴					
					// var sum = newPayTerm/12;						
					if(parseFloat(newPayTerm) <= parseFloat(policyYear)){
						alertMsg.error("变更后的交费期限不能小于变更前已缴保费年数，请确认。");
						return false;
					}
				}else if(newPayMode == '1'){
					alertMsg.error("变更后的交费期限不能小于变更前已缴保费年数，请确认。");
					return false;
				}
			
			}			
			// 5.变更后的交费期限>已缴保费年数
			if(initialPayMode != newPayMode){//变更前后缴费方式不相同
				if(newPayMode == '5'){// 变更后的缴费方式，如果是年交。
					debugger;
					if(parseFloat(newPayTerm) <= parseFloat(policyYear)){
						alertMsg.error("变更后的交费期限不能小于变更前已缴保费年数，请确认。");
						return false;
					}
				}else if(newPayMode == '2'){// 变更后的缴费方式，月缴					
					// var sum = newPayTerm/12;	
					if(parseFloat(newPayTerm) <= parseFloat(policyYear)){
						alertMsg.error("变更后的交费期限不能小于变更前已缴保费年数，请确认。");
						return false;
					}
				}else if(newPayMode == '1'){
					alertMsg.error("变更后的交费期限不能小于变更前已缴保费年数，请确认。");
					return false;
				}			
			}
		}
		if(!flage){
			alertMsg.info("交费方式和交费期限均未变更，请确认。");			
			return false;
		}
		//重新赋值
		for(var i=0;i<$trs.length;i++){
	

			$($trs[i]).find("td:eq(18)").text($($trs[i]).find("td:eq(17)").find('select').val());		/**lizhao_wb  根据需求添加td：附属险种 取值+1**/
			
			/*var newPayTerm = $($trs[i]).find("td:eq(16) input").val();
			if (newPayTerm!=null && newPayTerm!="undefined") {
				
				$($trs[i]).find("td:eq(17)").text(newPayTerm);
			} */
			
			/*else {
				
				newPayTerm = $($trs[i]).find("td:eq(17) input").val();
				alert("交费期限"+newPayTerm);
				$($trs[i]).find("td:eq(17)").text(newPayTerm);
			} */
		}
		
		var _table = $("#payModeInfoTable", navTab.getCurrentPanel());
		var _tableJs = _cs_tableToJson(_table);
// 		alert($("#payModeInfoTable").html());
// 		alert(_tableJs);
		var _tableJsObj = DWZ.jsonEval(_tableJs);
		
			$.ajax({
				type : "post",
				url : "${ctx}/cs/serviceitem_fm/savePayModeChange_PA_csEndorseFMAction.action?acceptId="
						+ acceptId
						+ "&changeId="
						+ changeId
						+ "&customerId=" + customerId,
				data : "payModeChangeTableJson=" + _tableJs,
				success : function(data) {
					var json = DWZ.jsonEval(data);
					if (json.statusCode == DWZ.statusCode.error) {
						alertMsg.error(json.message);
					} else if (json.statusCode == DWZ.statusCode.timeout) {
						DWZ.loadLogin();
					}else{
						alertMsg.correct("操作成功！");
						$("#payModeChangeForm", navTab.getCurrentPanel()).submit();
					}
				},
				error: function(){}
			});
		/* } */
	}
	//取出select里面的值 然后赋值给input 框
	function changeValue(obj){
		var riskCode = $(obj).parents('tr').find("td[id='tdId']").text();

		//$("#newPayTermId"+riskCode).val(obj.value);
		$("#newPayTermId"+riskCode).text(obj.value);
		
		//add by huangcc_wb 20160812 start 设置缴费期间附加险随主险变动
		$("select[name='newPayTerm']").each(function(){
			//defaultValueOrigin = 2 为同主险
			if(null!=$(this).attr("defaultValueOrigin")&&2==$(this).attr("defaultValueOrigin")){
				
				var newPayTermVal = $(obj).find("option:selected").val();
				var newPayTermText = $(obj).find("option:selected").text();
				$(this).find("option:selected").val(newPayTermVal);
				$(this).find("option:selected").text(newPayTermText);
				
			}
		});
		//end huangcc_wb
	}
	
	
	function changeInputValue(obj){
		var payTerm = document.getElementById("payTerm").value;
		//把这个值赋值给 id 为newPayTermId 的input框
		$("#newPayTermId").attr('value',payTerm);
	}
	
	function onchangeCoverageType(selectValue){
		var newPayTermSelected = "";
		var newPayMode=$(selectValue).val();
// 		var riskCode = $(selectValue).parents('tr').find("td[id='tdId']").text();
		var riskCode = $(selectValue).parent().parent().find("td[id='tdId']").text();
		
		//对于197000险种的特殊处理，由于不允许变更交费期限，下面两个值用于获取初始的交费期限值和初始的交费期限名称  --开始  wwx 20161215 
	    var initialPayTermName = $(selectValue).parents('tr').find("td[id='initialPayTermName']").text();
		var initialPayTermValue = $(selectValue).parents('tr').children().last().prev().text();
		// --结束  wwx 20161215 
		
		$("#newPayModeId"+riskCode).val($(selectValue).val());
		$.ajax({
			type : "post",
			url : "${ctx}/cs/serviceitem_fm/queryNewPayTermByMode_PA_csEndorseFMAction.action",
			dataType:'text',
			data :"newPayMode=" + $(selectValue).val()+"&elementId="+$(selectValue).parents('td').find('input[name = elementId]').val()+"&relationId="+$(selectValue).parents('td').find('input[name = relationId]').val(),
			success : function(data) {
				//$("#newPayModeId").val(selectValue);
				var payTerms = jQuery.parseJSON(data);
				//当没有查到交费方式变更关联的 交费期限 的动作记录时，交费期限列表不变化
				if(payTerms == null || payTerms.length == 0){
					return;	
				}
// 				$("#newPayTerm").empty();
// 				alert($(selectValue).parents('td').next().html());
				$(selectValue).parents('td').next().children().empty();
				var readonly=false;
				$.each(payTerms,function(i,value){
					if(value.isReadOnly == '0' && i == 0){
						//$("<option value=''>请选择</option>").appendTo($("#newPayTerm"));
						$("<option value=''>请选择</option>").appendTo($(selectValue).parents('td').next().find('select'));
						$("#newPayTermId"+riskCode).text(value.code);
						
					}
					//isReadOnly=1 只读
					if(value.isReadOnly == '1' && i == 0){
						
						$("#newPayTermId"+riskCode).text(value.code);
						readonly=true;
					}	
					
					// 变更后的缴费期限不能超过变更前的缴费期限
// 					alert(typeof($(selectValue).parent().parent().children().last().text())+"  "+value.code+"   "+typeof(value.code));
					
					if (parseInt($(selectValue).parents('tr').children().eq(-2).prev().text()) > parseInt($.trim(value.code))) {
// 						alert($(selectValue).parents('tr').children().last().html());
						$("<option value='"+value.code+"'>"+value.code2+"</option>").appendTo($(selectValue).parents('td').next().find('select'));
						
					}
					//$("<option value='"+value.code+"'>"+value.code2+"</option>").appendTo($("#newPayTerm"));
// 					$("<option value='"+value.code+"'>"+value.code2+"</option>").appendTo($(selectValue).parent().next().children());
					
					//add by huangcc_wb 20160812 start 设置缴费期间的默认属性
					if(value.prdElementValue!=null&&value.prdElementValue!=""&&value.code==value.prdElementValue){
					
						$("select[name='newPayTerm']").eq(0).find("option[value='"+value.prdElementValue+"']").attr("selected",true);
						newPayTermSelected = $("select[name='newPayTerm']").eq(0).find("option:selected").val();
						if(newPayTermSelected!=null&&newPayTermSelected!=""){
							$("select[name='newPayTerm']").each(function(){
									
									//设置附加险同主险的交费期间
									if($(this).attr("defaultValueOrigin")!=null&&$(this).attr("defaultValueOrigin")==2){
										$(this).children().empty();
										$("<option value='"+value.code+"'>"+value.code2+"</option>").appendTo($(this));
										$(this).find("option[value='"+newPayTermSelected+"']").attr("selected",true);
									}
							});
						}
					}//end huangcc_wb
				});
// 				alert("select   >>>  "+$(selectValue).parents('td').next().html());
				
				if(readonly){  
					//$("#newPayTerm").attr("disabled","disabled");
					
					$(selectValue).parents('td').next().children().attr("disabled","disabled");
				}else{
					//$("#newPayTerm").removeAttr("disabled");
					$(selectValue).parents('td').next().children().removeAttr("disabled");
				}
				
				
				//如果是19700险种，则做一些特殊的处理（交费期限不允许变更），并且是在交费方位不是一次交清的情况下，进行下面的操作，一次交清的按上面共用的处理方式即可 --开始  wwx 20161215 
			   	if(riskCode=='00197000'&& newPayMode!='1'){
					//清空已有的交费期限 
					$(selectValue).parents('td').next().children().empty();
					//将交费期限置为与原交费期限一致 
					$("<option value='"+ initialPayTermValue +"'>"+initialPayTermName+"</option>").appendTo($(selectValue).parents('td').next().children());
					//将新的交费期限置为选中状态
					$(selectValue).parents('td').next().children().find("option[value='"+initialPayTermValue+"']").attr("selected",true);
					//此处的赋值操作，用于保存操作的时候判断是否进行了选择交费方式或交费期限中的一种的校验判断
					$("#newPayTermId"+riskCode).text(initialPayTermName); 
					//由于不允许变更交费期限，将交费期限隐藏
					$(selectValue).parents('td').next().children().attr("disabled","disabled");
				  	//将所有附加险的交费期间置为与主险一致
					$("select[name='newPayTerm']").each(function(){
						if($(this).attr("defaultValueOrigin")!=null&&$(this).attr("defaultValueOrigin")==2){ //值为2表示'同主险'
							$(this).children().empty();
							$("<option value='"+initialPayTermValue+"'>"+initialPayTermName+"</option>").appendTo($(this));
							$(this).find("option[value='"+initialPayTermValue+"']").attr("selected",true);
						}
				    });   
				} 
				//  --结束  wwx 20161215 
			}
		});
		//add by huangcc_wb 20160812 start 设置附加险如果同主险的缴费方式 主险的缴费方式变动附加险的缴费方式也跟着变动
		$("select[name='newPayMode']").each(function(){
			if(null!=$(this).attr("defaultValueOrigin")&&2==$(this).attr("defaultValueOrigin")){
				var newPayModeVal = $(selectValue).find("option:selected").val();
				var newPayModeText = $(selectValue).find("option:selected").text();
				//设置附加险的缴费方式同主险
				$(this).find("option:selected").val(newPayModeVal);
				$(this).find("option:selected").text(newPayModeText);
				//
				 //$("th[colName='newPayTerm']").attr("inputType","select");
			}
		});
		
		
		
		//end huangcc_wb
		
	}
</script> 
<!-- ********帮助菜单***********begin********* -->
<s:include value="/cs/pages/common/jsp/helpMenu.jsp"></s:include>
<!-- *********帮助菜单***********end********* -->
<s:include value="csEndorseProgress.jsp" />


<div class = "backgroundCollor" layoutH="140">
	
	<form id="payModeChangeForm"
		action="${ctx}/cs/serviceitem_fm/showAfFM_PA_csEndorseFMAction.action"
		class="pageForm required-validate" method="post"
		onsubmit="return navTabSearch(this,'showAFPage')">
		<input type="hidden" name="changeId" id="changeId"
				value="${changeId}" /> <input type="hidden" name="acceptId"
				id="acceptId" value="${acceptId}" /> <input type="hidden"
				name="policyChgId" id="policyChgId" /><input type="hidden"
				name="customerId" id="customerId" value="${customerId}" />
	</form>
		<div class="divfclass">
			<h1>
				<img src="images/tubiao.png">交费方式及期限变更 
			</h1>
		</div>
		<s:include value="customerInfo_list.jsp" />
		<div>
			<div class="divfclass">
				<h1><img src="cs/img/icon/tubiao.png">变更前信息</h1>
			</div>
			<div class="tabdivclass">
				<table class="list" width="100%" id="payModeInfoTable"
					table_saveStatus="1">
					<thead>
						<tr>
							<th>保单号</th>
							<th style="display: none;" colName="policyId">policyId</th>
							<th style="display: none;" colName="policyChgId">policyChgId</th>
							<th colName="productId" >险种序号</th>
							<th>险种代码</th>
							<th>险种名称</th>
							<th>附属险种</th>
							<th>保额</th>
							<th>保费</th>
							<th>险种状态</th>
							<th>交费状态</th>
							<th>生效日期</th>
							<th>下次交费日</th>
							<th>周年日</th>
							<th>交费方式</th>
							<th>交费期限</th>
							<th colName="newPayMode" inputType="select">新交费方式</th>
							<!-- MODIFY BY huangcc 此处inputType="select"为input后台拿不到 -->
							<th colName="newPayTerm" inputType="select>input">新交费期限</th>
							<th style="display: none;" >变更后期限</th>
							<th style="display: none;" colName="payMode">变更前方式</th>
							<th style="display: none;" colName="payTerm">变更前期限</th>	
							<th style="display: none;" colName="policyYear">保单年度</th>		
							<th style="display: none;" colName="beforChangePeriod">变更前类型</th>								
						</tr>
					</thead>
					<tbody id="allInfoContent">
						<s:iterator value="payModeChangeBFVOs" status="st" id="BFList" var="payModeChangeBFVO">
							<tr align="center" tr_saveStatus='1'>
								<td>${policyCode}</td>
								<td style="display: none;">${policyId}</td>
								<td style="display: none;">${policyChgId}</td>
								<td>${productId}</td>
								<td id="tdId">${productItem}</td>
								<td><Field:codeValue
									tableName="APP___PAS__DBUSER.T_BUSINESS_PRODUCT" value="${productName}" /></td>
								<td>${subProduct}</td>
								<td>${productCost}</td>
								<td>${productFee}</td>
								<td><Field:codeValue tableName="APP___PAS__DBUSER.T_LIABILITY_STATUS"
										value="${prodStatus}" /></td>

								<td><Field:codeValue
									tableName="APP___PAS__DBUSER.T_PREM_STATUS" value="${premiumStatus}" /></td>
								<td><s:date name="validateDay" format="yyyy-MM-dd" /></td>
								<td><s:date name="nextPayDay" format="yyyy-MM-dd" /></td>
								<td><s:date name="anniversary" format="yyyy-MM-dd" /></td>
								<td><Field:codeValue tableName="APP___PAS__DBUSER.T_CHARGE_MODE"
										value="${payMode}" /></td>
									<td id = "initialPayTermName">${payTermName}</td>	
										
								<td>
										
										<s:if test="#payModeChangeBFVO.payCoverPeriod.size()>0" >
											<select name="newPayMode" id="newPayMode"  onchange="onchangeCoverageType(this)" defaultValueOrigin="${defaultValueOrigin}">
												<option value="">请选择 </option>
												<s:iterator value="#payModeChangeBFVO.payCoverPeriod" status="st" var="payMode">
													<s:if test="code==defaultValue&&defaultValue!=null">
															<option isReadOnly="${isReadOnly}" defaultValue="${defaultValue}" value="${code}" selected="selected">${code2}</option>
													</s:if>
													<s:else>
															<option isReadOnly="${isReadOnly}" defaultValue="${defaultValue}" value="${code}">${code2}</option>
													</s:else>
											</s:iterator>
											</select>
											<!-- modify by huangcc_wb 20160809 end -->
										</s:if>
										<input type="hidden" value="" id="newPayModeId${productItem}" name="newPayModeId">
										<input type="hidden" value="${relationId}" id="relationId${productItem}" name="relationId">
										<input type="hidden" value="${elementId}" id="elementId${productItem}" name="elementId">
										
								</td>
								<td>
									 <s:if test="#payModeChangeBFVO.payTerms.size()>0 || #payModeChangeBFVO.defaultValueOrigin==2" >
										 <s:if test="#payModeChangeBFVO.productItem=='00909000'">
										 <input type="text" name="newPayTerm" id="newPayTerm${productItem}" Value="">
										 </s:if>
										 <s:else>
											<s:select name="newPayTerm" id="newPayTerm" onchange="changeValue(this)"  list="#payModeChangeBFVO.payTerms" listValue="code2" listKey="code" headerKey="" headerValue="请选择" defaultValueOrigin="${defaultValueOrigin}">											
											</s:select>
										</s:else>
									 </s:if>										
									 <s:else>
									 	<input type="text" name="newPayTerm" id="newPayTerm" size="10" value="" />
									 </s:else> 
								</td>
								<td style="display: none;" id="newPayTermId${productItem}" name="newPayTerm"></td>	
								<td style="display: none;">${payMode}</td>
								<td style="display: none;">${payTerm}</td>
								<td style="display: none;">${policyYear}</td>	
								<td tyle="display: none;" name="beforChangePeriod" style="display: none;" value=""></td>	
							</tr>
						</s:iterator>
					</tbody>
				</table>
				</div>
				 				
				<div class="pageFormdiv">
					<s:if test="queryFlag!=1"> 
						<button type="button" class="but_blue"  onclick="payModeChangeSave();">保存</button>
					</s:if>
			    </div>
				 
	
		</div>
		
		<div id="showAFPage">
			<s:if test="payModeChangeAFVOs != null" >
				<s:include value="/cs/pages/serviceitem/CsEndorseFM_query.jsp"></s:include>
			</s:if> 
		</div>
		
	<s:if test="queryFlag != '1' " >
		<s:include value="/cs/pages/serviceitem/serviceItemBottomBar.jsp"></s:include>
	</s:if>
		
</div>



	
