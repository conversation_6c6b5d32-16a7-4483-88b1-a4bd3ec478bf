<!-- 万能险基本保额约定变更页面 -->
<%@ page contentType="text/html; charset=utf-8"%>
<%@ taglib uri="/struts-tags" prefix="s"%><%@include
	file="/udmpCommon.jsp"%>
<%@ taglib uri="http://www.git.com.cn/taglib/udmp/field" prefix="Field"%>
<%@ include file="/cs/pages/common/jsp/common_ctx.jsp"%>
<script type="text/javascript" src="${ctx}/cs/js/cs_common.js"></script>
<link rel="stylesheet" href="${ctx }/cs/css/cs_common.css"
	type="text/css">
<link rel="stylesheet" href="${ctx}/cs/css/public.css" type="text/css">

<script type="text/javascript" src="${ctx}/cs/js/query_advice.js"></script>
<script type="text/javascript" src="${ctx}/cs/js/policyPrint.js"></script>
<style type="text/css">
th,td {
	white-space: normal;
}

.searchBar li label {
	width: 60px;
}
</style>
<script type="text/javascript" src="${ctx}/cs/js/DomZoomZX.js"></script>
<script>
	$(document).ready(function(){
        var D = DomZoomZX('input,select,textarea,td')
	})
</script>
<form id="queryForm"
	action="${ctx}/cs/serviceitem_dc/saveDC_PA_csEndorseDCAction.action"
	onsubmit="return divSearch(this,'addDiv')" method="post">
	<input type="hidden" name="jsonMsg" /> <input type="hidden"
		name="customerId" value="${customerId}" id="customerId" /> <input
		type="hidden" id="changeId" value="${changeId}" /> <input
		type="hidden" id="acceptId" value="${acceptId}" /> <input
		type="hidden" id="policyCode" value="${policyCode}" />
</form>
<!-- 帮助菜单-->
<s:include value="/cs/pages/common/jsp/helpMenu.jsp" />

<!-- 步骤标识 -->
<s:include value="csEndorseProgress.jsp" />

<div onmousedown="MM_changeProp('holderInfoCmDiv','display','none')"
	style="background: #fafafa">
	<div class="" layoutH="90">

		<!--第三个页签-->
		<div class="pageFormInfoContent">
			<div class="divfclass">
				<h1>
					<img src="${ctx}/cs/img/icon/tubiao.png">万能险基本保额约定变更
				</h1>
			</div>
			<div class="pageContent" layoutH="150">
				<!-- 客户信息 -->
				<div class="pageFormInfoContent">
					<s:include value="customerInfo_list.jsp" />
				</div>
				<%-- <div class="panel" style="display: none">
							<h1>原投保人信息</h1>
							<div>
								<ul class="searchContent" style="height: auto;">
									<li class="nowrap"><label>客户姓名</label> <input type="text"
										value="${csCustomerVO.customerName}" readonly="readonly" /></li>
									<li class="nowrap"><label>出生日期</label> <input type="text"
										value="<s:date format="yyyy-MM-dd" name="csCustomerVO.customerBirthday"></s:date>"
										readonly="readonly" /></li>
								</ul>
								<ul class="searchContent" style="height: auto;">
									<li class="nowrap"><label>证件类型</label> <input
										readonly="readonly" type="text"
										value="<Field:codeValue tableName="APP___PAS__DBUSER.T_CERTI_TYPE" value="${csCustomerVO.customerCertType}"/>" /></li>
									<li class="nowrap"><label>证件号码</label> <input type="text"
										value="${csCustomerVO.customerCertiCode}" readonly="readonly" /></li>
									<li class="nowrap"><label style="width: auto;">性别&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
											<input type="text"
											value="<Field:codeValue tableName="APP___PAS__DBUSER.T_GENDER" value="${csCustomerVO.customerGender}"/>"
											readonly="readonly">
									</label></li>
								</ul>
							</div>
						</div> --%>
				<div class="main_tabdiv">
					<div class="pageFormInfoContent">
						<div class="divfclass">
							<h1>
								<img src="${ctx}/cs/img/icon/tubiao.png"">变更前信息
							</h1>
						</div>
						<div class="tabdivclass">
							<table class="list" id="dc2" width="100%" table_saveStatus="1">
								<thead>
									<tr>
										<th style="display: none;" colName="itemId">责任组ID</th>
										<th colName="policyCode">保单号</th>
										<th>险种代码</th>
										<th>险种名称</th>
										<th colName="amount">保额</th>
										<th colName="prem">保费</th>
										<th>下期缴费日</th>
										<th>下一保单周年日</th>
										<th>保单年度</th>
										<th>险种状态</th>
										<th colName="newAmount" inputType="input" style="">变更后保额</th>
										<th colName="age" inputType="input">约定年龄</th>
										<th style="display: none;" colName="changeId">保全变更Id</th>
										<th style="display: none;" colName="policyChgId">保单变更Id</th>
										<th style="display: none;" colName="policyId">保单Id</th>
										<th style="display: none;" colName="policyDate"
											dateType="s:date">保单生效日期</th>
										<th style="display: none;" colName="busiItemId">险种ID</th>
										<th style="display: none;" colName="acceptId">受理id</th>
										<th style="display: none;" colName="productType">险种类型</th>
									</tr>
								</thead>
								<tbody>
									<s:iterator value="csEndorseDCVOs">
										<tr align="center" tr_saveStatus="1">
											<td style="display: none;">${itemId}</td>
											<td>${policyCode}</td>
											<td>${busiPrdCode}</td>
											<td><Field:codeValue
													tableName="APP___PAS__DBUSER.T_BUSINESS_PRODUCT"
													value="${busiPrdId}" /></td>
											<td>${amount}</td>
											<td>${prem}</td>
											<td><s:date format="yyyy-MM-dd" name="payDueDate"></s:date></td>
											<td><s:date format="yyyy-MM-dd" name="nextPolicyDate"></s:date></td>
											<td>${policyAge}</td>
											<td><Field:codeValue
													tableName="APP___PAS__DBUSER.T_LIABILITY_STATUS"
													value="${liabiltyState}" /></td>
											<s:if test="productType==20003">
												<td><div>
														<div style="float: left;">
															<input size="10" class="form-control" minlength="0"
																maxlength="10" type="text" name="newAmount"
																id="newAmount" value="${newAmount}"
																<s:if test="queryFlag==1">disabled="disabled"</s:if> />
														</div>
														<%-- <div style="float: left;">
													<span style="color: red;">*</span>
												</div> --%>
													</div></td>
												<td><div>
														<div style="float: left;">
															<input size="3" class="number" minlength="0"
																maxlength="3" type="text" name="age" id="age"
																value="${age}" onchange="checkAmount(this);"
																<s:if test="queryFlag==1">disabled="disabled"</s:if> />
														</div>
														<%-- <div style="float: left;">
													<span style="color: red;">*</span>
												</div> --%>
													</div></td>
											</s:if>
											<s:else>
												<td></td>
												<td></td>
											</s:else>
											<td style="display: none;">${changeId}</td>
											<td style="display: none;">${policyChgId}</td>
											<td style="display: none;">${policyId}</td>
											<td style="display: none;"><s:date format="yyyy-MM-dd"
													name="policyDate"></s:date></td>
											<td style="display: none;">${busiItemId}</td>
											<td style="display: none;">${acceptId}</td>
											<td style="display: none;">${productType}</td>
										</tr>
									</s:iterator>
								</tbody>
							</table>
						</div>
						<br>
						<table style="width: 100%">
							<tbody>
								<tr>
									<td>
										<div style="text-align: center;">
											<button type="button" class="but_blue" id="endorseDCbutton"
												onclick="save()"
												<s:if test="queryFlag==1">disabled="disabled"</s:if>>约定变更基本保额</button>
										</div>
									</td>
								</tr>
							</tbody>
						</table>
					</div>
				</div>
				<div class="main_tabdiv">
					<div class="pageFormInfoContent">
						<div class="divfclass">
							<h1>
								<img src="${ctx}/cs/img/icon/tubiao.png"">约定变更计划
							</h1>
						</div>
						<div class="tabdivclass">
							<table class="list" id="rsTable" width="100%">
								<thead>
									<tr id="recid" align="center">
										<th>选择</th>
										<th>保单号</th>
										<th>险种代码</th>
										<th>险种名称</th>
										<th>保额</th>
										<th>保费</th>
										<th>下期缴费日</th>
										<th>下一保单周年日</th>
										<th>保单年度</th>
										<th>约定保额</th>
										<th>约定变更年龄</th>
										<th>约定变更保单周年日</th>
										<th>状态</th>
										<th style="display: none;">保全变更Id</th>
										<th style="display: none;">保单变更Id</th>
										<th style="display: none;">保单Id</th>
									</tr>
								</thead>
								<tbody>
									<s:iterator value="endorseDCVOs">
										<tr align="center" 
											<s:if test="precontStatus!=0">disabled="disabled"</s:if>>
											<td><input type="checkbox" class="endorseDC"
												value="${itemId}" /></td>
											<td>${policyCode}</td>
											<td>${busiPrdCode}</td>
											<td><Field:codeValue
													tableName="APP___PAS__DBUSER.T_BUSINESS_PRODUCT"
													value="${busiPrdId}" /></td>
											<td>${amount}</td>
											<td>${prem}</td>
											<td><s:date format="yyyy-MM-dd" name="payDueDate"></s:date></td>
											<td><s:date format="yyyy-MM-dd" name="nextPolicyDate"></s:date></td>
											<td>${policyAge}</td>
											<td>${newAmount}</td>
											<td>${age}</td>
											<td><s:date format="yyyy-MM-dd" name="appointDate"></s:date></td>
											<td><Field:codeValue
													tableName="APP___PAS__DBUSER.T_PRECONT_STATUS"
													value="${precontStatus}" /></td>
											<td style="display: none;">${changeId}</td>
											<td style="display: none;">${policyChgId}</td>
											<td style="display: none;">${policyId}</td>
										</tr>
									</s:iterator>
								</tbody>
							</table>
							
							
						</div>
						<br>
						
						<div style="text-align: center;">
							<button type="button" class="but_blue" id="cancelBut" onclick="cancel()"
								<s:if test="queryFlag==1">disabled="disabled"</s:if>>取消约定变更</button>
						</div>
<!-- 						<table style="width: 100%"> -->
<!-- 							<tbody> -->
<!-- 								<tr> -->
<!-- 									<td> -->
<!-- 										<div> -->
<!-- 											<div style="text-align: center;"> -->
<!-- 												<button type="button" class="but_blue" onclick="cancel()" -->
<!-- 													<s:if test="queryFlag==1">disabled="disabled"</s:if>>取消约定变更</button> -->
<!-- 											</div> -->
<!-- 										</div> -->
<!-- 									</td> -->
<!-- 								</tr> -->
<!-- 							</tbody> -->
<!-- 						</table> -->
					</div>
				</div>
				<br>
				<div id="addDiv" class="unitBox"></div>
				<s:if test="(queryFlag==1 || flag==1) && endorseDCVOsAfter.size != 0">
					<div id="faterflag" class="main_tabdiv">
						<s:include value="/cs/pages/serviceitem/CsEndorseDC_query.jsp"></s:include>
					</div>
				</s:if>
				<s:include value="/cs/pages/serviceitem/serviceItemBottomBar.jsp"></s:include>
				</div>

<!-- 				<div id="serviceItemBottomBar"> -->
					
<!-- 				</div> -->
<!-- 				<div class="formBar" id="bottomBar"> -->
<!-- 					<ul> -->
<!-- 						<li <s:if test="queryFlag==1">style="display:none"</s:if>> -->
<!-- 							<div> -->
<!-- 								<div> -->
								
<!-- 									<a class="but_blue main_buta" -->
<%-- 										href="${ctx}/cs/csEntry/showAcceptAndArapInfo_PA_csEntryAction.action?&menuId=${menuId}&changeId=${changeId}&acceptId=${acceptId}" --%>
<%-- 										target="navTab" title="保全录入" rel="${menuId}">录入完成</a> --%>
<%-- <%-- 									<button type="button"  class="but_blue" onclick="acceptStatus(${acceptId}) ">录入完成</button> --%> 
<!-- 								</div> -->
<!-- 							</div> -->
<!-- 						</li> -->
<!-- 						<li <s:if test="queryFlag==1">style="display:none"</s:if>> -->
<!-- 							<div> -->
<!-- 								<div> -->
<!-- 									<a class="but_blue main_buta" -->
<%-- 										href="${ctx}/cs/csEntry/showAcceptAndArapInfo_PA_csEntryAction.action?&menuId=${menuId}&changeId=${changeId}&acceptId=${acceptId}" --%>
<%-- 										target="navTab" title="保全录入" rel="${menuId}">上一步</a> --%>
<!-- 								</div> -->
<!-- 							</div> -->
<!-- 						</li> -->
<!-- 						<li <s:if test="queryFlag==1">style="display:none"</s:if>> -->
<!-- 							<div> -->
<!-- 								<div> -->
<!-- 									<a class="but_blue main_buta" -->
<%-- 										href="${ctx}/cs/csEntry/showAcceptAndArapInfo_PA_csEntryAction.action?&menuId=${menuId}&changeId=${changeId}&acceptId=${acceptId}" --%>
<%-- 										target="navTab" title="保全录入" rel="${menuId}">下一步</a> --%>
<!-- 								</div> -->
<!-- 							</div> -->
<!-- 						</li> -->
<!-- 						<li <s:if test="queryFlag!=1">style="display:none"</s:if>> -->
<!-- 							<div> -->
<!-- 								<div> -->
<!-- 									<button type="button"  class="but_gray" style="width: 50px;" class="close">关闭</button> -->
<!-- 								</div> -->
<!-- 							</div> -->
<!-- 						</li> -->
<!-- 					</ul> -->
<!-- 				</div> -->
<!-- 			</div> -->
<!-- 			<div class="tabsFooter"> -->
<!-- 				<div class="tabsFooterContent"></div> -->
<!-- 			</div> -->
		</div>
	</div>
</div>




<script type="text/javascript">
	//初始化
	$(document).ready(function() {
		$("#addDiv", navTab.getCurrentPanel()).hide();
		$("#bottomBar", navTab.getCurrentPanel()).hide();
	});

	/* function checkAmount(this) {
		alert($this.val()
		var reg = /^[1-9]*[1-9][0-9]*$/;
		if(!reg.test(value)){
			value = "";
		};
	} */

	function cancel() {
		var endorseDC = $(".endorseDC:checked", navTab.getCurrentPanel());
		var jsonMsg = "";
		if (endorseDC.size() == 0) {
			alertMsg.info("未选择要取消的约定变更计划，请选择。");
			return false;
		}
		$("#dc2",navTab.getCurrentPanel()).find("td input[name='age']").removeAttr("value");
		$("#dc2",navTab.getCurrentPanel()).find("td input[name='newAmount']").removeAttr("value");
		
		$("#rsTable", navTab.getCurrentPanel()).find("tr").each(
				function() {
					var $tds = $(this).find("td");
					var checkeds = $tds.eq(0).find("input:checkbox").attr(
							"checked");
					if (checkeds) {
						jsonMsg += $tds.eq(0).find("input").attr("value") + ","
								+ $tds.eq(13).text() + "," + $tds.eq(14).text()
								+ "," + $tds.eq(15).text() + "-";
					}
					;
				});
		$
				.ajax({
					type : "post",
					dataType : "text",
					url : "${ctx}/cs/serviceitem_dc/deleteDC_PA_csEndorseDCAction.action",
					data : 'jsonMsg=' + jsonMsg,
					success : function(data) {
						var adr = jQuery.parseJSON(data);
						for (var i = 0; i < adr.length; i++) {
							$("#dc2", navTab.getCurrentPanel())
									.find("tr")
									.each(
											function() {
												var td = $(this).children();
												if (td.eq(0).text().trim() == adr[i].itemId) {
													$("#endorseDCbutton", navTab.getCurrentPanel()).attr(
															"disabled",
															"disabled");
													td.eq(10).find("input")
															.attr("disabled",
																	"disabled");
													td.eq(11).find("input")
															.attr("disabled",
																	"disabled");
													$("#serviceItemBottomBar", navTab.getCurrentPanel())
															.hide();
													$("#bottomBar", navTab.getCurrentPanel()).show();
													afterDC(jsonMsg);
												}
												;
											});
						}
					}
				});
	}

	function afterDC(jsonMsg) {
		$("#faterflag", navTab.getCurrentPanel()).hide();
		$("#addDiv", navTab.getCurrentPanel()).show();
		var $obj = $("#queryForm", navTab.getCurrentPanel());
		var action = "${ctx}/cs/serviceitem_dc/loadAfterChangeInfo_PA_csEndorseDCAction.action";
		$obj.attr('action', action);
		$obj.find("input[name='jsonMsg']").val(jsonMsg);
		$obj.submit();
	}

	function save() {
		alertMsg.confirm("请确认是否需要保存录入的信息", {
			okCall : function() {
				$("#rsTable", navTab.getCurrentPanel()).attr("disabled",
						"disabled");
				$("#cancelBut", navTab.getCurrentPanel()).attr("disabled",
				"disabled");
				
				$("#faterflag", navTab.getCurrentPanel()).hide();
				$("#addDiv", navTab.getCurrentPanel()).show();
				var $table = $("#dc2", navTab.getCurrentPanel());
				var jsonMsg = _cs_tableToJson($table);
				var newAmount = $("#newAmount", navTab.getCurrentPanel()).val();
				var age = $("#age", navTab.getCurrentPanel()).val();
				if (newAmount == "" || newAmount == null || age == ""
						|| age == null) {
					alertMsg.error("必填项信息未完整录入，不能受理万能险基本保额约定变更，请确认。");
					return;
				}
				if (newAmount < 0 || newAmount == 0) {
					alertMsg.info("变更后保额不能小于等于0，请重新填写。");
					return;
				}
				var $obj = $("#queryForm", navTab.getCurrentPanel());
				$obj.find("input[name='jsonMsg']").val(jsonMsg);
				$obj.submit();
			},
			cancelCall : function() {

			}

		});
	}
	//帮助部分动作
	function MM_changeProp(objId, theProp, theValue) {
		var obj = null;
		with (document) {
			if (getElementById) {
				obj = getElementById(objId);
			}
		}
		if (obj) {
			if (theValue == true || theValue == false) {
				eval("obj.style." + theProp + "=" + theValue);
			} else {
				eval("obj.style." + theProp + "='" + theValue + "'");
			}
		}
	};
	//下一步
	function next() {
		var val1 = $("#changeId", navTab.getCurrentPanel()).val();
		var val2 = $("#acceptId", navTab.getCurrentPanel()).val();
		var val3 = $("#customerId", navTab.getCurrentPanel()).val();
		var title = "受理信息录入";
		var tabid = "_aplPermit_M";
		var fresh = eval("true");
		var external = eval("false");
		var url = "${ctx}/cs/csEntry/showAcceptAndArapInfo_PA_csEntryAction.action?&menuId=${menuId}&changeId="
				+ val1 + "&acceptId=" + val2 + "&customerId=" + val3;
		navTab.openTab(tabid, url, {
			title : title,
			fresh : fresh,
			external : external
		});

	}
	//录入完成，修改受理状态
	function acceptStatus(acceptId) {
		$
				.ajax({
					url : "${ctx}/cs/serviceitem_dc/changeSuccess_PA_csEndorseDCAction.action",
					type : "post",
					dataType : 'text',
					data : "acceptId=" + acceptId,
					success : function(data) {
						var json = jQuery.parseJSON(data);
						if (json.statusCode == 200) {
							alertMsg.correct("受理状态已更改为：录入完成！");
						} else {
							alertMsg.info(json.message);
						}
					},
					error : function() {
					}
				});
	}
</script>