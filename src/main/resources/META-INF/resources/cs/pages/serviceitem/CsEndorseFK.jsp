<%@ page language="java" contentType="text/html; charset=utf-8"
	pageEncoding="utf-8"%>
<%@ taglib uri="http://www.git.com.cn/taglib/udmp/field" prefix="Field"%>
<%@ taglib uri="/struts-tags" prefix="s"%><%@include file="/udmpCommon.jsp"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<%-- <s:set var="ctx">${pageContext.request.contextPath}</s:set> --%>
<%@ include file="/cs/pages/common/jsp/common_ctx.jsp" %>
<script type="text/javascript" src="${ctx}/cs/js/cs_common.js"></script>
<link rel="stylesheet" href="${ctx }/cs/css/cs_common.css" type="text/css">
<link rel="stylesheet" href="${ctx}/cs/css/public.css" type="text/css">
<script type="text/javascript" src="${ctx}/cs/js/DomZoomZX.js"></script>
<script>
	$(document).ready(function(){
        var D = DomZoomZX('input,select,textarea,td')
	})
</script>
	<!-- ********帮助菜单***********begin********* -->
<s:include value="/cs/pages/common/jsp/helpMenu.jsp"></s:include>
<!-- *********帮助菜单***********end********* -->

<script type="text/javascript">
	$(document).ready(function() {
		_cs_initMultipleBox();
		$("#updateEndInfo", navTab.getCurrentPanel()).hide();
	});
</script>
<!-- 帮助菜单 -->
<script
	src="${ctx }/udmp/plugins/ribbon/jquery.asyncorgtree.js"
	type="text/javascript"></script>
<script type="text/javascript">
</script>
<style type="text/css">
th,td {
	white-space: normal;
}

.searchBar li label {
	width: 90px;
}
</style>

<!-- 为了使用密码器 -->
<div style="display:none;">
	  <object classid="clsid:06CDE6AB-2508-448E-BD1A-B52F0D019D68"
	id="BpHidPinWeb"></object>
</div>
 <s:include value="csEndorseProgress.jsp" />

<div class="tabs" currentIndex="0" eventType="click" id="shtab"  layoutH="140px">
	<div class="tabsContent" layoutH="135" style="background: white;" >
			<!--第一个页签-->
		<div class="pageContent" >
			<!--客户的5项基本信息（姓名、出生日期、证件类型、证件号码、性别） -->
			<s:include value="customerInfo.jsp" />
				<div class="divfclass">
					<h1>
						<img src="images/tubiao.png" >与客户相关保单
					</h1>
				</div>
			<div class="pageFormInfoContent">
				<div class="tabdivclass">
					<table id="basicRemarkTable" class="list" style="width: 100%">
						<thead>
							<tr>
								<th>客户角色</th>
								<th>保单号</th>
								<th>投保人</th>
								<th>被保险人</th>
								<th>受益人</th>
								<th>险种代码</th>
								<th>险种名称</th>
								<th>保单状态</th>
							</tr>
						</thead>
						<tbody>
							<s:iterator value="groupCustomerVOs" status="st" var="var">
								<tr>
									<td><s:property value="roleName" /></td>
									<td><s:property value="policyCode" /></td>
									<td><s:property value="policyHolderName" /></td>
									<td><s:property value="insuredName" /></td>
									<td><s:property value="beneName" /></td>
									<td><s:property value="busiPrdCode" /></td>
									<td><Field:codeValue
											tableName="APP___PAS__DBUSER.T_BUSINESS_PRODUCT"
											value="${busiPrdId}" /></td>
									<td><Field:codeValue
											tableName="APP___PAS__DBUSER.T_LIABILITY_STATUS"
											value="${liabilityState}" /></td>
								</tr>
							</s:iterator>
						</tbody>
					</table>
				</div>
			</div>
<!-- ===================================================================================================================== -->
			<!-- 修改密码 -->
			<form id="csEndorseFKAction" method="post"
				action="${ctx }/cs/serviceitem_fk/savePassword_PA_csEndorseFKAction.action"
				class="required-validate" name="csEndorseFKAction" onsubmit="return validateCallback(this,_cs_fk_AjaxDone)">
				<OBJECT id = PinpadOcx 
				codebase="PinpadHandler.CAB" height="1" width="1" 
				classid="clsid:86FA742E-5029-439F-8F97-E4E2FB51233C">
				</OBJECT>
				<input name="status" type="hidden" id="status" value="${csCustomerPasswordVO.status}" /> 
				<input name="password" type="hidden" id="password" value="${csCustomerPasswordVO.password}" /> 
				<input name="policyPwd" type="hidden" id="policyPwd" value="${csContractMasterVO.policyPwd}" /> 
				<input id="customerId" type="hidden" name="customerId" value="${customerId}" /> 
				<input id="acceptId" type="hidden" name="acceptId" value="${acceptId}" />
				<input id="changeId" type="hidden" name="changeId" value="${changeId}" /> 
				<input id="errDate" type="hidden" name="errDate" value="${csCustomerPasswordVO.errDate}" />
				<input id="isGuiWaiQing" type="hidden" value="${guiWaiQing}" name="isGuiWaiQing">
				<!-- 存储保单号 -->
				<input id="policCodeStr" type="hidden" name="policCodeStr" value="" />
				<div class="divfclass">
					<h1>
						<img src="images/tubiao.png">密码设置/修改
					</h1>
				</div>
				<div class="pageFormInfoContent">
					<table style="width: 100%">
						<tr>
							<td>
								<div style="width: 100%">
									<dl>
										<dt>设置原因：</dt>
										<select id="setReason" name="csCustomerPasswordVO.setCause"
											style="width: 140px;" onchange="show_hidden()">
											<option value="4" checked="checked">---请选择---</option>
											<option value="1"
												<s:if test="csCustomerPasswordVO.setCause==1">selected</s:if>>初次设置</option>
											<option value="2"
												<s:if test="csCustomerPasswordVO.setCause==2">selected</s:if>>密码重置</option>
											<option value="3"
												<s:if test="csCustomerPasswordVO.setCause==3">selected</s:if>>修改密码</option>
										</select>
										</dd>
									</dl>
								</div>
							</td>
						</tr>
						<tr>
							<td>
								<div id="OldPwdDiv">
									<dl>
										<dt>原密码：</dt>
										<dd>
											<input type="password" id="password_old" name="csCustomerPasswordVO.password"
												value="${csCustomerPasswordVO.password}" maxLength="6" 
												onclick="ReadPinPassword_old();" onpaste="return false" readonly="readonly">
										</dd>
									</dl>
								</div>
							</td>
						</tr>
						<tr>
							<td>
								<div id="OldPwdDivs">
									<dl>
										<dt>新密码：</dt>
										<dd>
											<input type="password" id="newPwd" name="csCustomerPasswordVO_new.password" 
												value="${csCustomerPasswordVO.password}" maxLength="6" 
												onclick="ReadPinNewPwd();" onpaste="return false" readonly="readonly">
										</dd>
									</dl>
								</div>
							</td>
						<tr>
							<td>
								<div id="OldPwdDivs1">
									<dl>
										<dt>确认新密码：</dt>
										<dd>
											<input type="password" id="newPwds" name="csCustomerPasswordVO.password2"
												value="${csCustomerPasswordVO.password}" maxLength="6" 
												onclick="ReadPinNewPwds();" onpaste="return false" readonly="readonly">
										</dd>
									</dl>
								</div>
							</td>
						</tr>
					</table>
					<div class="pageFormdiv" >
						<button type="button" class="but_blue"
							onclick="savePwd('csEndorseFKAction')">保存</button>
					</div>
				</div>
			</form>

<!--   ======================================================================================================================= -->
				</div>
			</div>
		</div>
<s:include value="/cs/pages/serviceitem/serviceItemBottomBar.jsp"></s:include>
		

	<!-- *********帮助菜单***********end********* -->
<script type="text/javascript">

   $(document).ready(function(){
		csHelpMenu();
		show_hidden();
   });
   
   function ReadPinNewPwd()
   {
		var isGuiWaiQing = $("#isGuiWaiQing", navTab.getCurrentPanel()).val();
		if(isGuiWaiQing=='Y'){
			readPasswordByPinPad("newPwd");
		} else {
			readPasswordByBpHidPinWeb("newPwd");
		}		
	}
	function ReadPinNewPwds()
	{	
		var isGuiWaiQing = $("#isGuiWaiQing", navTab.getCurrentPanel()).val();
		if(isGuiWaiQing=='Y'){
			readPasswordByPinPad("newPwds");
		} else {
			readPasswordByBpHidPinWeb("newPwds");
		}	
	}
	function ReadPinPassword_old()
	{	
		var isGuiWaiQing = $("#isGuiWaiQing", navTab.getCurrentPanel()).val();
		if(isGuiWaiQing=='Y'){
			readPasswordByPinPad("password_old");
		} else {
			readPasswordByBpHidPinWeb("password_old");
		}	
	}
	/*柜外清读取密码*/
	function readPasswordByPinPad(elementId){
		var returnPassword='';
		var ret = csEndorseFKAction.PinpadOcx.readKey( 1, 120);
		if(ret==''){
			alertMsg.warn("请确认柜外清设备是否准备好，并输入了密码!");
		}else{
			if (ret=='-19') {
				alertMsg.warn("未检测到柜外清设备");
				return false;
			}else if (ret=='-22'){
				alertMsg.warn("调用柜外清设备出错");
				return false;
			} else {
				returnPassword=ret;
				$("#"+elementId).val(returnPassword);
				var va111 = checkPay("#"+elementId);	 				
				if(!va111){
					$("#"+elementId,navTab.getCurrentPanel()).val("");
					$("#"+elementId,navTab.getCurrentPanel()).blur() ;	 				
				} 
				return true;
			}		
		}
	}
	/*密码器读取密码*/
	function readPasswordByBpHidPinWeb(elementId){
		var returnPassword="";
		var Pindata=BpHidPinWeb.readKey(1);
		if (Pindata==''){
			alertMsg.warn("请确认密码键盘设备是否准备好，并输入了密码!");
		}else{
			if (Pindata == '-4') {
				alertMsg.warn("未检测到密码键盘设备");
				return false;
			}else{
				returnPassword=Pindata;
				$("#"+elementId).val(returnPassword);
				var va111 = checkPay("#"+elementId);	 				
				if(!va111){
					$("#"+elementId,navTab.getCurrentPanel()).val("");
					$("#"+elementId,navTab.getCurrentPanel()).blur() ;	 				
				} 
				return true;
			}
		}
	}
		 
	//保存
	//如果是 1 则原密码div 隐藏 否则显示
	function show_hidden(){
		var setReason = $("#setReason",navTab.getCurrentPanel()).val();
		
		$("#newPwd",navTab.getCurrentPanel()).val("");
		$("#newPwds",navTab.getCurrentPanel()).val("");
		$("#password_old",navTab.getCurrentPanel()).val("");
		
		if(setReason=='4'){
			$("#OldPwdDiv",navTab.getCurrentPanel()).hide();
			$("#OldPwdDivs",navTab.getCurrentPanel()).hide();
			$("#OldPwdDivs1",navTab.getCurrentPanel()).hide();
		}
		if(setReason=='1'){
			$("#OldPwdDiv",navTab.getCurrentPanel()).hide();
			$("#OldPwdDivs",navTab.getCurrentPanel()).show();
			$("#OldPwdDivs1",navTab.getCurrentPanel()).show();
		}
		if(setReason=='2'){
			$("#OldPwdDiv",navTab.getCurrentPanel()).hide();
			$("#OldPwdDivs",navTab.getCurrentPanel()).show();
			$("#OldPwdDivs1",navTab.getCurrentPanel()).show();
		}
		if(setReason=='3'){
			$("#OldPwdDiv",navTab.getCurrentPanel()).show();
			$("#OldPwdDivs",navTab.getCurrentPanel()).show();
			$("#OldPwdDivs1",navTab.getCurrentPanel()).show();
		}
	}
	//点击保存按钮
	function savePwd(formId){
		var $newPwd = $("#newPwd",navTab.getCurrentPanel());
		var $newPwds = $("#newPwds",navTab.getCurrentPanel());
		var setReason = $("#setReason").val();
		if(setReason==''){
			alertMsg.info("请选择设置原因");	
			return false;
		}
		 var setReason = $("#setReason",navTab.getCurrentPanel()).val();
	        if(setReason=='1'){
	            if($("#newPwd",navTab.getCurrentPanel()).val().length<1){
	                alertMsg.info("必填项信息未完整录入，不能受理客户层交易密码设置、修改，请确认。");    
	                return false;
	            }
	            if($("#newPwds",navTab.getCurrentPanel()).val().length<1){
	                alertMsg.info("必填项信息未完整录入，不能受理客户层交易密码设置、修改，请确认。"); 
	                return false;
	            }
	        }
	        if(setReason=='2'){
	            if($("#newPwd",navTab.getCurrentPanel()).val().length<1){
	                alertMsg.info("必填项信息未完整录入，不能受理客户层交易密码设置、修改，请确认。");    
	                return false;
	            }
	            if($("#newPwds",navTab.getCurrentPanel()).val().length<1){
	                alertMsg.info("必填项信息未完整录入，不能受理客户层交易密码设置、修改，请确认。"); 
	                return false;
	            }
	        }
	        if(setReason=='3'){
	            if($("#newPwd",navTab.getCurrentPanel()).val().length<1){
	                alertMsg.info("必填项信息未完整录入，不能受理客户层交易密码设置、修改，请确认。");    
	                return false;
	            }
	            if($("#newPwds",navTab.getCurrentPanel()).val().length<1){
	                alertMsg.info("必填项信息未完整录入，不能受理客户层交易密码设置、修改，请确认。"); 
	                return false;
	            }
	            if($("#password_old",navTab.getCurrentPanel()).val().length<1){
	                alertMsg.info("必填项信息未完整录入，不能受理客户层交易密码设置、修改，请确认。");  
	                return false;
	                }
	        }
		
	    //验证密码两次密码输入是否一致
		/* if(!checkPwd()){
			var newPwd=$($newPwd).val();
			var newPwds=$($newPwds).val();
			if(newPwd != newPwds){
				alertMsg.info("两次密码不一致请重新输入");
				return false;
			}
		} */
	    
		//验证密码设置规则
	     if(!checkPwd()){
			return false;
		}
			var $form = $("#"+formId,navTab.getCurrentPanel());
			$form.submit();
	}
	//判断新旧密码是否一致 
	function checkPwd(){
		var newPwd=$("#newPwd",navTab.getCurrentPanel()).val();
		var newPwds=$("#newPwds",navTab.getCurrentPanel()).val();
		if(newPwd==newPwds){
			return true;
		}else{
			alertMsg.info("两次密码不一致请重新输入");
			$("#newPwd",navTab.getCurrentPanel()).val("");
			$("#newPwds",navTab.getCurrentPanel()).val("");
			return false;
		}
	}	
	//只能输入正数字
	function checkPay(obj){
	 var val = $(obj).val();
	 var ab = $(obj).val().split("");
	 	//验证是否是六位密码
		var reg = /^\d{6}$/;
		if (!reg.test(val)) {
			alertMsg.error("新密码应为6位阿拉伯数字，不可连续3位（含）以上数字重复，不可位数字递增或递减，请重新输入！");
			return false;
		}
		var isSucc = true;
		//验证是否是连续3个递减
		var flags= 0;
		for(var a=0;a<=5;a++){
			for(var b=a;b<a+1;b++)	{
				if(eval(ab[b])==eval(ab[b+1])+1){
					flags++;
					if(flags==2){
						alertMsg.error("新密码应为6位阿拉伯数字，不可连续3位（含）以上数字重复，不可位数字递增或递减，请重新输入！");
						flags=0;
						isSucc = false;
					}
				} else {
					flags=0;
				}
			}	
		}
		//验证是否是递增 
		 for(var a=0;a<=5;a++){
			for(var b=a;b<a+1;b++){
				if(eval(ab[b])==eval(ab[b+1])-1){
					flags++;
					if(flags==2){
						alertMsg.error("新密码应为6位阿拉伯数字，不可连续3位（含）以上数字重复，不可位数字递增或递减，请重新输入！");
						flags=0;
						isSucc = false;
					}
				} else {
					flags=0;
				}
			}	
		} 
		//验证是否是相等
		 for(var a=0;a<=5;a++){
			for(var b=a;b<a+1;b++){
				if(eval(ab[b])==eval(ab[b+1])){
					flags++;
					if(flags==2){
						alertMsg.error("新密码应为6位阿拉伯数字，不可连续3位（含）以上数字重复，不可位数字递增或递减，请重新输入！");
						flags=0;
						isSucc = false;
					}
				} else {
					flags=0;
				}
			}	
		} 
		return isSucc;
	}
	
	//页面检索table中数据投保人信息与客户名称相同的保单号  .find("td:eq(2)").text()
	function findPolicyCodeByHolderName(){
		var $table = $("#basicRemarkTable",navTab.getCurrentPanel());
		var testTr =  $table.find("tr");
		//客户名称得到
		var customerName = $("#customerName").val();
		var policyCodeStr="";
		for(var a = 1; a<testTr.length; a++){
			var testOneTr = $(testTr[a],navTab.getCurrentPanel());
			var oneTrName = testOneTr.find("td:eq(2)").text();
			if(oneTrName==customerName){
				policyCodeStr += testOneTr.find("td:eq(1)").text()+",";
			}
		}
		return policyCodeStr;
	}

	function _cs_fk_AjaxDone(json){
		if (json.statusCode == DWZ.statusCode.ok) {
			alertMsg.correct(json.message);
			if (json.navTabId) { //把指定navTab页面标记为需要“重新载入”。注意navTabId不能是当前navTab页面的
				navTab.reloadFlag(json.navTabId);
			} else { //重新载入当前navTab页面
				navTabPageBreak();
			}
			if ("closeCurrent" == json.callbackType) {
				setTimeout(function() {
					navTab.closeCurrentTab();
				}, 100);
			} else if ("forward" == json.callbackType) {
				navTab.reload(json.forwardUrl);
			}
		} else {
			if(json.msgCsErrCsFk == "007"){//1--密码错误
				var value = $("#pwdFalseTime",navTab.getCurrentPanel()).val();
				if(value == null){
					$("#pwdFalseTime",navTab.getCurrentPanel()).val(1);
				} else {
					$("#pwdFalseTime",navTab.getCurrentPanel()).val( ++value);
				}
			}
				alertMsg.error(json.message);
				$("#newPwds",navTab.getCurrentPanel()).val("");
				$("#newPwd",navTab.getCurrentPanel()).val("");
				$("#password_old",navTab.getCurrentPanel()).val("");
		}
	}
</script>

