<%@ page language="java" contentType="text/html; charset=utf-8"
	pageEncoding="utf-8" import="java.util.*,java.text.*"%>
<%@ taglib uri="/struts-tags" prefix="s"%><%@include file="/udmpCommon.jsp"%>
<%@ taglib uri="http://www.git.com.cn/taglib/udmp/field" prefix="Field"%>
<%@ include file="/cs/pages/common/jsp/common_ctx.jsp" %>
<script type="text/javascript" src="${ctx}/cs/js/cs_common.js"></script>
<link rel="stylesheet" href="${ctx }/cs/css/cs_common.css"
	type="text/css">

<!-- ********帮助菜单***********begin********* -->
<s:include value="/cs/pages/common/jsp/helpMenu.jsp"></s:include>
<!-- *********帮助菜单***********end********* -->

<!-- 步骤标识 -->
<s:include value="csEndorseProgress.jsp" />

<div class="pageContent"
	style="background-color: white;" >
		<div class="divfclass">
			<h1>
				<img src="${ctx}/cs/img/icon/tubiao.png">领取日期变更
			</h1>
		</div>
		<div class="pageContent" layoutH="150">
		    <s:include value="customerInfo_list.jsp" />
			<div style="display: none">
				<form action="" id="changeGetAgeForm" onsubmit="" method="post">
					<input type="hidden" name="acceptId" value="${acceptId}"> <input
						type="hidden" name="changeId" value="${changeId}"> <input
						type="hidden" name="customerId" value="${customerId}"> <input
						type="hidden" id="logIds" name="logIds" value="">
				</form>
			</div>
			<s:if test="csEndorseLCVO.csEndorseLCndVOList.size()==0">
			<div class="pageFormInfoContent">
			    <h2 align="center" style="color:red;">无祝寿金信息！</h2>
			</div>
			</s:if>
			<s:else>
			     <!-- 保单信息 -->
				<div class="divfclass">
					<h1>
						<img src="${ctx}/cs/img/icon/tubiao.png">保单信息
					</h1>
				</div>
				<div class="main_FormContent">
					<table id="lcTableInform" name="lcTableInform" class="list" width="100%" >
						<thead>
							<tr>
								<th>选择<input type="checkbox" class="selectChange"
									onclick="allLCSelect()" id="checkControl"></th>
								<th style="display: none" >是否是税延产品</th>
								<th style="display: none">税延产品生效对应日</th>
								<th style="display: none">税延产品生日对应日</th>
								<th>保单号</th>
								<th>险种代码</th>
								<th>险种名称</th>
								<th>责任名称</th>
								<th>生效日期</th>
								<th>基本保额</th>
								<s:if test="isShowPrdFlag != null && isShowPrdFlag != '0'">
									<th>领取日期类型</th>
								</s:if>
								<th>领取日期</th>
								<s:if test="isShowPrdFlag != null && isShowPrdFlag != '0'">
									<th>新领取日期类型</th>
								</s:if>
								<th>新领取日期</th>
							</tr>
						</thead>
						
						<tbody>
							<s:iterator value="csEndorseLCVO.csEndorseLCndVOList" status="st">
								<tr align="center" id = "csEndorseLC_ID">
									<td width="50px"><input type="checkbox" class="logId"
										value="${logId}" onclick="selectChangeLC(this)"></td>
									<td style="display: none">${policyPrdFlag }</td>
									<td style="display: none"><s:date name="valiStartDate" format="MM-dd" /></td>
									<td style="display: none"><s:date name="birhtStartDate" format="MM-dd" /></td>
									<td>${policyCode }</td>
									<td>${busiProdCode}</td>
									<td><Field:codeValue
											tableName="APP___PAS__DBUSER.T_BUSINESS_PRODUCT" value="${businessPrdId}" /></td>
									<td><Field:codeValue tableName="APP___PAS__DBUSER.T_LIABILITY"
											value="${liabId}" /></td>
									<td><s:date name="validateDate"
											format="yyyy-MM-dd" /></td>
									<td>${totalAmount}</td>
									
									<s:if test="isShowPrdFlag != null && isShowPrdFlag != '0'">
										<td><Field:codeValue tableName="APP___PAS__DBUSER.T_PAYDATE_TYPE" value="${oldPaydateType}" /></td>
									</s:if>
									
									<td><s:date name="oldBeginDate" format="yyyy-MM-dd" /></td>
									 
									<s:if test="isShowPrdFlag != null && isShowPrdFlag != '0'">
										<td>
											<Field:codeTable  nullOption="true" name="changeLC" 
														tableName="APP___PAS__DBUSER.T_PAYDATE_TYPE"
														value="${newPaydateType}" onChange="newLCChange(this)" 
														whereClause="PAYDATE_TYPE_CODE != '${oldPaydateType }'"></Field:codeTable>
										</td>	
									</s:if>
										
									<s:if test="policyPrdFlag==1">
										<td><input  name="newBeginPrdDate"  dateFmt="MM-dd" type="date"  class="date" /></td>
													
									</s:if>
									<s:else>
										<td><input  name="newBeginDate"  value="<s:date name="newBeginDate" format="yyyy-MM-dd"/>" type="expandDateYMDRO" 
												disabled="true" class="date" /></td>
									</s:else>
								</tr>
							</s:iterator>
						</tbody>
					</table>
					
						<div class="pageFormdiv">
							<button class="but_blue" type="button" onclick="saveGetAgeChange()" <s:if test="queryFlag == 1">disabled="disabled"</s:if>>保存</button>
						</div>
				</div>
			</s:else>
	
			<div id="getDateChangeSaveResult"></div>
			<s:include value="/cs/pages/serviceitem/serviceItemBottomBar.jsp"></s:include>
		</div>
</div>
<script type="text/javascript">


function newLCChange(line){
	var table = $("#lcTableInform", navTab.getCurrentPanel());
	var rows = table.find("tr");
	for (var n = 1; n < rows.length; n++) {
		
	   var policyPrdFlag = $(rows[n]).find("td").eq(1).text();
		if(policyPrdFlag==1){
			if (line.value == 'B') {//生日对应
				$("select[name='changeLC']").find("option[value='B']").attr("selected", true);
				$("input[name='newBeginPrdDate']").val($(rows[n]).find("td").eq(3).text());
				$("input[name='newBeginPrdDate']").attr("disabled", true);
			}
			if (line.value == 'Z') {
				$("select[name='changeLC']").find("option[value='Z']").attr("selected", true);
				$("input[name='newBeginPrdDate']").val("");
				$("input[name='newBeginPrdDate']").attr("disabled", false);
			}
			if (line.value == 'S') {//生效日对应
				$("select[name='changeLC']").find("option[value='S']").attr("selected", true);
				$("input[name='newBeginPrdDate']").val($(rows[n]).find("td").eq(2).text());
				$("input[name='newBeginPrdDate']").attr("disabled", true);
			}
		}
	}
}

	//帮助部分动作
	function MM_changeProp(objId, theProp, theValue) {
		var obj = null;
		with (document) {
			if (getElementById) {
				obj = getElementById(objId);
			}
		}
		if (obj) {
			if (theValue == true || theValue == false) {
				eval("obj.style." + theProp + "=" + theValue);
			} else {
				eval("obj.style." + theProp + "='" + theValue + "'");
			}
		}
	};

	//全选
	function allLCSelect() {
		var $logId = $(".logId");
		var count = 0;
		for (var a = 0; a < $logId.length; a++) {
			if ($logId[a].checked) {
				count = count + 1;
			}
		}
		//全选
		if (count != $logId.length) {
			for (var a = 0; a < $logId.length; a++) {
				$logId[a].checked = true;
			}
			$("#checkControl").attr("checked", true);
		} else {
			//全不选
			for (var a = 0; a < $logId.length; a++) {
				$logId[a].checked = false;
			}
			$("#checkControl").attr("checked", false);
		}
		for (var a = 0; a < $logId.length; a++) {
			selectChangeLC($($logId[a]));
		}
	}

	//选择变更
	function selectChangeLC(line) {
		if ($(line).parents("tr").first().children("td").first().children(
				"input").first().attr("checked") != "checked") {
			$(line).parents("tr").first().children("td").eq(-1).children()
					.first().attr("disabled", true);
		} else {
			$(line).parents("tr").first().children("td").eq(-1).children()
					.first().attr("disabled", false);
		}
	}

	//保存变更
	function saveGetAgeChange() {
		alertMsg.confirm("请确认是否需要保存录入信息?", {
	        okCall: function(){
	    		//消除之前添加的信息
	    		var $newBeginDates=$(".newBeginDates");
	    		for(var a = 0; a < $newBeginDates.length; a++){
	    			$($newBeginDates[a]).remove();
	    		}
	    		var $newPaydateType=$(".newPaydateType");
	    		for(var a = 0; a < $newPaydateType.length; a++){
	    			$($newPaydateType[a]).remove();
	    		}
	    		var $newPayDates=$(".newPayDates");
	    		for(var a = 0; a < $newPayDates.length; a++){
	    			$($newPayDates[a]).remove();
	    		}
	    		
	    		
	    		//取得信息
	    		var $logId = $(".logId");
	    		var logIds = "";
	    		var policyPrd = 0; //税延标识，1：是
	    		var newDate = null;
	    		var count = 0;
	    		for (var a = 0; a < $logId.length; a++) {
	    			if ($logId[a].checked) {
	    				logIds = logIds + $logId[a].value + ",";
	    				var newage = $($logId[a]).parents("tr").first().children("td").eq(-1).find("input").val();
	    			
	    				//***************** 税延产品新增   st****************/
	    				var newDateType = $($logId[a]).parents("tr").first().children("td").eq(-2).find("select option:selected",navTab.getCurrentPanel()).val();
	    				var policyPrdFlag = $($logId[a]).parents("tr").first().children("td").eq(1).text();//税延产品标识=1
	    				
	    			
	    				if (newage == "") {
	    					alertMsg.info("必填项信息未完整录入，不能受理领取日期变更!");
	    					return false;
	    				}
	    				if(policyPrdFlag==1){
	    					policyPrd = policyPrdFlag;//税延产品标识=1
	    					$("#changeGetAgeForm").append("<input type='hidden' class='newPaydateType' name='newPaydateTypes["+count+"]' value='"+newDateType+"''>");
	    					$("#changeGetAgeForm").append("<input type='hidden' class='newPayDates' name='newPayDates["+count+"]' value='"+newage+"''>");
	    					if(newDate !=null && newDate != newage){
	    						alertMsg.info("税延产品领取日期变更应保持一致，请重选，谢谢!");
	    		    			return false;
	    					}
	    					newDate = newage;
	    				}
	    				else{
	    					$("#changeGetAgeForm")
    						.append(
    								"<input type='hidden' class='newBeginDates' name='newBeginDates["+count+"]' value='"+newage+"''>");
	    				}
	    				
	    				count++;
	    			}
	    		}
	    		
	    		//***********税延*********************************//
	    		if($logId.length != count && policyPrd ==1 ){
	    			alertMsg.info("税延产品不支持单独变更，请全选，谢谢!");
	    			return false;
	    		}
	    		//***********税延*********************************//
	    		
	    		if (count == 0) {
	    			alertMsg.info("请选择您想要改变的信息!");
	    			return false;
	    		}
	    		logIds = logIds.substring(0, logIds.length - 1);
	    		$("#logIds").val(logIds);
	    		//取得信息结束
	    		
	    		var action = "${ctx}/cs/serviceitem_lc/changeGetDate_PA_csEndorseLCAction.action";
	    		var onsubmit = "return divSearch(this,'getDateChangeSaveResult')";
	    		$("#changeGetAgeForm").attr("action", action);
	    		$("#changeGetAgeForm").attr("onsubmit", onsubmit);
	    		$("#changeGetAgeForm").submit();
	        }
		});
	}
	//下一步
	function saveGetAgeChangeNextStep() {
		//消除之前添加的信息
		var $newBeginDates=$(".newBeginDates");
		for(var a = 0; a < $newBeginDates.length; a++){
			$($newBeginDates[a]).remove();
		}
		
		
		//取得信息
		var $logId = $(".logId");
		var logIds = "";

		var count = 0;
		for (var a = 0; a < $logId.length; a++) {
			if ($logId[a].checked) {
				logIds = logIds + $logId[a].value + ",";
				var newage = $($logId[a]).parents("tr").first().children("td")
						.eq(-1).find("input").val();
				if (newage == "") {
					alertMsg.info("必填项信息未完整录入，不能受理领取日期变更!");
					return false;
				}
				$("#changeGetAgeForm")
						.append(
								"<input type='hidden' class='newBeginDates' name='newBeginDates["+count+"]' value='"+newage+"''>");
				count++;
			}
		}
		if (count == 0) {
			alertMsg.info("请选择您想要改变的信息!");
			return false;
		}
		logIds = logIds.substring(0, logIds.length - 1);
		$("#logIds").val(logIds);
		//取得信息结束
		
		var action = "${ctx}/cs/serviceitem_lc/nextStep_PA_csEndorseLCAction.action";
		var onsubmit = "return navTabSearch(this);";
		$("#changeGetAgeForm").attr("action", action);
		$("#changeGetAgeForm").attr("onsubmit", onsubmit);
		$("#changeGetAgeForm").submit();
	}
</script>
<script type="text/javascript">
	$(document).ready(function() {
		csHelpMenu();
	});

	
</script>