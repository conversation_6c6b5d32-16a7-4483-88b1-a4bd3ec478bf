<%@ page language="java" contentType="text/html; charset=utf-8"
	pageEncoding="utf-8"%>
<%@ taglib uri="/struts-tags" prefix="s"%><%@include file="/udmpCommon.jsp"%>
<!-- 引入点击下一步返回保全录入的js -->
<%-- <s:set var="ctx">${pageContext.request.contextPath}</s:set> --%>
<%@ include file="/cs/pages/common/jsp/common_ctx.jsp"%>
<script type="text/javascript"
	src="cs/pages/common/js/returnAcceptAndPay.js"></script>
<%@ taglib uri="http://www.git.com.cn/taglib/udmp/field" prefix="Field"%>
<script type="text/javascript" src="${ctx}/cs/js/cs_common.js"></script>
<link rel="stylesheet" href="${ctx }/cs/css/cs_common.css" type="text/css">
<link rel="stylesheet" href="${ctx}/cs/css/public.css" type="text/css">
<script type="text/javascript" src="${ctx}/cs/js/DomZoomZX.js"></script>
<script>
	$(document).ready(function(){
        var D = DomZoomZX('input,select,textarea,td')
	})
</script>
<input type="hidden" name="menuId" id="menuId" value="${menuId}">
<input type="hidden" name="itemFlag" id="itemFlag" value="mainProd">
<input type="hidden" name="itemName" id="itemName" value="附加险满期不续保">
<!-- 帮助菜单 -->
<!-- ********帮助菜单***********begin********* -->
<s:include value="/cs/pages/common/jsp/helpMenu.jsp"></s:include>
<s:include value="csEndorseProgress.jsp" />
<!-- *********帮助菜单***********end********* -->

<div layoutH="140">
<div class="pageContent">
<s:include value="customerInfo_list.jsp" />
<!-- validateCallback  -->
		<div class="pageFormContent" id="_enChange">
			<form id="addtionNoRenewalForm" action="${ctx}/cs/serviceitem_en/addtionBusiPrdNRenewal_PA_csEndorseENAction.action?flag=1"
				  class="pageForm required-validate" method="post" onsubmit="return navTabSearch(this);">
				<input type="hidden" id="customerId" name="customerId" value="${customerId}"/> 
				<input type="hidden" id="acceptId" name="acceptId" value="${acceptId}"/> 
				<input type="hidden" id="changeId" name="changeId" value="${changeId}"/> 
				<input type="hidden" id="plyIdsAndBuIds" name="plyIdsAndBuIds" value="${plyIdsAndBuIds}"/>
				
				
				<div class="panel"  style="display: none" >
					<h1>客户信息</h1>
					<div class="pageFormContent" id="">
						<dl>
							<dt>姓名</dt>
							<dd>
								<input type="text" readonly="readonly"
									name="csCustomerVO.customerName"
									value="${csCustomerVO.customerName}">
							</dd>
						</dl>
						<dl>
							<dt>生日</dt>
							<dd>
								<input type="text" readonly="readonly"
									name="csCustomerVO.customerBirthday"
									value="<s:date format="yyyy-MM-dd" name="csCustomerVO.customerBirthday"></s:date>">
							</dd>
						</dl>
						<dl>
							<dt>证件类型</dt>
							<dd>
								<input type="text" style="display: none"
									name="csCustomerVO.customerCertType"
									value="${csCustomerVO.customerCertType }" /> <input type="text"
									readonly="readonly"
									value='<Field:codeValue   tableName="APP___PAS__DBUSER.T_CERTI_TYPE"  value="${csCustomerVO.customerCertType}"/>'>
							</dd>
						</dl>
						<dl>
							<dt>证件号码</dt>
							<dd>
								<input type="text" style="display: none"
									name="csCustomerVO.customerCertiCode"
									value="${csCustomerVO.customerCertiCode }"> <input
									type="text" readonly="readonly"
									value="${csCustomerVO.customerCertiCode}">
							</dd>
						</dl>
						<dl>
							<dt>性别</dt>
							<dd>
								<input type="text" style="display: none"
									name="csCustomerVO.customerGender"
									value="${csCustomerVO.customerGender }" /> <input type="text"
									readonly="readonly"
									value='<Field:codeValue tableName="APP___PAS__DBUSER.T_GENDER"  value="${csCustomerVO.customerGender}"/>'>
							</dd>
						</dl>
					</div>
				</div>
					<div class="divfclass">
						 	    <h1>
								<img src="images/tubiao.png" >客户相关保单
								</h1>
					</div>
					<div>
					<div class="pageFormContent">
						<table id="addtionRenewoalTable" class="list" width="100%">
							<thead>
								<tr>
									<th style="width: 45px;"><input  type="checkbox" id="checkBoxs" 
										<s:if test="queryFlag==1">disabled</s:if> class="checkboxCtrl" group="acceptIds" value="0000" />选择</th>
									<th>保单号</th>
									<th style="width:45px;">投保人</th>
									<th style="width:45px;">被保险人</th>
									<th>主险代码</th>
									<th style="width:80px;">主险名称</th>
									<th style="width:60px;">附加险代码</th>
									<th style="width:80px;">附加险名称</th>
									<th>交至日期</th>
									<th>保额/份数/保障计划</th>
									<th>下期保费</th>
									
									<%--134663 保证续保期间届满申请流程（1）-核心系统满期不续保改造-一期-保全  start --%>
									<th style="width:60px;">是否为保证续保产品</th>
									<th style="width:60px;">当前保证续保期间起、止期</th>
									<th style="width:60px;">是否在保证续保期间最后一年</th>
									<th>当前续保标识</th>
									<th style="width:60px;">是否进入下一保证续保期间</th>
									<th colName="renewalFlag" inputType="select">申请事项</th>
									<%--134663 保证续保期间届满申请流程（1）-核心系统满期不续保改造-一期-保全  end --%>
									
									<th style="display:none" colName="policyId">policyId</th>
									<th style="display:none" colName="busiItemId">addtionBusiItemId</th>
									<th style="display:none" colName="policyChgId">policyChgId</th>
									<th style="display:none" colName="isGurntRenewEndYear">isGurntRenewEndYear</th>
								</tr>
							</thead>
							<tbody>
								<s:iterator value="csEndorseEnVOs" status="st" id="BFList">
									<tr align="center" tr_saveStatus="1">
										 <td>
											<input type="checkbox"  <s:if test="queryFlag==1">disabled</s:if>   <s:if test="operationType == 2">checked="checked"</s:if>
											name="acceptIds" value="${policyChgId}|${addtionBusiItemId}" class="acceptIds">
										 </td>
										<td>${policyCode }</td>
										<td>${policyHolderName }</td>
										<td>${insuredName }</td>
										<td>${busiProdCode }</td>
										<td><Field:codeValue tableName="APP___PDS__DBUSER.T_BUSINESS_PRODUCT" value="${busiProdCode}" /></td>
										<td>${addtionBusiProdCode }</td>
										<td><Field:codeValue tableName="APP___PDS__DBUSER.T_BUSINESS_PRODUCT" value="${addtionBusiProdCode}" /></td>
										<td><s:date name="payDueDate" format="yyyy-MM-dd"></s:date></td>
										<td>${amount }</td>
										<td><s:if test="renewalFlag==0">--</s:if><s:else>${ basicAmount}</s:else></td>
										
										<%--134663 保证续保期间届满申请流程（1）-核心系统满期不续保改造-一期-保全  start --%>
										<td>${assurerenewFlag==1?"是":"否" }</td>
										<td>${assurerenewFlag==1?gurntRenewDate:"--" }</td>
										<td><s:if test="assurerenewFlag==1">${isGurntRenewEndYear==1?"是":"否" }</s:if> <s:else>--</s:else></td>
										<td>${currRenew==1?"是":"否" }</td>
										
										<td id="_gurntPeriodFlag">
											<s:if test="assurerenewFlag==1">
												<s:if test="gurntPeriodFlag!=null">${gurntPeriodFlag==1?"是":"否" }</s:if>												
											</s:if>
											<s:else>--</s:else>
										</td>
										<td><select cssClass="combox" style="width: 120px" <s:if test="queryFlag==1" >disabled</s:if>>
												<s:if test="renewalFlag==1" >
													<option value="0" <s:if test="newRenew==0">selected</s:if>>不续保</option>
												</s:if>												
												<s:if test="showRenewFlag==1" >
													<option value="1"  selected="selected">续保</option>
												</s:if>												
												<s:if test="showFlag==1" >
													<option value="2"  <s:if test="newGurntPeriodFlag==1">selected</s:if>>申请进入下一保证续保期间并续保</option>
												</s:if>
										</select></td>
										<%--134663 保证续保期间届满申请流程（1）-核心系统满期不续保改造-一期-保全  end --%>
										
										<td style="display:none">${policyId }</td>
										<td style="display:none">${addtionBusiItemId }</td>
										<td style="display:none">${policyChgId }</td>
										<td style="display:none">${isGurntRenewEndYear }</td>
									</tr>
								</s:iterator>
							</tbody>
						</table>
				</div>
					
					<s:if test="queryFlag!=1">
						<div class="pageFormdiv">
							<button type="button" class="but_blue" onclick="addtionNoRenewalSave()">保存</button>
						</div>
					</s:if>
					
				</div>
			</form>
				<div class="divfclass">
								<h1>
							    <img src="images/tubiao.png" >变更后保单信息
								</h1>
				</div>
				<div class="pageFormContent" >
				<div>
					<table class="list" width="100%" id="decreaseAFDetail">
						<thead>
							<tr>
								<th>保单号</th>
								<th>投保人</th>
								<th>被保险人</th>
								<th>主险代码</th>
								<th>主险名称</th>
								<th>附加险代码</th>
								<th>附加险名称</th>
								<th>保额/份数/保障计划</th>
								<th style="color: red;">终止日期</th>
								<th>续保标识</th>
								<th>下一年应交保费</th>
								<th  style="width:80px;">是否进入下一保证续保期间</th>
							</tr>
						</thead>
						<tbody id="allInfoContent">
							<s:iterator value="csEndorseEnVONews" status="AFst" id="AFList">
								<tr align="center">
									<td>${policyCode }</td>
									<td>${policyHolderName }</td>
									<td>${insuredName }</td>
									<td>${busiProdCode }</td>
									<td><Field:codeValue tableName="APP___PDS__DBUSER.T_BUSINESS_PRODUCT" value="${busiProdCode}" /></td>
									<td>${addtionBusiProdCode }</td>
									<td><Field:codeValue tableName="APP___PDS__DBUSER.T_BUSINESS_PRODUCT" value="${addtionBusiProdCode}" /></td>
									<td>${amount }</td>
									<td><s:date name="expiryDate" format="yyyy-MM-dd"></s:date></td>
									<td><s:if test="renewalFlag==0">不可续保</s:if>
										<s:else>
											可续保
										</s:else>
									</td>
									<td><s:if test="renewalFlag==0">--</s:if><s:else>${ basicAmount}</s:else></td>
									<td><s:if test="gurntPeriodFlag!=null">${gurntPeriodFlag==1?"是":"否" }</s:if></td>
								</tr>
							</s:iterator>
						</tbody>
					</table>
				</div>
			</div>
			
		</div>
</div>
</div>
<s:include value="/cs/pages/serviceitem/serviceItemBottomBar.jsp"></s:include>
<script type="text/javascript">
	
	function checkWarnMsg(){
		debugger;
		var $form=$("#addtionNoRenewalForm",navTab.getCurrentPanel());
		$.ajax({
			type : "post",
			url : "${ctx}/cs/serviceitem_en/checkENWarnMsg_PA_csEndorseENAction.action",
			data : $form.serialize(),
			success : function(data) {
				debugger;
				var json = DWZ.jsonEval(data);				
				if (json.statusCode ==333) {
					alertMsg.confirm(json.message, {
						okCall : function() {
							_saveEnData();
						},
						cancelCall : function() {
							
						}
					});
				}else{
					_saveEnData();
				}
			}
		});
		
	}
	
	
	function _initEnData(){
		var $dataTable = $("#addtionRenewoalTable",navTab.getCurrentPanel());
	 	$dataTable.find("tbody tr").each(function(){
	 		var isCheck=$(this).find("input:checkbox:checked").length;	
			if(isCheck<=0){//未选中则不进行转换
				$(this).removeAttr("tr_saveStatus");
			}	
		});
		var plyIdsAndBuIds= _cs_tableToJson($dataTable);
		$("#plyIdsAndBuIds",navTab.getCurrentPanel()).val(plyIdsAndBuIds);		
		console.info(plyIdsAndBuIds);
	}
	
	//附加险满期不续保
	function addtionNoRenewalSave() {		
		debugger;					
		var $checkIds = $("#addtionRenewoalTable", navTab.getCurrentPanel()).find("input:checkbox:checked");
		if ($checkIds.size() <= 0) {
			alertMsg.info("请选择要操作的险种！");
			return false;
		}
		 
	 	_initEnData();//初始化保存信息
		
	 	checkWarnMsg();//非阻断校验
	}
	
	function _saveEnData(){
		debugger;
		var $form=$("#addtionNoRenewalForm",navTab.getCurrentPanel());
		$.ajax({
			type : "post",
			url : "${ctx}/cs/serviceitem_en/addtionBusiPrdNRenewal_PA_csEndorseENAction.action",
			data : $form.serialize(),
			success : function(data) {
				var json = DWZ.jsonEval(data);				
				if (json.statusCode == DWZ.statusCode.error) {
					alertMsg.error(json.message);
					return;
				}else{
					navTab.reload();
				}
			}
		});
	}
	
	
	
</script>
