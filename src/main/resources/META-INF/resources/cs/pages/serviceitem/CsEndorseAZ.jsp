<%@ page language="java" contentType="text/html;charset=UTF-8"
	pageEncoding="UTF-8" import="java.util.*,java.text.*"%>
<%@ taglib uri="/struts-tags" prefix="s"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/fmt" prefix="fmt"%>
<%@ taglib uri="http://www.git.com.cn/taglib/udmp/field" prefix="Field"%>
<%@ include file="/cs/pages/common/jsp/common_ctx.jsp"%>
<link rel="stylesheet" href="${ctx}/cs/css/public.css" type="text/css">

<script type="text/javascript" src="${ctx}/cs/js/cs_common.js"></script>

<script type="text/javascript">
	//点击支取形式变更后的状态
	function newModeChange(line) {
		//如果是银行转账 则显示银行账户
		$("select[name='payMode'] option").removeAttr("selected");
		$("select[name='payMode']").find("option[value='" + line.value + "']")
				.attr("selected", true);
		$("a[name='payMode']").get(0).innerHTML = $("select[name='newPayMode']")
				.find("option[value='" + line.value + "']").get(0).innerHTML;
		if (line.value == 2 || line.value == 3) {
			$("#bankAccount").attr("style", "display:block;");
		} else {
			$("#bankAccount").attr("style", "display:none;");
			$("#infoTable").attr("disabled", false);
		}
		$("select[name='payMode']").get(0).onchange();
	}

	function saveChangeBankMsg() {

		var bankList = $("#policyBankMsg select[name='banklist'");
		bankList.get(0).onchange();
		var $instalmentAmount = $(".instalmentAmount");
		for (var a = 0; a < $instalmentAmount.length; a++) {
			if (isNaN(parseFloat($($instalmentAmount[a]).text().trim()))
					|| parseFloat($($instalmentAmount[a]).text().trim()) <= 0) {
				alertMsg.info("没有足够的金额来领取！");
				return false;
			}
		}

		var flags = 0;
		var flag = 0;
		var payType = $("#newPayMode").val();
		//获取这个表格
		var $dataTable = $("#policyBankRelationTable", navTab.getCurrentPanel());
		var $trs = $dataTable.find("tbody tr");
		$trs.each(function() {
			var $tds = $(this).find("td");
			var checkeds = $tds.eq(5).children().children().children().find(
					"option:selected").val();
			var bankAcount = $tds.eq(6).children().val();
			if (payType != checkeds) {
				flag = 1;
			}
			if (checkeds == "") {
				flags = 1;
			}
			if (checkeds != 1 && bankAcount == "") {
				flags = 1;
			}
		});
		if (flags == 0) {
			if (flag == 0) {
				alertMsg
						.confirm(
								"确认保存以上信息？",
								{
									okCall : function() {
										var _jsons = "";
										var $jsonsText = $(
												"input[name='jsons']", navTab
														.getCurrentPanel());
										var $table = $(
												"#policyBankRelationTable",
												navTab.getCurrentPanel());
										_jsons += _cs_tableToJson($table);
										$jsonsText.val(_jsons);
										$.ajax({
											url : "${ctx}/cs/serviceitem_az/saveChangeBankMsg_PA_csEndorseAZAction.action",
											type : "post",
											dataType : 'html',
											data : "jsonString="
													+ $jsonsText.val(),
											cache : false,
											success : function(data) {
											var result = DWZ.jsonEval(data);
								        	  if(result.statusCode === "300"){
								        		  alertMsg.error(result.message);
								        	  }else{
													$("#changeBankMsg").html(data).initUI();
								        	  }
												
											}
										});
									},
									cancelCall : function() {
									}
								});
			} else {
				alertMsg.error("领取方式不一致");
			}
		} else {
			alertMsg.error("必填项信息为完整录入，不能受理新增领取责任保全项，请确认");
		}
	}
	//上一步
	function save() {
		alertMsg.confirm("请确认是否需要保存录入信息", {
			okCall : function() {
				$.post(url, data, DWZ.ajaxDone, "json");
			}
		});
	}
</script>
<!-- ********帮助菜单***********begin********* -->
<s:include value="/cs/pages/common/jsp/helpMenu.jsp"></s:include>
<!-- *********帮助菜单***********end********* -->

<!-- 步骤标识 -->
<s:if test="queryFlag!=1&&acceptStatus!='07'">
<s:include value="csEndorseProgress.jsp"></s:include>
</s:if><s:else>
<s:include value="entryProgressBar.jsp"></s:include>
</s:else>


<div class="divfclass"  layoutH="140">
<s:include value="customerInfo_list.jsp" />
<div class="panel" style="margin: 10px 0px; display: none">
	<h1>客户信息</h1>
	<div class="pageFormContent">
		<dl>
			<dt>姓名</dt>
			<dd>
				<input id="customerName" name="csCustomerVO.customerName"
					type="text" value="${csCustomerVO.customerName}"
					disabled="disabled" />
			</dd>
		</dl>
		<dl>
			<dt>性别</dt>
			<dd>
				<input type="text" id="customerGender" name="customerGender"
					value="<Field:codeValue  tableName="APP___PAS__DBUSER.T_GENDER" value="${csCustomerVO.customerGender}" /> "
					readonly="readonly">
			</dd>
		</dl>
		<dl>
			<dt>生日</dt>
			<dd>
				<input name="csCustomerVO.customerBirthday" type="text"
					value=<s:date format="yyyy-MM-dd" name="csCustomerVO.customerBirthday"></s:date>
					disabled="disabled" />
			</dd>
		</dl>
		<dl>
			<dt>证件类型</dt>
			<dd>
				<input type="text" readonly="readonly" id="customerCertType"
					name="customerCertType"
					value="<Field:codeValue  tableName="APP___PAS__DBUSER.T_CERTI_TYPE" value="${csCustomerVO.customerCertType}" />" />
			</dd>
		</dl>
		<dl>
			<dt>证件号码</dt>
			<dd>
				<input type="expandCertiCode" name="csCustomerVO.customerCertiCode"
					value="${csCustomerVO.customerCertiCode}" disabled="disabled" />
			</dd>
		</dl>

		<!--保全申请号 -->
		<input type="hidden" id="customerId" name="customerId"
			value="${customerId}" />
		<!--保全申请号 -->
		<input type="hidden" id="changeId" name="changeId" value="${changeId}" />
		<!--保全受理号 -->
		<input type="hidden" id="acceptId" name="acceptId" value="${acceptId}" />
	</div>
</div>
<div class="divfclass" >
	<h1>
		<img src="cs/img/icon/tubiao.png"">保单险种列表信息
	</h1>
	<div class="pageFormContent">
		<table id="azTableInform" name="azTableInform" class="list">
			<thead>
				<tr>
					<th colName="customerId" style="display: none"></th>
					<th colName="changeId" style="display: none"></th>
					<th colName="policyChgId" style="display: none"></th>
					<th colName="checkbox" style="width: 50px">选择</th>
					<th>序号</th>
					<th colName="policyCode">保单号</th>
					<th colName="policyId" style="display: none"></th>
					<th colName="busiProdCode">险种代码</th>
					<th colName="busiItemId" style="display: none"></th>
					<th>险种名称</th>
					<th colName="productId" style="display: none"></th>
					<th colName="itemId" style="display: none">责任组id</th>
					<th>累计缴费</th>
					<th colName="interestCapital">账户金额</th>

					<th colName="validateDate">生效日期</th>
					<th colName="payType" inputType="select" style="width: 100px">领取方式<span
						style="color: red">*</span></th>
					<th colName="instalmentProportion" inputType="select"
						style="width: 100px">领取比例%<span style="color: red">*</span></th>
					<th colName="beginDate" inputType="input" style="width: 200px">领取开始日期<span
						style="color: red">*</span></th>
					<th colName="acceptId" style="display: none"></th>
					<th colName="busiPrdId" style="display: none"></th>
					<th colName="acceptTime" style="display: none"></th>
					<!-- 申请时间 -->
				</tr>
			</thead>
			<tbody align="center" id="azTableInformTbody">
				<s:iterator value="csEndorseAZFeeVOs" status="st">
					<tr tr_saveStatus="0">
						<td style="display: none" class="payeeAccountID">${customerId}</td>
						<td style="display: none">${changeId}</td>
						<td style="display: none">${policyChgId}</td>
						<td><input type="checkbox" name="checkbox" /></td>
						<td><s:property value="#st.index+1" /></td>
						<td>${policyCode}</td>
						<td style="display: none">${policyId}</td>
						<td>${busiProdCode}</td>
						<td style="display: none">${busiItemId}</td>
						<td><Field:codeValue
								tableName="APP___PAS__DBUSER.T_BUSINESS_PRODUCT"
								value="${busiPrdId}" /></td>
						<td style="display: none">${productId}</td>
						<td style="display: none">${itemId}</td>
						<td>${totalAmout}</td>
						<td>${interestCapital}</td>
						<td><s:date name="validateDate" format="yyyy-MM-dd" /></td>
						<td>
							<s:if test="payType != null">
								<Field:codeTable cssClass="combox"  
									name="payType" tableName="APP___PAS__DBUSER.T_PAY_TYPE"
									value="${payType}" whereClause="pay_type in ('${payTypesString}')" 
									 />
							</s:if>
							
							<s:else>
								<Field:codeTable cssClass="combox"  nullOption="true"
									name="payType" tableName="APP___PAS__DBUSER.T_PAY_TYPE"
									value="${payType}" 
									/>
							</s:else>
						</td>
						<td>
						<s:if test="instalmentProportion != null">
							<s:select cssClass="combox" disabled="disabled" name="instalmentProportion"
								list="instalmentProportion" headerKey="0" ></s:select>
						</s:if>
						<s:else>
							<s:select cssClass="combox" name="instalmentProportion"
								list="instalmentProportions" headerKey="0" headerValue="请选择"></s:select>
						</s:else>
						</td>
						<td style="width: 200px"><input type="expandDateYMD"
							name="beginDate"
							value='<s:date name="beginDate" format="yyyy-MM-dd"/>'
							required="required" /></td>
						<td style="display: none">${acceptId}</td>
						<td style="display: none">${busiPrdId}</td>
						<td style="display: none"><fmt:formatDate
								value="${csAcceptChangeVO.acceptTime}" pattern="yyyy-MM-dd" /></td>
					</tr>
				</s:iterator>
			</tbody>
		</table>

		<div class="formBarButton">
			<s:if test="queryFlag != 1"><button type="button" class="but_blue" onclick="saveNewMsg()">保存</button>
			</s:if>
		</div>
	</div>
</div>
<!-- <form action=""> -->
<div >
<div class="divfclass">
	<h1>
		<img src="cs/img/icon/tubiao.png">变更信息录入
	</h1>
</div>	
	
	<div >
	<div class="pageFormContent">
		<dl>
			<dt>领取形式</dt>
			<dt style="display: none" id="NewsurvivalWModeFroBank">${csEndorseAZFeeVOs_change.size()==0?'':csEndorseAZFeeVOs_change.get(0).survivalWMode}</dt>
			<dd class='newSurvivalWMode'>
				<Field:codeTable cssClass="combox" nullOption="true" id="newPayMode"
					name="newPayMode" tableName="APP___PAS__DBUSER.T_SURVIVAL_W_MODE"
					value="${csEndorseAZFeeVOs_change.size()==0?'':csEndorseAZFeeVOs_change.get(0).survivalWMode}"
					onChange="newModeChange(this)" disabled="false" />
			</dd>
		</dl>
	</div>	
	<div >
		<!--变更信息的json字符串 -->
		<input id="jsons" name="jsons" type="hidden" value="" />
		<s:if
			test="csEndorseAZFeeVOs_change.get(0).survivalWMode==2 || csEndorseAZFeeVOs_change.get(0).survivalWMode==3">
			<div  id="bankAccount">
				<s:include value="/cs/pages/common/jsp/CsBankAccountsave.jsp"></s:include>
			</div>
		</s:if>
		<div   id="bankAccount" style="display: none">
			<s:include value="/cs/pages/common/jsp/CsBankAccountsave.jsp"></s:include>
		</div>
	</div>
	</div>
	<div class="divfclass" id="infoTable">
		<h1>
			<img src="cs/img/icon/tubiao.png">保单领取信息
		</h1>
		<div class="pageFormContent">
			<table id="policyBankRelationTable" class="list" width="100%">
				<thead>
					<tr>
						<th colName="logId" style="display: none">logID</th>
						<th>序号</th>
						<th colName="policyCode">保单号</th>
						<th colName="busiProdCode" style="display: none">险种代码</th>
						<th colName="itemId" style="display: none">责任组ID</th>
						<th colName="survivalWMode" inputType="select">领取形式</th>
						<th>银行代码/账号</th>
						<th colName="bankAccount" style="display: none">账号</th>
						<th colName="bankCode" style="display: none">银行代码</th>
						<th colName="customerId" style="display: none">领取人ID</th>
						<th colName="instalmentAmount" style="display: none">领取金额</th>
						<th colName="acceptId" style="display: none">acceptId</th>
						<th colName="busiPrdId" style="display: none">busiPrdId</th>
					</tr>
				</thead>
				<tbody align="center" id="policyBankMsg">
					<s:iterator value="csEndorseAZFeeBankVOs" status="st">
						<tr align="center" tr_saveStatus="1" class="instalment">
							<td style="display: none">${logId}</td>
							<td><s:property value="#st.index+1" /></td>
							<td>${policyCode}</td>
							<td style="display: none">${busiProdCode}</td>
							<td style="display: none">${itemId}</td>
							<td class='newSurvivalWMode'><Field:codeTable id="payMode"
									cssClass="combox" nullOption="true" name="payMode"
									tableName="APP___PAS__DBUSER.T_SURVIVAL_W_MODE"
									value="${survivalWMode}" onChange="setBank(this)"></Field:codeTable></td>
							<s:if test="survivalWMode==2 || survivalWMode==3">
								<td><select name="banklist" id="banklist"
									onchange="getBankCode(this)"></select></td>
							</s:if>
							<s:else>
								<td><select disabled=true name="banklist" id="banklist"
									onchange="getBankCode(this)"></select></td>
							</s:else>

							<td id="expandBankAccount" style="display: none">${bankAccount}</td>
							<td id="expandBankCode" style="display: none">${bankCode}</td>
							<td class="payeeAccountID" style="display: none">${customerId}</td>
							<td class="instalmentAmount" style="display: none">${instalmentAmount}</td>
							<td style="display: none">${acceptId}</td>
							<td style="display: none">${busiPrdId}</td>
						</tr>
					</s:iterator>
				</tbody>
			</table>
		</div>
	</div>
		<div class="formBarButton">
		<s:if test="queryFlag != 1">
			<button type="button" class="but_blue" onclick="saveChangeBankMsg()">保存</button>
			</s:if>
		</div>
</div>
<!-- </form> -->
<div class="divfclass">
	<h1>
		<img src="cs/img/icon/tubiao.png">变更后领取信息
	</h1>
	<div class="pageFormContent" id="changeBankMsgDiv">
		<table class="list" width="100%">
			<thead>
				<tr>
					<th>保单号</th>
					<th>险种代码</th>
					<th>险种名称</th>
					<th style="display: none">责任组代码</th>
					<th>账户金额</th>
					<th>生效日期</th>
					<th>领取形式</th>
					<th>领取方式</th>
					<th>领取比例%</th>
					<th>领取开始日期</th>
				</tr>
			</thead>
			<tbody align="center" id="changeBankMsg">

				<s:iterator value="csEndorseAZFeeVOs_change">
					<tr>
						<td>${policyCode}</td>
						<td>${busiProdCode}</td>
						<td><Field:codeValue
								tableName="APP___PAS__DBUSER.T_BUSINESS_PRODUCT"
								value="${busiPrdId}" /></td>
						<td style="display: none">${itemId}</td>
						<td>${interestCapital}</td>
						<td><s:date name="validateDate" format="yyyy-MM-dd" /></td>
						<td><Field:codeValue
								tableName="APP___PAS__DBUSER.T_SURVIVAL_W_MODE"
								value="${survivalWMode}" /></td>
						<td><Field:codeValue tableName="APP___PAS__DBUSER.T_PAY_TYPE"
								value="${payType}" /></td>
						<td>${instalmentProportion}</td>
						<td><s:date name="beginDate" format="yyyy-MM-dd" /></td>
					</tr>
				</s:iterator>


			</tbody>
		</table>
	</div>
</div>
</div>
<s:include value="/cs/pages/serviceitem/serviceItemBottomBar.jsp"></s:include>
<script type="text/javascript">
	$(function() {
		loadBankInfo();
	});

	function loadBankInfo() {
		var table = $("#bankAccountTable");
		var rows = table.find("tr");
		var str = "";
		//循环下拉框 
		var bankinfo = $("select[name='banklist']");
		var expandBankAccount = $("#expandBankAccount", navTab
				.getCurrentPanel());
		var expandBankCode = $("#expandBankCode", navTab.getCurrentPanel());
		var codeAccount = expandBankCode.text() + "/"
				+ expandBankAccount.text();
		var payMode = $("#payMode", navTab.getCurrentPanel());
		var first = "";
		for (var i = 0; i < bankinfo.length; i++) {
			str = "<option value=''>请选择</option>";
			var obj = bankinfo[i];
			for (var n = 1; n < rows.length; n++) {

				var bankCode = $(rows[n]).find("td").eq(5).text();
				var bankAccount = $(rows[n]).find("td").eq(6).find("input")
						.val();
				str += "<option value='"+bankCode+ "/"+bankAccount+"'>"
						+ bankCode + "/" + bankAccount + "</option>";
				if (n === 1) {
					first = bankCode + "/" + bankAccount;
				}
			}
			$(obj).html(str);

			//让当前值选中		
			if (payMode.val() == '2' || payMode.val() == '3') {
				$("select[name='banklist']").val(first);
			} else {
				$("select[name='banklist']").val('');
			}
			if (expandBankAccount.text() != '') {
				$("select[name='banklist']").val(codeAccount);
				expandBankAccount.text('');
				expandBankCode.text('');
			}
		}
	}

	function saveNewMsg() {
		var flag = 1;
		var flags = 0;
		//获取这个表格
		var $dataTable = $("#azTableInform", navTab.getCurrentPanel());
		var $trs = $dataTable.find("tbody tr");
		$trs.each(function() {
			var $tds = $(this).find("td");
			var payType = $tds.eq(15).children().children().children().find(
					"option:selected").val();
			var part = $tds.eq(16).children().children().children().find(
					"option:selected").val();
			var time = $tds.eq(17).children().val();
			var amount = $tds.eq(13).text();

			if ($tds.eq(3).children().attr("checked") == "checked") {
				if (payType != "" && part != 0 && time != "" && amount != "") {
					flags = 1;
					$(this).attr("tr_saveStatus", "1");
				} else {
					flags = 2;
				}
				if (amount <= 0) {
					flag = 0;
				}
			}
			var reg = /\d{4}-\d{2}-\d{2}/;
			if (reg.test(time)) {
			} else {
				flag = 3;
				if (time.length == 0) {
					flag = 2;
				}
			}

		});
		if (flag == 3) {
			alertMsg.error("领取开始日期格式不合法 ");
			return;
		}
		 if (flag == 0) {
			alertMsg.error("无法从金额为0的账户领取！");
			return;
		} 
		if (flags == 0) {
			alertMsg.error("请选择要保存的数据！");
			return;
		}
		if (flags == 2) {
			alertMsg.error("必填项信息未完整录入，不能受理新增领取责任，请确认。");
			return;
		}
		alertMsg
				.confirm(
						"确认保存以上信息？",
						{
							okCall : function() {
								var _jsons = "";
								var $jsonsText = $("input[name='jsons']",
										navTab.getCurrentPanel());
								var $table = $("#azTableInform", navTab
										.getCurrentPanel());
								_jsons += _cs_tableToJson($table);

								$jsonsText.val(_jsons);
								$
										.ajax({
											url : "${ctx}/cs/serviceitem_az/saveAZMsg_PA_csEndorseAZAction.action",
											type : "post",
											dataType : 'html',
											data : "jsonString="
													+ $jsonsText.val(),
											cache : false,
											success : function(response) {
												var json = DWZ
														.jsonEval(response);
												if (undefined == json.statusCode) {
													$("#policyBankMsg").html(
															response).initUI();
													loadBankInfo();
													$("a[name='newPayMode']")
															.get(0).innerHTML = '请选择';
													$("#newPayMode").get(0).value = '';
													$(
															"#op_combox_newPayMode a[class='selected']")
															.removeAttr('class');
													$(
															"#op_combox_newPayMode a:first")
															.addClass(
																	'selected');
													$("#newPayMode").get(0)
															.onchange();
												} else {
													alertMsg
															.error(json.message);
												}

											}
										});
							},
							cancelCall : function() {
							}
						});

	}

	function getBankCode(obj) {
		//给银行账号一列赋值
		var strs = obj.value.split("/");
		$(obj).parent().next().html(strs[1]);
		$(obj).parent().next().next().html(strs[0]);
	}

	function setBank(obj) {
		if (obj.value == 2 || obj.value == 3) {
			loadBankInfo();
			$(obj).parent().parent().parent().next().find("select").get(0).disabled = false;
			$(obj).parent().parent().parent().next().get(0).disabled = false;
		} else {
			$($(obj).parent().parent().parent().next().find("select").get(0))
					.attr("disabled", "disabled");
			$(obj).parent().parent().parent().next().find("select").get(0).options[0].selected = true;
			$(obj).parent().parent().parent().next().next().text('');
			$(obj).parent().parent().parent().next().next().next().text('');
		}
	}
	//function calculate(obj) {
	//获取这个表格
	//var $dataTable = $("#azTableInform", navTab.getCurrentPanel());
	//var $trs = $dataTable.find("tbody tr");
	//操作的保单号
	//var policyCode = $(obj).parent().parent().find("td:eq(5)").text();
	//操作的险种id
	//var busiId = $(obj).parent().parent().find("td:eq(7)").text();
	//领取比列
	///var realRate = $(obj).val();
	//总比例
	//	var rateTotal=0;
	//$trs.each(function() {
	//	if($(this).find("td:eq(5)").text()==policyCode && $(this).find("td:eq(7)").text()==busiId && $(this).find("td:eq(16)").children().val()!=null && $(this).find("td:eq(16)").children().val()!=""){
	//		rateTotal+=eval($(this).find("td:eq(16)").children().val());
	//	}
	//});
	//}
</script>

