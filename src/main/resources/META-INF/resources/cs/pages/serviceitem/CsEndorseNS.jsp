<!-- 新增附加险 -->
<%@ page language="java" pageEncoding="utf-8"%>
<%@ taglib uri="http://www.git.com.cn/taglib/udmp/field" prefix="Field"%>
<%@ taglib uri="/struts-tags" prefix="s"%><%@include file="/udmpCommon.jsp"%>
<%@ include file="/cs/pages/common/jsp/common_ctx.jsp" %>
<script type = "text/javascript" src="${ctx}/cs/js/cs_common.js"></script>
<link rel="stylesheet" href="${ctx}/cs/css/public.css" type="text/css">
<!-- ********帮助菜单***********begin********* -->
<s:include value="/cs/pages/common/jsp/helpMenu.jsp"></s:include>
<!-- *********帮助菜单***********end********* -->
<div  class="backgroundCollor" layOutH='140'>
		<%-- 进度条 --%> 
		<s:include value="entryProgressBar.jsp"></s:include>
		<s:include value="customerInfo_list.jsp" />
       <div class="divfclass" style="display: none;">
				<h1>客户信息</h1>
				<div class="searchBar" id="" >
						<ul class="searchContent" style="height: auto;">
							<li class="nowrap">
								<label>姓名</label>								
 								<input type="text"  readonly="readonly" name="csCustomerVO.customerName " value="${csCustomerVO.customerName }">
							</li>
							<li class="nowrap">
								 <label style="width: auto;">性别&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
								<Field:codeValue  tableName="APP___PAS__DBUSER.T_GENDER" value="${csCustomerVO.customerGender}"/></label>
							</li>
							<li class="nowrap">
								<label>出生日期</label> 
								<input type="text"  name="csCustomerVO.customerBirthday" 
											value="<s:date format="yyyy-MM-dd" name="csCustomerVO.customerBirthday"></s:date>" readonly="readonly"/>
							</li>
						</ul>
						<ul class="searchContent" style="height: auto;">
							<li class="nowrap">
								<label style="width: auto;">证件类型&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
								<Field:codeValue  tableName="APP___PAS__DBUSER.T_CERTI_TYPE" value="${csCustomerVO.customerCertType}"/></label>
							</li>
							<li class="nowrap">
								<label>证件号码</label> 
								<input type="text" name="csCustomerVO.customerCertCode" 
											 value="${csCustomerVO.customerCertiCode}" readonly="readonly"/>
							</li>
						</ul>
				</div>
		</div> 
		<!--变更前信息  -->
		<div class="divfclass">
			<h1><img src="cs/img/icon/tubiao.png">变更前信息</h1>
			<div class="divfclass">
				<table class="list" width="100%">
					<thead>
						<tr> 
						    <th style = "display:none" colName="changeId">changeId</th>
						    <th  style = "display:none" colName="policyId">policyId</th>
						    <th style = "display:none" colName="policyChgId">policyChgId</th>
						    <th style = "display:none" colName="policyCode">policyCode</th>
							<th>选择</th>
							<th>险种代码</th>
							<th>险种名称</th>
							<th style ="display: none"></th>
							<th>保额/份数</th>
							<th>每份保额</th>
							<th>保费</th>
							<th>被保人</th>
							<th>下期缴费日</th>
							<th style = "display:none" colName="acceptId">acceptId</th>
						</tr>
					</thead>
					<tbody id="tableInfo">
						<s:iterator value="csAddSubBusiProdVOS" id="policyInfo" status="st">
							<tr align="center" <s:if test="queryFlag==1">disabled="disabled"</s:if> >
							   <td style ="display: none"><s:property value="changeId"/></td>	
							   <td id="policyIdth_" style ="display: none"><s:property value="policyId"/></td>		
							   <td style ="display:none"><s:property value="policyChgId"/></td>	
							   <td style ="display:none "><s:property value="policyCode" />
							   <input type="hidden"  value="${policyCode}" id="policyCode${st.index}">
							   </td>	
							   <s:if test="isMaster==true"><td><input onclick="getBusiItem(this)" class = "myClass" type="radio"  name="radio" value="${busiItemId }"/></td></s:if>
							   	<s:else>
								<td></td>
								</s:else>
								<td>${productCodeSys }</td>
								<td><Field:codeValue tableName="APP___PAS__DBUSER.T_BUSINESS_PRODUCT" value="${productNameSys }"/></td>
								<td style ="display: none">${productNameSys}</td>
								<td>${amout }</td>
								<td>${perAmout}</td>
								<td>${stdPremAf }</td>
								<td>${insured }</td>
								<td><s:date name="payDueDate" format="yyyy-MM-dd"></s:date></td>
								<td style ="display: none"><s:property value="acceptId"/></td>		
							</tr>
						</s:iterator>
					</tbody>
				</table>
			</div>
		</div>
		<div class="divfclass" >
			<!-- 健康告知的引用页面 -->
			<h1><img src="cs/img/icon/tubiao.png">投保人/被保人健康告知</h1>
		</div>
		<div id="insertSurveyNS"  class="divfclass"></div>
		<!-- <div class="panel collapse" style="margin: 10px;">
			健康告知的引用页面
			如果投保人存在豁免责任的时候显示 反之隐藏 
		
				<h1>投保人健康告知</h1>
					<div id="holderSurvey">
				</div>
		</div> -->
<!-- 上线外功能		<div class="divfclass" >
			社保标识变更引用页面 
			<h1><img src="cs/img/icon/tubiao.png">客户社保标识变更</h1>
		</div>
		<div id="insertSurveySO"  class="divfclass"></div> -->
	<!--  变更前社保状态-->
	<!-- 151299 start -->
	<s:if test=" multiInsuredFlag==1 ">
	<div class="divfclass" style="max-width:50%;">
		<h1>
			<img src="cs/img/icon/tubiao.png">被保人社保状态
		</h1>
		<div class="divfclass" >
		<form id="customerSocialSecuForm" action="${ctx}/cs/serviceitem_so/saveSocialSecuList_PA_csEndorseSOAction.action" method="post" class="pageForm required-validate"
			  onsubmit="return navTabSearch(this,'addSubBusiProdNew')">
			<input type="hidden" id="changeId" name="csCustomerSocialSecuVO.changeId" value="${csCustomerSocialSecuVO.changeId }"> 
			<input type="hidden" id="acceptId" name="csCustomerSocialSecuVO.acceptId" value="${csCustomerSocialSecuVO.acceptId }"> 
			<input type="hidden" id="isSaved" name="csCustomerSocialSecuVO.isSaved" value="${csCustomerSocialSecuVO.isSaved }">
			<input type="hidden" id="newSocialSecu" name="csCustomerSocialSecuVO.newSocialSecu" value="0">
			<input type="hidden" name="changeId" value="${csCustomerSocialSecuVO.changeId }"> 
			<input type="hidden" name="acceptId" value="${csCustomerSocialSecuVO.acceptId }">
			<input type="hidden" id="resetFlag" name="csCustomerSocialSecuVO.resetFlag" value="${csCustomerSocialSecuVO.resetFlag}">
			<input type="hidden" id="multiInsuredFlag" value="${multiInsuredFlag }"> 
		</form>
			<table class="list" id="csCustomerSocialSecuVOList" table_saveStatus="1">
				<thead>
					<tr>
						<th colName="customerId" inputType="input">被保险人</th>
						<th colName="socialSecu" inputType="input">当前社保状态</th>
						<th colName="newSocialSecu" inputType="select">变更后社保状态</th>
						<th colName="changeId" inputType="input" style="display: none">changeId</th>
						<th colName="acceptId" inputType="input" style="display: none">acceptId</th>
					</tr>
				</thead>
				<tbody id="csCustomerSocialSecuVOListBody">
					<s:iterator value="csCustomerSocialSecuVOList" var="sr"
						status="st1">
						<tr tr_saveStatus="1">
							<td><input type="hidden" id="customerId" name="csCustomerSocialSecuVO.customerId" value="<s:property value="customerId"/>"/>${csCustomerVO.customerName}</td>
							<td><input type="hidden" id="oldSocialSecu" value="${socialSecu}"><Field:codeValue tableName="APP___PAS__DBUSER.T_YES_NO" value="${socialSecu}"/></td>
							<td>
								<s:select list="#{0:'否', 1:'是'}" id="newSocialSecu" name="csCustomerSocialSecuVO.newSocialSecu" listKey="key"
											listValue="value" value="newSocialSecu">
								</s:select>
							</td>
							<td style="display: none"><input type="hidden" id="changeId" name="csCustomerSocialSecuVO.changeId" value="${changeId }"> </td>
							<td style="display: none"><input type="hidden" id="acceptId" name="csCustomerSocialSecuVO.acceptId" value="${acceptId }"> </td>
						</tr>
					</s:iterator>
				</tbody>
			</table>
		
		</div>
		<div  style="float:left; margin-left:10%;">
			<table style="width: 100%">
				<tr>
					<td style="width: 120px">
						<button class="but_blue" type="button"
							onclick="updateSocialSecu('customerSocialSecuForm','addSubBusiProdNew')">社保状态保存</button>
					</td>
					<td style="width: 120px">
						<button class="but_blue" type="button" onclick="resetSOList('customerSocialSecuForm','addSubBusiProdNew')">社保状态重置</button>
					</td>
				</tr>
			</table>
		</div>
	</div>
	</s:if>
	<s:else>
	<!-- 151299 end -->
	<div class="divfclass">
		<h1>
			<img src="cs/img/icon/tubiao.png">客户社保状态变更信息
		</h1>
			<div class="panel" style="margin: 10px">
				<h1>客户社保状态信息</h1>
				<div class="pageFormContent">
					<div style="width: 32%;float:left; margin-left:1%;">
						<dl>
							<dt>社保状态变更前</dt>
							<dd>
								<s:select list="#{0:'否', 1:'是',2:''}" id="socialSecu"
									name="csCustomerSocialSecuVO.socialSecu" disabled="true"
									listKey="key" listValue="value"
									value="csCustomerSocialSecuVO.socialSecu">
								</s:select>
							</dd>
						</dl>
					</div>
					<div style="width: 32%;float:left; margin-left:1%;">
						<form id="customerSocialSecuForm"
							action="${ctx}/cs/serviceitem_so/saveSocialSecu_PA_csEndorseSOAction.action"
							method="post" class="pageForm required-validate"
							onsubmit="return navTabSearch(this,'addSubBusiProdNew')">
							<input type="hidden" id="customerId"
								name="csCustomerSocialSecuVO.customerId"
								value="${csCustomerSocialSecuVO.customerId}"> <input
								type="hidden" id="changeId"
								name="csCustomerSocialSecuVO.changeId"
								value="${csCustomerSocialSecuVO.changeId }"> <input
								type="hidden" id="acceptId"
								name="csCustomerSocialSecuVO.acceptId"
								value="${csCustomerSocialSecuVO.acceptId }"> <input
								type="hidden" id="isSaved" name="csCustomerSocialSecuVO.isSaved"
								value="${csCustomerSocialSecuVO.isSaved }"> <input type="hidden"
								name="changeId" value="${csCustomerSocialSecuVO.changeId }"> <input type="hidden"
								name="acceptId" value="${csCustomerSocialSecuVO.acceptId }">
							<div style="width: 60%">
								<dl>
									<dt>社保状态变更后</dt>
									<dd>
										<s:select list="#{0:'否', 1:'是'}" id="newSocialSecu"
											name="csCustomerSocialSecuVO.newSocialSecu" listKey="key"
											listValue="value"
											value="csCustomerSocialSecuVO.newSocialSecu">
										</s:select>
										<%-- <input type="text" id="newSocialSecu" name="csCustomerSocialSecuVO.newSocialSecu" value="${csCustomerSocialSecuVO.newSocialSecu}"> --%>
									</dd>
								</dl>
							</div>
						</form>
					</div>
					<div  style="width: 32%;float:left; margin-left:1%;">
						<table style="width: 100%">
							<tr>
								<td style="width: 120px">
									<button class="but_blue" type="button"
										onclick="updateSocialSecu('customerSocialSecuForm','addSubBusiProdNew')">社保状态保存</button>
								</td>
								<td style="width: 120px">
									<button class="but_blue" type="button" onclick="resetSO()">社保状态重置</button>
								</td>
							</tr>
						</table>
					</div>
				</div>
			</div>
		</div>	
		<!-- 151299 start -->
		</s:else>
		<!-- 151299 end -->
	<form  id ="addSubBusiProdForm" "
		 method="post"
		onsubmit="return navTabSearch(this,'addSubBusiProdNew')">
		<div class="divfclass"   id="newAddBusiInfo" <s:if test="queryFlag==1">style="display:none"</s:if><s:else>style="margin: 10px"</s:else> >
			<div class="divfclass" ><h1><img src="cs/img/icon/tubiao.png">新增产品</h1></div>
			
			<div class="pageFormInfoContent">
				<input type="hidden" name="iswavid" value="${policyHolderVO.iswavid}" id="iswavid">
				<input type="hidden" name="policyHolderId" value="${policyHolderVO.customerId}" id="policyHolderId">
				<input type="hidden" name="insuredId" value="${policyInsurdVO.customerId}" id="insuredId">
			    <input  type="hidden"name="customerId" value="${customerId}" id="customerId">
		        <input type="hidden" name="changeId" value="${changeId }">
		        <input type="hidden" name="acceptId" value="${acceptId }">
		        <input type="hidden" id="queryFlag" value="${queryFlag }">
			    <input type="hidden"  id="changeId" name="csAddSubBusiProdVO.changeId" value="${changeId }"/>
		        <input type="hidden"  id="policyChgId" name="csAddSubBusiProdVO.policyChgId" value="${policyChgId }"/>
		        <input type="hidden"  id="csPolicyId" name="csAddSubBusiProdVO.policyId" value="${policyId }"/>
		        <input type="hidden"  id="busiItemId" name="csAddSubBusiProdVO.busiItemId" value="${busiItemId }"/>
		        <input type="hidden"  id="csPolicyCode" name="csAddSubBusiProdVO.policyCode" value="${policyCode }"/>
		        <input type="hidden"  id="acceptId" name="csAddSubBusiProdVO.acceptId" value="${acceptId }"/>
		        <!-- 所属产品 code -->
		        <input  type="hidden" id="productCodeSys" name="csAddSubBusiProdVO.productCodeSys" value="${productCodeSys }"/>
		        <!-- 所属产品 id -->
		        <input type="hidden" id="productNameSys" name="csAddSubBusiProdVO.productNameSys" value="${productNameSys }"/>
				<dl>
					<dt>附加险代码</dt>
					<dd <s:if test="queryFlag==1">disabled="disabled"</s:if>>
						<!--<select id="otherProductCodeSys" name="csAddSubBusiProdVO.otherProductCodeSys" onChange="selectAddSubBusi(this)" style="width:141px">				
						</select>-->
						<select id="otherProductCodeSys" name="csAddSubBusiProdVO.otherProductCodeSys" onChange="selectAddSubBusi(this)" style="width:141px">				
							<option value="0">请选择</option>
							<s:iterator value="addRisksResVOs" var="businessProductVO">
								<option value="${businessProductVO.productCodeSys}">${businessProductVO.productNameSys}</option>
							</s:iterator>
						</select>
					</dd>
				</dl>
				<dl id ='period812'>
					<dt>保险期间</dt>
						<input  type="text" disabled="disabled" value="1年"  style="width: 141px">
					</dd>
					
				</dl>
				<dl id ='period461'>
					<dt>保险期间</dt>
						<s:select list="#{'Y-20':'20年','Y-30':'30年', 'A-60':'至60周岁保单生效对应日', 'A-70':'至70周岁保单生效对应日', 'A-80':'至80周岁保单生效对应日'}" id="coveragePeriod461"
						name="coveragePeriod461" listKey="key" listValue="value" onchange="chageCoveragePeriod461(this)">
						</s:select>
						<input type="hidden"  id="coveragePeriodFor461" name="csAddSubBusiProdVO.coveragePeriod" value="Y"  />
						<input type="hidden" id="coverageYearFor461" name="csAddSubBusiProdVO.coverageYear"  value="20" />
					</dd>
					
				</dl>
				<dl id ='manner812'>
					<dt>交费方式</dt>
						<input  type="text" disabled="disabled" value="一次交清"  style="width: 141px"/>
					</dd>
					
				</dl>
				<dl id ='chargePeriodFor461'>
					<dt>交费方式</dt>
						<input type="text" id="premFreqFor461_val"  readonly="readonly" />
						<input id="premFreq461" type="hidden" name="csAddSubBusiProdVO.premFreq"   disabled="disabled" />
						<input id="chargePeriod461" type="hidden" name="csAddSubBusiProdVO.chargePeriod"  disabled="disabled"/>
					</dd>
				</dl>
				<dl id ='chargeYearFor461'>
					<dt>交费期间</dt>
						<input type="text" id="chargeYearFor461" name="csAddSubBusiProdVO.chargeYear" disabled="disabled" />
					</dd>
				</dl>
				<dl id ='amount461'>
					<dt>基本保险金额</dt>
						<input  type="text"   id="amountFor461" name="csAddSubBusiProdVO.amout"   />
					</dd>
				</dl>
				<dl id="_insuredDL">
					<dt>被保人</dt>
					<dd <s:if test="queryFlag==1">disabled="disabled"</s:if>>
						<select name="policyInsurdVO.customerId"  style="width:141px" id="_insured">									
						</select>
					</dd>
					
				</dl>
				<input type="hidden" id="policyorgfiag" name="policyHolderVO.policyorgfiag" value="${policyHolderVO.policyorgfiag }">
				<dl id="applicantSpePeople">
					<dt>特殊人员类型</dt>
					<s:select list="#{0:'请选择',1:'脱贫户', 2:'边缘户'}" id="isMeetPovStandardFlag"
						name="csAddSubBusiProdVO.meetPovStandardFlag" listKey="key"
						listValue="value"
						value="csAddSubBusiProdVO.meetPovStandardFlag">
					</s:select>
				</dl>
				
				<div  id="loadCsProduct"></div> 
				<dl style="display: none;" id="GuaranteeDiv">
					<dt>保障计划</dt>
					<dd>
						<select  style="width:141px" onchange="guaranteeProjectChange()" id ="GuaranteeProject">
							<option>请选择</option>
						</select>
						<input type="hidden" name="pagecfgElementValueBO.code">
						<input type="hidden" id="GuaranteeProjectValue" value=""/><!-- 保障计划的值-->
					</dd>
				</dl>
				<dl id ='amountDL'>
					<dt>保额/份数</dt>
					<dd>
						<input class="number" type="text"  maxlength="15"  onmouseout="perIsNumber()"  onchange="changeGuaranteeProject()" id ="perAmout" <s:if test="queryFlag==1">disabled="disabled"</s:if> name="csAddSubBusiProdVO.perAmout" style="width: 135px" />
						<s:select list="#{0:'请选择',5000:'5000',10000:'10000', 20000:'20000',30000:'30000',40000:'40000', 50000:'50000'}" 
						id="perAmoutFor812" listKey="key" listValue="value"  onchange="changePerAmoutFor812()"  style="width: 141px" ><s:if test="queryFlag==1">disabled="disabled"</s:if></s:select>
					</dd>
				</dl> 
				<dl id="isWaived">
					<dt>每期豁免保费</dt>
					<dd>
						<input class="number" type="text"  maxlength="15" id ="waivedStdAll" disabled="disabled" style="width: 135px" />
					</dd>
				</dl>
				<dl id ='premDL'>
					<dt>保费</dt>
					<dd>
						<input class="number" type="text"  maxlength="15"  onmouseout="perIsNumber()"  id ="prem" <s:if test="queryFlag==1">disabled="disabled"</s:if> name="csAddSubBusiProdVO.prem" style="width: 135px" />
					</dd>
				</dl>
				<dl id ='chargePeriodDL'>
					<dt>缴费年期类型</dt>
					<dd <s:if test="queryFlag==1">disabled="disabled"</s:if>>
					    <Field:codeTable cssClass="combox" id="chargePeriod" name="chargePeriod" tableName="APP___PAS__DBUSER.T_CHARGE_PERIOD"/>
					</dd>
				</dl>
					<dl id ='chargeYearDL'>
					<dt>缴费年期</dt>
					<dd>
					   <%--  <Field:codeTable name="csAddSubBusiProdVO.chargeYear" tableName="APP___PAS__DBUSER.T_CHARGE_PERIOD"/> --%>
						<input  class="number" id="chargeYear" <s:if test="queryFlag==1">disabled="disabled"</s:if> type="text" name="chargeYear" style="width: 135px" /> 
					</dd>
				</dl>
				<dl id ='coveragePeriodDL'>
					<dt>保障年期类型</dt>
					<dd <s:if test="queryFlag==1">disabled="disabled"</s:if>>
					 <Field:codeTable cssClass="combox" 
					 id = 'coveragePeriodDL_2' 
					 name="coveragePeriod" 
					 tableName="APP___PAS__DBUSER.T_COVERAGE_PERIOD" 
					 onChange ="changeCoveragePeriod(this);"/>
						<!-- <input type="text" name="csAddSubBusiProdVO.perAmout" style="width: 135px" /> -->
					</dd>
				</dl>
				<dl id ='coverageYearDL'>
					<dt>保障年期</dt>
					<dd>
					   <%--  <Field:codeTable name="csAddSubBusiProdVO.chargeYear" tableName="APP___PAS__DBUSER.T_CHARGE_PERIOD"/> --%>
						<input id='coverageYear' <s:if test="queryFlag==1">disabled="disabled"</s:if> type="text" name="coverageYear" style="width: 135px" class="number" /> 
					</dd>
				</dl>
				<dl id ='premFreqDL'>
					<dt>缴费方式</dt>
					<dd>
					  <Field:codeTable  id="premFreq" name="premFreq" cssClass="combox" tableName="APP___PAS__DBUSER.T_CHARGE_MODE" />						
					</dd>
				</dl>
				<dl id='deductibleFranchiseDL'>
					<dt>免赔额</dt>
					<dd>
					  <input id='deductibleFranchise'  type="text" name="csAddSubBusiProdVO.deductibleFranchise" style="width: 135px" class="number" />						
					</dd>
				</dl>
				<dl id='payoutRateDL'>
					<dt>赔付比例</dt>
					<dd>
					  <input id='payoutRate' type="text" name="csAddSubBusiProdVO.payoutRate" style="width: 135px" class="number"   />	<span>%	</span>				
					</dd>
				</dl>
 				<dl id ='isRenew'>
					<dt>是否续保</dt>
					<dd>
						<dd>
										<s:select list="#{2:'请选择',1:'是',0:'否'}" id="isRenew"
											name="csAddSubBusiProdVO.renew" listKey="key"
											listValue="value"
											value="csAddSubBusiProdVO.renew">
										</s:select>
						</dd>
					
					</dd>
				</dl> 
				<div <s:if test="queryFlag==1">style="display:none"</s:if><s:else></s:else> >
				
				<table style="width:100%">
				<tbody>
				<tr>
				<td></td>
				<td style="width:70px;">
					<button type="button" class="but_blue" onclick="addBusiProd('addSubBusiProdForm','addSubBusiProdNew')">新增</button>
				 </td>
				 <td></td>
				 </tr>
				   </tbody>
				 </table>
				</div>				
			</div>
			
			
		</div>
	</form>
	<div id= "payInfoSave" class="divfclass" style="display:none">
		<input type="hidden"  id="isLongBusi"  value="${isLongBusi }"/>
		<s:include value="CsEndorseNS_payInfo.jsp"></s:include>
	</div>
	
		<!--变更后保单信息  -->		
		<div id= "addSubBusiProdNew" class="divfclass" >
			<s:include value="CsEndorseNS_new.jsp"></s:include>
		</div>	
		<s:include value="/cs/pages/serviceitem/serviceItemBottomBar.jsp"></s:include>
</div>

<script type="text/javascript">

//保障年期类型为“终身”时，默认保障年限为999
function changeCoveragePeriod(obj){	
	var $coverYearBox = $("#coverageYear",navTab.getCurrentPanel());
	var $selectOptions = $(obj).find("option:selected");
	var coeragePeriod =  $selectOptions.val();
	if(coeragePeriod == "W"){//终身
		$coverYearBox.val(999);
		//$coverYearBox.attr("readonly","readonly");
		$coverYearBox.attr("disabled","disabled");
	} else {
	//	$coverYearBox.attr("readonly","");
		$coverYearBox.removeAttr("disabled");
	}
}
</script>

<script type="text/javascript">
/* 点击重置按钮*/
function resetSO(){
	alertMsg.confirm("请确认是否需要重置社保状态信息？",{
		okCall:function(){
			var _socialSecu = $("#socialSecu", navTab.getCurrentPanel()).val();//变更前状态
			$("#newSocialSecu", navTab.getCurrentPanel()).val(_socialSecu);//设置成变更前状态
				 $("#newbusiItemDiv").show();
				 $("#customerSocialSecuForm",navTab.getCurrentPanel()).submit();
		},
	    cancelCall:function(){
	    	
	    }
		
	}); 
	
	
}
/*151299 485点击重置按钮*/
function resetSOList(formId,boxId){
	alertMsg.confirm("请确认是否需要重置社保状态信息？",{
		okCall:function(){
			$form=$("#" + formId, navTab.getCurrentPanel());
			$("#"+boxId,navTab.getCurrentPanel()).show();
			$("#resetFlag", navTab.getCurrentPanel()).val("1");
			$.post("${ctx}/cs/serviceitem_ns/saveSocialSecuInfo_PA_csEndorseNSAction.action",
				$("#customerSocialSecuForm").serialize(),
				function(data){
					debugger;
					var json = DWZ.jsonEval(data);
					if (json.statusCode == DWZ.statusCode.error) {
						if (json.message && alertMsg){
							$("#resetFlag", navTab.getCurrentPanel()).val("");
							alertMsg.error(json.message);
						}
					} else{
						$("#resetFlag", navTab.getCurrentPanel()).val("");
						var sameCustomers = $("#csCustomerSocialSecuVOListBody").find("select[id='newSocialSecu']");
						sameCustomers.each(function(){
							$(this).val($(this).closest("tr").find("input#oldSocialSecu").val());
						});
						$form.attr("action","${ctx}/cs/serviceitem_ns/queryNewBusiProd_PA_csEndorseNSAction.action");
						$form.submit();
					}
				})
		},
	    cancelCall:function(){
	    	
	    }
		
	}); 
	
	
}
/** 保存该保单的社保状态变更信息  */
// 险种信息录入区域--保存按钮--保存录入信息
function updateSocialSecu(formId,boxId){
	
	var _changeId=$("#changeId", navTab.getCurrentPanel()).val();
	var _newSocialSecu = $("#newSocialSecu",navTab.getCurrentPanel()).val();
	var _acceptId = $("#acceptId",navTab.getCurrentPanel()).val();
	var _customerId=$("#customerId",navTab.getCurrentPanel()).val();
	var _flag = "1";
	var saveFlag = "update";
	var _socialSecu = $("#socialSecu",navTab.getCurrentPanel()).val();
	alertMsg.confirm("社保状态会影响保费、理赔金额，请再次与客户确认！",{
		 okCall: function(){
			 
			 if(_newSocialSecu == _socialSecu){
				 alertMsg.confirm("变更后的社保状态与原社保状态相同，请确认最新社保状态。",{
					 okCall: function(){
						 newAddCheck(formId,boxId,_newSocialSecu,_flag,saveFlag);	
					 },
					 cancelCall: function(){
					 }
				 });
			 }else{
				 newAddCheck(formId,boxId,_newSocialSecu,_flag,saveFlag);
			 };
		 },
		 cancelCall: function(){
		 }
	});
}

function saveSocialSecuNew(formId,boxId){
	$form=$("#" + formId, navTab.getCurrentPanel());
	$("#"+boxId,navTab.getCurrentPanel()).show();
	/* 开始非阻断校验 */
	debugger;
	//151299 start
	var multiInsuredFlag=$("#multiInsuredFlag",navTab.getCurrentPanel()).val();
	if(multiInsuredFlag == '1'){
		var $jsons = $("#csCustomerSocialSecuVOList",navTab.getCurrentPanel());
   		var _jsons = _cs_tableToJson($jsons);
	   	_jsons = eval('('+ _jsons + ')');
	   	_jsons = JSON.stringify(_jsons);
		var sendData = "jsonSocialSecuVOList="+ _jsons;
		$.post("${ctx}/cs/serviceitem_ns/saveSocialSecuInfoList_PA_csEndorseNSAction.action",
				sendData,
				function(data){
				var json = DWZ.jsonEval(data);
				if (json.statusCode == DWZ.statusCode.error) {
					if (json.message && alertMsg){
							alertMsg.error(json.message);
						}
					} else{
						$form.attr("action","${ctx}/cs/serviceitem_ns/queryNewBusiProd_PA_csEndorseNSAction.action");
						$form.submit();
						alertMsg.correct("保存成功！");	
				}
		});
	}else{
	//151299 end
	$.post("${ctx}/cs/serviceitem_ns/saveSocialSecuInfo_PA_csEndorseNSAction.action",
			$("#customerSocialSecuForm").serialize(),
			function(data){
			var json = DWZ.jsonEval(data);
			if (json.statusCode == DWZ.statusCode.error) {
				if (json.message && alertMsg){
						alertMsg.error(json.message);
					}
				} else{
					$form.attr("action","${ctx}/cs/serviceitem_ns/queryNewBusiProd_PA_csEndorseNSAction.action");
					$form.submit();
					alertMsg.correct("保存成功！");	
			}
	});
	//151299 start
	}
	//151299 end
}

function addBusiProd(formId,boxId){
	debugger
	var aa = $("#otherProductCodeSys")
	var _otherProductCodeSys = $("#otherProductCodeSys",navTab.getCurrentPanel()).val();
	var _newSocialSecu = $("#newSocialSecu",navTab.getCurrentPanel()).val();
	/**if(_otherProductCodeSys == '00552000'){
		alertMsg.error('此附加险不能附加于主险，请重新输入。');
		return false;
	} */
	var radio=$(".myClass:checked");
	var _flag = "1";
	var saveFlag = "add";
	if(radio.length == 0){
		alertMsg.error("请选择主险");
	}else{
		var reg = /^[0-9]+(.[0-9]+)?$/;
		var _prem = $("#prem").val();
		var _perAmount = $("#perAmout",navTab.getCurrentPanel()).val();
		var _chargeYear = $("#chargeYear",navTab.getCurrentPanel()).val();
		var _coverageYear = $("#coverageYear",navTab.getCurrentPanel()).val();
		var _premFreqFor461_val = $("#premFreqFor461_val",navTab.getCurrentPanel()).val();
		var _chargeYearFor461 = $("#chargeYearFor461",navTab.getCurrentPanel()).val();
    	var  _isRenew = $("#isRenew option:selected").val();
    	if($("#premFreqFor461_val").is(":visible")){
    		if(_premFreqFor461_val == null || _premFreqFor461_val.trim() == ""){
    			alertMsg.warn("险种461附加爱满分定期寿险的交费方式为：一次交清、年交，且需要同主险一致。");
				return false;
    		}
    	}
    	if($("#chargeYearFor461").is(":visible")){
    		if(_chargeYearFor461 == null || _chargeYearFor461.trim() == ""){
    			alertMsg.warn("险种461附加爱满分定期寿险的交费期间需要同主险剩余交费期间一致。");
				return false;
    		}
    	}
		if($("#prem").is(":visible")){
			if(_prem != null && _prem.trim() != ""){
				if(!reg.test(_prem)){
					alertMsg.warn("保费只能是数字！");
					return false;
				}
			}else{
				alertMsg.warn("保费不能为空！");
				return false;
			}
			
		}
		if($("#perAmout").is(":visible")){
			if (_otherProductCodeSys != '00554000') {
				if(_perAmount != null && _perAmount.trim() != ""){
					if(!reg.test(_perAmount)){
						alertMsg.warn("保额只能是数字！");
						return false;
					}
				}/* else{
					alertMsg.warn("保额不能为空！");
					return false;
				} */
			}
		}
		if($("#chargeYear").is(":visible") ){
			if(_chargeYear != null && _chargeYear.trim() != ""){
				if(!reg.test(_chargeYear)){
					alertMsg.warn("缴费年期只能是数字！");
					return false;
				}
			}else{
				alertMsg.warn("缴费年期不能为空！");
				return false;
			}
			
		}
		
		if($("#coverageYear").is(":visible")){
			if(_coverageYear != null && _coverageYear.trim() != ""){
				if(!reg.test(_coverageYear)){
					alertMsg.warn("保障年期只能是数字！");
					return false;
				}
			}else{
				alertMsg.warn("保障年期不能为空！");
				return false;
			}
			
		}
		//applicantSpePeopleSelect
		if($("#applicantSpePeople").is(":visible")){
			var applicantSpePeopleSelectedValue=$("#applicantSpePeopleSelect option:selected").val(); //获取选中的项并拿到选中项的值
			console.log(applicantSpePeopleSelectedValue);
			//alert(applicantSpePeopleSelectedValue);
		}
		if($("#isRenew").is(":visible")){
			if(_isRenew == "2"){
				alertMsg.warn("请录入‘是否续保’信息。");
				return false;
			}
		}
		
		/* 开始非阻断验证 */
		alertMsg.confirm("请确认是否需要保存录入的信息", {
			okCall : function() {
				if($("#isRenew").is(":visible") && _isRenew == "0"){
					alertMsg.confirm("新增险种已勾选“不续保”，根据申请本次新增险种保险期间（1年）届满将不自动续保，届满即终止。", {
						okCall : function() {
							newAddCheck(formId,boxId,_newSocialSecu,_flag,saveFlag);
						},
						cancelCall : function() {
						}
						
					})
				}else{
					newAddCheck(formId,boxId,_newSocialSecu,_flag,saveFlag);
				}
			},
			cancelCall : function() {
			}
			
		})
	}	
		
	
}
/* 非阻断 后台验证 */
function newAddCheck(formId,boxId,_newSocialSecu,_flag,saveFlag){
	debugger;
	
	var productCodeSys = $("#otherProductCodeSys").val()
	if(productCodeSys!='00461000'){
		$("#premFreq461", navTab.getCurrentPanel()).attr("disabled",true);
		$("#chargePeriod461", navTab.getCurrentPanel()).attr("disabled",true);        		
		$("#premFreq461", navTab.getCurrentPanel()).attr("disabled",true);
		$("#coveragePeriodFor461", navTab.getCurrentPanel()).attr("disabled",true);
		$("#coverageYearFor461", navTab.getCurrentPanel()).attr("disabled",true);
	}
	//151299 start
	var multiInsuredFlag=$("#multiInsuredFlag",navTab.getCurrentPanel()).val();
	if(multiInsuredFlag == '1'){
		var $jsons = $("#csCustomerSocialSecuVOList",navTab.getCurrentPanel());
   		var _jsons = _cs_tableToJson($jsons);
	   	_jsons = eval('('+ _jsons + ')');
	   	_jsons = JSON.stringify(_jsons);
	   	_newSocialSecu += "&jsonSocialSecuVOList="+ _jsons;
	}
	//151299 end
	$form=$("#" + formId, navTab.getCurrentPanel());
	
				$.post("${ctx}/cs/serviceitem_ns/checkSocialSecuInfo_PA_csEndorseNSAction.action?newSocialSecu="+_newSocialSecu+"&checkFlag="+_flag,
						$form.serialize(),
						function(data){
					debugger;
					var json = DWZ.jsonEval(data);
					var msg = json.message;
					if (json.statusCode == DWZ.statusCode.error) {
						if (json.message && alertMsg)
							//弹出阻断异常信息?
							alertMsg.error(json.message);
					}
					if(json.statusCode == '200'){
						var msgArray = msg.split('_');
						var len = msgArray.length ;
						//js的特殊性返回空的msg截取之后长度也是2
						if(!(!msgArray[0])){
							//索引1 不为空 返回了非阻断信息
							//返回了阻断信息 弹出信息   点击确定 ==> 去验证
							alertMsg.confirm(msgArray[0],{
								okCall : function() {
									//如果这已经是最后一个验证直接去保存
									if(msgArray[1] == "stop"){
										if(saveFlag == "add"){
											//去保存新增附加险
											saveNewBusiProd(formId,boxId);
										}else{
											//去变更社保状态
											saveSocialSecuNew(formId,boxId);
										}
									}else{
										//不是最后一个继续验证
										_flag = _flag + "1";
										newAddCheck(formId,boxId,_newSocialSecu,_flag,saveFlag);
									}
								},
								cancelCall : function() {
								}
							})
						}else if(msgArray[1] === "stop"){//索引1为空 通过了此次验证 如果有结束标记 去保存
							
							if(saveFlag == "add"){
								//去保存新增附加险
								saveNewBusiProd(formId,boxId);
							}else{
								//去变更社保状态
								saveSocialSecuNew(formId,boxId);
							}
						}else{//索引1为空 通过了此次验证 如果没有结束标记 去继续验证
							_flag = _flag + "1";
							newAddCheck(formId,boxId,_newSocialSecu,_flag,saveFlag);
						}
						
					}
					
				})
}

/* 保存的 post 方法*/ 

function saveNewBusiProd(formId,boxId){
	$form=$("#" + formId, navTab.getCurrentPanel());
	$("#"+boxId,navTab.getCurrentPanel()).show(); 
	$.ajax({
		type : "post",
		url : "${ctx }/cs/serviceitem_ct/checkStopBusiProd_PA_csEndorseNSAction.action",
		data : $("#"+formId, navTab.getCurrentPanel()).serialize(),
		success : function(data) {
			var json = DWZ.jsonEval(data);
			if (json.statusCode == DWZ.statusCode.error) {
				alertMsg.error(json.message);
			} else if (json.statusCode == DWZ.statusCode.timeout) {
				DWZ.loadLogin();
			}else{
				$.post("${ctx}/cs/serviceitem_ns/saveNewBusiProd_PA_csEndorseNSAction.action",
						$("#addSubBusiProdForm").serialize(),
						function(data){
							var json = DWZ.jsonEval(data);
							if (json.statusCode == DWZ.statusCode.error) {
							if (json.message && alertMsg)
								alertMsg.error(json.message);
							} else{
								$form.attr("action","${ctx}/cs/serviceitem_ns/queryNewBusiProd_PA_csEndorseNSAction.action");
								$form.submit();
								alertMsg.correct("保存成功！");	
							}
						});
			}
		},
	});
}

/* -- 点击单选按钮 为 新增附件险 页面复赋值*/
function getBusiItem(obj){	
	//0 changeid 
	//1 policyId
	//2 policyChgId
	//4选择  busiitemid 
	//5险种代码 
	//6 所属产品 id （险种名称 ）
	//13 acceptId 
	var radio=$(".myClass:checked");
	var _busiItemId = radio.val();		
	var changeId=$("#changeId").val();	
	var policyId=$(obj).parent().parent().find("td:eq(1)").text().trim();	
	var productCodeSys= $(obj).parent().parent().find("td:eq(5)").text().trim();
	
	//提交 可查询 该主险的附加险 都有哪些   为 附件险字段赋值  	
	$.ajax({
	 	   type : "post",
	       dataType : "text",
	       url : "${ctx}/cs/serviceitem_ns/queryBusiProd_PA_csEndorseNSAction.action",
	       data : 'csAddSubBusiProdVO.productCodeSys='+productCodeSys+'&csAddSubBusiProdVO.changeId='+changeId+'&csAddSubBusiProdVO.policyId='+policyId,
	       success : function(data) {
	    	   debugger;

	     	     var adr = jQuery.parseJSON(data); 
		    	   if(adr.length === 0){
		    		   alertMsg.warn("该主险没有附加险，不可以做新增附加险保全。");
		    		   return null;
	    		   }
	             var str="";
	             for (var i = 0; i < adr.length; i++) {                
	                str+=("<option value='"+adr[i].productCodeSys+"' bisPrdId='"+ adr[i].businessPrdId+"'>"
	                             + adr[i].productNameSys + "</option>");                   
	             }
	             $("#otherProductCodeSys").html(str);
	             selectAddSubBusi( $("#otherProductCodeSys"));
	       }
	   });
	//policyChgId
	$("#policyChgId").attr('value',$(obj).parent().parent().find("td:eq(2)").text().trim());
	//policyId
	$("#csPolicyId").attr('value',$(obj).parent().parent().find("td:eq(1)").text().trim());
	//busiItemId
	$("#busiItemId").attr('value',_busiItemId);
	//policyCode
	$("#csPolicyCode").attr('value',$(obj).parent().parent().find("td:eq(3)").text().trim());
	//productNameSys
	$("#productNameSys").attr('value',$(obj).parent().parent().find("td:eq(7)").text().trim());
	//productCodeSys
	$("#productCodeSys").attr('value',$(obj).parent().parent().find("td:eq(5)").text().trim());
}
/**
 * 保额修改后验证是否为数字
 */
function perIsNumber(){
	var _otherProductCodeSys = $("#otherProductCodeSys",navTab.getCurrentPanel()).val();
	var reg = /^[0-9]+(.[0-9]+)?$/;
	var perAmout = $("#perAmout").val();
	if($("#perAmout").is(":visible")){
		if (_otherProductCodeSys != '00554000') {
			if(perAmout != null && perAmout.trim() != ""){
				if(!reg.test(perAmout)){
					alertMsg.warn("保额只能是数字！");
					$("#perAmout",navTab.getCurrentPanel()).val("");
					return false;
				}
			}/* else{
				alertMsg.warn("保额不能为空！");
				return false;
			} */
		}
	}
};
/**
 * 修改保障计划是给保额赋值
 */
function guaranteeProjectChange(){
	$("input[name='pagecfgElementValueBO.code']").val($("#GuaranteeProject").val());
	var guaranteeVal = $("#GuaranteeProjectValue").val().split("/");
	$("#perAmout").val(guaranteeVal[$("input[name='pagecfgElementValueBO.code']").val()-1]);
}

/**
 * 修改保额是选择保障计划
 */
function changeGuaranteeProject(){
	
	//perAmout 保额
	var perAmout = $("#perAmout").val();
	var guaranteeVal = $("#GuaranteeProjectValue").val().split("/");
	if(guaranteeVal.length > 0){
		for(var i = 0; i <guaranteeVal.length; i++ ){
			if(perAmout == guaranteeVal[i]){
				$("#GuaranteeProject").val(i+1);
				$("input[name='pagecfgElementValueBO.code']").val($("#GuaranteeProject").val());
				break;
			}
		}
	}	
}

// 查询附加险下的被保人信息
function insuredListInfo(){
	debugger;
	// 主险代码
	var _busiItemId = $(".myClass:checked",navTab.getCurrentPanel()).val();
	// 附加险代码
	var _option = $("#otherProductCodeSys",navTab.getCurrentPanel()).find("option:selected");
	var _otherBusiPrdId = _option.attr('bisPrdId');

	var changeId=$("#changeId",navTab.getCurrentPanel()).val();
	var acceptId=$("input[name='acceptId']",navTab.getCurrentPanel()).val();
	var policyId=$(".myClass:checked",navTab.getCurrentPanel()).parent().parent().find("td:eq(1)").text().trim();
	var otherProductCodeSys = $("#otherProductCodeSys").val()
	var productCodeSys = $("input[name='csAddSubBusiProdVO.productCodeSys']").val();
	console.info(acceptId);
	// 查询被保人信息
	$.ajax({
		type : "post",
		dataType : "text",
		url : "${ctx}/cs/serviceitem_ns/queryInsuredListInfo_PA_csEndorseNSAction.action",
		data : '&csAddSubBusiProdVO.changeId='+changeId+'&csAddSubBusiProdVO.acceptId='+acceptId
			+'&csAddSubBusiProdVO.policyId='+policyId+'&csAddSubBusiProdVO.busiItemId='+_busiItemId
			+'&csAddSubBusiProdVO.otherBusiPrdId='+_otherBusiPrdId+"&csAddSubBusiProdVO.otherProductCodeSys="+otherProductCodeSys+"&csAddSubBusiProdVO.productCodeSys="+productCodeSys,
		success : function(data) {
			var adr=jQuery.parseJSON(data);
			var str="";
			for (var i = 0; i < adr.length; i++) { 
				console.info(adr[i].customerName+"--"+adr[i].orderId);
				if(1==adr[i].orderId){
					str+=("<option value='"+adr[i].customerId+"' selected='selected'>"+ adr[i].customerName + "</option>");
				}else if(productCodeSys != "00462000"){
					str+=("<option value='"+adr[i].customerId+"'>"+ adr[i].customerName + "</option>"); 
				}
			}
			$("#_insured",navTab.getCurrentPanel()).html(str);
		}
	});
}

//查询附加险必选责任组的计算方向及长短期类型
function selectAddSubBusi(obj){
	var _option = $(obj).find("option:selected");
	var _busiPrdId = _option.attr('bisPrdId');	
	var _isWaived = $("#isWaived", navTab.getCurrentPanel());
	var _amountDL = $("#amountDL", navTab.getCurrentPanel());
	var  _premDL = $("#premDL", navTab.getCurrentPanel());
	var _loadCsProduct = $("#loadCsProduct",navTab.getCurrentPanel());
	var _acceptId = $("input[name='acceptId']").val();
	var productCodeSys = $("#otherProductCodeSys").val();
	var masterBusiItemId = $("#busiItemId", navTab.getCurrentPanel()).val();
	var _perAmout = $("#perAmout", navTab.getCurrentPanel());
	var _perAmoutFor812 = $("#perAmoutFor812", navTab.getCurrentPanel());
	var _insuredDL = $("#_insuredDL", navTab.getCurrentPanel());
	var _manner812 = $("#manner812", navTab.getCurrentPanel());
	var _period812 = $("#period812", navTab.getCurrentPanel());
	var _period461 = $("#period461", navTab.getCurrentPanel());
	var _amount461 = $("#amount461", navTab.getCurrentPanel());
	var _chargePeriodFor461 = $("#chargePeriodFor461", navTab.getCurrentPanel());
	var _chargeYearFor461 = $("#chargeYearFor461", navTab.getCurrentPanel());
	var acceptId=$("input[name='acceptId']",navTab.getCurrentPanel()).val();
	var _policyorgfiag= $("#policyorgfiag", navTab.getCurrentPanel()).val();
		//提交 可查询 该主险的附加险 都有哪些   为 附件险字段赋值  	
	  $.ajax({
		  type : "post",
          dataType : "text",
          url : "${ctx}/cs/serviceitem_ns/queryCountWay_PA_csEndorseNSAction.action",
          data : 'addBusiPremVO.addBusPrdId=' + _busiPrdId+"&addBusiPremVO.masterBusiItemId="+masterBusiItemId+"&addBusiPremVO.acceptId="+acceptId,
          success : function(data) {
        	  var prams = DWZ.jsonEval(data);
        	  	//var prams = data.split(",");
        	    var adr = prams.adr;//计算方式
        	    var type = prams.type;//长短期类型
        	    var coverPeriodType = prams.coverPeriodType;//保障期间
        	    var renewOption = prams.renewOption;//保证续保标识
            	if(adr != null ){//2为保费计算保额,
            		if(adr == '1' || adr == '3'|| adr =='5'){//1保额算保费 2保费算保额3按档次4保额保费分别录入5按份数
            			_amountDL.show();
            			_premDL.hide();
            		}
            		if(adr == '2'){
            			_amountDL.hide();
            			_premDL.show();
            		}
            		if(adr == '4'){
            			_amountDL.show();
            			_premDL.show();
            		}
            	}
            	debugger;
            	var _chargePeriodDL = $("#chargePeriodDL", navTab.getCurrentPanel());
            	var  _chargeYearDL = $("#chargeYearDL", navTab.getCurrentPanel());
            	var _coveragePeriodDL = $("#coveragePeriodDL", navTab.getCurrentPanel());
            	var  _coverageYearDL = $("#coverageYearDL", navTab.getCurrentPanel());
            	var  _premFreqDL = $("#premFreqDL", navTab.getCurrentPanel());
            	var  _deductibleFranchiseDL = $("#deductibleFranchiseDL", navTab.getCurrentPanel());
            	var  _payoutRateDL = $("#payoutRateDL", navTab.getCurrentPanel());
            	var  _payInfoSave = $("#payInfoSave", navTab.getCurrentPanel());
            	var _applicantSpePeople = $("#applicantSpePeople", navTab.getCurrentPanel());
            	var  _isRenew = $("#isRenew", navTab.getCurrentPanel());
            	
            	_deductibleFranchiseDL.hide();
            	_payoutRateDL.hide();
            	_deductibleFranchiseDL.val(null);
            	_payoutRateDL.val(null);
            	_applicantSpePeople.hide();
            	_perAmoutFor812.hide();
            	_period812.hide();
    			_manner812.hide();
    			_period461.hide();
    			_amount461.hide();
    			_chargePeriodFor461.hide();
    			_chargeYearFor461.hide();
    			_isRenew.hide();
    			$("#isRenew option[value='2']").prop("selected", true);
            	if(type != null){
        			if(coverPeriodType == '1' && renewOption == '1' || coverPeriodType == '1' && renewOption == '2'){
        				_isRenew.show();
        			}
            		if(productCodeSys=='00812000'){
            			_perAmout.hide();
            			_perAmoutFor812.show();
            			_manner812.show();
            			_period812.show();
            			_insuredDL.hide();
            		}else{
            			_perAmout.show();
            			_perAmoutFor812.hide();
            		}
            		if((productCodeSys=='00392000'||
            				productCodeSys=='00378000'||
            				productCodeSys=='00389000')&&_policyorgfiag=='1'){//
                		_applicantSpePeople.show();
                	}
            		if(type == "L" && productCodeSys!='00461000'){//长期险
            			_chargePeriodDL.hide();
                    	_chargeYearDL.hide();
                    	_coveragePeriodDL.hide();
                    	_coverageYearDL.hide();
                    	_premFreqDL.hide();
						//长期险去查询具体锁定值
						queryBusinessInfo(productCodeSys);
						_payInfoSave.show();
						
						
                    	if(_busiPrdId=="603"){
                    	_deductibleFranchiseDL.val(null);
                    	_payoutRateDL.val(null);
                    	_deductibleFranchiseDL.show();
                    	_payoutRateDL.show();}
            		}else{//短期险
            			_payInfoSave.hide();
            			_chargePeriodDL.hide();
                    	_chargeYearDL.hide();
                    	_coveragePeriodDL.hide();
                    	_coverageYearDL.hide();
                    	_premFreqDL.hide();
                    	$("#loadCsProduct input").val(null);
                    	_chargePeriodDL.val(null);
                    	_chargeYearDL.val(null);
                    	_coveragePeriodDL.val(null);
                    	_coverageYearDL.val(null);
                    	_premFreqDL.val(null);
                    	if(productCodeSys=='00577000'){//577需要录入免赔额和赔付比例
                    		_deductibleFranchiseDL.val(null);
                        	_payoutRateDL.val(null);
                        	_deductibleFranchiseDL.show();
                        	_payoutRateDL.show();
                    	}
            		}
            		
            	}
            	
            	if(productCodeSys=='00461000'){
            		debugger;
            		_premFreqDL.remove();
            		_chargePeriodDL.remove();
            		_chargeYearDL.remove();
        			_amountDL.hide();
        			_coveragePeriodDL.remove();
                	_coverageYearDL.remove();
        			_perAmout.hide();
        			_insuredDL.hide();
        			_period461.show();
        			_amount461.show();
        			_chargePeriodFor461.show();
        			_chargeYearFor461.show();
        			_isWaived.hide();
        			
        			if(prams.premFreq==1){//一次交清
        				$("#premFreqFor461_val").val("一次交清");
        			}else if(prams.premFreq==5){//年交
        				$("#premFreqFor461_val").val("年交");
        			}
        			$("#premFreq461", navTab.getCurrentPanel()).removeAttr("disabled");
        			$("#chargePeriod461", navTab.getCurrentPanel()).removeAttr("disabled");
        			$("#chargeYearFor461", navTab.getCurrentPanel()).removeAttr("disabled");
        			$("#coverageYearFor461", navTab.getCurrentPanel()).removeAttr("disabled");
        			$("#coveragePeriodFor461", navTab.getCurrentPanel()).removeAttr("disabled");
        			
        			$("#chargeYearFor461", navTab.getCurrentPanel()).attr("readonly",true);
        			
        			$("#premFreq461", navTab.getCurrentPanel()).val(prams.premFreq);
        			$("#chargePeriod461", navTab.getCurrentPanel()).val(prams.chargePeriod461);
        			$("#chargeYearFor461", navTab.getCurrentPanel()).val(prams.chargeYear461);
        		}else{
        			$("#premFreq461", navTab.getCurrentPanel()).attr("disabled",true);
        			$("#chargePeriod461", navTab.getCurrentPanel()).attr("disabled",true);        		
        			$("#premFreq461", navTab.getCurrentPanel()).attr("disabled",true);
        			$("#coveragePeriodFor461", navTab.getCurrentPanel()).attr("disabled",true);
        			$("#coverageYearFor461", navTab.getCurrentPanel()).attr("disabled",true);
        			$("#chargeYearFor461", navTab.getCurrentPanel()).attr("disabled",true);
        		}
          }
	  	
      });
		
	  $.ajax({
		  type : "post",
          dataType : "text",
          url : "${ctx}/cs/serviceitem_ns/queryGuaranteeProject_PA_csEndorseNSAction.action",
          data : 'productCodeSys=' + productCodeSys,
          success : function(data) { 
        	   var result = DWZ.jsonEval(data);
        	  if(result.statusCode === "300"){
        		  $("#GuaranteeProjectValue").val(""); 
        		  //如果没有结果那么就隐藏保障计划
        		  $("#GuaranteeDiv").hide();
        		  //alertMsg.error(result.message);
        		  $("#GuaranteeProject").empty();
        		$("#perAmout", navTab.getCurrentPanel()).val("");
        		$("#perAmout", navTab.getCurrentPanel()).attr("readonly", false);
        		$("input[name='pagecfgElementValueBO.code']", navTab.getCurrentPanel()).val("");
        	  }else{
        		 //展示保障期限 
        		 //填充数据
        		 var guaranteeType = DWZ.jsonEval(result.message);
        		 $("#GuaranteeProject").html("<option>请选择</option>");
        		for(var i = 0 ; i<$(guaranteeType)[0].length;i++){
        			$("#GuaranteeProject").append("<option value='"+$(guaranteeType)[0][i].code+"'"+">"+$(guaranteeType)[0][i].code2+"</option>"); 
        		 } 
        		 
        		 var values = "";
        		 var len = $(guaranteeType)[1].length;
        		 for(var i = 0 ; i<len;i++){
        			 if(i == (len-1)){
        				 values = values + $(guaranteeType)[1][i].code;
        			 }else{
        				 values = values + $(guaranteeType)[1][i].code +"/";
        			 }
         			$("#GuaranteeProjectValue").val(values); 
         		 }
        		// 兼容保障计划与保额配置错误 start
        		var llen = $(guaranteeType)[0].length;
        		/* if (llen > len) {
            		for (var i = 0; i<llen-len; i++) {
            			$("#GuaranteeProjectValue").val($("#GuaranteeProjectValue").val()+"/"); 
            		}
        		} */
        		// 经排查目前仅9581产品配置的保障计划数量大于保额数量->9581计划三的保额与计划二一致
        		if (llen > len) {
            		for (var i = 0; i<llen-len; i++) {
            			$("#GuaranteeProjectValue").val($("#GuaranteeProjectValue").val()+"/"+$(guaranteeType)[1][len-1].code); 
            		}
        		}
        		// 兼容保障计划与保额配置错误 end
        		
        		 $("#GuaranteeDiv").show();
        		$("#perAmout", navTab.getCurrentPanel()).val("");
        		if(productCodeSys != "00844000"){
        			$("#perAmout", navTab.getCurrentPanel()).attr("readonly","readonly");
        		}
        		
        		 /* guaranteeType.each(function(){
        			 alert($(this));
        		 }); */
        		 /* for(var i = 0; i < guaranteeType.length; i++){
        			 alert("<option value='"+"'i"+">"+guarrantetype[i]+"</option>");
        		 } */
        	  } 
          }
        	  	
      });
      $.ajax({
		  type : "post",
          dataType : "text",
          url : "${ctx}/cs/serviceitem_ns/calWaivedStdAll_PA_csEndorseNSAction.action?acceptId="+_acceptId,
          data : 'addBusPrdId=' + _busiPrdId +"&masterBusiItemId="+masterBusiItemId,
          success : function(data) {
        	  var result = DWZ.jsonEval(data);
       	   var msg = result.message;
       	 debugger;
       	  if(msg.split("|")[0] == "true"){
       		  _isWaived.show();
       		  _amountDL.hide();
       		  $("#waivedStdAll", navTab.getCurrentPanel()).val(msg.split("|")[1]);
       		  if(msg.split("|")[1] == "null" && productCodeSys!='00461000'){
       			_isWaived.hide();
                _amountDL.show();  
       		  }
       	  }else if(productCodeSys!='00461000'){
       		_isWaived.hide();
            _amountDL.show();  
       		  $("#waivedStdAll", navTab.getCurrentPanel()).val("");
       	  } 
          }
      });
	insuredListInfo();
}


//告知页面初始化加载
$(document).ready(function() {
			initNotific();
			/* initNotificSO(); */
	});
	//查询该险种配置数据(缴费年期,保险期间....)
	function queryBusinessInfo(productCodeSys){
		var policyId = $("#csPolicyId", navTab.getCurrentPanel()).val();
		//alert(policyId);
		var relBefore = $("#loadCsProduct", navTab.getCurrentPanel());
		var masterBusiItemId = $("#busiItemId", navTab.getCurrentPanel()).val();
		var changeId=$("#changeId",navTab.getCurrentPanel()).val();
		var acceptId=$("input[name='acceptId']",navTab.getCurrentPanel()).val();
		relBefore.loadUrl("${ctx}/cs/serviceitem_ns/queryBusinessInfo_PA_csEndorseNSAction.action?productCodeSys="+productCodeSys+"&policyId="+policyId+
				"&masterBusiItemId="+masterBusiItemId+"&acceptId="+acceptId+"&changeId="+changeId);
		//payInfoSave
		//var payInfoSave = $("#payInfoSave", navTab.getCurrentPanel());
		//payInfoSave.loadUrl("${ctx}/cs/serviceitem_ns/queryBusinessInfo_PA_csEndorseNSAction.action?productCodeSys="+productCodeSys+"&policyId="+policyId);
	}	
	
	
function initNotificSO(){
	var nPolicyCode=$("#policyCode0").val();
	var changeId="${changeId}";
	var acceptId="${acceptId}";
	
	var policyHolderId=$("#policyHolderId").val();
	var policyInsuredId=$("#insuredId").val();
	var iswavid=$("#iswavid").val();
	
	//var type="1";
	//var operate="query";
	var rel = $("#insertSurveySO", navTab.getCurrentPanel());
	var queryFlag = $("#queryFlag").val();
	var isHolderNeed = 1;
	//根据需求，.当保单下不存在投保人豁免责任时，不显示投保人健康告知
	//3.即使存在投保人豁免责任，当投保人被保人为同一人时，只填写一次被保人健康告知。
	if(iswavid==0||(policyHolderId==policyInsuredId&&iswavid!=0)){
		//只有被保人的健康告知
		 isHolderNeed = 1;
	}else{
		 isHolderNeed= 2;
	}
	rel.loadUrl("${ctx}/cs/serviceitem_so/loadSOPageNs_PA_csEndorseSOAction.action?customerId="+policyInsuredId+"&changeId="+changeId+"&acceptId="+acceptId);
	
}

function initNotific(){
	var nPolicyCode=$("#policyCode0").val();
	var changeId="${changeId}";
	var acceptId="${acceptId}";
	
	var policyHolderId=$("#policyHolderId").val();
	var policyInsuredId=$("#insuredId").val();
	var iswavid=$("#iswavid").val();
	
	//var type="1";
	//var operate="query";
	var rel = $("#insertSurveyNS", navTab.getCurrentPanel());
	var queryFlag = $("#queryFlag").val();
	var isHolderNeed = 1;
	//根据需求，.当保单下不存在投保人豁免责任时，不显示投保人健康告知
	//3.即使存在投保人豁免责任，当投保人被保人为同一人时，只填写一次被保人健康告知。
	if(iswavid==0||(policyHolderId==policyInsuredId&&iswavid!=0)){
		//只有被保人的健康告知
		 isHolderNeed = 1;
	}else{
		 isHolderNeed= 2;
	}
	rel.loadUrl("${ctx}/cs/common/loadMainPageNS_PA_csNotificAction.action?customerId="+policyInsuredId+"&changeId="+changeId+"&acceptId="+acceptId+"&queryFlag="+"&isHolderNeed="+isHolderNeed+"&nPolicyCode="+nPolicyCode+"&serviceCode=NS");
	
}
$(function(){
	var isSaved = $("#isSaved",navTab.getCurrentPanel()).val();
	if(isSaved == 1){
		$("#newbusiItemDiv").show();
	}
	/* var isLongBusi = $("#isLongBusi",navTab.getCurrentPanel()).val();
	if(isLongBusi == 1){
		$("#payInfoSave").show();
	} */
});
	
//退出按钮功能
function exit(){
	navTab.closeCurrentTab();
}

/**
 * 812新产品将下拉框中选中的值赋值给隐藏的input框,用于向后台传值
 */
function changePerAmoutFor812(){
	
	//perAmout 保额
	var perAmoutFor812 = $("#perAmoutFor812").val();
	$("#perAmout").val(perAmoutFor812);
	
}

function chageCoveragePeriod461(obj){
	debugger;
	var data=$(obj).val();
	var arr=data.split("-");
	$("#coveragePeriodFor461").val(arr[0].trim());
	$("#coverageYearFor461").val(arr[1].trim());
}
</script>