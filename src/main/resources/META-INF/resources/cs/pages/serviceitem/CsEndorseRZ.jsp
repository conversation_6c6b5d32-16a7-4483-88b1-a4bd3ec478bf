<%@ page language="java" contentType="text/html; charset=utf-8"
	pageEncoding="utf-8"%>
<%@ taglib uri="http://www.git.com.cn/taglib/udmp/field" prefix="Field"%>
<%@ taglib uri="/struts-tags" prefix="s"%><%@include
	file="/udmpCommon.jsp"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<%-- <s:set var="ctx">${pageContext.request.contextPath}</s:set> --%>
<%@ include file="/cs/pages/common/jsp/common_ctx.jsp"%>
<script type="text/javascript" src="${ctx}/cs/js/cs_common.js"></script>
<link rel="stylesheet" href="${ctx }/cs/css/cs_common.css"
	type="text/css">
<link rel="stylesheet" href="${ctx}/cs/css/public.css" type="text/css">
<!-- ********帮助菜单***********begin********* -->
<s:include value="/cs/pages/common/jsp/helpMenu.jsp"></s:include>
<!-- *********帮助菜单***********end********* -->

<script type="text/javascript">
	$(document).ready(function() {
		_cs_initMultipleBox();
		$("#updateEndInfo", navTab.getCurrentPanel()).hide();
	});
</script>
<!-- 帮助菜单 -->
<script src="${ctx }/udmp/plugins/ribbon/jquery.asyncorgtree.js"
	type="text/javascript"></script>
<script type="text/javascript">
	//帮助部分动作
	function MM_changeProp(objId, theProp, theValue) {
		var obj = null;
		with (document) {
			if (getElementById) {
				obj = getElementById(objId);
			}
		}
		if (obj) {
			if (theValue == true || theValue == false) {
				eval("obj.style." + theProp + "=" + theValue);
			} else {
				eval("obj.style." + theProp + "='" + theValue + "'");
			}
		}
	};
</script>
<style type="text/css">
th,td {
	white-space: normal;
}

.searchBar li label {
	width: 90px;
}
</style>

<!-- 为了使用密码器 -->
<div style="display: none;">
	<object classid="clsid:06CDE6AB-2508-448E-BD1A-B52F0D019D68"
		id="BpHidPinWeb"></object>
</div>
<div onmousedown="MM_changeProp('premAddCmDiv','display','none')"
	class="pageContent" layoutH="20">
	<form method="post"
		action="${ctx }/cs/serviceitem_rz/savePassword_PA_csEndorseRZAction.action"
		class="required-validate" onsubmit="return validateCallback(this)"
		id="csEndorseFKAction" name="csEndorseFKAction">
		<OBJECT id = PinpadOcx 
				codebase="PinpadHandler.CAB" height="1" width="1" 
				classid="clsid:86FA742E-5029-439F-8F97-E4E2FB51233C">
				</OBJECT>
		<input name="status" type="hidden" id="status"
			value="${csCustomerPasswordVONew.status}" />
		<input name="password" type="hidden" id="password" value="${csCustomerPasswordVONew.password}" />
		<input id="customerId" type="hidden" name="customerId" value="${customerId}" />
		<input id="acceptId" type="hidden" name="acceptId" value="${acceptId}" />
		<input id="changeId" type="hidden" name="changeId" value="${changeId}" />
		<input id="errDate" type="hidden" name="errDate" value="${csCustomerPasswordVONew.errDate}" />
		<!-- 判断密码是不是比录项 -->
		<input id="passwordFlag" type="hidden" value="${passwordFlag}"
			name="passwordFlag">
		<input id="isGuiWaiQing" type="hidden" value="${guiWaiQing}" name="isGuiWaiQing">
		<s:if test="queryFlag!=1">
			<s:include value="csEndorseProgress.jsp" />
		</s:if>

		<div class="tabsContent">
			<!--第一个页签-->
			<div class="pageContent">
				<div class="divfclass">
					<h1>
						<img src="images/tubiao.png">客户信息
					</h1>
				</div>
				<div class="pageFormInfoContent">
					<dl style="display: inline-block; margin: 5px 5px;">
						<dt>客户姓名</dt>
						<dd>
							<input type="text" id="customerName" readonly="readonly"
								name="csCustomerVO.customerName " maxlength="100"
								value="${csCustomerVO.customerName }">
						</dd>
					</dl>
					<dl style="display: inline-block; margin: 5px 5px;">
						<dt>出生日期</dt>
						<dd>
							<input type="text" readonly="readonly"
								name="csCustomerVO.customerBirthday"
								value="<s:date format="yyyy-MM-dd" name="csCustomerVO.customerBirthday"></s:date>">
						</dd>
					</dl>
					<dl style="display: inline-block; margin: 5px 5px;">
						<dt>证件类型</dt>
						<dd>
							<input type="text" readonly="readonly" style="display: none"
								name="csCustomerVO.customerCertType" disabled="disabled"
								value="${csCustomerVO.customerCertType}">
							<input type="text" readonly="readonly"
								value='<Field:codeValue tableName="APP___PAS__DBUSER.T_CERTI_TYPE" value="${csCustomerVO.customerCertType}" />'>
							<%-- <Field:codeTable   id="customerCertiType" name="csCustomerVO.customerCertiType"
                           				tableName="APP___PAS__DBUSER.T_CERTI_TYPE" nullOption="true" value="${csCustomerVO.customerCertiType}"/> --%>
						</dd>
					</dl>
					<dl style="display: inline-block; margin: 5px 5px;">
						<dt>证件号码</dt>
						<dd>
							<input type="text" readonly="readonly"
								name="csCustomerVO.customerCertiCode"
								value="${csCustomerVO.customerCertiCode}">
						</dd>
					</dl>
					<dl style="display: inline-block; margin: 5px 5px;">
						<dt>性别</dt>
						<dd>
							<input type="text" style="display: none" name="customerCertType"
								value="${csCustomerVO.customerGender }" disabled="disabled" />
							<input type="text" readonly="readonly"
								value='<Field:codeValue   tableName="APP___PAS__DBUSER.T_GENDER"  value="${csCustomerVO.customerGender}"/>'>
						</dd>
					</dl>
					<dl style="display: inline-block; margin: 5px 5px;">
						<dt>认证等级</dt>
						<dd>
							<input id="levelCheck" name="csEndorseRZVO.levelCheck"
								value="${csEndorseRZVO.levelCheck }">
						</dd>
					</dl>
					<!-- 如果没有查到 则为必填项新增到后台 -->
					<dl style="display: inline-block; margin: 5px 5px;"
						id="accountName1">
						<dt>银行名称</dt>
						<dd>
							<%-- <input class="required" id="accountName"
								name="csEndorseRZVO.accountName"
								value="${csEndorseRZVO.bankName }"> --%>
							<input type="text" class="autocomplete textLeft" id="rzbank" name="csEndorseRZVO.bankCode"
							style="width:52px;"
							value="${csEndorseRZVO.bankCode }" data-width="130px"
							data-showValue="accountName" data-mode="2"
							data-disCurrentValFlag="1"
							data-tableName="APP___PAS__DBUSER.T_BANK"
							data-tableCol="BANK_CODE"
							data-tableDesc="BANK_CODE||'-'||BANK_NAME" data-separator="-"
							data-like="3" data-orderBy="BANK_CODE" data-view="BANK_NAME"
							data-disUpShowFlag="1" />
							<input type="text" id="accountName"  name="csEndorseRZVO.accountName" readOnly class="textRight" />	<font
								color="red">*</font>	
								
								
						</dd>
					</dl>
					<dl style="display: inline-block; margin: 5px 5px;" id="banckCode1">
						<dt>银行卡号</dt>
						<dd>
							<!-- doublecheck -->
							<input id="banckCode" onblur="checkBank(this.value)" 
								name="csEndorseRZVO.banckCode"  maxlength="100" 
								value="${csEndorseRZVO.bankAccount }">
								<font color="red">*</font>
								<input type="hidden" name="bank1"/>
							<input type="hidden" name="bank2"/>
						</dd>
					</dl>
					<dl style="display: inline-block; margin: 5px 5px;"
						id="banckMoble1">
						<dt>银行预留手机号</dt>
						<dd>
							<input class="required" type="expandMobile" id="banckMoble"
								name="csEndorseRZVO.banckMoble"
								value="${csEndorseRZVO.mobilePhone}"><font color="red">*</font>
						</dd>
					</dl>
				</div>
				<div class="divfclass">
					<h1>
						<img src="images/tubiao.png">交易密码设置<span id="span1">已设置交易密码，可选择重新设置</span>
					</h1>
				</div>
				<div class="pageFormInfoContent">
					<dl style="display: inline-block; margin: 5px 5px;">
						<dt>请输入交易密码</dt>
						<dd>
							<input class="required" type="password" id="newPwd1" maxLength="6"
								name="csCustomerPasswordVONew.password" value="${csEndorseRZVO.transPwd}" 
								onselectstart="return false;" onpaste="return false;" readonly="readonly">
								<font id="font1" color="red">*</font>
						</dd>					
					</dl>

					<dl style="display: inline-block; margin: 5px 5px;">
						<dt>请再次输入交易密码</dt>
						<dd>
							<input class="required" type="password" id="newPwd2"
								maxLength="6" name="csCustomerPasswordVONew.password2"
								value="${csEndorseRZVO.transPwd}" 
								onselectstart="return false;" onpaste="return false;" readonly="readonly">
								<font id="font2" color="red">*</font>
						</dd>
					</dl>
				</div>
				<div class="divfclass" id="payPasId2">
					<h1>
						<img src="images/tubiao.png">支付密码设置
					</h1>
				</div>
				<div class="pageFormInfoContent" id="payPasId">
					<dl style="display: inline-block; margin: 5px 5px;">
						<dt>请输入支付密码</dt>
						<dd>							
							<input class="required" type="password" id="payPwd1" 
								name="csEndorseRZVO.payPwd" value="${csEndorseRZVO.payPwd}"  maxLength="6"
								onselect="return false;" onpaste="return false;" readonly="readonly">
								<font color="red" >*</font>
						</dd>
					</dl>
					<dl style="display: inline-block; margin: 5px 5px;">
						<dt>请再次输入支付密码</dt>
						<dd>
							<input class="required" class="required" type="password" 
								id="payPwd2" name="csEndorseRZVO.payPwd1" value="${csEndorseRZVO.payPwd}"
								onselect="return false;" onpaste="return false;" maxLength="6" readonly="readonly">
								<font color="red">*</font>
						</dd>
					</dl>
				</div>
				<div class="pageFormdiv">
					<button type="button" class="but_blue"
						onclick="savePwd('csEndorseFKAction')">保存</button>
				</div>
				<s:include value="/cs/pages/serviceitem/serviceItemBottomBar.jsp"></s:include>
			</div>
		</div>
	</form>
</div>
<!-- *********帮助菜单***********end********* -->
<script type="text/javascript">
	var pwd1 = false;
	var pwd2 = false;
	$("#newPwd1",navTab.getCurrentPanel()).click(function(){
		var isGuiWaiQing = $("#isGuiWaiQing", navTab.getCurrentPanel()).val();
		if(isGuiWaiQing=='Y'){
			readPasswordByPinPad("newPwd1");
		} else {
			readPasswordByBpHidPinWeb("newPwd1");
		}	
	 });
	$("#newPwd2",navTab.getCurrentPanel()).click(function(){
		var isGuiWaiQing = $("#isGuiWaiQing", navTab.getCurrentPanel()).val();
		if(isGuiWaiQing=='Y'){
			readPasswordByPinPad("newPwd2");
		} else {
			readPasswordByBpHidPinWeb("newPwd2");
		}	
	 });
	$("#payPwd1",navTab.getCurrentPanel()).click(function(){
		var isGuiWaiQing = $("#isGuiWaiQing", navTab.getCurrentPanel()).val();
		if(isGuiWaiQing=='Y'){
			readPasswordByPinPad("payPwd1");
		} else {
			readPasswordByBpHidPinWeb("payPwd1");
		}	
	 });
	$("#payPwd2",navTab.getCurrentPanel()).click(function(){
		var isGuiWaiQing = $("#isGuiWaiQing", navTab.getCurrentPanel()).val();
		if(isGuiWaiQing=='Y'){
			readPasswordByPinPad("payPwd2");
		} else {
			readPasswordByBpHidPinWeb("payPwd2");
		}	
	 });
	
	
	/**
	 * 银行帐号两次输入是否一样校验 
	 如果第一次与第二次不同那么就清空输入的信息,并将两次是否一样的标记赋值为fals
	 如果第一次与第二次相同就将两次是否一样的标记赋值为true
	 */
	function checkBank(value){

		var bank1 = $("input[name='bank1']",navTab.getCurrentPanel()).val();
		var bank2 = "";//第二次输入的值
		if(bank1 == ""){//如果没有值是第一次输入
			$("input[name='bank1']",navTab.getCurrentPanel()).attr('value',value).initUI();
			bank1 = $("input[name='bank1']",navTab.getCurrentPanel()).val();
			$("#banckCode",navTab.getCurrentPanel()).attr('value',"");//清空输入框信息
		}else{//如果有值那么就是第二次输入
			//第二次输入赋值
			bank2 = value;
			if(bank1 != bank2){//不同的话提示并赋值fase并清空历史输入信息
				alertMsg.error("两次输入不一致,请重新输入");
				$("#banckCode",navTab.getCurrentPanel()).attr('value',"");//清空输入框信息
				$("input[name='bank1']",navTab.getCurrentPanel()).attr('value',"");
				$("input[name='bank2']",navTab.getCurrentPanel()).attr('value',"");
			}else{
				if(bank1 == bank2){//清空并赋值true
					$("#banckCode",navTab.getCurrentPanel()).val(bank2);
					$("input[name='bank1']",navTab.getCurrentPanel()).attr('value',"");
					$("input[name='bank2']",navTab.getCurrentPanel()).attr('value',"");
				}
			}
		}
	}
	
	
	
	
	
	/*柜外清读取密码*/
	function readPasswordByPinPad(elementId){
		var returnPassword='';
		var ret = csEndorseFKAction.PinpadOcx.readKey( 1, 120);
		if(ret==''){
			alertMsg.warn("请确认柜外清设备是否准备好，并输入了密码!");
		}else{
			if (ret=='-19') {
				alertMsg.warn("未检测到柜外清设备");
				return false;
			}else if (ret=='-22'){
				alertMsg.warn("调用柜外清设备出错");
				return false;
			} else {
				returnPassword=ret;
				$("#"+elementId).val(returnPassword);
				var va111 = checkPay("#"+elementId);	 				
				if(!va111){
					$("#"+elementId,navTab.getCurrentPanel()).val("");
					$("#"+elementId,navTab.getCurrentPanel()).blur() ;	 				
				} 
				return true;
			}		
		}
	}
	/*密码器读取密码*/
	function readPasswordByBpHidPinWeb(elementId){
		var returnPassword="";
		var Pindata=BpHidPinWeb.readKey(1);
		if (Pindata==''){
			alertMsg.warn("请确认密码键盘设备是否准备好，并输入了密码!");
		}else{
			if (Pindata == '-4') {
				alertMsg.warn("未检测到密码键盘设备");
				return false;
			}else{
				returnPassword=Pindata;
				$("#"+elementId).val(returnPassword);
				var va111 = checkPay("#"+elementId);	 				
				if(!va111){
					$("#"+elementId,navTab.getCurrentPanel()).val("");
					$("#"+elementId,navTab.getCurrentPanel()).blur() ;	 				
				} 
				return true;
			}
		}
	}

	//点击保存按钮
	function savePwd(formId) {
		//客户交易密码 如果之前没有设置过则校验
		/* var passwordFlag=$("#passwordFlag").val(); */
		var newPwd1 = $("#newPwd1").val();
		var newPwd2 = $("#newPwd2").val();
		var passwordFlag = $("#passwordFlag").val();
		var levelCheck = $("#levelCheck").val();
		var accountName = $("#accountName").val();
		var banckCode = $("#banckCode").val();
		var banckMoble = $("#banckMoble").val();
		var payPwd1 = $("#payPwd1").val();
		var payPwd2 = $("#payPwd2").val();
		if ('未认证' == levelCheck) {
			if (accountName == '') {
				alertMsg.info("银行账户名称不能为空");
				return false;
			}
			if (banckCode == '') {
				alertMsg.info("银行卡号不能为空");
				return false;
			}
			if (banckMoble == '') {
				alertMsg.info("银行预留手机号不能为空");
				return false;
			}

			if ('1' != passwordFlag) {
				if (newPwd1 == '') {
					alertMsg.info("必录项交易密码不得为空");
					return false;
				}
				if (newPwd2 == '') {
					alertMsg.info("必录项交易密码不得为空");
					return false;
				}

				if (!checkPay("newPwd1")) {
					checkPay("newPwd1");
					return false;
				}
				if (!checkPay("newPwd2")) {
					checkPay("newPwd2");
					return false;
				}
				var flags = 0;
				var ab = $("#newPwd1").val().split("");
				for (var a = 0; a <= 5; a++) {
					for (var b = a; b < a + 1; b++) {
						if (eval(ab[b]) == eval(ab[b + 1]) + 1) {
							flags++;

							if (flags == 5) {
								alertMsg.info("新密码应为6位阿拉伯数字，不可连续3位（含）以上数字重复，不可为数字递增或递减，请重新输入！");
								$("#newPwd1").val("");
								$("#newPwd2").val("");
								flags = 0;
								return false;
							}
						} else {
							flags = 0;
						}
					}
				}

				//验证是否是递增 
				flags = 0;
				for (var a = 0; a <= 5; a++) {
					for (var b = a; b < a + 1; b++) {
						if (eval(ab[b]) == eval(ab[b + 1]) - 1) {
							flags++;
							if (flags == 5) {
								alertMsg
										.info("新密码应为6位阿拉伯数字，不可连续3位（含）以上数字重复，不可数字递增或递减，请重新输入！");
								$("#newPwd1").val("");
								$("#newPwd2").val("");
								flags = 0;
								return false;
							}
						} else {
							flags = 0;
						}
					}
				}

				//验证是否是相等
				flags = 0;
				for (var a = 0; a <= 5; a++) {
					for (var b = a; b < a + 1; b++) {
						if (eval(ab[b]) == eval(ab[b + 1])) {
							flags++;
							if (flags == 2) {
								alertMsg
										.info("新密码应为6位阿拉伯数字，不可连续3位（含）以上数字重复，不可为数字递增或递减，请重新输入！");
								$("#newPwd1").val("");
								$("#newPwd2").val("");
								flags = 0;
								return false;
							}
						} else {
							flags = 0;
						}
					}
				}
			} else if (newPwd1 != '' || newPwd2 != '') { //如果设置过并且有值才校验
				var _banckCode = $("#banckCode");
				/* _banckCode.remove(".doublecheck"); */
				if (!checkPay("newPwd1")) {
					checkPay("newPwd1");
					return false;
				}
				if (!checkPay("newPwd2")) {
					checkPay("newPwd2");
					return false;
				}
				if ($("#newPwd1").val().length < 6) {
					alertMsg
							.info("新密码应为6位阿拉伯数字，不可连续3位（含）以上数字重复，不可为数字递增或递减，请重新输入！");
					$("#newPwd1").val("");
					$("#newPwd2").val("");
					return false;
				}
				if ($("#newPwd2").val().length < 6) {
					alertMsg
							.info("新密码应为6位阿拉伯数字，不可连续3位（含）以上数字重复，不可为数字递增或递减，请重新输入！");
					$("#newPwd1").val("");
					$("#newPwd2").val("");
					return false;
				}
				var flags = 0;
				var ab = $("#newPwd1").val().split("");
				for (var a = 0; a <= 5; a++) {
					for (var b = a; b < a + 1; b++) {
						if (eval(ab[b]) == eval(ab[b + 1]) + 1) {
							flags++;

							if (flags == 5) {
								alertMsg
										.info("新密码应为6位阿拉伯数字，不可连续3位（含）以上数字重复，不可为数字递增或递减，请重新输入！");
								$("#newPwd1").val("");
								$("#newPwd2").val("");
								flags = 0;
								return false;
							}
						} else {
							flags = 0;
						}
					}
				}

				//验证是否是递增 
				flags = 0;
				for (var a = 0; a <= 5; a++) {
					for (var b = a; b < a + 1; b++) {
						if (eval(ab[b]) == eval(ab[b + 1]) - 1) {
							flags++;
							if (flags == 5) {
								alertMsg
										.info("新密码应为6位阿拉伯数字，不可连续3位（含）以上数字重复，不可为数字递增或递减，请重新输入！");
								$("#newPwd1").val("");
								$("#newPwd2").val("");
								flags = 0;
								return false;
							}
						} else {
							flags = 0;
						}
					}
				}

				//验证是否是相等
				flags = 0;
				for (var a = 0; a <= 5; a++) {
					for (var b = a; b < a + 1; b++) {
						if (eval(ab[b]) == eval(ab[b + 1])) {
							flags++;
							if (flags == 2) {
								alertMsg
										.info("新密码应为6位阿拉伯数字，不可连续3位（含）以上数字重复，不可为数字递增或递减，请重新输入！");
								$("#newPwd1").val("");
								$("#newPwd2").val("");
								flags = 0;
								return false;
							}
						} else {
							flags = 0;
						}
					}
				}
			}

			//数字校验

			if (!checkPay("payPwd1")) {
				checkPay("payPwd1");
				return false;
			}

			if (!checkPay("payPwd2")) {
				checkPay("payPwd2");
				return false;
			}
			if (payPwd1 == '') {
				alertMsg.info("必录项支付密码不得为空");
				return false;
			}
			if (payPwd2 == '') {
				alertMsg.info("必录项支付密码不得为空");
				return false;
			}
			if ($("#payPwd1").val().length < 6) {
				alertMsg.info("新密码应为6位阿拉伯数字，不可连续3位（含）以上数字重复，不可为数字递增或递减，请重新输入！");
				$("#payPwd1").val("");
				$("#payPwd2").val("");
				return false;
			}
			if ($("#payPwd2").val().length < 6) {
				alertMsg.info("新密码应为6位阿拉伯数字，不可连续3位（含）以上数字重复，不可为数字递增或递减，请重新输入！");
				$("#payPwd1").val("");
				$("#payPwd2").val("");
				return false;
			}
			var flags = 0;
			var ab = $("#payPwd1").val().split("");
			for (var a = 0; a <= 5; a++) {
				for (var b = a; b < a + 1; b++) {
					if (eval(ab[b]) == eval(ab[b + 1]) + 1) {
						flags++;

						if (flags == 5) {
							alertMsg.info("新密码应为6位阿拉伯数字，不可连续3位（含）以上数字重复，不可为数字递增或递减，请重新输入！");
							$("#payPwd1").val("");
							$("#payPwd2").val("");
							flags = 0;
							return false;
						}
					} else {
						flags = 0;
					}
				}
			}

			//验证是否是递增 
			flags = 0;
			for (var a = 0; a <= 5; a++) {
				for (var b = a; b < a + 1; b++) {
					if (eval(ab[b]) == eval(ab[b + 1]) - 1) {
						flags++;
						if (flags == 5) {
							alertMsg.info("新密码应为6位阿拉伯数字，不可连续3位（含）以上数字重复，不可为数字递增或递减，请重新输入！");
							$("#payPwd1").val("");
							$("#payPwd2").val("");
							flags = 0;
							return false;
						}
					} else {
						flags = 0;
					}
				}
			}

			//验证是否是相等
			flags = 0;
			for (var a = 0; a <= 5; a++) {
				for (var b = a; b < a + 1; b++) {
					if (eval(ab[b]) == eval(ab[b + 1])) {
						flags++;
						if (flags == 2) {
							alertMsg.info("新密码应为6位阿拉伯数字，不可连续3位（含）以上数字重复，不可为数字递增或递减，请重新输入！");
							$("#payPwd1").val("");
							$("#payPwd2").val("");
							flags = 0;
							return false;
						}
					} else {
						flags = 0;
					}
				}
			}
		}

		//验证2次输入的是否一致
		if (!checkPwd()) {
			return false;
		}
		var $form = $("#" + formId, navTab.getCurrentPanel());
		$form.submit();
	}

	//判断新旧密码是否一致 
	function checkPwd() {
		var newPwd1 = $("#newPwd1").val();
		var newPwd2 = $("#newPwd2").val();
		var payPwd1 = $("#payPwd1").val();
		var payPwd2 = $("#payPwd2").val();

		//判断新旧交易/支付密码
		if (newPwd1 != newPwd2) {
			alertMsg.info("交易密码两次录入不一致，请重新输入");
			$("#newPwd1").val("");
			$("#newPwd2").val("");
			return false;
		}
		if (payPwd1 != payPwd2) {
			alertMsg.info("支付密码两次录入不一致，请重新输入");
			$("#payPwd1").val("");
			$("#payPwd2").val("");
			return false;
		}
		return true;

	}

	function checklength(id) {
		var payNum = $("#" + id).val();
		var msg = "";
		//	var regs = /^\\d+$/;
		if (payNum != null && payNum != "") {
			if (payNum.length != 6) {
				if (id == "newPwd1") {
					msg = "请输入交易密码";
				}
				if (id == "newPwd2") {
					msg = "请再次输入交易密码";
				}
				if (id == "payPwd1") {
					msg = "请输入支付密码";
				}
				if (id == "payPwd2") {
					msg = "请再次输入支付密码";
				}
				alertMsg.info(msg
						+ "新密码应为6位阿拉伯数字，不可连续3位（含）以上数字重复，不可为数字递增或递减，请重新输入！");
				$("#" + id).val("");
				return false;
			}
		}
		return true;
	}

	//只能输入正数字
	function checkPay(id) {
		var payNum = $("#" + id).val();
		var reg = /^[0-9]*[0-9][0-9]*$/;
		var msg = "";
		//	var regs = /^\\d+$/;
		if (payNum != null && payNum != "") {
			if (!reg.test(payNum)) {
				if (id == "newPwd1") {
					msg = "请输入交易密码";
				}
				if (id == "newPwd2") {
					msg = "请再次输入交易密码";
				}
				if (id == "payPwd1") {
					msg = "请输入支付密码";
				}
				if (id == "payPwd2") {
					msg = "请再次输入支付密码";
				}
				alertMsg.info(msg
						+ "新密码应为6位阿拉伯数字，不可连续3位（含）以上数字重复，不可为数字递增或递减，请重新输入！");
				return false;
			}

		}
		return true;
	}

	//上一步
	function upToCsEntry() {
		$("#gotoCsEntry").click();

	}
	//下一步
	function next() {
		var val1 = $("#changeId").val();
		var val2 = $("#acceptId").val();
		var val3 = $("#customerId").val();
		var title = "受理信息录入";
		var tabid = "_aplPermit_M";
		var fresh = eval("true");
		var external = eval("false");
		var url = "${ctx }"
				+ "/cs/csEntry/showAcceptAndArapInfo_PA_csEntryAction.action?&menuId=${menuId}&changeId="
				+ val1 + "&acceptId=" + val2 + "&customerId=" + val3;
		navTab.openTab(tabid, url, {
			title : title,
			fresh : fresh,
			external : external
		});

	}
	//校验密码和
	function update() {
		//1.获取变更数据
		var status = $("#status").val();
		var password = $("#newPwd1").val();
		var customerId = $("#customerId").val();
		var errDate = $("#errDate").val();
		var changeId = $("#changeId").val();
		$
				.ajax({
					url : "${ctx }"
							+ "/cs/serviceitem_rz/updatePassword_PA_csEndorseRZAction.action",
					type : "post",
					data : "csCustomerPasswordVO.status=" + status
							+ "&csCustomerPasswordVONew.password=" + password
							+ "&customerId=" + customerId
							+ "&csCustomerPasswordVO.errDate=" + errDate
							+ "&changeId=" + changeId,
					dataType : 'text',
					success : function(data) {
						var json = jQuery.parseJSON(data);
						if (json.statusCode != 200) {
							alertMsg.info(json.message);
						}
					},
					error : function() {
						alertMsg.error(data);
					}
				});
	};

	/* 页面初始化加载 */
	$(document).ready(function() {
		//页面初始化的时候 判断是否为远程认证和柜面认证  是 则影藏银行信息和下面支付密码的文本框 如果为未认证则显示并且为必录项
		var levelCheck = $("#levelCheck").val();
		if ('远程认证' == levelCheck || '柜面认证' == levelCheck) {
			//影藏
			$("#accountName1", navTab.getCurrentPanel()).hide();
			$("#banckCode1", navTab.getCurrentPanel()).hide();
			$("#banckMoble1", navTab.getCurrentPanel()).hide();
			$("#payPasId", navTab.getCurrentPanel()).hide();
			$("#payPasId2", navTab.getCurrentPanel()).hide();

			//去掉必填项
			$('#accountName').removeClass("required");
			$('#banckCode').removeClass("required");
			$('#banckMoble').removeClass("required");
			$('#payPwd1').removeClass("required");
			$('#payPwd2').removeClass("required");

		}

		//如果客户设置过密码 则密码框的* 去掉 输入的时候视为修改 否则为必须项
		var passwordFlag = $("#passwordFlag").val();
		if ('1' == passwordFlag) {//设置过密码  并且字体为红色
			//把 class="required" 去掉
			$('#newPwd1').removeClass("required");
			$('#newPwd2').removeClass("required");
			$('#span1').css('color', 'red');
			$("#font1", navTab.getCurrentPanel()).hide();
			$("#font2", navTab.getCurrentPanel()).hide();

		} else {
			//去掉校验功能
			$('#newPwd1').removeAttr('onblur').on('blur', "");
			$("#span1", navTab.getCurrentPanel()).hide();
		}
	});

	function checkP(obj) {
		var val = $(obj).val();
		var ab = $(obj).val().split("");
		//验证是否是六位密码
		var reg = /^\d{6}$/;
		if (!reg.test(val)) {
			alertMsg.info("新密码应为6位阿拉伯数字，不可连续3位（含）以上数字重复，不可为数字递增或递减，请重新输入！");
			$(obj).val("");
			return false;
		}

		//验证是否是连续3个递减
		var flags = 0;
		for (var a = 0; a <= 5; a++) {
			for (var b = a; b < a + 1; b++) {
				if (eval(ab[b]) == eval(ab[b + 1]) + 1) {
					flags++;
					if (flags == 2) {
						alertMsg.info("新密码应为6位阿拉伯数字，不可连续3位（含）以上数字重复，不可为数字递增或递减，请重新输入！");
						$(obj).val("");
						flags = 0;
						return false;
					}
				} else {
					flags = 0;
				}
			}
		}

		//验证是否是递增 
		flags = 0;
		for (var a = 0; a <= 5; a++) {
			for (var b = a; b < a + 1; b++) {
				if (eval(ab[b]) == eval(ab[b + 1]) - 1) {
					flags++;
					if (flags == 2) {
						alertMsg.info("新密码应为6位阿拉伯数字，不可连续3位（含）以上数字重复，不可为数字递增或递减，请重新输入！");
						flags = 0;
						$(obj).val("");
						return false;
					}
				} else {
					flags = 0;
				}
			}
		}
		//验证是否是相等
		flags = 0;
		for (var a = 0; a <= 5; a++) {
			for (var b = a; b < a + 1; b++) {
				if (eval(ab[b]) == eval(ab[b + 1])) {
					flags++;
					if (flags == 2) {
						alertMsg.info("新密码应为6位阿拉伯数字，不可连续3位（含）以上数字重复，不可为数字递增或递减，请重新输入！");
						$(obj).val("");
						flags = 0;
						return false;
					}
				} else {
					flags = 0;
				}
			}
		}
		return true;
	}
</script>