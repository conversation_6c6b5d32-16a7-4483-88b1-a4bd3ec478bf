<%@ page contentType="text/html; charset=utf-8"%>
<%@ taglib uri="/struts-tags" prefix="s"%><%@include
	file="/udmpCommon.jsp"%>
<%@ taglib uri="http://www.git.com.cn/taglib/udmp/field" prefix="Field"%>
<%@ include file="/cs/pages/common/jsp/common_ctx.jsp"%>
<script type="text/javascript" src="${ctx}/cs/js/cs_common.js"></script>
<link rel="stylesheet" href="${ctx}/cs/css/cs_common.css"
	type="text/css">
<link rel="stylesheet" href="${ctx}/cs/css/public.css" type="text/css">

<!-- ********帮助菜单***********begin********* -->
<s:include value="/cs/pages/common/jsp/helpMenu.jsp"></s:include>
<!-- *********帮助菜单***********end********* -->
<div class="pageFormContent" layoutH="100px" style="padding-top: 5px;">
<%-- 进度条 --%>
<div class="step_header">
	<table width="100%" border="0" cellspacing="0" cellpadding="0">
		<tr>
			<td id="n1"width="2%"><div class="main_n1d">1</div></td>
			<td id="step1"><div class="main_step">保全任务查询</div></td>
			<td id="n2"width="2%"><div class="main_n1d">2</div></td>
			<td id="step2"><div class="main_step">保全录入</div></td>
			<td id="n3"width="2%"><div class="main_n1d">3</div></td>
			<td id="step3"><div class="main_step">变更信息入录</div></td>
		</tr>
	</table>
</div>

	<form action="" id="productEventCodeForm" onsubmit="" method="post">
		<div class="divfclass">
			<h1><img src="cs/img/icon/tubiao.png">事件信息录入</h1>
			<div class="pageFormContent">
				<table style="width: 100%; margin-bottom: 6px">
					<tbody>
						<tr>
							<td></td>
							<td style="width: 300px;">
									<div class="buttonContent">
										<button type="button" id="acceptEventCode" class="but_blue" onclick="acceptEventCode()">申请事件号</button>
										<button type="button" class="but_blue"
											onclick="queryExistEvent('productEventCodeForm','batchsurrenderDiv')">查询已有事件</button>
									</div>
							</td>
							<td></td>
						</tr>
					</tbody>
				</table>
				<div
					style="clear: both; margin-bottom: 10px; border-bottom: 1px solid rgb(184, 208, 214);"></div>
				<dl>
					<dt><font color="red">*</font>事件号码</dt>
					<dd>
						<input id="b_eventCode" name="batchSurrenderVO.eventCode" readonly="readonly"
							type="text" value="${batchSurrenderVO.eventCode}"
							/>
					</dd>
				</dl>
				<dl>
					<dt><font color="red">*</font>事件日期</dt>
					<dd>
						<input id="b_eventTime" name="batchSurrenderVO.eventTime"
							value="<s:date name="BatchSurrenderVO.eventTime" format="yyyy-MM-dd"/>"
							type="expandDateYMDRO"  />
					</dd>
				</dl>

				<dl>
					<dt><font color="red">*</font>事件名称</dt>
					<dd>
						<input id="b_eventName" name="batchSurrenderVO.eventName"
							type="text" value="${batchSurrenderVO.eventName}"
							 />
					</dd>
				</dl>
				<dl>
					<dt><font color="red">*</font>事件描述</dt>
					<dd>
						<input id="b_eventDesc" name="batchSurrenderVO.eventDesc"
							type="text" value="${batchSurrenderVO.eventDesc}"
							 />
					</dd>
				</dl>
				<table style="width: 100%;">
					<tbody>
						<tr>
							<td></td>
							<td style="width: 170px">
										<button type="button" class="but_blue" onclick="saveEventone()">保存</button>
							</td>
							<td></td>
						</tr>
					</tbody>
				</table>
			</div>
		</div>
	</form>
	<div class="buttonContent" style="display: none">
		<button type="button"  class="but_blue" onclick="saveEvent('cspolicyTaskForm','shareTaskResult')">保存</button>
	</div>
	<div id="batchsurrenderDiv" class="unitBox"></div>
	<form action="" id="productAgentCodeForm" onsubmit="" method="post">
		<div class="divfclass">
			<h1><img src="cs/img/icon/tubiao.png">受理信息</h1>
			<div class="pageFormContent">
				<dl>
					<dt><font color="red">*</font>是否已实付</dt>
					<dd>
						<select required="required" name="payFlag" style="width: 120px"
							id="payFlag">
							<option value="">请选择</option>
							<option value="0">已实付</option>
							<option value="1">未实付</option>
						</select>
					</dd>
				</dl>
				<dl>
					<dt><font color="red">*</font>申请方式</dt>
					<dd>
						<Field:codeTable cssClass="combox" nullOption="true"
							name="batchSurrenderVO.serviceType"
							tableName="APP___PAS__DBUSER.T_SERVICE_TYPE" id="serviceType"
							value="${batchSurrenderVO.serviceType}"
							onChange="_checkShowAgentDiv(this)" />
					</dd>
				</dl>
				<table style="width: 100%;">
					<tbody>
						<tr>
							<td></td>
							<td style="width: 170px">
										<button type="button"  class="but_blue" onclick="saveAgent()">保存</button>
							</td>
							<td></td>
						</tr>
					</tbody>
				</table>
			</div>
			<div style="clear: both;"></div>
			<div id="agentInfoEntryDiv" class="panel">
				<h1>代办人信息</h1>
				<div class="pageFormContent">
					<dl>
						<dt>业务员代码</dt>
						<dd>
							<input id="agentCode" name="batchSurrenderVO.agentCode"
								class="required" value="${batchSurrenderVO.agentCode}"
								type="text" />
						</dd>
					</dl>
					<dl>
						<dt>新业务员代码</dt>
						<dd>
							<input id="agentCodeNew" name="batchSurrenderVO.agentCodeNew"
								 value="${batchSurrenderVO.agentCodeNew}"
								type="text" />
						</dd>
					</dl>
					<dl>
						<dt>绩优等级</dt>
						<dd>
							<input id="agentLevel" name="batchSurrenderVO.agentLevel"
								class="digits required" value="${batchSurrenderVO.agentLevel}"
								type="text" /> <input id="result" type="hidden"
								value="${reresult}" />
						</dd>
					</dl>
					<dl>
						<dt>代办人证件类型</dt>
						<dd>
							<Field:codeTable nullOption="true" cssClass="combox"
								id="agentCeritType" name="batchSurrenderVO.agentCeritType"
								tableName="APP___PAS__DBUSER.T_CERTI_TYPE" />
						</dd>
					</dl>
					<dl>
						<dt>代办人姓名</dt>
						<dd>
							<input id="agentName" name="batchSurrenderVO.agentName"
								class="required" value="${batchSurrenderVO.agentName}"
								type="text" />
						</dd>
					</dl>
					<dl>
						<dt>代办人证件号码</dt>
						<dd>
							<input id="agentCeritCode" name="batchSurrenderVO.agentCeritCode"
								class="required" value="${batchSurrenderVO.agentCeritCode}"
								type="text" />
						</dd>
					</dl>
					<dl>
						<dt>代办人联系电话</dt>
						<dd>
							<input id="agentTel" name="batchSurrenderVO.agentTel"
								class="phone required" value="${batchSurrenderVO.agentTel}"
								type="expandMobile" />
						</dd>
					</dl>
				</div>
			</div>
		</div>
	</form>

	<div class="divfclass">
		<h1><img src="cs/img/icon/tubiao.png">保单信息录入</h1>
		<div class="pageFormContent">
			<dl style="width: 80px; margin-left: 50px; margin-top: 10px;">
				<dt>
					<a title="确定要导出这些记录吗?"
						href="${ctx}/cs/serviceitem_bs/exportToExcel_PA_csEndorseBSAction.action"
						target="dwzExport" id="exportToExcel" style="display: none;">
					</a>
					<button type="button" class="but_blue" onclick="exportToExcel()">模板导出</button>
				</dt>
			</dl>
			<form id="testss"
				action="${ctx}/cs/serviceitem_bs/exportBatchToExcel_PA_csEndorseBSAction.action"
				method="post" enctype="multipart/form-data" style="margin-top: 10px"
				onsubmit="return iframeCallback(this,testRespons);">
				<input type="hidden" name="eventCode" value="${eventCode }"/>
				<dl style="width: 350px; margin-top: 3px">
					<dt style="width: 100px;">
						<span>上传文件</span>
					</dt>
					<dd>
						<input type="file" name="fileName" id="fileName">
					</dd>
				</dl>
				<dl style="margin-left: 20px;">
					<dt>
						<button type="button"  class="but_blue" onclick="sumit()">批量导入</button>
					</dt>
						
				</dl>
			</form>
		</div>
		<%-- <div>
			<%@include  file="./CsEndorseBS_Error_Query.jsp"%>
		</div> --%>
		<!-- 展示导入数据 -->
		<div id="testDiv" class="pageContent"></div>
		<!-- 变更后的数据 -->
		<div id="changeEndDiv" class="pageContent"></div>
	</div>
	</div>
<script type="text/javascript">
$(document).ready(function() {
	$("#agentInfoEntryDiv", navTab.getCurrentPanel())
	.css("display", 'none');	
})
//申请事件号
function acceptEventCode(){
	var action = "${ctx}/cs/serviceitem_bs/createEventCode_PA_csEndorseBSAction.action";
	var onsubmit=  "return navTabSearch(this);";
	$("#productEventCodeForm", navTab.getCurrentPanel()).attr("action", action);
	$("#productEventCodeForm", navTab.getCurrentPanel()).attr("onsubmit", onsubmit);
	$("#productEventCodeForm", navTab.getCurrentPanel()).submit();
}
//查询已有事件
function queryExistEvent(){
    $("#batchsurrenderDiv").show();
    var action = "${ctx}/cs/serviceitem_bs/queryExitEvent_PA_csEndorseBSAction.action";
    var onsubmit = "return navTabSearch(this,'batchsurrenderDiv');";
	$("#productEventCodeForm", navTab.getCurrentPanel()).attr("action", action);
	$("#productEventCodeForm", navTab.getCurrentPanel()).attr("onsubmit", onsubmit);
	$("#productEventCodeForm", navTab.getCurrentPanel()).submit();
}

function sumit(){
	var fileName = $("#fileName").val();
	if(fileName == null || '' == fileName){
		alertMsg.info("请选择要导入的文件！");
		return false;
	}
    $("#testss").submit();
}

function testRespons(response){
// 	var json = DWZ.jsonEval(response);
var json = response;
	if (json.statusCode == DWZ.statusCode.error) {
		alertMsg.error(decodeURI(json.message));
	} else {
		var $box = $("#testDiv", navTab.getCurrentPanel());
		$box.html(response).initUI();
	}
	
};
//保存事件录入信息
function saveEventone(){
	
	var eventCode = $("#b_eventCode", navTab.getCurrentPanel()).val();
	var eventTime = $("#b_eventTime", navTab.getCurrentPanel()).val();
	var eventName = $("#b_eventName", navTab.getCurrentPanel()).val();
	var eventDesc = $("#b_eventDesc", navTab.getCurrentPanel()).val();
	if(eventCode == null || eventCode == ''){
		alertMsg.info("必填项信息未完整录入，不能受理批量退保，请确认！");
		return false;
	}
	if(eventTime == null){
		alertMsg.info("必填项信息未完整录入，不能受理批量退保，请确认！");
		return false;
	}
	if(eventName == null || eventName == ''){
		alertMsg.info("必填项信息未完整录入，不能受理批量退保，请确认！");
		return false;
	}
	if(eventDesc == null || eventDesc == ''){
		alertMsg.info("必填项信息未完整录入，不能受理批量退保，请确认！");
		return false;
	}
	
	$.ajax({
		url : "${ctx}/cs/serviceitem_bs/saveEventInfo_PA_csEndorseBSAction.action",
		type : "post",
		dataType : 'json',
		data : {
			"batchSurrenderVO.eventCode":eventCode,
			"batchSurrenderVO.eventTime":eventTime,
			"batchSurrenderVO.eventName":eventName,
			"batchSurrenderVO.eventDesc":eventDesc,
		},
		success : function(result){
			if(result.statusCode == 200){
				$("input[name='eventCode']", navTab.getCurrentPanel()).val(eventCode);
				alertMsg.correct("保存成功！");
			}else{
				alertMsg.info(result.message);
			}
		}
	});
	
		 /* var action = "${ctx}/cs/serviceitem_bs/saveEventInfo_PA_csEndorseBSAction.action";
		var onsubmit=  "return navTabSearch(this);";
		$("#productEventCodeForm").attr("action", action);
		$("#productEventCodeForm").attr("onsubmit", onsubmit);
		$("#productEventCodeForm").submit();
		var result=$("#result").val();
		if(result=="succ"){
		alertMsg.info("保存成功！");
		}else{
			alertMsg.error("保存失败！");
			
		}  */
	}
//保存受理信息
	function saveAgent(){
		var serviceType = $("#serviceType",navTab.getCurrentPanel()).val();
		var agentCode = $("#agentCode",navTab.getCurrentPanel()).val();
		var agentCodeNew = $("#agentCodeNew",navTab.getCurrentPanel()).val();
		var agentLevel = $("#agentLevel",navTab.getCurrentPanel()).val();
		var agentCeritType = $("#agentCeritType",navTab.getCurrentPanel()).val();
		var agentName = $("#agentName",navTab.getCurrentPanel()).val();
		var agentCeritCode = $("#agentCeritCode",navTab.getCurrentPanel()).val();
		var agentTel = $("#agentTel",navTab.getCurrentPanel()).val();
		
		$.ajax({
			url : "${ctx}/cs/serviceitem_bs/saveAgentInfo_PA_csEndorseBSAction.action",
			type : "post",
			dataType : 'json',
			data : {
				"batchSurrenderVO.serviceType":serviceType,
				"batchSurrenderVO.agentCode":agentCode,
				"batchSurrenderVO.agentCodeNew":agentCodeNew,
				"batchSurrenderVO.agentLevel":agentLevel,
				"batchSurrenderVO.agentCeritType":agentCeritType,
				"batchSurrenderVO.agentName":agentName,
				"batchSurrenderVO.agentCeritCode":agentCeritCode,
				"batchSurrenderVO.agentTel":agentTel,
			},
			success : function(result){
				if(result.statusCode == 200){
					alertMsg.correct("保存成功！");
				}else{
					alertMsg.info(result.message);
				}
			}
		});
			/*  var action = "${ctx}/cs/serviceitem_bs/saveAgentInfo_PA_csEndorseBSAction.action";
			var onsubmit=  "return navTabSearch(this);";
			$("#productAgentCodeForm").attr("action", action);
			$("#productAgentCodeForm").attr("onsubmit", onsubmit);
			$("#productAgentCodeForm").submit();
			var result=$("#result").val();
			if(result=="succ"){
				alertMsg.info("保存成功！");
			
			}else{
				alertMsg.error("保存失败！");
			} */
		/* 	$("#agentInfoEntryDiv", navTab.getCurrentPanel()).show();
			$("#agentInfoEntryDiv", navTab.getCurrentPanel())
			.find("a,select,input").removeAttr("disabled"); */
		} 
//保存批量退保信息
function saveEvent(formId,tableId){
// 	console.info(formId);
	//查询申请方式
	/*  var serviceType = $("#serviceType").val();
	if(serviceType == '' || serviceType == null){
		alertMsg.info("申请方式不能为空！");
		return false;
	}
	$("#serviceTypeHidden").val(serviceType);
	//申请方式为 
	if (serviceType == '02') {
		//业务员代码
		var agentId = $("#agentId").val();
		//代办人姓名
		var agentName = $("#agentName").val();
		//代办人证件类型
		var agentCertiType = $("#agentCertiType").val();
		//代办人证件号码
		var agentCertiCode = $("#agentCertiCode").val();
		//代办人联系电话
		var agentTel = $("#agentTel").val();
		//绩优等级
		var agentLevel = $("#agentLevel").val();
		// 验证代办人姓名
		if (agentName == null || agentName == '') {
			alertMsg.info("请输入代办人姓名！");
			return false;
		}
		//验证代办人证件号码
		if(agentCertiCode == null || agentCertiCode == ''){
			alertMsg.info("请输入代办人证件号码！");
			return false;
		}
		//验证代办人电话 
		if(agentTel == null || agentTel == ''){
			alertMsg.info("请输入代办人电话！");
			return false;
		}
		//代办人证件类型
		if(agentCertiType == null || agentCertiType == ''){
			alertMsg.info("请输入代办人证件类型！");
			return false;
		}
		//验证绩优等级
		if(agentLevel == null || agentLevel == ''){
			alertMsg.info("请输入绩优等级！");
			return false;
		}
		 var jsonBatchSurrenderVO = "[{";
		jsonBatchSurrenderVO += "'serviceType':'"+serviceType+"',";
		jsonBatchSurrenderVO += "'agentId':'"+agentId+"',";
		jsonBatchSurrenderVO += "'agentName':'"+agentName+"',";
		jsonBatchSurrenderVO += "'agentCertiType':'"+agentCertiType+"',";
		jsonBatchSurrenderVO += "'agentCertiCode':'"+agentCertiCode+"',";
		jsonBatchSurrenderVO += "'agentTel':'"+agentTel+"',";
		jsonBatchSurrenderVO += "'agentLevel':'"+agentLevel+"'";
		jsonBatchSurrenderVO += "}]"; 
		 $("#jsonBatchSurrenderVO").val(jsonBatchSurrenderVO);
	}else{
		var jsonBatchSurrenderVO = "[{";
		jsonBatchSurrenderVO += "'serviceType':'"+serviceType+"'";
		jsonBatchSurrenderVO += "}]";
		$("#jsonBatchSurrenderVO").val(jsonBatchSurrenderVO); 
	}
	if(serviceType == '' || null == serviceType){
 		alertMsg.info("请选择申请方式");
	} */
	var $dataTable = $("#bfChangeMsgTable",navTab.getCurrentPanel());
	var $form = $("#"+formId,navTab.getCurrentPanel());
	var testTr =  $dataTable.find("tr");
	if(testTr.length == 0){
		alertMsg.info("至少录入一张保单！");
		return false;
	}
	//alert(_cs_tableToJson($dataTable));
	debugger;
	var _jsons = "";
	_jsons +="[";
	for(var a = 0; a<testTr.length; a++){
		
		_jsons += "{";
		
		var testOneTr = $(testTr[a],navTab.getCurrentPanel());
		if(testOneTr.find("td:eq(0)").find("input").val()!=null){
		_jsons += "'sequenceNumber':'"+testOneTr.find("td:eq(0)").find("input").val()+"',";
		}
		if(testOneTr.find("td:eq(1)").find("input").val()!=null){
		_jsons += "'policyCode':'"+testOneTr.find("td:eq(1)").find("input").val()+"',";
		}
		if(testOneTr.find("td:eq(2)").find("input").val()!=null){
		_jsons += "'holderName':'"+testOneTr.find("td:eq(2)").find("input").val()+"',";
		}
		if(testOneTr.find("td:eq(3)").find("input").val()!=null){
		_jsons += "'validateDate':'"+testOneTr.find("td:eq(3)").find("input").val()+"',";
		}
		if(testOneTr.find("td:eq(4)").find("input").val()!=null){
		_jsons += "'applyTime':'"+testOneTr.find("td:eq(4)").find("input").val()+"',";
		}
		if(testOneTr.find("td:eq(5)").find("input").val()!=null){
		_jsons += "'validateDateSurrender':'"+testOneTr.find("td:eq(5)").find("input").val()+"',";
		}
		if(testOneTr.find("td:eq(6)").find("input").val()!=null){
		_jsons += "'payAmount':'"+testOneTr.find("td:eq(6)").find("input").val()+"',";
		}
		if(testOneTr.find("td:eq(7)").find("input").val()!=null){
		_jsons += "'realPayFlag':'"+testOneTr.find("td:eq(7)").find("input").val()+"',";
		}
		if(testOneTr.find("td:eq(8)").find("input").val()!=null){
		_jsons += "'realPayAmount':'"+testOneTr.find("td:eq(8)").find("input").val()+"',";
		}
		if(testOneTr.find("td:eq(9)").find("input").val()!=null){
		_jsons += "'survivalExtraFlag':'"+testOneTr.find("td:eq(9)").find("input").val()+"',";
		}
		if(testOneTr.find("td:eq(10)").find("input").val()!=null){
		_jsons += "'payMode':'"+testOneTr.find("td:eq(10)").find("input").val()+"',";
		}
		
		if(testOneTr.find("td:eq(11)").find("select[name='bankCode']").val()!=null){
		_jsons += "'bankCode':'"+testOneTr.find("td:eq(11)").find("select[name='bankCode']").val()+"',";
		}
		if(testOneTr.find("td:eq(12)").find("input").val()!=null){
		_jsons += "'bankAccount':'"+testOneTr.find("td:eq(12)").find("input").val()+"',";
		}
		if(testOneTr.find("td:eq(13)").find("input").val()!=null){
		_jsons += "'bankAccountName':'"+testOneTr.find("td:eq(13)").find("input").val()+"',";
		}
		_jsons = _jsons.substring(0, _jsons.length - 1);
		_jsons += "},";
	}
	_jsons = _jsons.substring(0, _jsons.length - 1);
	_jsons += "]";
	$("#jsonData").val(_jsons);
	/* alert($form.serialize());
	var onsubmit = "return divSearch(this,'changeEndDiv')";
	var action = "${ctx}/cs/serviceitem_bs/saveBatchBSinfo_PA_csEndorseBSAction.action";
	$form.attr("onsubmit",onsubmit);
	$form.attr("action",action);
	$form.submit(); */
	$.ajax({
			type:'POST',
			url:"${ctx}/cs/serviceitem_bs/infoAccept_PA_csEndorseBSAction.action",
			data : "jsonData="+encodeURI(encodeURI(_jsons))+"&&eventCode="+$("#b_eventCode",navTab.getCurrentPanel()).val()+"&&"+$("#productAgentCodeForm",navTab.getCurrentPanel()).serialize(),
			cache: false,
			success :function (response){
				var json = DWZ.jsonEval(response);
				if (json.statusCode == 200) {
					
					/**
					*录入之前先校验提示信息
					*/
					$.ajax({
						type:'POST',
						url:"${ctx}/cs/serviceitem_bs/saveBatchBSinfoAfter_PA_csEndorseBSAction.action",
						data : "jsonData="+encodeURI(encodeURI(_jsons))+"&&eventCode="+$("#b_eventCode",navTab.getCurrentPanel()).val()+"&&"+$("#productAgentCodeForm",navTab.getCurrentPanel()).serialize(),
						cache: false,
						success :function (response){
							var flg = true;
							var json = DWZ.jsonEval(response);
								if(json.statusCode == 400){
									return null;
								}
								if (json.statusCode == 200) {
									//受理成功-->录入
									$.ajax({
										type:'POST',
										url:"${ctx}/cs/serviceitem_bs/updateAcceptStatus_PA_csEndorseBSAction.action",
										data : "jsonData="+encodeURI(encodeURI(_jsons))+"&&eventCode="+$("#b_eventCode",navTab.getCurrentPanel()).val()+"&&"+$("#productAgentCodeForm",navTab.getCurrentPanel()).serialize(),
										cache: false,
										success :function (response){
											var json = DWZ.jsonEval(response);
											if(typeof(json.statusCode) == "undefined"){
												$("#changeEndDiv", navTab.getCurrentPanel()).empty();	
												$("#changeEndDiv", navTab.getCurrentPanel()).append(response).initUI();
											}else{
												if (json.statusCode == 200) {
													//录入完成
													alertMsg.info("完成批量退保");
												}else{//出错
													alertMsg.error(json.message);
												}
											}
											
										}
									});
								}else{//有提示信息
									alertMsg.confirm(json.message, {
										okCall : function(){
											//受理成功-->录入
											$.ajax({
												type:'POST',
												url:"${ctx}/cs/serviceitem_bs/updateAcceptStatus_PA_csEndorseBSAction.action",
												data : "jsonData="+encodeURI(encodeURI(_jsons))+"&&eventCode="+$("#b_eventCode",navTab.getCurrentPanel()).val()+"&&"+$("#productAgentCodeForm",navTab.getCurrentPanel()).serialize(),
												cache: false,
												success :function (response){
													var json = DWZ.jsonEval(response);
													if(typeof(json.statusCode) == "undefined"){
														$("#changeEndDiv", navTab.getCurrentPanel()).empty();
														$("#changeEndDiv", navTab.getCurrentPanel()).append(response).initUI();
													}else{
														if (json.statusCode == 200) {
															//录入完成
															alertMsg.info("完成批量退保");
														}else{//出错
															alertMsg.error(json.message);
														}
													}
													
												}
											});
										},
										cancleCall : function() {
											
										}
									});
								}
						}
					});
					
				}else{//出错
					debugger;
					alertMsg.error(json.message);
				}
			}
		});
}

function exportToExcel(){
	$("#exportToExcel", navTab.getCurrentPanel()).click();
}
//显示代办人信息输入区域
function _checkShowAgentDiv(obj) {
	var $this = $(obj);
	var _value = $this.val();
	var _text = $this.find("option[value='" + _value + "']").text();
	if (_text.indexOf('代办') > 0) {
		$("#agentInfoEntryDiv", navTab.getCurrentPanel()).show();
		$("#agentInfoEntryDiv", navTab.getCurrentPanel())
				.find("a,select,input").removeAttr("disabled");
	} else {
		$("#agentInfoEntryDiv", navTab.getCurrentPanel()).hide();
		$("#agentInfoEntryDiv", navTab.getCurrentPanel())
				.find("a,select,input").attr("disabled", "disabled");
		$("#agentInfoEntryDiv", navTab.getCurrentPanel()).find("#agentId").val(
				"");
	}

}
</script>