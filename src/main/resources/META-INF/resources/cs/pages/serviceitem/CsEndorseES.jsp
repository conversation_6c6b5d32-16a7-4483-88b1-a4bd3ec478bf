<%@ page language="java" contentType="text/html; charset=utf-8"
	pageEncoding="utf-8"%>
<%@ taglib uri="/struts-tags" prefix="s"%><%@include
	file="/udmpCommon.jsp"%>
<!-- 引入点击下一步返回保全录入的js -->
<%-- <s:set var="ctx">${pageContext.request.contextPath}</s:set> --%>
<%@ include file="/cs/pages/common/jsp/common_ctx.jsp"%>
<script type="text/javascript"
	src="cs/pages/common/js/returnAcceptAndPay.js"></script>
<%@ taglib uri="http://www.git.com.cn/taglib/udmp/field" prefix="Field"%>
<script type="text/javascript" src="${ctx}/cs/js/cs_common.js"></script>
<link rel="stylesheet" href="${ctx }/cs/css/cs_common.css"
	type="text/css">
<link rel="stylesheet" href="${ctx}/cs/css/public.css" type="text/css">

<input type="hidden" name="menuId" id="menuId" value="${menuId}">
<input type="hidden" name="itemFlag" id="itemFlag" value="mainProd">
<input type="hidden" name="itemName" id="itemName" value="连续投保">
<!-- 帮助菜单 -->
<!-- ********帮助菜单***********begin********* -->
<s:include value="/cs/pages/common/jsp/helpMenu.jsp"></s:include>
<s:include value="csEndorseProgress.jsp" />
<!-- *********帮助菜单***********end********* -->
<div layoutH="140">
	<div class="pageContent">
		<s:include value="customerInfo_list.jsp" />

		<div class="divfclass">
			<h1>
				<img src="images/tubiao.png">保单主险基本信息
			</h1>
			<div class="pageFormContent">
				<table id="aa" class="list" width="100%">
					<thead>
						<tr>
							<th>保单号</th>
							<th>险种编码</th>
							<th>险种名称</th>
							<th>被保人客户号</th>
							<th>被保人姓名</th>
							<th>标准保费</th>
							<th>份数</th>
							<th>基本保额</th>
							<th>生效日期</th>
							<th>交费对应日</th>
						</tr>
					</thead>
					<tbody>
						<s:iterator value="mainsEndorseESVOs" status="st" id="BFList1">
							<tr align="center">
								<td>${policyCode }</td>
								<td>${busiProdCode }</td>
								<td><Field:codeValue
										tableName="APP___PAS__DBUSER.T_BUSINESS_PRODUCT"
										value="${busiPrdId}" /></td>
								<td>${insuredListCode }</td>
								<td>${insuredName }</td>
								<td>${TotalPremAf }</td>
								<td>${unit }</td>
								<td>${basicAmount}</td>
								<td><s:date name="validateDate" format="yyyy-MM-dd"></s:date></td>
								<td><s:date name="payDueDate" format="yyyy-MM-dd"></s:date></td>
							</tr>
						</s:iterator>
					</tbody>
				</table>
			</div>
		</div>
		<!-- validateCallback  -->
		<div class="pageFormContent">
			<form id="addtionNoRenewalForm"
				action="${ctx}/cs/serviceitem_es/addtionBusiPrdNRenewal_PA_csEndorseESAction.action?flag=1"
				class="pageForm required-validate" method="post"
				onsubmit="return navTabSearch(this);">
				<input type="hidden" id="customerId" name="customerId"value="${customerId}" /> 
				<input type="hidden" id="acceptId" name="acceptId" value="${acceptId}" /> 
				<input type="hidden" id="changeId" name="changeId" value="${changeId}" />
				<input type="hidden" id="policyChgId" name="policyChgId" value="${policyChgId}" />
				<input type="hidden" id="plyIdsAndBuIdsAndRwfg" name="plyIdsAndBuIdsAndRwfg" value="${plyIdsAndBuIdsAndRwfg}" />
				<input type="hidden" id="payModeId" name="payModeId"  />
				<input type="hidden" id="bankCode" name="bankCode"  />
				<input type="hidden" id="bankAccount" name="bankAccount" />
				<input type="hidden" id="medicalInsuranceCard" name="medicalInsuranceCard"  />
				<input type="hidden" id="accountName" name="accountName"  />
				<div class="divfclass">
					<h1>
						<img src="images/tubiao.png">满期续保
					</h1>
					<div>
					<div class="pageFormContent">
						<table id="addtionRenewoalTable" class="list" width="100%">
							<thead>
								<tr>
									<th><input type="checkbox" id="checkBoxs"
										style="position: relative; top: 2px; right: 5px;"
										class="checkboxCtrl" group="acceptIds" value="0000" />选择</th>
									<th>保单号</th>	
									<th>险种编码</th>
									<th>险种名称</th>
									<th>主险编码</th>
									<th>保额</th>
									<th>保费</th>		
									<th>份数</th>
									<th>生效日期</th>
									<th>交费对应日</th>
								</tr>
							</thead>
							
							<tbody id="addtionRenewoalbody">
								<s:iterator value="csEndorseESVOs" status="st" id="BFList">
									<tr align="center">
									<td>
									<s:if test= "yesOrNO == 1">
									<input type="checkbox"  name="acceptIds"
													value="${policyChgId}|${busiPrdId}"	
													disabled 												
													class="acceptIds">
									</s:if><s:elseif test="operationType == 2">
											<input type="checkbox" checked="checked" name="acceptIds"
													value="${policyChgId}|${busiPrdId}"
													class="acceptIds">
											</s:elseif> <s:else>
												<input type="checkbox" name="acceptIds"
													value="${policyChgId}|${busiPrdId}"
													class="acceptIds">
											</s:else></td>
										<s:if test="yesOrNO == 1">
										<td disabled>${policyCode}</td>
										<td disabled>${busiProdCode}</td>
										<td disabled><Field:codeValue
												tableName="APP___PAS__DBUSER.T_BUSINESS_PRODUCT"
												value="${busiPrdId}" /></td>
										<td disabled>${mainBusiProdCode}</td>
										<td disabled>${basicAmount}</td>
										<td disabled>${TotalPremAf }</td>
										<td disabled>${unit }</td>
										<td disabled><s:date name="validateDate" format="yyyy-MM-dd"></s:date></td>
										<td disabled><s:date name="payDueDate" format="yyyy-MM-dd"></s:date></td>
										</s:if> 
										<s:else>
										<td>${policyCode}</td>
										<td>${busiProdCode}</td>
										<td><Field:codeValue
												tableName="APP___PAS__DBUSER.T_BUSINESS_PRODUCT"
												value="${busiPrdId}" /></td>
										<td>${mainBusiProdCode}</td>
										<td>${basicAmount}</td>
										<td>${TotalPremAf }</td>
										<td>${unit }</td>
										<td><s:date name="validateDate" format="yyyy-MM-dd"></s:date></td>
										<td><s:date name="payDueDate" format="yyyy-MM-dd"></s:date></td>
										</s:else>
										
									</tr>
								</s:iterator>
							</tbody>
						</table>
					</div>
					</div>
					<div class="divfclass">
						<h1>
							<img src="images/tubiao.png">续期缴费信息
						</h1>
						<dl>
							<dt>交费方式</dt>
							<dd>
								<Field:codeTable cssClass="combox" id="cpayModeId" 
									nullOption="false" name="csEndorseES.payNext" 
									whereClause="code in (32,20,31,10,18)" defaultValue="32"
									tableName="APP___PAS__DBUSER.T_PAY_MODE" value="${csEndorseES.payNext}"
									onChange="changeaccountName()"
									/>
							</dd>
						</dl>
						<dl>
							<dt>开户银行</dt>
							<dd>
								<input type="text" class="autocomplete textLeft"
									name="csEndorseES.bankCode"
									style="width: 60px; margin-right: -2px;"
									value="${csEndorseES.bankCode}" data-width="321px"
									data-showValue="nbank" data-mode="2" data-disCurrentValFlag="1"
									data-tableName="APP___PAS__DBUSER.T_BANK"
									data-tableCol="BANK_CODE"
									data-tableDesc="BANK_CODE||'-'||BANK_NAME" data-separator="-"
									data-like="1" data-orderBy="BANK_CODE" data-view="BANK_NAME"
									data-view="TYPE"
									data-whereClause="BANK_NAME LIKE 'NTS%' AND IS_CREDIT_CARD='0'"
									id = "cbankCode" 
									 />
								<input type="text" id="nbank" class="textRight" readOnly
									style="width: 100px; margin-right: -134px;" />
							</dd>
						</dl>
						<dl>
							<dt>银行账号</dt>
							<dd>
								<input type="text" value="${csEndorseES.bankAccount }"
									name="csEndorseES.bankAccount" id = "cbankAccount" onblur="bankAccountMethod(this)"  />
						 <input type="hidden" id="cbankAccountCode" value=""  />	
							<font style="color: red; font-size: 11px; width: 150px;" id="certiCodeDIV"></font>
									
							</dd>
						</dl>
						<dl>
							<dt>医保卡号</dt>
							<dd>
								<input type="text"
									value="${csEndorseES.medicalInsuranceCard }"
									name="csEndorseES.medicalInsuranceCard" id = "cmedicalInsuranceCard"  />
							</dd>
						</dl>
						<dl>
							<dt>户名</dt>
							<dd>
								<input type="text" value="${csEndorseES.accountName }"
									name="csEndorseES.accountName" id = "caccountName"  />
								<input type="hidden"  value="${csEndorseES.accountName }"
									name="csEndorseES.accountName" id = "accountName_new" />
								<input type="hidden" value="${csEndorseES.insuredName }"
									name="csEndorseES.insuredName" id = "insuredNameNew" />	
							</dd>
						</dl>
					</div>
					<s:if test="queryFlag!=1">
					<table style="width: 100%; margin-top: 10px;">
						<tbody>
							<tr>
								<td></td>
								<td style="width: 150px">
									<div class="pageFormdiv">
										<div class="buttonContent">
											<button type="button" class="but_blue"
												onclick="addtionNoRenewalSave('addtionNoRenewalForm')">保存</button>
										</div>
									</div>
								</td>
								<td></td>
							</tr>
						</tbody>
					</table>
					</s:if>
				</div>				
			</form>
		</div>
	</div>
</div>
<s:include value="/cs/pages/serviceitem/serviceItemBottomBar.jsp"></s:include>
<script type="text/javascript">
	$(document).ready(function() {
		csHelpMenu();
	});
	
	function addtionNoRenewalSave(formId) {	
		// 获取续期缴费信息
		var payModeId = $("#cpayModeId").val();
		var bankCode = $("#cbankCode").val();
		var bankAccount = $("#cbankAccount").val();
		var medicalInsuranceCard = $("#cmedicalInsuranceCard").val();
		var accountName = $("#caccountName").val();
		if(payModeId == '31' || payModeId == '32'){
			if(bankCode==null || bankCode == ''){
				alertMsg.info("交费形式为银行转账制返盘、银行转账非制返盘, 开户银行为必录项");
				return false;
			}
			if(bankAccount==null || bankAccount == ''){
				alertMsg.info("交费形式为银行转账制返盘、银行转账非制返盘, 银行账号为必录项"); 
				return false;
			}
		}
		if(payModeId == '18' ){
			if(medicalInsuranceCard==null || medicalInsuranceCard == ''){
				alertMsg.info("交费形式为社保缴纳, 医保卡号为必录项");
				return false;
			}
		}
		 alertMsg.confirm("是否保存？", {
				okCall : function() { 
					var $form = $("#addtionNoRenewalForm", navTab.getCurrentPanel());
					var plyIdsAndBuIdsAndRwfg = "";
					var trchecked=$("#addtionRenewoalbody").find("tr").find("input:checkbox:checked");
					var count=$(trchecked).size();
					if($(trchecked).size()>0){
						for (var i = 0; i <$(trchecked).size(); i++) {
							var checkId = $($(trchecked)[i], navTab.getCurrentPanel());
							if (checkId.val() == 0000) {
								continue;
							}
							plyIdsAndBuIdsAndRwfg += checkId.val().trim() + ",";
						}
						$("#plyIdsAndBuIdsAndRwfg", navTab.getCurrentPanel()).attr("value",plyIdsAndBuIdsAndRwfg.trim());
						$("#payModeId", navTab.getCurrentPanel()).attr("value",payModeId.trim());
						$("#bankCode", navTab.getCurrentPanel()).attr("value",bankCode.trim());
						$("#bankAccount", navTab.getCurrentPanel()).attr("value",bankAccount.trim());
						$("#medicalInsuranceCard", navTab.getCurrentPanel()).attr("value",medicalInsuranceCard.trim());
						$("#accountName", navTab.getCurrentPanel()).attr("value",accountName.trim());
						$form.submit();
					} else {
						alertMsg.info("请选择满期续保条目");
						return false;
					}
				},
				cancelCall : function() {
				}
			});  
		
		
	}
	//上一步
	function upToCsEntry() {
		alertMsg
				.confirm(
						"请确认是否需要保存变更的信息？",
						{
							okCall : function() {
								//保存变更信息---返回保全录入主页面
								var onsubmit = "return validateCallback(this,reusltAjaxDone)";
								$("#mainProdRenewalForm",
										navTab.getCurrentPanel()).attr(
										"onsubmit", onsubmit);
								var action = "${ctx}/cs/serviceitem_mr/mainProdRenewal_PA_csEndorseMRAction.action?flag=2";
								$("#mainProdRenewalForm",
										navTab.getCurrentPanel()).attr(
										"action", action);
								mainProdRenewal();
							},
							cancelCall : function() {
								$("#gotoCsEntry").click();
							}
						});
	}
	//下一步
	function nextStep() {
		var onsubmit = "return validateCallback(this,reusltAjaxDone)";
		$("#addtionNoRenewalForm", navTab.getCurrentPanel()).attr("onsubmit",
				onsubmit);
		var action = "${ctx}/cs/serviceitem_es/addtionBusiPrdNRenewal_PA_csEndorseESAction.action?flag=2";
		$("#addtionNoRenewalForm", navTab.getCurrentPanel()).attr("action",
				action);
		addtionNoRenewalSave('addtionNoRenewalForm', 'addtionRenewoalTable');
	}
	//回调函数，跳转页面
	function reusltAjaxDone(json) {
		DWZ.ajaxDone(json);
		if (json.statusCode == DWZ.statusCode.ok) {
			$("#gotoCsEntry").click();
		} else {
			alertMsg.error("系统异常!");
		}
	}
	//下一步
	function next() {
		var val1 = $("#changeId").val();
		var val2 = $("#acceptId").val();
		var val3 = $("#customerId").val();
		var title = "受理信息录入";
		var tabid = "_aplPermit_M";
		var fresh = eval("true");
		var external = eval("false");
		var url = "${ctx}/cs/csEntry/showAcceptAndArapInfo_PA_csEntryAction.action?&menuId=${menuId}&changeId="
				+ val1 + "&acceptId=" + val2 + "&customerId=" + val3;
		navTab.openTab(tabid, url, {
			title : title,
			fresh : fresh,
			external : external
		});

	}
	function changeaccountName() {
		var tpayModeId = $("#cpayModeId", navTab.getCurrentPanel()).val();
		var taccountName = $("#insuredNameNew", navTab.getCurrentPanel()).val();
		if(tpayModeId == '31' || tpayModeId == '32' || tpayModeId == '18'){
			$("#caccountName", navTab.getCurrentPanel()).attr("disabled","disabled");
			$("#caccountName", navTab.getCurrentPanel()).val(taccountName);
		}else{
			$("#caccountName", navTab.getCurrentPanel()).attr("disabled",false);
			$("#caccountName", navTab.getCurrentPanel()).val("");
		}
	}
	$(document).ready(function() {
		_cs_initMultipleBox();
		var tpayModeId = $("#cpayModeId", navTab.getCurrentPanel()).val();
		var taccountName = $("#accountName_new", navTab.getCurrentPanel()).val();
 		if(tpayModeId !="10" ){
 			$("#caccountName", navTab.getCurrentPanel()).attr("disabled","disabled");
 			$("#caccountName", navTab.getCurrentPanel()).val(taccountName);
 		}
	});
	/* * 
	 *  银行卡号，两次校验
	 */
	function bankAccountMethod(obj) {
		var cbankAccount = obj.value;
		var cbankAccountCode = $("#cbankAccount", navTab.getCurrentPanel()).val();
		if ($("#cbankAccountCode").val() == "false") {
			obj.value = "";
			$("#cbankAccountCode").val("");
			$("#certiCodeDIV").html('请重新输入');
		} else if ($("#cbankAccountCode").val() == ""
				|| $("#cbankAccountCode").val() == null) {
			$("#cbankAccountCode").attr("value", cbankAccountCode);
			obj.value = "";
			$(obj).focus();
			$("#certiCodeDIV").html('请再次输入');
		} else {
			if (obj.value == $("#cbankAccountCode").val()) {
				$("#cbankAccountCode").val("");
				$("#cbankAccountCode").attr("class", "textInput holder");
				$("#certiCodeDIV").html("");
			} else {
				$("#cbankAccountCode").attr("class", "error");
				$("#cbankAccountCode").val("");
				$("#certiCodeDIV").html("");
				$(obj).val("");
				alertMsg.warn("两次输入银行账号不一致!");
				return;
			}
		}
	}	
</script>
