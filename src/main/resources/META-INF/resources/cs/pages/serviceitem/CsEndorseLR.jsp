<%@ page contentType="text/html; charset=utf-8"%>
<%@ taglib uri="/struts-tags" prefix="s"%><%@include file="/udmpCommon.jsp"%>
<%@ taglib uri="http://www.git.com.cn/taglib/udmp/field" prefix="Field"%>
<%@ include file="/cs/pages/common/jsp/common_ctx.jsp"%>
<script type="text/javascript" src="${ctx}/cs/js/cs_common.js"></script>
<link rel="stylesheet" href="${ctx}/cs/css/cs_common.css" type="text/css">
<link rel="stylesheet" href="${ctx}/cs/css/public.css" type="text/css">
<script type="text/javascript" src="${ctx}/cs/js/DomZoomZX.js"></script>
<script>
	$(document).ready(function(){
        var D = DomZoomZX('input,select,textarea,td')
/*  		var printMode = $("#printMode",navTab.getCurrentPanel()).val().trim();
		if(printMode == '2' || printMode == '外包打印'){
			$("#receiverDiv",navTab.getCurrentPanel()).show();
	 	}else{
	 		$("#receiverDiv",navTab.getCurrentPanel()).attr("style", "display:none;");
	 	}  */
	})
</script>
<!-- ********帮助菜单***********begin********* -->
<s:include value="/cs/pages/common/jsp/helpMenu.jsp"></s:include>
<s:if test="queryFlag!=1">
<s:include value="csEndorseProgress.jsp" />
</s:if>
<%-- <s:if test="queryFlag!=1">
<div class="step_header">
	<table width="100%" border="0" cellspacing="0" cellpadding="0">
		<tr align="center">
			<td rowspan="2" class="n1d" id="n1"></td>
			<td class="step" id="step1">保全任务查询</td>
			<td rowspan="2" class="n2d" id="n2"></td>
			<td class="step" id="step2">保全录入</td>
			<td rowspan="2" class="n3d" id="n3"></td>
			<td class="step" id="step3">保单补发</td>
			<td class="step_end"></td>
		</tr>
		<tr>
			<td height="8"></td>
		</tr>
	</table>
</div>
</s:if> --%>
<div layoutH=140>
<s:include value="customerInfo_list.jsp" />
	<div class="panel" style="display: none">
		<h1>客户基本信息</h1>
		<div class="pageFormContent">
			<dl>
				<dt>客户姓名</dt>
				<dd>
					<input type="text" id="customerName"
						name="csCustomerVO.customerName"
						value="${csCustomerVO.customerName }" readonly="readonly">
				</dd>
			</dl>
			<dl>
				<dt>出生日期</dt>
				<dd>
					<input readonly="readonly" id="customerBirthday"
						name="customerBirthday"
						value="<s:date format="yyyy-MM-dd" name="csCustomerVO.customerBirthday"></s:date>" />
				</dd>
			</dl>
			<dl>
				<dt>证件类型</dt>
				<dd>
					<input type="text" readonly="readonly" id="customerCertType"
						name="customerCertType"
						value="<Field:codeValue  tableName="APP___PAS__DBUSER.T_CERTI_TYPE" value="${csCustomerVO.customerCertType}" />" />
				</dd>
			</dl>
			<dl>
				<dt>证件号码</dt>
				<dd>
					<input type="text" id="customerIdCode" name="customerIdCode"
						value="${csCustomerVO.customerCertiCode}" readonly="readonly">
				</dd>
			</dl>
			<dl>
				<dt>性别</dt>
				<dd>
					<input type="text" id="customerGender" name="customerGender"
						value="<Field:codeValue  tableName="APP___PAS__DBUSER.T_GENDER" value="${csCustomerVO.customerGender}" /> "
						readonly="readonly">
				</dd>
			</dl>
		</div>
	</div>
		<div class="divfclass">
			<h1>
				<img src="images/tubiao.png" >保单险种列表信息
			</h1>
		</div>
		<div>
			<div class="pageFormContent">
			<table class="list" width="100%" id="policyBFDetailTable">
				<thead>
					<tr>
						<th>序号</th>
						<th>保单号</th>
						<th>险种代码</th>
						<th>险种名称</th>
						<th>保额</th>
						<th>保费</th>
						<th>生效日期</th>
						<th>保单状态</th>
					</tr>
				</thead>
				<tbody id="policyBFDetailtbody">
					<s:iterator value="reissueBFList" status="st" id="reissueBFList">
						<tr align="center">
							<td><s:property value="#st.count" /></td>
							<td>${policyCode}</td>
							<td>${productItem}</td>
							<td><Field:codeValue tableName="APP___PAS__DBUSER.T_BUSINESS_PRODUCT"
									value="${productName}" /></td>
							<td>${basicSA}</td>
							<td>${productFee}</td>
							<td><s:date format="yyyy-MM-dd" name="validateDate" /></td>
							<td><Field:codeValue tableName="APP___PAS__DBUSER.T_LIABILITY_STATUS"
									value="${liabilityState}" /></td>
						</tr>
					</s:iterator>
				</tbody>
			</table>
			</div>
		</div>
	<div class="divfclass">
			<h1>
				<img src="images/tubiao.png" >补发信息
			</h1>
		</div>
		
	<form method="post"
		action="${ctx}/cs/serviceitem_lr/savePolicyReissueInfo_PA_csEndorseLRAction.action"
		class="required-validate" onsubmit="return validateCallback(this);"
		id="saveReissueInfoForm">
			<div class="pageFormContent" id ="lrEntryDiv" >
				<input type="hidden" id="customerId" name="customerId"
					value="${customerId}" /> <input type="hidden" id="acceptId"
					name="acceptId" value="${acceptId}" /> <input type="hidden"
					id="changeId" name="changeId" value="${changeId}" />
				<input type="hidden" id="customerEmail" value="${csCustomerVO.email}" >
				<input type="hidden" id="customerMobileTel" value="${csCustomerVO.mobileTel}" >
				<input type="hidden" id="customerId" name="reissueVO.customerId" value="${csCustomerVO.customerId}" >
				<input type="hidden" id="policyCode" name="reissueVO.policyCode" value="${reissueBFList.get(0).policyCode}" >
				<s:if test="queryFlag==1">
				
					<dl>
						<dt>
							补发原因<span style="color: red">*</span>
						</dt>
						<dd>
							<input type="text" id='reissueCause'
								name="reissueVO.reissueCause"
								value="<Field:codeValue  tableName="APP___PAS__DBUSER.T_POLICY_REISSUE_CAUSE" value="${reissueVO.reissueCause}" /> "
								readonly="readonly" >
						</dd>
					</dl>
					<dl>
						<dt>丢失日期</dt>
						<dd>
							<input id='loseDate' name="reissueVO.loseDate"
								value="<s:date format="yyyy-MM-dd" name="reissueVO.loseDate"></s:date>"
								readonly="readonly">
						</dd>
					</dl>

					<dl>
						<dt>
							保单类型<span style="color: red">*</span>
						</dt>
						<dd>
							<input type="text" id='printType' name="reissueVO.printType"
								value="<Field:codeValue  tableName="APP___PAS__DBUSER.T_MEDIA_TYPE" value="${reissueVO.printType}" /> "
								readonly="readonly">
						</dd>
					</dl>
					<s:if test="reissueVO.informType!=null">
					<dl>
						<dt>
							保单通知方式<span style="color: red">*</span></span>
						</dt>
						<dd>
							<input type="text" id='informType' name="reissueVO.informType"
								value="<Field:codeValue  tableName="APP___PAS__DBUSER.T_INFORM_TYPE" value="${reissueVO.informType}" /> "
								readonly="readonly">
						</dd>
					</dl>
					</s:if>
					<dl>
						<dt>
							保单打印方式<span style="color: red">*</span>
						</dt>
						<dd>
							 <input type="text" id='printMode' name="reissueVO.printMode"
								value="<Field:codeValue  tableName="APP___PAS__DBUSER.T_PRINT_MODE" value="${reissueVO.printMode}" /> "
								readonly="readonly">
						</dd>
					</dl> 
					<div id="receiverDiv"  style = "display:none;" >
						<dl>
							<dt>收件人姓名</dt>
							<dd>
								<input id="receiverName" type="text" name="reissueVO.name" nullOption="true" value="${reissueVO.name}" readonly="readonly" />
							</dd>
						</dl>
						<dl>
							<dt>收件人手机号</dt>
							<dd>
								<input id="receiverMobile" type="text" name="reissueVO.mobileTel" nullOption="true" value="${reissueVO.mobileTel}" readonly="readonly"/>
							</dd>
						</dl>
		           	<dl>
						<dt style="width:130px;">收件人地址 ：省/直辖市</dt>
						<dd style="width:180px;">
						<input id="state" name="reissueVO.state" type="text" nullOption="true" value="<Field:codeValue  tableName="APP___PAS__DBUSER.T_DISTRICT" value="${reissueVO.state}" />" readonly="readonly"/>
						</dd>
					</dl>
					<dl>
						<dt>市</dt>
						<dd>
						<input id="city" name="reissueVO.city" type="text" nullOption="true" value="<Field:codeValue  tableName="APP___PAS__DBUSER.T_DISTRICT" value="${reissueVO.city}" />" readonly="readonly"/>
						</dd>
					</dl>
					<dl>
						<dt>区/县 </dt>
						<dd>
							<input id="district" name="reissueVO.district" type="text" nullOption="true" value="<Field:codeValue  tableName="APP___PAS__DBUSER.T_DISTRICT" value="${reissueVO.district}" />" readonly="readonly"/>
						</dd>
					</dl>
						<dl>
							<dt>详细地址</dt>
							<dd>
								<input id="address" type="text" name="reissueVO.address"
									value="${reissueVO.address}" readonly="readonly" />
							</dd>
						</dl>
					</div>
				</s:if>
				<s:else>
					<dl>
						<dt>
							补发原因<span style="color: red">*</span>
						</dt>
						<dd>
							<Field:codeTable cssClass="combox" nullOption="true" whereClause="REISSUE_CAUSE != '07' AND REISSUE_CAUSE != '08'"
								id='reissueCause' name="reissueVO.reissueCause"
								tableName="APP___PAS__DBUSER.T_POLICY_REISSUE_CAUSE"
								value='${reissueVO.reissueCause}' />

						</dd>
					</dl>
					<dl>
						<dt>丢失日期</dt>
						<dd>
							<input id='loseDate' name="reissueVO.loseDate"
								value="<s:date format="yyyy-MM-dd" name="reissueVO.loseDate"></s:date>"
								type="expandDateYMD">
						</dd>
					</dl>
					
					<dl style = "display:none;">
						<dt>
							保单类型<span style="color: red">*</span>
						</dt>
						<dd>
							<Field:codeTable cssClass="combox" nullOption="true"
							
								id='printType' name="reissueVO.printType"
								whereClause="MEDIA_TYPE_CODE = '0' "
								defaultValue="0"
								value="0"
								tableName="APP___PAS__DBUSER.T_MEDIA_TYPE" />
								
								
								<%-- <Field:codeTable nullOption="true"
						name="customerPolicySearchVO.customerCertType"
						tableName="APP___PAS__DBUSER.T_CERTI_TYPE"
						whereClause="CODE != '83' "
						value="${customerPolicySearchVO.customerCertType}"  id="customerCertType" /> --%>
						
						
						
						</dd>
					</dl>				
					<%-- <dl>
						<dt>
							保单通知方式<span style="color: red">*</span></span>
						</dt>
						<dd>
							<Field:codeTable cssClass="combox" nullOption="true" defaultValue="0"
								id='informType' name="reissueVO.informType"
								value="${reissueVO.informType}"
								tableName="APP___PAS__DBUSER.T_INFORM_TYPE" />
						</dd>
					</dl>--%>
					 <dl>
						<dt>
							保单打印方式<span style="color: red">*</span>
						</dt>
						<dd>
							<Field:codeTable nullOption="true"  cssStyle="width:145px;"
								id='printMode' name="reissueVO.printMode" 
								whereClause="PRINT_MODE_CODE = '2' " 
								tableName="APP___PAS__DBUSER.T_PRINT_MODE"
								value='${reissueVO.printMode}'  />

						</dd>
					</dl> 
					<dl>
						<dt>
							保单打印次数<span style="color: red">*</span>
						</dt>
						<dd>
							<input type="text" id='informType' name="unitPrint" value="${unitPrint}" readonly="readonly" disabled="disabled" />
						</dd>
					</dl>
				<div id="receiverDiv"  style = "display:none;" >
						<dl>
							<dt>收件人姓名</dt>
							<dd>
								<input id="receiverName" name="reissueVO.name" nullOption="true" value="${reissueVO.name}" />
							</dd>
						</dl>
						<dl>
							<dt>收件人手机号</dt>
							<dd>
								<input id="receiverMobile" name="reissueVO.mobileTel" nullOption="true" maxlength="14" value="${reissueVO.mobileTel}" onkeyup="value=value.replace(/[^\d-]/g,'')"/>
						  </dd>
						</dl>
		           	<dl>
						<dt style="width:130px;">收件人地址 ：省/直辖市</dt>
						<dd  style="width:180px;">
						<s:if test='reissueVO.city!=null' >
							<s:select list="districtMap" listKey="key" listValue="value" headerKey="all" headerValur="全部" cssClass="combox"
								id="province_panelInfoAdd" name="reissueVO.state"
								ref="city_panelInfoAdd"  initval="${reissueVO.city }"
								refUrl="${ctx }/cs/common/getCity_PA_distictUtilAction.action?provinceCode={value}"
								>
							</s:select>
						</s:if>
						<s:else>
							<s:select list="districtMap" listKey="key" listValue="value" headerKey="all" headerValur="全部" cssClass="combox"
								id="province_panelInfoAdd" name="reissueVO.state"
								ref="city_panelInfoAdd"  initval=" "
								refUrl="${ctx }/cs/common/getCity_PA_distictUtilAction.action?provinceCode={value}"
								>
							</s:select>
						</s:else>
						</dd>
					</dl>
					<dl>
						<dt>市</dt>
						<dd>
							<select id="city_panelInfoAdd" name="reissueVO.city"
								ref="district_panelInfoAdd"
								refUrl="${ctx }/cs/common/getDistrict_PA_distictUtilAction.action?cityCode={value}"
								class="combox" initval="${reissueVO.district }"
								>
								<option value=""> 全部</option>
							</select>
						</dd>
					</dl>
					<dl>
						<dt>区/县 </dt>
						<dd>
							<select id="district_panelInfoAdd" name="reissueVO.district" class="combox"  >
							<option value=""> 全部</option>
							</select>
						</dd>
					</dl>
						<dl>
							<dt>详细地址</dt>
							<dd>
								<input id="address" type="text" name="reissueVO.address"
									value="${reissueVO.address}" />
							</dd>
						</dl>
					</div>
				</s:else>
				<table <s:if test="queryFlag==1">style="display:none"</s:if><s:else>style="width: 100%"</s:else>>			
					<tbody>
						<tr>
							<td></td>
							<td style="width:100px;">
								<div class="pageFormdiv" style="margin-top: 5px;">
									<div class="buttonContent">
										<button type="button"  class="but_blue" onclick="checkErr()">保存</button>
									</div>
								</div>
							</td>
							<td></td>
						</tr>
					</tbody>
				</table>
			</div>
		</form>
		
	<div class="tabsFooter">
		<div class="tabsFooterContent"></div>
	</div>
</div>
<s:include value="/cs/pages/serviceitem/serviceItemBottomBar.jsp"></s:include>
<script>

function _cs_LR_nextStep(acceptId){
	if(savePolicyReissueInfo()){
		updateAcceptStatus(acceptId);
	}
}

	//保存时，非阻断提示
	function checkErr(){
		var printMode = $("#printMode",navTab.getCurrentPanel()).val().trim();
		var receiverName = $("#receiverName",navTab.getCurrentPanel()).val();
		var receiverMobile = $("#receiverMobile",navTab.getCurrentPanel()).val();
		var state = $("#province_panelInfoAdd",navTab.getCurrentPanel()).val();
		var city = $("#city_panelInfoAdd",navTab.getCurrentPanel()).val();
		var district = $("#district_panelInfoAdd",navTab.getCurrentPanel()).val();
		var address = $("#address",navTab.getCurrentPanel()).val();
		if(printMode==""||printMode==null){
			alertMsg.error("请录入保单打印方式。");
			return;
		}
/* 		 if(printMode != null && printMode == '2'){//外包打印
 			if(receiverName == null || receiverName == ""){
 				alertMsg.error("请录入收件人姓名信息！");
 				return false;
 			};	
 			if(receiverMobile == null || receiverMobile == ""){
 				alertMsg.error("请录入收件人手机号信息！");
 				return false;
 			};	
 			if(state == null || state == "" || city == null || city == "" ||
 					district == null || district == "" || address == null || address == "" ){
 				alertMsg.error("请完整录入收件人地址信息！");
 				return false;
 			};	
 			var flag = checkAddress(state,address);
 			if (!flag) {
 				return false;
 			}
 		}  */
		var $form = $("#saveReissueInfoForm",navTab.getCurrentPanel());
		var projectPath = getRootPath();
		var changeId = $("#changeId",navTab.getCurrentPanel()).val();
		var policyCode = $("#policyCode",navTab.getCurrentPanel()).val();
		var reissueCause = $("#reissueCause",navTab.getCurrentPanel()).val();
		$.ajax({
			url:projectPath+'/cs/serviceitem_lr/checkErr_PA_csEndorseLRAction.action',
			type:"post",		
			data:"policyCode=" + policyCode + "&reissueCause=" + reissueCause + "&changeId=" + changeId +"&reissueVO.name=" +encodeURI(encodeURI(receiverName)) +"&reissueVO.mobileTel=" +receiverMobile
			     +"&reissueVO.state=" +state +"&reissueVO.city=" +city +"&reissueVO.district=" +district +"&reissueVO.address=" +encodeURI(encodeURI(address))+"&reissueVO.printMode=" +printMode ,
			success:function(data){
				var json = DWZ.jsonEval(data);
				if(json.warnMsg != ''&&json.warnMsg != null){
					alertMsg.confirm(json.warnMsg, {
						okCall : function() {
							$form.submit();
						},
						cancelCall : function() {
//							return false;
						}
					});
				} else {
					$form.submit();
				}
			}
		});
	}

	function savePolicyReissueInfo() {
		alertMsg.confirm("请确认是否需要保存录入的信息?", {
 	        okCall: function(){
	        	var _lrEntryDivBox = $("#lrEntryDiv",navTab.getCurrentPanel());
	    		var _customerEmail = $("#customerEmail",_lrEntryDivBox).val();
	    		var _customerMobileTel = $("#customerMobileTel",_lrEntryDivBox).val();
	    		var $form = $("#saveReissueInfoForm",navTab.getCurrentPanel());
	    		var reissueCause = $("#reissueCause",navTab.getCurrentPanel()).val();
	    		var printType = $("#printType",navTab.getCurrentPanel()).val();
	    		var informType = $("#informType",navTab.getCurrentPanel()).val();
	    		var loseDate = $("#loseDate",navTab.getCurrentPanel()).val();
	    		var printMode = $("#printMode",navTab.getCurrentPanel()).val();
	    		
	    		if (reissueCause == "" || printType == "" || informType == "" || printMode =="") {
	    			alertMsg.error("必填项信息未完整录入，不能受理保单补发项目，请确认!");
	    			return false;
	    		}
	    		//var reissueCause_text = $("#reissueCause option:selected").text();
	    		if ((reissueCause == "01"||reissueCause == "03") && loseDate == "") {
	    			alertMsg.error("补发原因为丢失补发，需录入丢失日期，请确认!");
	    			return false;
	    		}	    		
	  		if(informType != null && informType == '1' ){//短信
	    			if(_customerMobileTel == null || _customerMobileTel == ""){
	    				alertMsg.error("移动电话为空，请操作客户基本资料变更!");
	    				return false;
	    			};			
	    		}
	    		 if(informType != null && informType == '2'){//电邮
	    			if(_customerEmail == null || _customerEmail == ""){
	    				alertMsg.error("电子邮箱为空，请操作客户基本资料变更!");
	    				return false;
	    			};			
	    		} 
	    		//$form.submit();
	    		checkErr();
	    		return true;
 	        },
 			cancelCall : function() {
 				return false;
 				}
 		});
		
	}	
	function checkMseeage(){
		alertMsg.confirm("请确认是否需要保存录入的信息?", {
		        okCall: function(){
		        	var changeId = $("#changeId").val();
		    		var acceptId = $("#acceptId").val();
		    		var json="";
		    		$.ajax({
		    			url : "${ctx}/cs/serviceitem_ag/checkLifeFlag_PA_csEndorseLRAction.action",
		    			type : "post",
		    			dataType : 'text',
		    			data : "changeId="+changeId+"&acceptId="+acceptId,
		    			cache : false,
		    			success : function(data){
		    				if(data.indexOf("{")==0){
		    					json=jQuery.parseJSON(data);
		    					if(json.statusCode!=200){
		    						alertMsg.confirm(
		    								json.message+" 是否继续？"	,
		    								{
		    									okCall : function(){
		    										savePolicyReissueInfo();
		    									}
		    								});
		    					}
		    				}else{
		    					savePolicyReissueInfo();
		    				}
		    			}
		    		});
		        },
		        cancelCall : function() {
				}
		});
	}
	
	/**
	 * 系统提示是否保存变更信息
	 */
	function _cs_FK_upStep(changeId,acceptId,menuId){
		alertMsg.confirm("请确认是否需要保存录入的信息？",
				{
					okCall : function(){
						_cs_FK__upStep("/cs/csEntry/showAcceptAndArapInfo_PA_csEntryAction.action?"
								+"&menuId=${menuId}"
								+"&acceptId="+acceptId
								+"&changeId="+changeId
								+"&menuId="+menuId
								+"&type=1"
								+"&clear=0",menuId);
					},
					cancelCall : function() {
						_cs_FK__upStep("/cs/csEntry/showAcceptAndArapInfo_PA_csEntryAction.action?"
								+"&menuId=${menuId}"
								+"&acceptId="+acceptId
								+"&changeId="+changeId
								+"&menuId="+menuId
								+"&type=1"
								+"&clear=1",menuId);
					}

				});
		}

	// _entryTaskPool_M
	function _cs_FK__upStep(urls,menuId){
		var projectPath = getRootPath();
		var title = "保全录入";
		var tabid = menuId;
		if(tabid == null){
			tabid = "_entryTaskPool_M";
		}
		var fresh = eval("true");
		var external = eval("false");
		navTab.openTab(tabid, projectPath+urls, {
			title : title,
			fresh : fresh,
			external : external
		});
	}
	dwz_combox_myarray = new Array('province_panelInfoAdd',
			'city_panelInfoAdd', 'district_panelInfoAdd');
	dwz_combox_mybox = navTab.getCurrentPanel();
	
/* 	function changeReceiverDivShow(){
		var printMode = $("#printMode",navTab.getCurrentPanel()).val().trim();
		if(printMode != '2'){
	 		$("#receiverDiv",navTab.getCurrentPanel()).attr("style", "display:none;");
	 	}else{
	 		$("#receiverDiv",navTab.getCurrentPanel()).show();
	 	}
	} */
</script>
