<%@ page language="java" contentType="text/html; charset=utf-8"
	pageEncoding="utf-8" import="java.util.*,java.text.*"%>
<%@ taglib uri="/struts-tags" prefix="s"%><%@include file="/udmpCommon.jsp"%>
<%@ taglib uri="http://www.git.com.cn/taglib/udmp/field" prefix="Field"%>
<%@ include file="/cs/pages/common/jsp/common_ctx.jsp"%>
<script type="text/javascript" src="${ctx}/cs/js/cs_common.js"></script>
<link rel="stylesheet" href="${ctx}/cs/css/public.css" type="text/css"> 
<!-- ********帮助菜单***********begin********* -->
<s:include value="/cs/pages/common/jsp/helpMenu.jsp"></s:include>
<!-- *********帮助菜单***********end********* -->
<!-- 步骤标识 -->
<s:include value="csEndorseProgress.jsp" />

<div class="pageFormInfoContent" layoutH="140">

		<input type="hidden"  name="specialAccountFlag"  id="specialAccountFlag" value="${oldCsEndorseAAFeeVOs[0].specialAccountFlag}"/>
			
	<div class="divfclass">
		<h1>
			<img src="${ctx}/cs/img/icon/tubiao.png">红利领取形式变更
		</h1>
	</div>
		<div class="pageContent" >
		   
				<!-- <div class="divfclass">
					<h1>
						<img src="cs/img/icon/tubiao.png">客户信息
					</h1>
				</div> -->
				<div class="pageFormInfoContent">
				 <!--客户的5项基本信息（姓名、出生日期、证件类型、证件号码、性别） -->
			     <s:include value="customerInfo_list.jsp" />
			     
					<%-- <dl>
						<dt>姓名</dt>
						<dd>
							<input id="customerName" name="csCustomerVO.customerName"
								type="text" value="${csCustomerVO.customerName }"
								disabled="disabled" />
						</dd>
					</dl>
					<dl>
						<dt>性别</dt>
						<dd>
							<input type="text" id="customerGender" name="customerGender"
								value="<Field:codeValue  tableName="APP___PAS__DBUSER.T_GENDER" value="${csCustomerVO.customerGender}" /> "
								readonly="readonly" disabled="disabled" />
						</dd>
					</dl>
					<dl>
						<dt>生日</dt>
						<dd>
							<input name="csCustomerVO.customerBirthday" type="text"
								value=<s:date format="yyyy-MM-dd" name="csCustomerVO.customerBirthday"></s:date>
								disabled="disabled" />
						</dd>
					</dl>
					<dl>
						<dt>证件类型</dt>
						<dd>
							<input type="text" readonly="readonly" id="customerCertType"
								name="customerCertType"
								value="<Field:codeValue  tableName="APP___PAS__DBUSER.T_CERTI_TYPE" value="${csCustomerVO.customerCertType}" />"
								disabled="disabled" />
						</dd>
					</dl>
					<dl>
						<dt>证件号码</dt>
						<dd>
							<input type="expandCertiCode"
								name="csCustomerVO.customerCertiCode"
								value="${csCustomerVO.customerCertiCode}" disabled="disabled" />
						</dd>
					</dl> --%>

					<!--客户id-->
					<input type="hidden" id="customerId" name="customerId"
						value="${customerId}" />
					<!--保全变更Id-->
					<input type="hidden" id="changeId" name="changeId"
						value="${changeId}" />
					<!--保全受理号 -->
					<input type="hidden" id="acceptId" name="acceptId"
						value="${acceptId}" />
					<!--客户id-->
					<input type="hidden" id="insuredId" name="insuredId"
						value="${csCustomerVO.insuredId}" />
					<!--保全变更Id-->
					<input type="hidden" id="insuredName" name="insuredName"
						value="${csCustomerVO.insuredName}" />
					<!--判断是否为自行领取-->
					<input type="hidden" id="isShow" name="isShow"
						value="${isShow}" />
				    <!--判断是否保存-->
					<input type="hidden" id="isSaved" name="isSaved"
						value="${isSaved}" />
				</div>
			<!-- </div> -->

			<!--险种列表展示 -->
			   <div class="divfclass">
					<h1>
						<img src="cs/img/icon/tubiao.png">保单险种列表信息
					</h1>
				</div>
				<!-- <h1>保单险种列表信息</h1> -->
				<div class="main_FormContent">
					<!--json字符串 -->
					<input id="jsons" name="jsons" type="hidden" value="" />
					<table id="aaTableInform" name="aaTableInform" class="list"
						width="100%">
						<thead>
							<tr>
								<th colName="policyCode">保单号</th>
								<th colName="busiProdCode">险种代码</th>
								<th colName="busiPrdId" style="display: none"></th>
								<th>险种名称</th>
								<th colName="amount">基本保额</th>
								<th>红利领取形式</th>
								<th>收付费方式</th>
								<th>银行代码/名称</th>
								<th>银行账号</th>
								<th>户名</th>
								<th colName="customerId" style="display: none"></th>
								<th colName="changeId" style="display: none"></th>
								<th colName="policyChgId" style="display: none"></th>
								<th colName="acceptId" style="display: none"></th>
								<th colName="productId" style="display: none"></th>
								<th colName="policyId" style="display: none"></th>
								<th colName="itemId" style="display: none"></th>
							</tr>
						</thead>
						<tbody align="center" id="aaTableInformTbody">
							<s:iterator value="oldCsEndorseAAFeeVOs" status="st">
								<tr tr_saveStatus="1">
									<td><input type="radio"  style="float:left" name="listId" value="${busiItemId}" onclick="selectMainBusiness(this)"/>
										${policyCode }
									</td>
									<td>${busiProdCode }</td>
									<td style="display: none">${busiPrdId }</td>
									<td><Field:codeValue
											tableName="APP___PAS__DBUSER.T_BUSINESS_PRODUCT"
											value="${busiPrdId}" /></td>
									<td>${amount}</td>
									<td>
										<!-- 181559
											原领取形式显示为“支取-自行领取”时，调整为显示“现金领取-需办理保全项目领取”；
											原领取形式显示为“支取-银行转账、支取-网上银行”时，显示为“现金领取-约定银行转账”；
											原领取形式显示为“转指定险种”时，显示为“转万能账户
										 -->
										<s:if test="oldSurvivalMode==1 && oldSurvivalWMode!=null"> 
											<s:if test="oldSurvivalWMode==1"> 
												现金领取-需办理保全项目领取
											</s:if>
											<s:else>
												现金领取-约定银行转账
											</s:else>
										</s:if>	
										<s:else>
											<Field:codeValue
														tableName="APP___PAS__DBUSER.T_SURVIVAL_MODE"
														value="${oldSurvivalMode}" />
										</s:else>
									</td>
									<td>
										<!-- 181559
											显示变更前“收付费方式”；
											“红利领取形式”为“现金领取-约定银行转账”时，支持显示“银行转账（制返盘）”、“网上银行”；
											其他情况显示为“--”表示无关
										 -->
										<s:if test="oldSurvivalMode==1 && (oldSurvivalWMode==2 || oldSurvivalWMode==3)" >
											<Field:codeValue
												tableName="APP___PAS__DBUSER.T_SURVIVAL_W_MODE"
												value="${oldSurvivalWMode}" />
										</s:if>
										<s:else>
											--
										</s:else>
									</td>
									<td>
										<!-- 181559
											“收付费方式”为“银行转账（制返盘）”显示对应的银行代码/名称；
											“收付费方式”为“网上银行”时，显示对应的联行号/开户行名称；
										 -->
										<s:if test="oldSurvivalMode==1 && oldSurvivalWMode==2" >
											${bankCode }/${issueBankName }
										</s:if>
										<s:elseif test="oldSurvivalMode==1 && oldSurvivalWMode==3">
											${correspondentNo }/ ${correspondentName }
										</s:elseif>
									</td>
										
									<td>
										<!--
											“收付费方式”为“银行转账（制返盘）、网上银行”时，显示对应的银行账号；
										-->
										<s:if test="oldSurvivalMode==1 && (oldSurvivalWMode==2 || oldSurvivalWMode==3)" >
											${bankAccount }
										</s:if>
									</td>
									<td>
										<!--
											“收付费方式”为“银行转账（制返盘）、网上银行”时，显示对应的户名；
										-->
										<s:if test="oldSurvivalMode==1 && (oldSurvivalWMode==2 || oldSurvivalWMode==3)" >
											${accoName }
										</s:if>
									</td>
									
									
									<td style="display: none">${customerId }</td>
									<td style="display: none">${changeId }</td>
									<td style="display: none">${policyChgId }</td>
									<td style="display: none">${acceptId }</td>
									<td style="display: none">${productId }</td>
									<td style="display: none">${policyId }</td>
									<td style="display: none">${itemId }</td>
								</tr>
							</s:iterator>
						</tbody>
					</table>
					
							<!-- <div class="pageFormdiv">
								<button type="button" onclick="saveNewMsg()" class="but_blue">保存</button>
							</div> -->
					

				</div>
		<!-- 	</div> -->


			<!-- 変更信息錄入 -->
			<form action="" id="formBgMsg">
					 <div class="divfclass">
						<h1>
							<img src="cs/img/icon/tubiao.png">红利领取变更信息录入
						</h1>
					 </div>
					<div class="panelPageFormContent" id="formBgMsg1">
					  
							<!--变更信息的json字符串 -->
						    <input id="hcMSG" name="hcMSG" type="hidden" value="" />
						    <div class="tabdivclass">
							<table id="hlGetTable" name="hlGetTable" class="list" width="100%">
								<thead>
									<tr>
										<th style="display: none">原领取形式</th> 
										<th colName="issueBankName" style="display: none">开户银行</th>
										<th colName="bankCode" style="display: none">银行代码</th>
										<th colName="bankAccount" style="display: none">账号</th>
										<th colName="accoName" style="display: none">户名</th> 
										
										<th colName="newSurvivalMode" inputType="select"
													style="width: 200px">红利领取形式</th>
										<th colName="newSurvivalWMode" inputType="select"
													style="width: 150px">收付费方式</th>	
										<th colName="policyCode" style="display: none">保单号</th>
										<th colName="busiProdCode" style="display: none">险种代码</th>
										<!-- <th colName="survivalWMode" inputType="select"
											style="width: 150px">支取形式</th> -->
										<th colName="busiPrdId" style="display: none">busiPrdId</th>
										<th colName="accountId" style="display: none">账号id</th>
										<th colName="customerId" style="display: none">领取人ID</th>
										<th colName="acceptId" style="display: none">acceptId</th>
										<th colName="itmeId" style="display: none">itemId</th>
										<th colName="changeId" style="display: none">changeId</th>
										<th colName="busiItemId" style="display: none">busiItemId</th>
										<th colName="amount" style="display: none">基本保额</th>
										<th colName="policyPrdFlag" style="display: none">policyPrdFlag</th>
										<th>
											银行代码/名称
										</th>
										<th>
											银行账号
										</th>
										<th>
											户名
										</th>
										<th colName="correspondentNo" style="display: none">联行号</th>
										
										<th colName="specialAccountFlag" style="display: none">个人养老金账户标识</th>
									</tr>
								</thead>
								<tbody id="policyBankMsg">
									<s:iterator value="oldCsEndorseAAFeeVOs" status="st" var='itemVO'>
										<tr tr_saveStatus="1">
											<td style="display: none">
												<s:if test="oldSurvivalMode==1&&oldSurvivalWMode!=null"> 
												<Field:codeValue
														tableName="APP___PAS__DBUSER.T_SURVIVAL_W_MODE"
														value="${oldSurvivalWMode}" />
												</s:if>
												 <s:if test="oldSurvivalMode==2">
												   <Field:codeValue
														tableName="APP___PAS__DBUSER.T_SURVIVAL_MODE"
														value="${oldSurvivalMode}" />
												</s:if> 
											</td>
											 
											<td style="display: none">${issueBankName }</td>
											<td style="display: none">${bankCode }</td>
											<td style="display: none">${bankAccount }</td>
											<td style="display: none">${accoName }</td>
											<td>
												<select id="changeSurvivalMode" name="changeSurvivalMode" onchange="newPlanChange(this);" style="width: 190px">
													<option selected>请选择</option>
												     <s:iterator value="bonusModes" var="plan" >
	                                                  	<s:if test="#plan==1" >
	                                                  		<option value="1" <s:if test="newSurvivalMode == 1 && newSurvivalWMode == 1"> selected </s:if>>
	                                                  			现金领取-需办理保全项目领取
	                                                  		</option>
	                                                  	</s:if>
	                                                  	<s:if test="#plan==1">
	                                                  		<option value="1" <s:if test="newSurvivalMode == 1 && newSurvivalWMode != 1"> selected </s:if>>
	                                                  			现金领取-约定银行转账
	                                                  		</option>
	                                                  	</s:if>
                                                      	<s:if test="#plan==2"><option value="2" <s:if test="newSurvivalMode == 2"> selected </s:if>>累积生息</option></s:if>
                                                    	<s:if test="#plan==3"><option value="3" <s:if test="newSurvivalMode == 3"> selected </s:if>>抵缴保费</option></s:if>
                                                    	<s:if test="#plan==5"><option value="5" <s:if test="newSurvivalMode == 5"> selected </s:if>>购买交清增额保险</option></s:if>
			                                          </s:iterator>
		                                         </select>	
											</td>
											<td>
												<select class="payModeClass" onchange="newModeChange(this)" id="payMode" name="payMode" <s:if test="newSurvivalMode != 1 || (newSurvivalMode==1 && newSurvivalWMode == 1) "> style="display:none;" </s:if> >
												     <option selected style="display: none;" value="1"></option>
												     <option value="2" <s:if test="newSurvivalWMode == 2"> selected </s:if>>
	                                                  	银行转账（制返盘）
	                                                 </option>
	                                                 <option value="3" <s:if test="newSurvivalWMode == 3"> selected </s:if>>
	                                                  	网上银行
	                                                 </option>
		                                         </select>	
											</td> 
											<td style="display: none">${policyCode}</td>
											<td style="display: none">${busiProdCode}</td>
											<td style="display: none">${busiPrdId}</td>
											<td style="display: none"><input type="text" id="accountId" name="accountId"
												value="${accountId}" /></td>
											<td style="display: none">${customerId}</td>
											<td style="display: none">${acceptId}</td>
											<td style="display: none">${itemId}</td>
											<td style="display: none">${changeId}</td>
											<td style="display: none">${busiItemId}</td>
											<td style="display: none">${amount}</td>
											<td style="display: none">${policyPrdFlag}</td>
											<td>
												<input type="text" class="bankAccountReadOnly" 
										    		<s:if test=" survivalWMode!=2">style="display: none"</s:if>
										    	value="${issueBankName}" readOnly = true />
										    	
											    <div class="banklistInput" <s:if test="survivalWMode!=3">style="display: none"</s:if>>
											    	<input class="inputOrg1" name="org1.orgNum" value="" type="hidden"/><!-- 联行号 -->
											    	<input class="inputCreator" name= "org1.creator" value="" type="hidden"/><!-- 联行号对应的银行代码 -->
											   		<input class="required" name="org1.orgName" type="text" postField="keyword" readonly="readonly"
									suggestFields="orgNum,orgName,creator" suggestUrl="cs/pages/csEntry/districtBankCodeQuery.jsp" lookupGroup="org1"/>
													<a class="btnLook" href="${ctx}/cs/csEntry/selectReturnNumber_PA_csEntryAction.action" 
													lookupGroup="org1" style="float: right;">查找带回</a>
											    </div>
										    </td>
										    <td>
										    	<input class="bankAccountInput" type="text"  
										    		<s:if test="survivalWMode!=3">style="display: none"</s:if>
										    	onchange="checkAccountBank(this)" value="${bankAccount}" />
										    	
										    	<select
													<s:if test="survivalWMode!=2">disabled=true style="display: none"</s:if>
												    name="banklist"  id="banklist" onchange="getBankCode(this)">
											    </select>
										    </td>
										    <td>
										    	<input type="text" class="accoNameInput" onchange="checkAccountName(this)" value="${accoName}" readOnly = true/>
										    </td>
										    <td style="display: none" class="correspondentNoTd" name="correspondentNo">${correspondentNo }</td>
										    
										    <td  style="display: none">${specialAccountFlag}</td>
										</tr>
									</s:iterator>
								</tbody>
							</table>
							</div>
						<!-- </div> -->
						<!-- 銀行信息 -->
						<%-- <div id="bankAccount" style="display: none">
							<s:include value="/cs/pages/common/jsp/CsBankAccountsave.jsp"></s:include>
						</div> --%>
						
						<div class="pageFormdiv">
							<button class="but_blue" type="button" onclick="saveHCMsg()">保存</button>
						</div>
						<!-- 银行信息 -->
						<div id="bankAccount" style="display: none">
							<s:include value="/cs/pages/common/jsp/CsBankAccountpubsave.jsp"></s:include>
						</div>
					</div>
			</form>

				 <div class="divfclass">
					<h1>
						<img src="cs/img/icon/tubiao.png">应领未领红利信息
					</h1>
				</div>
				<div class="tabdivclass">
					<table id="baseInfo" class="list" width="100%">
						<thead>
							<tr>
								<th>保单号</th>
								<th>险种代码</th>
								<th>应付项目</th>
								<th>应付日期</th>
								<th>应付金额</th>
								<th style="display: none">productId</th>
							</tr>
						</thead>
						<tbody>
							<s:iterator value="csEndorseAAGetVOs">
							<tr align="center"
								<s:if test="queryFlag==1">disabled="disabled"</s:if>>
								<td>${policyCode }</td>
								<td>${busiProdCode }</td>
								<td>红利</td>
								<td><s:date name="dueTime" format="yyyy-MM-dd" /></td>
								<td>${feeAmount }</td>
								<td style="display: none"></td>
							</tr>
							</s:iterator>
						</tbody>
					</table>
				</div>
				
				<div id="isShowHCMsg" style="display: none"> 
				 <div class="divfclass">
					<h1>
						<img src="cs/img/icon/tubiao.png">变更后信息
					</h1>
				</div>
				<div class="tabdivclass">
					<table class="list" width="100%">
						<thead>
							<tr>
								<th>保单号</th>
								<th>险种代码</th>
								<th>险种名称</th>
								<th>基本保额</th>
								<th>红利领取形式</th>
								<th>收付费方式</th>
								<th>银行代码/名称</th>
								<th>账号</th>
								<th>户名</th>
							</tr>
						</thead>
						<tbody align="center" id="changeBankMsg">
							<s:iterator value="newCsEndorseAAFeeVOs" status="st">
								<tr align="center">
									<td>${policyCode }</td>
									<td>${busiProdCode }</td>
									<td><Field:codeValue
											tableName="APP___PAS__DBUSER.T_BUSINESS_PRODUCT"
											value="${busiPrdId}" /></td>
									<td>${amount}</td>
									<td>
										<s:if test="bonusModeCode==1 && bonusWMode!=null"> 
											<s:if test="bonusWMode==1"> 
												现金领取-需办理保全项目领取
											</s:if>
											<s:else>
												现金领取-约定银行转账
											</s:else>
										</s:if>	
										<s:else>
											<Field:codeValue
														tableName="APP___PAS__DBUSER.T_SURVIVAL_MODE"
														value="${bonusModeCode}" />
										</s:else>	
									</td>
									<td>
										<!-- 181559
											显示“收付费方式”；
											“红利领取形式”为“现金领取-约定银行转账”时，支持显示“银行转账（制返盘）”、“网上银行”；
											其他情况显示为“--”表示无关
										 -->
										<s:if test="bonusModeCode==1 && (bonusWMode==2 || bonusWMode==3)" >
											<Field:codeValue
												tableName="APP___PAS__DBUSER.T_SURVIVAL_W_MODE"
												value="${bonusWMode}" />
										</s:if>
										<s:else>
											--
										</s:else>	
									</td>
									<td>
										<s:if test="bonusModeCode==1 && bonusWMode==2" >
											${bankCode }/${issueBankName }
										</s:if>
										<s:elseif test="bonusModeCode==1 && bonusWMode==3">
											${correspondentNo }/${correspondentName }
										</s:elseif>
										<s:else>
											--
										</s:else>
									</td>
									<td>
										<s:if test="bonusModeCode==1 && (bonusWMode==2 || bonusWMode==3)" >
											${bankAccount }
										</s:if>
										<s:else>
											--
										</s:else>
									</td>
									<td>
										<s:if test="bonusModeCode==1 && (bonusWMode==2 || bonusWMode==3)" >
											${accoName }
										</s:if>
										<s:else>
											--
										</s:else>
									</td>
								</tr>
							</s:iterator>
						</tbody>
					</table>
				</div>
			</div> 

		</div>
	
	<s:if test="queryFlag!=1">
	<s:include value="/cs/pages/serviceitem/serviceItemBottomBar.jsp"></s:include>
	</s:if>
</div>
<script type="text/javascript">


$(function() {
	var isShow = $("#isShow", navTab.getCurrentPanel()).val();
	if (isShow != '1') {
		$("#formBgMsg", navTab.getCurrentPanel()).attr("style", "display:none;");
		$("#formBgMsg1", navTab.getCurrentPanel()).attr("style", "display:none;");
		$("#bankAccount", navTab.getCurrentPanel()).attr("style", "display:none;");
		$("#infoTable", navTab.getCurrentPanel()).attr("style", false);
	}

	var isSaved = $("#isSaved", navTab.getCurrentPanel()).val();
	if (isSaved == '1') {
		$("#isShowHCMsg", navTab.getCurrentPanel()).attr("style","display:block;");
	}
	//修改GC初始化领取形式为银行转账和网上支付时候将客户银行账号显示 
	var $tr = $("#policyBankMsg", navTab.getCurrentPanel()).find("tr");
	for (var k = 0 ; k < $tr.length; k++ ) {
		var payMode = $("#policyBankMsg", navTab.getCurrentPanel()).find("tr:eq("+k+")").find('td:eq(6)').find("select[name='payMode']", navTab.getCurrentPanel()).val();
		if (payMode == 2) {
			$("#bankAccount", navTab.getCurrentPanel()).attr("style", "display:block;");
			$("select[name='banklist']").removeAttr("disabled");
		} else {
			$("#bankAccount", navTab.getCurrentPanel()).attr("style", "display:none;");
			$("select[name='banklist']").attr("disabled","true");
			$("#infoTable", navTab.getCurrentPanel()).attr("disabled", false);
		}
	}
	
	var busiItemId = $("input[name='listId']:checked").attr();
	var $tr = $("#policyBankMsg", navTab.getCurrentPanel()).find("tr");
	if(busiItemId == undefined){
		for (var k = 0 ; k < $tr.length; k++ ) {
			$("#policyBankMsg", navTab.getCurrentPanel()).find("tr:eq("+k+")").attr("style", "display:none;");
		}
	}
});


function selectMainBusiness(){
	$("#formBgMsg", navTab.getCurrentPanel()).attr("style", "display:block;");
	$("#formBgMsg1", navTab.getCurrentPanel()).attr("style", "display:block;");
	$("#bankAccount", navTab.getCurrentPanel()).attr("style", "display:block;");
	$("#infoTable", navTab.getCurrentPanel()).attr("style", true);
	
	var busiItemId = $("input[name='listId']:checked").val();
	var line="";
	var $tr = $("#policyBankMsg", navTab.getCurrentPanel()).find("tr");
	var optionsLength = $("select[name='banklist']").get(0).options.length;
	for (var k = 0 ; k < $tr.length; k++ ) {
		var busiItemId1 = $("#policyBankMsg", navTab.getCurrentPanel()).find("tr:eq("+k+")").find('td:eq(15)').text();
		if(busiItemId!=null && busiItemId1 == busiItemId){
			$("#policyBankMsg", navTab.getCurrentPanel()).find("tr:eq("+k+")").attr("style", "display:table-row;");
			line=k;
		}else{
			$("#policyBankMsg", navTab.getCurrentPanel()).find("tr:eq("+k+")").attr("style", "display:none;");
		}
		$("#bankAccount", navTab.getCurrentPanel()).attr("style", "display:none;");
		if(optionsLength > 1){
			$("select[name='banklist']").get(k).options[0].selected = true;
		}
		
		$("select[name='banklist']").attr("disabled","true");
		$("#infoTable", navTab.getCurrentPanel()).attr("disabled", false);
		$("#policyBankMsg", navTab.getCurrentPanel()).find("tr:eq("+k+")").find('td:eq(2)').text("");
		$("#policyBankMsg", navTab.getCurrentPanel()).find("tr:eq("+k+")").find('td:eq(3)').text("");
		$("#policyBankMsg", navTab.getCurrentPanel()).find("tr:eq("+k+")").find('td:eq(4)').text("");
		$("#policyBankMsg", navTab.getCurrentPanel()).find("tr:eq("+k+")").find('td:eq(19)').find("input").val("");
		$("#policyBankMsg", navTab.getCurrentPanel()).find("tr:eq("+k+")").find('td:eq(20)').find("input").val("");
	}
	var payMode = $("#policyBankMsg", navTab.getCurrentPanel()).find("tr:eq("+line+")").find('td:eq(6)').find("select[name='payMode']", navTab.getCurrentPanel()).val();
	var survivalMode = $("#policyBankMsg", navTab.getCurrentPanel()).find("tr:eq("+line+")").find('td:eq(5)').find("select[name='changeSurvivalMode']", navTab.getCurrentPanel()).val();

	if (survivalMode == 1 && payMode == 2) {
		$("#bankAccount", navTab.getCurrentPanel()).attr("style", "display:block;");
		$("select[name='banklist']").removeAttr("disabled");
		if(optionsLength>1){
			$("select[name='banklist']").get(line).options[1].selected = true;
			var bankCodeAccount = $("#policyBankMsg", navTab.getCurrentPanel()).find("tr:eq("+line+")").find('td:eq(19)').find("select[name='banklist']", navTab.getCurrentPanel()).val();
			if(bankCodeAccount != null && bankCodeAccount != ""){
				$("#policyBankMsg", navTab.getCurrentPanel()).find("tr:eq("+line+")").find('td:eq(2)').text(bankCodeAccount.split("/")[0]);
				$("#policyBankMsg", navTab.getCurrentPanel()).find("tr:eq("+line+")").find('td:eq(3)').text(bankCodeAccount.split("/")[1]);
				$("#policyBankMsg", navTab.getCurrentPanel()).find("tr:eq("+line+")").find('td:eq(4)').text(bankCodeAccount.split("/")[2]);
				$("#policyBankMsg", navTab.getCurrentPanel()).find("tr:eq("+line+")").find('td:eq(19)').find("input").val(bankCodeAccount.split("/")[1]);
				$("#policyBankMsg", navTab.getCurrentPanel()).find("tr:eq("+line+")").find('td:eq(20)').find("input").val(bankCodeAccount.split("/")[2]);
			}else{
				$("select[name='banklist']").get(line).options[0].selected = true;
				$("#policyBankMsg", navTab.getCurrentPanel()).find("tr:eq("+line+")").find('td:eq(2)').text("");
				$("#policyBankMsg", navTab.getCurrentPanel()).find("tr:eq("+line+")").find('td:eq(3)').text("");
				$("#policyBankMsg", navTab.getCurrentPanel()).find("tr:eq("+line+")").find('td:eq(4)').text("");
				$("#policyBankMsg", navTab.getCurrentPanel()).find("tr:eq("+line+")").find('td:eq(19)').find("input").val("");
				$("#policyBankMsg", navTab.getCurrentPanel()).find("tr:eq("+line+")").find('td:eq(20)').find("input").val("");
			}
		}else{
			$("select[name='banklist']").get(line).options[0].selected = true;
			$("#policyBankMsg", navTab.getCurrentPanel()).find("tr:eq("+line+")").find('td:eq(2)').text("");
			$("#policyBankMsg", navTab.getCurrentPanel()).find("tr:eq("+line+")").find('td:eq(3)').text("");
			$("#policyBankMsg", navTab.getCurrentPanel()).find("tr:eq("+line+")").find('td:eq(4)').text("");
			$("#policyBankMsg", navTab.getCurrentPanel()).find("tr:eq("+line+")").find('td:eq(19)').find("input").val("");
			$("#policyBankMsg", navTab.getCurrentPanel()).find("tr:eq("+line+")").find('td:eq(20)').find("input").val("");
		}
	} else {
		$("#bankAccount", navTab.getCurrentPanel()).attr("style", "display:none;");
		$("select[name='banklist']").get(line).options[0].selected = true;
		$("select[name='banklist']").attr("disabled","true");
		$("#infoTable", navTab.getCurrentPanel()).attr("disabled", false);
		$("#policyBankMsg", navTab.getCurrentPanel()).find("tr:eq("+line+")").find('td:eq(2)').text("");
		$("#policyBankMsg", navTab.getCurrentPanel()).find("tr:eq("+line+")").find('td:eq(3)').text("");
		$("#policyBankMsg", navTab.getCurrentPanel()).find("tr:eq("+line+")").find('td:eq(4)').text("");
		$("#policyBankMsg", navTab.getCurrentPanel()).find("tr:eq("+line+")").find('td:eq(19)').find("input").val("");
		$("#policyBankMsg", navTab.getCurrentPanel()).find("tr:eq("+line+")").find('td:eq(20)').find("input").val("");
		
		$("#policyBankMsg", navTab.getCurrentPanel()).find("tr:eq("+line+")").find(".accoNameInput", navTab.getCurrentPanel()).attr("style", "display:none;");
	}
}

//点击支取形式变更后的状态
function newModeChange(line) {
	
	var table = $("#hlGetTable", navTab.getCurrentPanel());
	var rows = table.find("tr");
	var surviWM = line.value;
	
	var payModeBoolean = false;//默认false表示，所有的收付费方式没有一个为银行转账（控制客户银行账号的展示与否）
	for (var n = 1; n < rows.length; n++) {
		
		var policyPrdFlag = $(rows[n]).find("td").eq(17).text();
		if(policyPrdFlag==1){
			if (line.value == 1) {
				$("select[name='payMode']").find("option[value='1']").attr("selected", true);
			}
			else if(line.value == 2){
				$("select[name='payMode']").find("option[value='2']").attr("selected", true);
			}
			else{
				$("select[name='payMode']").find("option[value='']").attr("selected", true);
			}
		}
		
		//判断收付费方式中是否存在银行转账（制返盘）
		var payMode = $(rows[n]).find(".payModeClass").val();
		if(payMode == 2 && payModeBoolean == false){
			payModeBoolean = true;
		}
	}
	
	//此处控制客户银行账号的展示与否
	if(payModeBoolean){
		$("#bankAccount", navTab.getCurrentPanel()).attr("style", "display:block;");
	}else{
		$("#bankAccount", navTab.getCurrentPanel()).attr("style", "display:none;");
	}

	commonShowOrHide(line);
}



function commonShowOrHide(line){
	var payMode = $(line).parent().parent().find('td:eq(6)').find("select[name='payMode']", navTab.getCurrentPanel()).val();
	// 个人养老金产品标识
	var specialAccountFlag=$("#specialAccountFlag", navTab.getCurrentPanel()).val();
	
	$(line).closest('tr').find(".accoNameInput", navTab.getCurrentPanel()).attr("style", "display:block;");
	
	//如果是银行转账 则显示银行账户
	if (payMode == 2) {
		var bankListShow = $("select[name='banklist']").get(0).options.length;;
		if(bankListShow > 1){
			$("select[name='banklist']").get(0).options[1].selected = true;
		}
		
		// 若为专户投保保单，领取形式为“银行转账”时，自动带出保单绑定的中银保信个人养老金账户信息；不允许修改
		if((specialAccountFlag =='1' || specialAccountFlag ==1)){
			$("select[name='banklist']").attr("disabled","true");
			// 不允许添加 银行账户
			$("#panelPageFormContentkhyhdiv", navTab.getCurrentPanel()).hide();
		}else{
			$("select[name='banklist']").removeAttr("disabled");
			$("#panelPageFormContentkhyhdiv", navTab.getCurrentPanel()).show();
		}
		
		$(line).closest('tr').find("select[name='banklist']").attr("style", "display:block;");
		$(line).closest('tr').find(".banklistInput").attr("style", "display:none;");

		var bankCodeAccount = $(line).closest('tr').find('td:eq(19)').find("select[name='banklist']", navTab.getCurrentPanel()).val();
		$(line).closest('tr').find('td:eq(2)').text(bankCodeAccount.split("/")[0]);
		$(line).closest('tr').find('td:eq(3)').text(bankCodeAccount.split("/")[1]);
		$(line).closest('tr').find('td:eq(4)').text(bankCodeAccount.split("/")[2]);
		$(line).closest('tr').find('td:eq(18)').find(".bankAccountReadOnly").val(bankCodeAccount.split("/")[3]);
		$(line).closest('tr').find('td:eq(20)').find("input").val(bankCodeAccount.split("/")[2]);
		
		//181559:银行转账（制返盘）银行账号、户名只读
		$(line).closest('tr').find(".bankAccountReadOnly", navTab.getCurrentPanel()).attr("style", "display:block;");
		$(line).closest('tr').find(".bankAccountInput", navTab.getCurrentPanel()).attr("style", "display:none;");
		$(line).closest('tr').find(".accoNameInput", navTab.getCurrentPanel()).prop("readOnly", true);
	} else if(payMode == 3){
		
		$(line).closest('tr').find("select[name='banklist']").attr("disabled","true");
		$(line).closest('tr').find("select[name='banklist']").attr("style", "display:none;");
		$(line).closest('tr').find(".banklistInput").attr("style", "display:block;");
		
		$("#infoTable", navTab.getCurrentPanel()).attr("disabled", false);
		$(line).closest('tr').find(".bankAccountInput", navTab.getCurrentPanel()).val("");
		$(line).closest('tr').find('td:eq(2)').text("");
		$(line).closest('tr').find('td:eq(3)').text("");
		$(line).closest('tr').find('td:eq(4)').text("");
		$(line).closest('tr').find('td:eq(18)').find(".bankAccountReadOnly").val("");
		$(line).closest('tr').find('td:eq(20)').find("input").val("");
		$(line).closest('tr').find("#banklist", navTab.getCurrentPanel()).val("");
		
		//181559:银行转账（制返盘）银行账号、户名 可编辑输入
		$(line).closest('tr').find(".bankAccountReadOnly", navTab.getCurrentPanel()).attr("style", "display:none;");
		$(line).closest('tr').find(".bankAccountInput", navTab.getCurrentPanel()).attr("style", "display:block;");
		$(line).closest('tr').find(".bankAccountInput", navTab.getCurrentPanel()).prop("readOnly", false);
		$(line).closest('tr').find(".accoNameInput", navTab.getCurrentPanel()).prop("readOnly", false);
	}
}



function newPlanChange(line) {
	debugger	
	var selectText = "";
    $(line, navTab.getCurrentPanel()).find('option:selected').each(function() {
    	selectText = $(this).text().trim() // 输出被选中的option的文本
    });
    
    var table = $("#hlGetTable", navTab.getCurrentPanel());
	var rows = table.find("tr");
	var payModeBoolean = false;//默认false表示，所有的收付费方式没有一个为银行转账（控制客户银行账号的展示与否）
	for (var n = 1; n < rows.length; n++) {
		//判断收付费方式中是否存在银行转账（制返盘）
		var payMode = $(rows[n]).find(".payModeClass").val();
		if(payMode == 2 && payModeBoolean == false){
			payModeBoolean = true;
		}
	}
	
	//此处控制客户银行账号的展示与否
	if(line.value == 1 && selectText.indexOf("约定银行")!=-1 && payModeBoolean){
		$("#bankAccount", navTab.getCurrentPanel()).attr("style", "display:block;");
	}else{
		$("#bankAccount", navTab.getCurrentPanel()).attr("style", "display:none;");
	}
    

	//如果是银行转账 则显示银行账户
	if (line.value == 1 && selectText.indexOf("约定银行")!=-1) {	
		//只有为约定银行转换的才能看到收付费方式，否则收付费不显示
		$(line).closest('tr').find(".payModeClass").attr("style", "display:block;");
		commonShowOrHide(line);
	} else{
		if(selectText.indexOf("保全项目领取") != -1 ){
			//如果红利领取形式为保全项目领取，则收付费方式默认为1
			$(line).closest('tr').find(".payModeClass option[value='1']").prop('selected', true);
		}else{
			//如果红利领取形式为累计生息等，收付费方式默认为空
			$(line).closest('tr').find(".payModeClass").prop('selectedIndex', -1).val('');
		}
		
		
		$(line).closest('tr').find(".payModeClass").attr("style", "display:none;");
		
		$("#infoTable", navTab.getCurrentPanel()).attr("disabled", false);
		
		$(line).parent().parent().find('td:eq(2)').text("");
		$(line).parent().parent().find('td:eq(3)').text("");
		$(line).parent().parent().find('td:eq(4)').text("");
		$(line).parent().parent().find('td:eq(18)').find(".bankAccountReadOnly").val("");
		$(line).parent().parent().find('td:eq(20)').find("input").val("");
		$(line).closest('tr').find(".payModeClass", navTab.getCurrentPanel()).val("");
		$(line).closest('tr').find("select[name='banklist']", navTab.getCurrentPanel()).val("");
		$(line).closest('tr').find("select[name='banklist']", navTab.getCurrentPanel()).attr("style", "display:none;");
		
		//181559:银行转账（制返盘）银行账号、户名只读
		
		$(line).closest('tr').find(".accoNameInput", navTab.getCurrentPanel()).attr("style", "display:none;");
		$(line).closest('tr').find(".bankAccountInput", navTab.getCurrentPanel()).attr("style", "display:none;");
		$(line).closest('tr').find(".bankAccountReadOnly", navTab.getCurrentPanel()).attr("style", "display:none;");
		$(line).closest('tr').find(".banklistInput").attr("style", "display:none;");
		$(line).closest('tr').find(".banklistInput").find(".inputOrg1").val("");
		$(line).closest('tr').find(".banklistInput").find(".inputCreator").val("");
		$(line).closest('tr').find(".banklistInput").find(".required").val("");
		

	}
}

//点击领取形式变更后的状态
function newChanglq(line) {
	if (line.value == 1) {
		$("#formBgMsg", navTab.getCurrentPanel()).attr("style", "display:block;");
		$("#formBgMsg1", navTab.getCurrentPanel()).attr("style", "display:block;");
		$("select[name='getType']").find("option[value='4']").attr("selected", true);
		$("a[name='getType']").text('现金领取');
	} else {
		//如果支付方式是累计生息  则异常变更录入信息
		$("#formBgMsg", navTab.getCurrentPanel()).attr("style", "display:block;");
		$("#formBgMsg1", navTab.getCurrentPanel()).attr("style", "display:block;");
		$("#bankAccount", navTab.getCurrentPanel()).attr("style", "display:block;");
		$("#infoTable", navTab.getCurrentPanel()).attr("style", true);
	}
}


//红利领取信息保存按钮
function saveHCMsg() {
		alertMsg.confirm(
						"确认保存以上信息？",
						{okCall : function() {
							var table = $("#hlGetTable", navTab.getCurrentPanel());
							var rows = table.find("tr");
							
							for (var k = 1; k < rows.length; k++) {
								//判断传递的红利领取方式为现金领取
								var survivalMode = $("#formBgMsg1", navTab.getCurrentPanel()).find("tr:eq("+k+")").find('td:eq(5)').find("select[name='changeSurvivalMode']", navTab.getCurrentPanel()).val();
								var payMode = $("#formBgMsg1", navTab.getCurrentPanel()).find("tr:eq("+k+")").find('td:eq(6)').find("select[name='payMode']", navTab.getCurrentPanel()).val();
								//判断，如果收付费方式为银行转账，需要将银行账号和户名赋值为客户的银行账号；银行代码不为空，联行号为空；如果收付费方式为网上银行，银行账号和户名赋值为输入的内容，且银行代码为空，联行号不为空
								if(survivalMode == 1 && payMode == 2){
									var bankCodeAccount = $("#formBgMsg1", navTab.getCurrentPanel()).find("tr:eq("+k+")").find('td:eq(19)').find("select[name='banklist']", navTab.getCurrentPanel()).val();
									$("#formBgMsg1", navTab.getCurrentPanel()).find("tr:eq("+k+")").find('td:eq(2)').text(bankCodeAccount.split("/")[0]);
									$("#formBgMsg1", navTab.getCurrentPanel()).find("tr:eq("+k+")").find('td:eq(3)').text(bankCodeAccount.split("/")[1]);
									$("#formBgMsg1", navTab.getCurrentPanel()).find("tr:eq("+k+")").find('td:eq(4)').text(bankCodeAccount.split("/")[2]);
								}else if(survivalMode == 1 && payMode == 3){
									var inputOrg1 = $("#formBgMsg1", navTab.getCurrentPanel()).find("tr:eq("+k+")").find(".inputOrg1", navTab.getCurrentPanel()).val();
									if(inputOrg1 != null || inputOrg1 != ''){
										$("#formBgMsg1", navTab.getCurrentPanel()).find("tr:eq("+k+")").find(".correspondentNoTd", navTab.getCurrentPanel()).text(inputOrg1);
									}
									var bankCode = $("#formBgMsg1", navTab.getCurrentPanel()).find("tr:eq("+k+")").find(".inputCreator", navTab.getCurrentPanel()).val();
									var bankAccount = $("#formBgMsg1", navTab.getCurrentPanel()).find("tr:eq("+k+")").find('td:eq(19)').find("input").val();
									var accoName = $("#formBgMsg1", navTab.getCurrentPanel()).find("tr:eq("+k+")").find('td:eq(20)').find("input").val();
									if(bankAccount==null || bankAccount =='' || accoName==null || accoName==''){
										alertMsg.error("银行账号/户名不能为空");
									}else{
										$("#formBgMsg1", navTab.getCurrentPanel()).find("tr:eq("+k+")").find('td:eq(2)').text(bankCode);
										$("#formBgMsg1", navTab.getCurrentPanel()).find("tr:eq("+k+")").find('td:eq(3)').text(bankAccount);
										$("#formBgMsg1", navTab.getCurrentPanel()).find("tr:eq("+k+")").find('td:eq(4)').text(accoName);
									}
								}
							}
							
							
							
							
								var _jsons = "";
								var $jsonsText = $("input[name='jsons']",
										navTab.getCurrentPanel());
								var $table = $("#hlGetTable", navTab
										.getCurrentPanel());
								_jsons += _cs_tableToJson($table);
								$jsonsText.val(_jsons);
								
								$.ajax({
										url : "${ctx}/cs/serviceitem_hc/checkName_PA_csEndorseHCAction.action",
										type : "post",
										dataType : "html",
										data : "jsonString="+ encodeURI( $("#jsons", navTab.getCurrentPanel()).val()),
										cache : false,
										success : function(data) {
											var json = DWZ.jsonEval(data);
											
											if (json.statusCode == 300) {
												alertMsg.confirm(
														"户名与被保险人不一致",
														{okCall : function() {
														saveHCMessage();
													    },
													   cancelCall : function() {
													   }
												});
												
											} else {
												saveHCMessage();
											}
										}
									}); 
								 
							},
							cancelCall : function() {
							}
						});
		
		

	}


	function saveHCMessage(){
		$.ajax({
			url : "${ctx}/cs/serviceitem_hc/saveHCMsg_PA_csEndorseHCAction.action",
			type : "post",
			dataType : "html",
			data : "jsonString="
				+ encodeURI( $("#jsons", navTab.getCurrentPanel()).val() ),
			contentType: "application/x-www-form-urlencoded; charset=UTF-8", 
			cache : false,
			success : function(response) {
				alertMsg.correct("信息保存成功");
			    var json = DWZ
						.jsonEval(response);
				if (undefined == json.statusCode) {
					$("#changeBankMsg", navTab.getCurrentPanel()).html("");
					$("#changeBankMsg", navTab.getCurrentPanel()).html(response);
					
					$("#isShowHCMsg",navTab.getCurrentPanel()).attr("style", "display:block;");
				} else {
					alertMsg.error(json.message);
				} 
				//navTab.reload();//刷新整体的tab页面
			}
		});
	}



	$(function() {
		loadBankInfo();
		
		var table = $("#hlGetTable", navTab.getCurrentPanel());
		var rows = table.find("tr");
		var payModeBoolean = false;
		var specialAccountFlag=$("#specialAccountFlag", navTab.getCurrentPanel()).val();
		for (var k = 1; k < rows.length; k++) {
			//判断传递的红利领取方式为现金领取
			var survivalMode = $("#formBgMsg1", navTab.getCurrentPanel()).find("tr:eq("+k+")").find('td:eq(5)').find("select[name='changeSurvivalMode']", navTab.getCurrentPanel()).val();
			var payMode = $("#formBgMsg1", navTab.getCurrentPanel()).find("tr:eq("+k+")").find('td:eq(6)').find("select[name='payMode']", navTab.getCurrentPanel()).val();
			
			if(survivalMode ==1 && payMode == 2){
				if(!payModeBoolean){
					payModeBoolean = true;
				}
				
				// 若为专户投保保单，领取形式为“银行转账”时，自动带出保单绑定的中银保信个人养老金账户信息；不允许修改
				if(specialAccountFlag =='1' || specialAccountFlag ==1){
					$("select[name='banklist']").attr("disabled","true");
					var bankListShow = $("select[name='banklist']").get(0).options.length;;
					if(bankListShow > 1){
						$("select[name='banklist']").get(0).options[1].selected = true;
					}
					
					// 不允许添加 银行账户
					$("#panelPageFormContentkhyhdiv", navTab.getCurrentPanel()).hide();
				}else{
					$("select[name='banklist']").removeAttr("disabled");
					// 不允许添加 银行账户
					$("#panelPageFormContentkhyhdiv", navTab.getCurrentPanel()).show();
				}
				
				$("#formBgMsg1", navTab.getCurrentPanel()).find("tr:eq("+k+")").find("select[name='banklist']").attr("style", "display:block;");
				$("#formBgMsg1", navTab.getCurrentPanel()).find("tr:eq("+k+")").find(".banklistInput").attr("style", "display:none;");

				var bankCodeAccount = $("#formBgMsg1", navTab.getCurrentPanel()).find("tr:eq("+k+")").find('td:eq(19)').find("select[name='banklist']", navTab.getCurrentPanel()).val();
				$("#formBgMsg1", navTab.getCurrentPanel()).find("tr:eq("+k+")").find('td:eq(2)').text(bankCodeAccount.split("/")[0]);
				$("#formBgMsg1", navTab.getCurrentPanel()).find("tr:eq("+k+")").find('td:eq(3)').text(bankCodeAccount.split("/")[1]);
				$("#formBgMsg1", navTab.getCurrentPanel()).find("tr:eq("+k+")").find('td:eq(4)').text(bankCodeAccount.split("/")[2]);
				$("#formBgMsg1", navTab.getCurrentPanel()).find("tr:eq("+k+")").find('td:eq(18)').find(".bankAccountReadOnly").val(bankCodeAccount.split("/")[3]);
				$("#formBgMsg1", navTab.getCurrentPanel()).find("tr:eq("+k+")").find('td:eq(20)').find("input").val(bankCodeAccount.split("/")[2]);
				
				//181559:银行转账（制返盘）银行账号、户名只读
				$("#formBgMsg1", navTab.getCurrentPanel()).find("tr:eq("+k+")").find(".bankAccountReadOnly", navTab.getCurrentPanel()).attr("style", "display:block;");
				$("#formBgMsg1", navTab.getCurrentPanel()).find("tr:eq("+k+")").find(".bankAccountInput", navTab.getCurrentPanel()).attr("style", "display:none;");
				$("#formBgMsg1", navTab.getCurrentPanel()).find("tr:eq("+k+")").find(".accoNameInput", navTab.getCurrentPanel()).prop("readOnly", true);
			}else if(survivalMode ==1 && payMode == 3){
				$("#formBgMsg1", navTab.getCurrentPanel()).find("tr:eq("+k+")").find("select[name='banklist']").attr("disabled","true");
				$("#formBgMsg1", navTab.getCurrentPanel()).find("tr:eq("+k+")").find("select[name='banklist']").attr("style", "display:none;");
				$("#formBgMsg1", navTab.getCurrentPanel()).find("tr:eq("+k+")").find(".banklistInput").attr("style", "display:block;");
				
				$("#infoTable", navTab.getCurrentPanel()).attr("disabled", false);
				$("#formBgMsg1", navTab.getCurrentPanel()).find("tr:eq("+k+")").find('td:eq(2)').text("");
				$("#formBgMsg1", navTab.getCurrentPanel()).find("tr:eq("+k+")").find('td:eq(3)').text("");
				$("#formBgMsg1", navTab.getCurrentPanel()).find("tr:eq("+k+")").find('td:eq(4)').text("");
				$("#formBgMsg1", navTab.getCurrentPanel()).find("tr:eq("+k+")").find('td:eq(18)').find(".bankAccountReadOnly").val("");
				$("#formBgMsg1", navTab.getCurrentPanel()).find("tr:eq("+k+")").find('td:eq(20)').find("input").val("");
				$("#formBgMsg1", navTab.getCurrentPanel()).find("tr:eq("+k+")").find("#banklist", navTab.getCurrentPanel()).val("");
				
				//181559:银行转账（制返盘）银行账号、户名 可编辑输入
				$("#formBgMsg1", navTab.getCurrentPanel()).find("tr:eq("+k+")").find(".bankAccountReadOnly", navTab.getCurrentPanel()).attr("style", "display:none;");
				$("#formBgMsg1", navTab.getCurrentPanel()).find("tr:eq("+k+")").find(".bankAccountInput", navTab.getCurrentPanel()).attr("style", "display:block;");
				$("#formBgMsg1", navTab.getCurrentPanel()).find("tr:eq("+k+")").find(".bankAccountInput", navTab.getCurrentPanel()).prop("readOnly", false);
				$("#formBgMsg1", navTab.getCurrentPanel()).find("tr:eq("+k+")").find(".accoNameInput", navTab.getCurrentPanel()).prop("readOnly", false);
			}else {
				$("#formBgMsg1", navTab.getCurrentPanel()).find("tr:eq("+k+")").find(".accoNameInput", navTab.getCurrentPanel()).attr("style", "display:block;");
			}
		}
		
		
		//此处控制客户银行账号的展示与否
		if(payModeBoolean){
			$("#bankAccount", navTab.getCurrentPanel()).attr("style", "display:block;");
		}else{
			$("#bankAccount", navTab.getCurrentPanel()).attr("style", "display:none;");
		}

		
		
		
	});

	function loadBankInfo() {
		var table = $("#bankAccountTable", navTab.getCurrentPanel());
		var rows = table.find("tr");
		/* alert(table.html()); */
		var str = "";
		//循环下拉框 
		var bankinfo = $("select[name='banklist']");

		var first = "";
		for (var i = 0; i < bankinfo.length; i++) {
			str = "<option value=''>请选择</option>";
			var obj = bankinfo[i];
			for (var n = 1; n < rows.length; n++) {
				var issueBankName = $(rows[n]).find("td").eq(4).text();
				var bankCode = $(rows[n]).find("td").eq(5).text();
				var bankAccount = $(rows[n]).find("td").eq(6).find("input").val();
				var accoName = $(rows[n]).find("td").eq(7).text();
				str += "<option value='"+ bankCode + "/"+bankAccount+ "/"+accoName+ "/"+issueBankName+"'>" + bankCode + "/" + bankAccount +"</option>";
				if (n === 1) {
					first = bankCode + "/"+bankAccount +"/"+accoName+"/"+issueBankName;
				}
			}
			$(obj).html(str);
			var code = $(obj).prev().val();
			//让当前值选中
			$("select[name='banklist']").val(first);
			$("select[name='banklist']").selectedIndex = 1;
		}
	}

	function getBankCode(obj) {
		//给银行账号一列赋值
		if(obj.value == "" ||obj.value == null){
			$(obj).parent().parent().find('td:eq(2)').text("");
			$(obj).parent().parent().find('td:eq(3)').text("");
			$(obj).parent().parent().find('td:eq(4)').text("");
			$(obj).parent().parent().find('td:eq(18)').find(".bankAccountReadOnly").val("");
			$(obj).parent().parent().find('td:eq(20)').find("input").val("");
		}else{
			$(obj).parent().parent().find('td:eq(2)').text(obj.value.split("/")[0]);
			$(obj).parent().parent().find('td:eq(3)').text(obj.value.split("/")[1]);
			$(obj).parent().parent().find('td:eq(4)').text(obj.value.split("/")[2]);
			$(obj).parent().parent().find('td:eq(18)').find(".bankAccountReadOnly").val(obj.value.split("/")[3]);
			$(obj).parent().parent().find('td:eq(20)').find("input").val(obj.value.split("/")[2]);
		}
	}
/* 
	function getBankCode(obj) {
		//给银行账号一列赋值
		$(obj).parent().next().text(obj.value);
	} */

	function setBank(obj) {

		if (obj.value == 2) {
			$(obj).parent().next().removeAttr("disabled");
		} else {
			$(obj).parent().next().attr("disabled", "disabled");
			$(obj).parent().next().find("select").get(0).options[0].selected = true;
			$(obj).parent().next().next().text('');
		}
	}
	
	
	//银行账号二次重复校验
	var bankAccountCheckTime = 1;//账号输入次数
	var bankAccountFirst="";//第一次输入
	function checkAccountBank(obj){
		if (bankAccountCheckTime==1) {
			bankAccountFirst=obj.value;//第一次输入
			bankAccountCheckTime+=1;
			obj.value="";
		}else if (bankAccountCheckTime==2) {	
			if (bankAccountFirst!="" && bankAccountFirst!=obj.value) {
				alertMsg.info("两次输入不一致，请重新输入！");				
				bankAccountCheckTime=1;
				return;
			}
		}		
	}
	
	//户名二次重复校验
	var accountNameCheckTime = 1;//户名输入次数
	var accountNameFirst="";//第一次输入
	function checkAccountName(obj){
		if (accountNameCheckTime==1) {
			accountNameFirst=obj.value;//第一次输入
			accountNameCheckTime+=1;
			obj.value="";
		}else if (accountNameCheckTime==2) {	
			if (accountNameFirst!="" && accountNameFirst!=obj.value) {
				alertMsg.info("两次输入不一致，请重新输入！");				
				accountNameCheckTime=1;
				return;
			}
		}		
	}

	
</script>

