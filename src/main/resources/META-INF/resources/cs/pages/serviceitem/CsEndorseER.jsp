<%@ page language="java" contentType="text/html; charset=utf-8"
	pageEncoding="utf-8"%>
<%@ taglib uri="/struts-tags" prefix="s"%><%@include file="/udmpCommon.jsp"%>
<!-- 引入点击下一步返回保全录入的js -->
<%-- <s:set var="ctx">${pageContext.request.contextPath}</s:set> --%>
<%@ include file="/cs/pages/common/jsp/common_ctx.jsp"%>
<script type="text/javascript"
	src="cs/pages/common/js/returnAcceptAndPay.js"></script>
<%@ taglib uri="http://www.git.com.cn/taglib/udmp/field" prefix="Field"%>
<script type="text/javascript" src="${ctx}/cs/js/cs_common.js"></script>
<link rel="stylesheet" href="${ctx }/cs/css/cs_common.css" type="text/css">
<link rel="stylesheet" href="${ctx}/cs/css/public.css" type="text/css">
<input type="hidden" name="menuId" id="menuId" value="${menuId}">
<input type="hidden" name="itemFlag" id="itemFlag" value="mainProd">
<input type="hidden" name="itemName" id="itemName" value="附加险满期降低保额续保">
<!-- ********帮助菜单***********begin********* -->
<s:include value="/cs/pages/common/jsp/helpMenu.jsp"></s:include>
<s:include value="csEndorseProgress.jsp" />
<!-- *********帮助菜单***********end********* -->
<%-- 进度条 --%>
<!-- <div class="step_header">
	<table width="100%" border="0" cellspacing="0" cellpadding="0">
		<tr>
			<td rowspan="2" class="n1d" id="n1"></td>
			<td class="step" id="step1">受理信息修改</td>
			<td rowspan="2" class="n2d" id="n2"></td>
			<td class="step" id="step2">项目信息录入</td>
			<td rowspan="2" class="n3d" id="n3"></td>
			<td class="step" id="step3">录入完成</td>
		</tr>
	</table>
</div> -->
<div layoutH="140">
<div class="pageContent">
	<s:include value="customerInfo_list.jsp" />
		<!-- validateCallback  -->
			<form id="ErForm" action="${ctx}/cs/serviceitem_er/saveDownAmountOrUnit_PA_csEndorseERAction.action"
				  class="pageForm required-validate" method="post" onsubmit="return validateCallback(this,updateEndAjaxDone)">
				<input type="hidden" id="customerId" name="customerId" value="${customerId}" /> 
				<input type="hidden" id="acceptId" name="acceptId" value="${acceptId}" /> 
				<input type="hidden" id="changeId" name="changeId" value="${changeId}" /> 
				<input type="hidden" id="downAmountAndUnit" name="downAmountAndUnit" value="" />
				<input type="hidden" name="workDate" value="<s:date name='workDate' format='yyyy-MM-dd'></s:date>" />
				<div class="panel" style="display: none">
					<s:include value="customerInfo_list.jsp" />
				</div>
					<div class="divfclass">
							<h1>
							    <img src="images/tubiao.png" >变更前数据
							</h1>
					</div>
					<div class="pageFormContent" >
						<table id="dataTable" class="list" width="100%">
							<thead>
								<tr>
									<th colName="policyChgId" style="display: none"></th>
									<th colName="countWay" style="display: none"></th>
									<th colName="downType" style="display: none">减保类型</th>
									<th colName="policyId" style="display: none">保单ID</th>
									<th colName="addtionBusiItemId" style="display: none">附加险种ID</th>
									<th colName="policyCode">保单号</th>
									<th>被保险人</th>
									<th colName="addtionBusiProdCode">险种代码</th>
									<th>险种名称</th>
									<th colName="oldData">当前保额/份数</th>
									<th colName="onesAmount">每份保额</th>
									<th>续保保额/份数</th>
									<th colName="downData" inputType="input">减少的保额/份数</th>
									<th colName="totalPremAf">保费</th>
									<th>下期交费日</th>
								</tr>
							</thead>
							<tbody>
								<s:iterator value="csEndorseErVOs" status="st" id="BFList">
									<tr align="center" tr_saveStatus="1">
										<!-- 0：减份数   1：减保额 -->
										<td style="display: none">${policyChgId}</td>
										<td style="display: none">${countWay}</td>
										<td name="downType" style="display: none"><s:if
												test="countWay == 5">0</s:if> <s:if test="countWay == 1">1</s:if>
										</td>
										<td style="display: none">${policyId}</td>
										<td style="display: none">${addtionBusiItemId}</td>
										<td>${policyCode }</td>
										<td>${insuredName }</td>
										<td>${addtionBusiProdCode }</td>
										<td><Field:codeValue tableName="APP___PAS__DBUSER.T_BUSINESS_PRODUCT"
												value="${addtionBusiPrdId}" /></td>
										<td name="oldData"><s:if test="countWay == 1">
												${basicAmount}
											</s:if> <s:if test="countWay ==5">
												${unit}
											</s:if></td>
										<td><s:if test="onesAmount != 0">
												${onesAmount}
											</s:if></td>
											
										<td id="renewal"><s:if test="renewalAmount != 0">
												${renewalAmount}
											</s:if> <s:if test="renewalUnit != 0">
												${renewalUnit}
											</s:if></td>
										<td><s:if test="yesOrNo =='yes'"><input name="downData" type="text" value=${afterAmount}></input></s:if>
											<s:if test="yesOrNo =='no'"><input name="downData" type="text" style="display: none"/></s:if>
										</td>
										<td>${totalPremAf}</td>
										<td id="payDueDate"><s:date name="payDueDate" format="yyyy-MM-dd"></s:date></td>
									</tr>
								</s:iterator>
							</tbody>
						</table>
				</div>
											<div class="pageFormdiv">
									 			<button type="button" class="but_blue" onclick="saveData('ErForm','dataTable')">降低保额续保</button>
								    		</div>
					
			</form>
			
 
		<div id="updateEndDiv" <s:if test="updateDowncsEndorseErVOs == null || updateDowncsEndorseErVOs.size()==0">style="display:none"</s:if>>
		 <s:if test="updateDowncsEndorseErVOs != null && updateDowncsEndorseErVOs.size()>0">		
			<div class="divfclass">
			<h1>
				   <img src="images/tubiao.png" >变更保单信息
			</h1>
	    </div>
		<div>
		<div class="pageFormContent" >
			<table class="list" width="100%" id="decreaseAFDetail">
				<thead>
					<tr>
						<th colName="policyChgId" style="display: none"></th>
						<th colName="countWay" style="display: none"></th>
						<th colName="downType" style="display: none">减保类型</th>
						<th colName="policyId" style="display: none">保单ID</th>
						<th colName="addtionBusiItemId" style="display: none">附加险种ID</th>
						<th colName="policyCode">保单号</th>
						<th>被保险人</th>
						<th colName="addtionBusiProdCode">险种代码</th>
						<th>险种名称</th>
						<th colName="oldData">保额/份数</th>
						<th>每份保额</th>
						<th colName="totalPremAf">保费</th>
						<th>下期交费日</th>
					</tr>
				</thead>
				<tbody id="allInfoContent">
					<s:iterator value="updateDowncsEndorseErVOs" status="st" id="AFList">
						<tr align="center" tr_saveStatus="1">
							<!-- 0：减份数   1：减保额 -->
							<td style="display: none">${policyChgId}</td>
							<td style="display: none">${countWay}</td>
							<td name="downType" style="display: none">
								<s:if test="countWay == 5">0</s:if>
								<s:if test="countWay == 1">1</s:if>
							</td>
							<td style="display: none">${policyId}</td>
							<td style="display: none">${addtionBusiItemId}</td>
							<td>${policyCode }</td>
							<td>${insuredName }</td>
							<td>${addtionBusiProdCode}</td>
							<td><Field:codeValue tableName="APP___PAS__DBUSER.T_BUSINESS_PRODUCT" value="${addtionBusiPrdId}"/></td>
							<td name="oldData">
								<s:if test="countWay == 1">
									${basicAmount}
								</s:if>
								<s:if test="countWay == 5">
									${unit}
								</s:if>
							</td>
							<td>
								<s:if test="onesAmount != 0">
									${onesAmount}
								</s:if>
							</td>
							<td>${totalPremAf}</td>
							<td><s:date name="payDueDate" format="yyyy-MM-dd"></s:date></td>
						</tr>
					</s:iterator>
				</tbody>
			</table>
	</div>
</div>
</s:if>
		</div>
		 


			<%-- <s:include value="cs/pages/serviceitem/CsEndorseER_query.jsp"></s:include> --%>
	</div>
</div>
<s:include value="/cs/pages/serviceitem/serviceItemBottomBar.jsp"></s:include>
<script type="text/javascript">
	$(document).ready(function() {
		csHelpMenu();
	});
	//附加险满期降低保额续保
	function saveData(formId, tableId) {
		var flag = true;// 用于判断减少的保额或份数是否大于当前保额或份数
		var workDate = new Date();// 当前系统时间
		var payDueDate = new Date();// 下期缴费日期  业务生效日期
		var errorType = 1;// 错误提示类型 1说明减少的保额或份数必须小于原保额或份数/2说明减少的保额或份数必须小于续保保额或份数
		// 判断减少的保额或份数是否大于当前(或续保)的保额或份数
		$('input[name="downData"]').each(function(data) {
			var downData = Number($(this).val());// 减少的保额或份数
			if(downData != null && downData != undefined && downData != 0 && downData != '') {
				var parentTr = $(this).parent().parent();
				var oldData = Number(parentTr.find('td[name="oldData"]').text());// 当前的保额或份数
				var renewal = Number(parentTr.find('#renewal').text());// 续保的保额或份数
				if(renewal != null && renewal != undefined && renewal != 0 && renewal != '') {// 先判断续保的保额或份数是否为空
					workDate = $('input[name="workDate"]').val();
					payDueDate = parentTr.find('#payDueDate').text();
					if((payDueDate > workDate) && (downData >= renewal)) {// 如果下期缴费日期大于当前系统日期  并且本次减少的保额或份数大于续保的保额或份数
						flag = false;
						errorType = 2;
					} else if ((payDueDate <= workDate) && (downData >= oldData)) {
						flag = false;
						errorType = 1;
					}
				} else {
					if(downData >= oldData) {
						flag = false;
						errorType = 1;
					}
				}
			}
		});
		
		if(!flag) {
			if(errorType == 1) {// 减少的保额或份数大于当前保额或份数 提示阻断信息
				alertMsg.error("减少的保额或份数必须小于原保额或份数，请重新输入。");
				return;
			} else if (errorType == 2) {// 减少的保额或份数必须大于续保保额或份数 提示阻断信息
				alertMsg.error("减少的保额或份数必须小于续保保额或份数，请重新输入。");
				return;
			}
		}
		alertMsg.confirm("请确认是否需要保存录入信息？", {
			okCall : function(){
				var onsubmit = "return validateCallback(this,updateEndAjaxDone)";
				var action = "${ctx}/cs/serviceitem_er/saveDownAmountOrUnit_PA_csEndorseERAction.action";
				var $dataTable = $("#" + tableId, navTab.getCurrentPanel());
				var $formId = $("#" + formId, navTab.getCurrentPanel());
				var _jsons = "";
				var $downAmountAndUnit = $("#downAmountAndUnit", navTab
						.getCurrentPanel());
				_jsons += _cs_tableToJson($dataTable);
				var jsonStr = iGetInnerText(_jsons);// 去掉所有的回车换行及'\t'
				$downAmountAndUnit.val(jsonStr);
				$formId.attr("onsubmit", onsubmit);
				$formId.attr("action", action);
				$formId.submit();
			},
			cancleCall : function() {
				return false;
			}
		})
		
	}
	
	//上一步
	function upToCsEntry() {
		alertMsg
				.confirm(
						"请确认是否需要保存变更的信息？",
						{
							okCall : function() {
								//保存变更信息---返回保全录入主页面
								var onsubmit = "return validateCallback(this,reusltAjaxDone)";
								$("#mainProdRenewalForm",
										navTab.getCurrentPanel()).attr(
										"onsubmit", onsubmit);
								var action = "${ctx}/cs/serviceitem_mr/mainProdRenewal_PA_csEndorseMRAction.action?flag=2";
								$("#mainProdRenewalForm",
										navTab.getCurrentPanel()).attr(
										"action", action);
								formSubmit();
							},
							cancelCall : function() {
								$("#gotoCsEntry").click();
							}
						});
	}
	
	//下一步
	function nextStep() {
		var $form = $("#ErForm", navTab.getCurrentPanel());
		var $dataTable = $("#dataTable", navTab.getCurrentPanel());
		var _jsons = "";
		var $downAmountAndUnit = $("#downAmountAndUnit", navTab
				.getCurrentPanel());
		_jsons += _cs_tableToJson($dataTable);
		$downAmountAndUnit.val(_jsons);
		$form.attr("onsubmit", onsubmit);
		var onsubmit = "return validateCallback(this,reusltAjaxDone)";
		var action = "${ctx}/cs/serviceitem_er/saveDownAmountOrUnit_PA_csEndorseERAction.action";
		$form.attr("onsubmit", onsubmit);
		$form.attr("action", action);
		$form.submit();
	}
	
	//回调函数，跳转页面
	function reusltAjaxDone(json) {
		DWZ.ajaxDone(json);
		if (json.statusCode == DWZ.statusCode.ok) {
			$("#gotoCsEntry").click();
		} else {
			return false;
		}
	}
	
	//加载变更后的数据
	function updateEndAjaxDone(json) {
		DWZ.ajaxDone(json);
		if (json.statusCode == DWZ.statusCode.ok) {
			$("#updateEndDiv", navTab.getCurrentPanel()).show();
			var onsubmit = "return navTabSearch(this,'updateEndDiv')";
			var action = "${ctx}/cs/serviceitem_er/loadChangeData_PA_csEndorseERAction.action";
			var $form = $("#ErForm", navTab.getCurrentPanel());
			var $dataTable = $("#dataTable", navTab.getCurrentPanel());
			var _jsons = "";
			var $downAmountAndUnit = $("#downAmountAndUnit", navTab
					.getCurrentPanel());
			_jsons += _cs_tableToJson($dataTable);
			$downAmountAndUnit.val(_jsons);
			$form.attr("onsubmit", onsubmit);
			$form.attr("action", action);
			$form.submit();
		} else {
			return false;
		}
	}
	
	//下一步
	function next() {
		var val1 = $("#changeId").val();
		var val2 = $("#acceptId").val();
		var val3 = $("#customerId").val();
		var title = "受理信息录入";
		var tabid = "_aplPermit_M";
		var fresh = eval("true");
		var external = eval("false");
		var url = "${ctx}/cs/csEntry/showAcceptAndArapInfo_PA_csEntryAction.action?&menuId=${menuId}&changeId="
				+ val1 + "&acceptId=" + val2 + "&customerId=" + val3;
		navTab.openTab(tabid, url, {
			title : title,
			fresh : fresh,
			external : external
		});

	}

	// 去掉所有的回车换行及'\t'
	function iGetInnerText(testStr) {
        var resultStr = testStr.replace(/\ +/g, ""); // 去掉空格
        resultStr = testStr.replace(/[ ]/g, "");    // 去掉空格
        resultStr = testStr.replace(/[\r\n\t]/g, ""); // 去掉回车换行及'\t'
        return resultStr;
    }
	

</script>