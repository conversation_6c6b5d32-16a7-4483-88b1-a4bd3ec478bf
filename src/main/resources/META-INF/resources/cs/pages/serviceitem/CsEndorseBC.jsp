<!--保全录入- 基本信息备注项变更页面 -->
<%@ page contentType="text/html; charset=utf-8"%>
<%@ taglib uri="/struts-tags" prefix="s"%><%@include file="/udmpCommon.jsp"%>
<%@ taglib uri="http://www.git.com.cn/taglib/udmp/field" prefix="Field"%>
<%@ include file="/cs/pages/common/jsp/common_ctx.jsp"%>
<script type="text/javascript" src="${ctx}/cs/js/cs_common.js"></script>
<link rel="stylesheet" href="${ctx }/cs/css/cs_common.css" type="text/css">
<link rel="stylesheet" href="${ctx}/cs/css/public.css" type="text/css">

<script type="text/javascript">

	var policyCode="";
	function baodanChoice(formId, boxId,obj) {
		//debugger;
		var radio = $(".myClass:checked",navTab.getCurrentPanel());
		var length = radio.length;
		
		var policyId;
		
		if(length == 0) {
			alertMsg.error("请至少选择一个保单号");
		} else {
			policyId=radio.val();
		}
		
		var $form = $("#"+formId,navTab.getCurrentPanel());
		$("#"+boxId,navTab.getCurrentPanel()).show();
		var  rootPath= getRootPath();
		var action = rootPath+"/cs/serviceitem_bc/getBaodan_PA_csEndorseBCAction.action";		
		var onsubmit="return divSearch("+formId+",'baodanChoiceResult') ";		
		$("#baodanChoiceInfoForm",navTab.getCurrentPanel()).attr("action",action);
		$("#baodanChoiceInfoForm",navTab.getCurrentPanel()).attr("onsubmit",onsubmit);
		$("#_policyId",navTab.getCurrentPanel()).val($(obj).find("td:eq(0)").find("input[name='policyId']").val());
		$("#_policyChgId",navTab.getCurrentPanel()).val($(obj).find("td:eq(0)").find("input[name='policyChgId']").val());
// 		$("#_acceptId").val($(obj).next().next().next().val());
		$form.submit();
			
	};
</script>

<!-- ********帮助菜单***********begin********* -->
<s:include value="/cs/pages/common/jsp/helpMenu.jsp"></s:include>
<!-- *********帮助菜单***********end********* -->
<%-- 进度条 --%>
<s:if test="queryFlag!=1">
	<s:include value="entryProgressBar.jsp"></s:include>
</s:if>
<form id="hiddenForm">
	<!--保全申请号 -->
	<input name="" type="hidden" value="" />
	<!--保全受理号 -->
	<input name="" type="hidden" value="" />
	<!--变更信息的json字符串 -->
	<input name="jsons" type="hidden" value="" />
</form>
<div class="backgroundCollor" layoutH="140">
	<div style="margin-left: 10px; margin-right: 10px; margin-top: 10px; margin-bottom: 10px;">       
		<div class="divfclass">
			<h1>
				<img src="${ctx}/cs/img/icon/tubiao.png" />受益人变更
			</h1>
		</div>
		<div class="divfclass" >
			<!--客户的5项基本信息（姓名、出生日期、证件类型、证件号码、性别） -->
			<s:include value="customerInfo_list.jsp" />
			<s:if test="verifyFlag==1">
				<s:include value="verifyInfo_list.jsp" />
			</s:if>
			<input type="hidden" name="applyTime" id="applyTime" value="<s:date name="csAcceptChangeVO.applyTime" format="yyyy-MM-dd"/>" />
			<form id="baodanChoiceInfoForm" method="post" class="required-validate"
				onsubmit="return navTabSearch(this,'baodanChoiceResult');">
				<input type="hidden" id="_policyId" name="policyId" value="" /> <input
					type="hidden" id="_changeId" name="changeId" value="${changeId}" />
				<input type="hidden" id="_customerId" name="customerId"
					value="${customerId }" /> <input type="hidden" id="_policyChgId"
					name="policyChgId" value="${policyChgId}" /> <input type="hidden"
					id="_acceptId" name="acceptId" value="${acceptId}" />
			</form>
			
				<div class="divfclass">
					<h1>
						<img src="${ctx}/cs/img/icon/tubiao.png" />客戶相关保单
					</h1>
				</div>
				<div class="tabdivclass">
					<table id="basicRemarkTable" class="list" style="width: 100%">
						<thead>
							<tr>
								<s:if test="queryFlag==1">
								</s:if>
								<s:else>
									<th width="30px">选择</th>
								</s:else>
								<th>保单号</th>
								<!-- <th>险种代码</th>
								<th>险种名称</th> -->
								<th>投保人</th>
								<th>被保险人</th>
								<th>受益人</th>
								<th>保单状态</th>
								<!-- <th>受益性质</th> -->
							</tr>
						</thead>
						<tbody id="">
							<s:iterator value="bcContractMasterVOs">
							<input type="hidden" value="${bcContractMasterVOs.get(0).validateDate}" id="validateDate">
								<tr align="center">							
									<s:if test="queryFlag==1">
									
									</s:if>
									<s:elseif test="productCode =='00737000'">
										<td><input type="radio" class="myClass" name="policyId"
										value="${policyId}"
										 disabled="disabled" />								
										<input type="hidden" name="policyChgId" value="${policyChgId}" />								
										</td>
									</s:elseif>
									<s:elseif test="productCode !='00737000'">
										<td><input type="radio" class="myClass" name="policyId"
										value="${policyId}"
										 />								
										<input type="hidden" name="policyChgId" value="${policyChgId}" />								
										</td>
									</s:elseif>								
									
									<td>
										${policyCode}
										<input name="policyCodeName" value="${policyCode}" type="hidden">
									</td>
									<%-- <td>
										${productCode }
										<input name="productCodeName" value="${productCode}" type="hidden">
									</td>
									<td><Field:codeValue tableName="APP___PAS__DBUSER.T_BUSINESS_PRODUCT" value="${busiPrdId}" /></td> --%>
									<td>${policyHolderName}</td>
									<td>
										${insuredName}
										<input name="insuredNameName" value="${policyHolderName}" type="hidden">
									</td>
									<td>${csContractBeneName}</td>
									<td><Field:codeValue tableName="APP___PAS__DBUSER.T_LIABILITY_STATUS" value="${liabilityState}" /></td>
									<%-- <td><Field:codeValue tableName="APP___PAS__DBUSER.T_BENEFICIARY_TYPE" value="${beneType}" /></td> --%>
                                    <td style="display: none">${organCode}</td>
								</tr>
							</s:iterator>
						</tbody>
					</table>
				</div>
		
		
		<s:if test="queryFlag==1">
			<div id="policyRevivalAFVOs" class="unitBox">
				<s:include value="CsEndorseBC_policy.jsp"></s:include>
			</div>
		</s:if>
	
		<div id="baodanChoiceResult" class="unitBox"></div>
		</div>
	</div>
	<s:include value="/cs/pages/serviceitem/serviceItemBottomBar.jsp"></s:include>
</div>

<script type="text/javascript">
//退出按钮功能
function exit(){
	navTab.closeCurrentTab();
}
//下一步
function next() {
	var val1 = $("#_changeId", navTab.getCurrentPanel()).val();
	var val2 = $("#_acceptId", navTab.getCurrentPanel()).val();
	var val3 = $("#_customerId", navTab.getCurrentPanel()).val();
	var title = "受理信息录入";
	var tabid = "_aplPermit_M";
	var fresh = eval("true");
	var external = eval("false");
	var  rootPath= getRootPath();
	var url = rootPath+"/cs/csEntry/showAcceptAndArapInfo_PA_csEntryAction.action?&menuId=${menuId}&_changeId="
			+ val1 + "&_acceptId=" + val2 + "&_customerId=" + val3;
	navTab.openTab(tabid, url, {
		title : title,
		fresh : fresh,
		external : external
	});

}
	//帮助部分动作
	function MM_changeProp(objId, theProp, theValue) {
		var obj = null;
		with (document) {
			if (getElementById) {
				obj = getElementById(objId);
			}
		}
		if (obj) {
			if (theValue == true || theValue == false) {
				eval("obj.style." + theProp + "=" + theValue);
			} else {
				eval("obj.style." + theProp + "='" + theValue + "'");
			}
		}
	};

	//上一步
	function save() {
		alertMsg.confirm("请确认是否需要保存录入信息", {
			okCall : function() {
				$.post(url, data, DWZ.ajaxDone, "json");
			}
		});
	}
	//下一步 保存基本信息备注项变更信息
	function nextStep_basicRemark(tableId) {
		//1.获取变更数据
		var _jsons = basicRmark_jsons(tableId);//获取变更jsons数据
		var $jsonsText = $("input[name='jsons']", navTab.getCurrentPanel());
		$($jsonsText).val(_jsons);

		//2.保存变更数据
		var serializeArray = $("#hiddenForm", navTab.getCurrentPanel())
				.serializeArray();
		var  rootPath= getRootPath();
		$
				.ajax({
					url : rootPath+"/cs/csEntry/saveCsBasicRemark_PA_csEntryAction.action?",
					type : "post",
					dataType : 'html',
					data : serializeArray,
					cache : false,
					success : function(data) {
						alertMsg.correct("保存成功！");
						var title = "基本信息备注项";
						var tabid = "${menuId}";
						var fresh = true;
						var external = false;
						var url = unescape(
								"${ctx}/cs/csEntry/saveCsBasicRemark_PA_csEntryAction.action?&menuId=${menuId}")
								.replaceTmById(
										$(navTab).parents(".unitBox:first"));
						navTab.openTab(tabid, url, {
							title : title,
							fresh : fresh,
							external : external
						});
					},
					error : function() {
						alertMsg.error("保存失败！");
					}
				});
	};

	function basicRmark_jsons(tableId) {
		var _jsons = "";
		var $table = $("#" + tableId, navTab.getCurrentPanel());
		var keys = new Array();
		$($table).find("thead tr th").each(function(i) {
			if (typeof ($(this).attr("colName")) != "undefined") {
				keys[i] = $(this).attr("colName").trim();
			} else {
				keys[i] = "";
			}
		});
		var $trs = $($table).find("tbody tr");
		_jsons = _jsons + "[";
		$($trs).each(function(i) {
			_jsons = _jsons + "{";
			var $tds = $(this).find("td");
			$(this).find("td").each(function(j) {
				if (keys[j] != "") {
					var _tdVal = "";
					var $txtare = $(this).find("textarea");
					if ($txtare.length > 0) {
						_tdVal = $($txtare).text();
					} else {
						_tdVal = $(this).html();
					}
					_jsons = _jsons + "'" + keys[j] + "':'" + _tdVal + "',";

				}
			});
			_jsons = _jsons.substring(0, _jsons.length - 1);
			_jsons = _jsons + "},";
		});
		_jsons = _jsons.substring(0, _jsons.length - 1);
		_jsons = _jsons + "]";
		return _jsons;

	}
	
</script>
<script type="text/javascript">

	
	$(document).ready(function() {
		csHelpMenu();//帮助菜单初始化
		$("#basicRemarkTable", navTab.getCurrentPanel()).find("tr").click(function(){
			debugger;
			$(this).find("input:radio").attr('checked','true');
			/* $("#customerId", navTab.getCurrentPanel()).val($(this).find("input:radio").val());*/
			baodanChoice('baodanChoiceInfoForm','baodanChoiceResult',$(this)) 
		});
		
	});	
</script>