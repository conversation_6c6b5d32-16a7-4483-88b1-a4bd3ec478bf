<!-- 投保人变更页面 -->
<%@ page contentType="text/html; charset=utf-8"%>
<%@ taglib uri="/struts-tags" prefix="s"%><%@include file="/udmpCommon.jsp"%>
<%@ taglib uri="http://www.git.com.cn/taglib/udmp/field" prefix="Field"%>
<%@ include file="/cs/pages/common/jsp/common_ctx.jsp"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/fmt" prefix="fmt"%>
<script type="text/javascript" src="${ctx}/cs/js/cs_common.js"></script>
<link rel="stylesheet" href="${ctx}/cs/css/public.css" type="text/css">

<script type="text/javascript" src="${ctx}/cs/js/DomZoomZX.js"></script>
<script>
	$(document).ready(function(){
        var D = DomZoomZX('input,select,textarea,td')
	})
</script>
<style type="text/css">
th,td {
	white-space: normal;
}

.searchBar li label {
	width: 60px;
}
</style>
<!-- ********帮助菜单***********begin********* -->
<s:include value="/cs/pages/common/jsp/helpMenu.jsp"></s:include>
<!-- *********帮助菜单***********end********* -->

<s:if test="queryFlag!=1">
	<s:include value="csEndorseProgress.jsp"></s:include>
</s:if>

<div onmousedown="MM_changeProp('holderInfoCmDiv','display','none')">
	
	<div>
		<div  layoutH="110">
			
			<div class="pageContent">
			<input name="queryFlag" id="queryFlag" value="${queryFlag}" type="hidden"/>
				<div class="searchBar">
				<div class="divfclass">
					<h1>
						<img src="cs/img/icon/tubiao.png">保单冻结
					</h1>
				</div>
		
				<s:include value="customerInfo_list.jsp" />
					<!-- <div class="panel"> -->
						<!-- <h1>原投保人信息</h1> -->
						<div style="display: none">
							<ul class="searchContent" style="height: auto;">
								<li class="nowrap"><label>客户姓名</label> <input type="text"
									value="${csCustomerVO.customerName}" readonly="readonly" /></li>
								<li class="nowrap"><label>出生日期</label> <input type="text"
									value="<s:date format="yyyy-MM-dd" name="csCustomerVO.customerBirthday"></s:date>"
									readonly="readonly" /></li>
							</ul>
							<ul class="searchContent" style="height: auto;">
								<li class="nowrap"><label>证件类型</label> <input
									readonly="readonly" type="text"
									value="<Field:codeValue tableName="APP___PAS__DBUSER.T_CERTI_TYPE" value="${csCustomerVO.customerCertType}"/>" /></li>
								<li class="nowrap"><label>证件号码</label> <input type="text"
									value="${csCustomerVO.customerCertiCode}" readonly="readonly" /></li>
								<li class="nowrap"><label style="width: auto;">性别&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
										<input type="text"
										value="<Field:codeValue tableName="APP___PAS__DBUSER.T_GENDER" value="${csCustomerVO.customerGender}"/>"
										readonly="readonly">
								</label></li>
							</ul>
						</div>
				<!-- 	</div> -->

						<div class="pageContent">
						<div class="divfclass">
							<h1>
								<img src="cs/img/icon/tubiao.png">保单信息
							</h1>
						</div>
						<div class="tabdivclass">
							<table class="list" width="100%">
								<thead>
									<tr>
										<th>序号</th>
										<th>保单号</th>
										<th>投保人</th>
										<th>被保人</th>
										<th>生效日期</th>
										<th>保险起期</th>
										<th>保险止期</th>
										<th>保单状态</th>
										<th>险种代码</th>
										<th>险种名称</th>
										<th>受益人</th>
										<th>基本保额</th>
									</tr>
								</thead>
								<tbody align="center">
									<s:iterator value="policyInformationVOs" status="st" id="qr">
										<tr>
											<td>${st.index+1}</td>
											<td>${policyCode}</td>
											<td>${HName}</td>
											<td>${LName}</td>
											<td><fmt:formatDate value="${validateDate }" pattern="yyyy-MM-dd" /></td>
											<td><fmt:formatDate value="${applyDate }" pattern="yyyy-MM-dd" /></td>
											<td><fmt:formatDate value="${expiryDate }" pattern="yyyy-MM-dd" /></td>
											<td><Field:codeValue tableName="APP___PAS__DBUSER.t_liability_status" value="${liabilityState}"/></td>											
											<td>${productCodeSys}</td>
											<td>${productNameStd}</td>
											<td>${BName}</td>
											<td>${shareRate}</td>
										</tr>
									</s:iterator>
								</tbody>

							</table>
						</div>
					</div>
					<div class="backgroundCollor"  >
						<div class="divfclass">
							<h1>
								<img src="cs/img/icon/tubiao.png">冻结信息
							</h1>
						</div>
						<div>
							<form method="post"
								action="${ctx}/cs/serviceitem_cf/updatefreezePolicyInfo_PA_csEndorseCFAction.action?&acceptId=${acceptId}&changeId=${changeId}"
								class="required-validate"
								onsubmit="return validateCallback(this);"
								id="saveFreezeInfoForm">
								<input id="customerId" type="hidden" value="${customerId}">
								<input id="acceptId" type="hidden" value="${acceptId}">
								<div id="getCsContractMaster"></div>
								<div class="pageFormContent">
									<dl>
										<dt>
											冻结起期<span style="color: red">*</span>
										</dt>
										<dd>
											<input class="required" id="_freezeDate"
												name="cfFreezePolicyInfoVOSave.freezeDate"
												type="expandDateYMD"   myOption = "getPeriodNum()" onchange = "getPeriodNum()"
												value="<s:date format="yyyy-MM-dd" name="cfFreezePolicyInfoVOSave.freezeDate"></s:date>" />

										</dd>
									</dl>
									<dl>
										<dt>
											冻结期间<span style="color: red">*</span>
										</dt>
										<dd>
											<input type="text" id="freeze_Period" onchange="_chgDate(this)" value =""  maxlength="10">
										</dd>
									</dl>
									<dl>
										<dt>
											冻结止期<span style="color: red">*</span>
										</dt>
										<dd>
											<input class="required" id="_freezeEndDate" name="cfFreezePolicyInfoVOSave.freezeEndDate"
												type="expandDateYMD" myOption = "getPeriodNum()" onchange = "getPeriodNum()"
												value="<s:date format="yyyy-MM-dd" name="cfFreezePolicyInfoVOSave.freezeEndDate"></s:date>" />
											<input class="required" id="_freezeEndDate1" name="cfFreezePolicyInfoVOSave.freezeEndDate"
												type="expandDateYMD" myOption = "getPeriodNum()" onchange = "getPeriodNum()"
												value="<s:date format="yyyy-MM-dd" name="cfFreezePolicyInfoVOSave.freezeEndDate"></s:date>" />
											
											<input id="endDate" type="hidden"value="<s:date format="yyyy-MM-dd" name="cfFreezePolicyInfoVOSave.freezeEndDate" var="tt"></s:date>"/>
											<input  type="checkbox" onclick="endDateClear()" id="longDate" value="" />长期
										</dd>
									</dl>
									<dl style="display:none">
										<dt>
											冻结原因<span style="color: red">*</span>
										</dt>
										<dd>
											<Field:codeTable cssClass="combox" nullOption="true"  name="cfFreezePolicyInfoVO.freezeCause"  tableName="APP___PAS__DBUSER.T_FREEZE_CAUSE" value="${cfFreezePolicyInfoVOSave.freezeCause}" id="freezeCause"  defaultValue="请选择"/>
											<%-- <Field:codeTable cssClass="combox" nullOption="true"  name="reviewErrCfgVO.errType"            tableName="APP___PAS__DBUSER.T_ERROR_TYPE"   value=""                              id='errType' defaultValue="请选择"/> 
											 --%><%-- 				<Field:codeValue tableName="APP___PAS__DBUSER.T_CERTI_TYPE" value="${cfFreezePolicyInfoVOSave.freezeCause}"/>
								 --%>
											<%-- <Field:codeValue name="cfFreezePolicyInfoVO.freezeCause" tableName="APP___PAS__DBUSER.T_CERTI_TYPE" value="${cfFreezePolicyInfoVOSave.freezeCause}" /> --%>
										</dd>
									</dl>
									<dl style="display:none">
										<dt>执行通知书编号</dt>
										<dd>
											<input type="text" name="cfFreezePolicyInfoVO.execDocNo"
												value="${cfFreezePolicyInfoVOSave.execDocNo}" />
										</dd>
									</dl>
									<dl style="display:none">
										<dt>
											执行单位<span style="color: red">*</span>
										</dt>
										<dd>
											<input type="text" id="_execOrg"
												name="cfFreezePolicyInfoVO.execOrg"
												value="${cfFreezePolicyInfoVOSave.execOrg}">
										</dd>
									</dl>
									<dl style="display:none">
										<dt>组织机构代码</dt>
										<dd>
											<input type="text" id="_organCode"
												name="cfFreezePolicyInfoVO.organCode"
												value="${cfFreezePolicyInfoVOSave.organCode}">
										</dd>
									</dl>
									<dl style="display:none">
										<dt>经办人姓名</dt>
										<dd>
											<input type="text" id="operatorName" name="cfFreezePolicyInfoVO.anntName"
												value="${cfFreezePolicyInfoVOSave.anntName}">
										</dd>
									</dl>
									<dl style="display:none">
										<dt>经办人联系电话</dt>
										<dd>
											<input  id="anntPhone"
												name="cfFreezePolicyInfoVO.anntPhone"
												value="${cfFreezePolicyInfoVOSave.anntPhone}">
										</dd>
									</dl>
									<dl style="display:none">
										<dt>经办人证件类型</dt>
										<dd>
											<Field:codeTable cssClass="combox" nullOption="true"  name="cfFreezePolicyInfoVO.anntCertiType" tableName="APP___PAS__DBUSER.T_CERTI_TYPE" value="${cfFreezePolicyInfoVOSave.anntCertiType}"  defaultValue="请选择"/>
											
										</dd>
									</dl>

									<dl style="display:none">
										<dt>经办人证件号</dt>
										<dd>
											<input id="" name="cfFreezePolicyInfoVO.anntCertiCode"
												value="${cfFreezePolicyInfoVOSave.anntCertiCode}">
											<%-- <input class="required" name="cfFreezePolicyInfoVOSave.freezeDate" type="expandDateYMD"  value="<s:if test="${cfFreezePolicyInfoVOSave.freezeDate==null}">12</s:if><s:if test="${cfFreezePolicyInfoVOSave.freezeDate!=null}"><s:date format="yyyy-MM-dd" name="cfFreezePolicyInfoVOSave.freezeDate"></s:date></s:if>"/> --%>

										</dd>

									</dl>

									<dl style="display: none">
										<dt>冻结号</dt>
										<dd>
											<input id="" name="cfFreezePolicyInfoVO.freezeId"
												value="${cfFreezePolicyInfoVOSave.freezeId}">
										</dd>
										<dt>changeId</dt>
										<dd>
											<input id="" name="cfFreezePolicyInfoVO.changeId"
												value="${cfFreezePolicyInfoVOSave.changeId}">
										</dd>
										<dt>policychgId</dt>
										<dd>
											<input id="" name="cfFreezePolicyInfoVO.policyChgId"
												value="${cfFreezePolicyInfoVOSave.policyChgId}">
										</dd>
										<dt>policyId</dt>
										<dd>
											<input id="" name="cfFreezePolicyInfoVO.policyId"
												value="${cfFreezePolicyInfoVOSave.policyId}">
										</dd>
										<dt>policyCode
										<dt>
										<dd>
											<input id="" name="cfFreezePolicyInfoVO.policyCode"
												value="${cfFreezePolicyInfoVOSave.policyCode}">
										</dd>

									</dl>
									
									<table style="width:100%">
									<tbody>
									<tr>
									<td></td>
									<td style="width:100px;">
										<div class="buttonActive" id="saveMesId">
											<div class="buttonContent">
												<button type="button" onclick="savePolicyFreezeInfo()">保存</button>
											</div>
										</div>
									</td>
									<td></td>
									</tr>	
										</tbody>
										</table>
									
								</div>
							</form>
						</div>
					</div>
				</div>
				<s:include value="/cs/pages/serviceitem/serviceItemBottomBar.jsp"></s:include>
			</div>
			<div class="tabsFooter">
				<div class="tabsFooterContent"></div>
			</div>
		</div>
	</div>
	
<script type="text/javascript">
	//冻结期间录入修改后 修改冻结止起 修改后不可变动
	function _chgDate(obj){
		//冻结止起=冻结起期+冻结期间
		var _freezeDate = $("#_freezeDate").val();//冻结起期
		var freeze_Period=$("#freeze_Period").val();//冻结期间
		
		var aa = new Date(_freezeDate.replace(/-/g,"\/"));
		
		aa.setMonth(aa.getMonth()+Number(freeze_Period));
		//getMonth()从0开始所以+1
		var month=aa.getMonth()+Number(1);
		//格式转换月份小于10 前边加"0"
		month=month<10?"0"+month:month;
		$("#_freezeEndDate").val(aa.getYear()+"-"+month+"-"+(aa.getDate()<10?"0"+aa.getDate():aa.getDate()));
		$("#_freezeEndDate", navTab.getCurrentPanel()).attr('disabled', false);
	}
	$(".field").change(function(){
		  $(this).css("background-color","#FFFFCC");
		});

</script>	
<script>
	function checkTel(){
		var mobile=/^1[3|5|7|8]\d{9}$/,phone=/^0\d{2,3}-?\d{7,8}$/;
		var tel=document.getElementById("anntPhone").value;
	    if( mobile.test(tel)||phone.test(tel)){
	    	return true;
	    }else if(tel==null||tel==""){
			alertMsg.error("电话号码为空！");
			return false;
	    }else{
	    	alertMsg.error("请输入正确的电话号码！");
	    	return false;
	    };
	}
	


	function isCardName(s) 
	{
	    var patrn = /^\s*[\u4e00-\u9fa5]{1,}[\u4e00-\u9fa5.·]{0,15}[\u4e00-\u9fa5]{1,}\s*$/; 
	    if(!patrn.exec(s))
	    {
	        return false;
	    }
	    return true;
	}
		function savePolicyFreezeInfo() {
			
			/* 按照缺陷集合-20151214-v7.2  959*/

			var _freezeDate = $("#_freezeDate").val();
			var _organCode = $("#_organCode").val();
			var operatorName = $("#operatorName").val();
			var _freezeEndDate=$("#_freezeEndDate").val();
			var longDate=$("input[type='checkbox']").is(':checked');
			
			//冻结起期不能为空
			if(_freezeDate==""){
				alertMsg.error("请录入冻结起期。");
				return false;
			}
			//冻结止起/长期两者必录之一
			if(_freezeEndDate==""&&longDate==false){
				alertMsg.error("请录入冻结止期/长期。");
				return false;
			}
			var $form = $("#saveFreezeInfoForm");
			$form.submit();
		}

		function updatefreezePolicyInfo(formId) {
			alertMsg
					.confirm(
							"请确认是否需要保存信息",
							{
								okCall : function() {
									//获取解冻原因
									var unfreezeCause = $("#unfreezeCause option:selected");
									var rootPath = getRootPath();
									//获取解冻日期的值
									var unfreezeDate = $('#unfreezeDate').val();

									var _jsons = "";
									var $table = $("#updateUnFreezedTable",
											navTab.getCurrentPanel());
									_jsons += _cs_tableToJson($table);

									/* alert(_jsons); */
									$("#jsons").val(_jsons);
									var acceptId = document
											.getElementById("acceptId").value;
									var changeId = document
											.getElementById("changeId").value;

									$
											.ajax({

												type : "post",
												url : rootPath
														+ '/cs/serviceitem_cf/updateUnfreezePolicyInfo_PA_csEndorseCFAction.action',
												cache : false,
												data : "jsonString="
														+ $("#jsons").val()
														+ "&jsonUnfreezeCause="
														+ unfreezeCause.val()
														+ "&jsonUnfreezeDate="
														+ unfreezeDate
														+ "&changeId="
														+ changeId
														+ "&acceptId="
														+ acceptId,
												dataType : "json",
												success : function(data) {

													//$("#saveUnFreezeInfoForm",navTab.getCurrentPanel()).submit();

												},

												error : function() {
													alertMsg.error("保存失败！");
												}
											});
								},
								cancleCall : function() {

								}
							});

		};
		//下一步
		function next() {
			var val1 = $("#changeId").val();
			var val2 = $("#acceptId").val();
			var val3 = $("#customerId").val();
			var title = "受理信息录入";
			var tabid = "_aplPermit_M";
			var fresh = eval("true");
			var external = eval("false");
			var url = "${ctx}/cs/csEntry/showAcceptAndArapInfo_PA_csEntryAction.action?&menuId=${menuId}&changeId="
					+ val1 + "&acceptId=" + val2 + "&customerId=" + val3;
			navTab.openTab(tabid, url, {
				title : title,
				fresh : fresh,
				external : external
			});

		}

		//帮助部分动作
		function MM_changeProp(objId, theProp, theValue) {
			var obj = null;
			with (document) {
				if (getElementById) {
					obj = getElementById(objId);
				}
			}
			if (obj) {
				if (theValue == true || theValue == false) {
					eval("obj.style." + theProp + "=" + theValue);
				} else {
					eval("obj.style." + theProp + "='" + theValue + "'");
				}
			}
		};
	
	//页面加载的时候判断 queryFlag 如果是1则隐藏按钮	
	$(document).ready(function(){
		$("#readonly input").attr("readonly","true");
		$("#readonly input").attr("UNSELECTABLE","on");
		var queryFlag = $("#queryFlag").val();
		
		$("#_freezeEndDate1",navTab.getCurrentPanel()).hide();

		getPeriodNum();
		if('1'==queryFlag){
			//保存按钮隐藏
			$("#saveMesId", navTab.getCurrentPanel()).hide();
			$("#step_header",navTab.getCurrentPanel()).hide();
			$("#formBar",navTab.getCurrentPanel()).hide();
			
		}  
	}); 
	
	function endDateClear(){
		if ($("#longDate").attr("checked") == "checked") {
			$("#_freezeEndDate", navTab.getCurrentPanel()).val("9999-12-31");
			$("#_freezeEndDate1", navTab.getCurrentPanel()).val("9999-12-31");
			$("#_freezeEndDate", navTab.getCurrentPanel()).hide();
			$("#_freezeEndDate1", navTab.getCurrentPanel()).show();
			$("#_freezeEndDate1", navTab.getCurrentPanel()).attr('disabled', true);
			$("#freeze_Period", navTab.getCurrentPanel()).attr('disabled', true);
			$("#freeze_Period", navTab.getCurrentPanel()).val("");
		} else {
			$("#_freezeEndDate", navTab.getCurrentPanel()).val("");
			$("#_freezeEndDate", navTab.getCurrentPanel()).show();
			$("#_freezeEndDate1", navTab.getCurrentPanel()).hide();
			$("#freeze_Period", navTab.getCurrentPanel()).attr('disabled', false);
			$("#freeze_Period", navTab.getCurrentPanel()).val("");
			//$("#longDate").attr("checked") == "false";

		}
	}
	//计算月份差 
	function getPeriodNum() {
		var _freezeDate = $("#_freezeDate", navTab.getCurrentPanel()).val();//冻结起期
		var _freezeEndDate = $("#_freezeEndDate", navTab.getCurrentPanel()).val();//冻结止期
		if (_freezeDate != null && _freezeDate != "" && _freezeEndDate != null && _freezeEndDate != "") {
			//计算月份
			
			var freezeDate = _freezeDate.replaceAll("-", "");//冻结起期
			var freezeDate_year = parseInt(freezeDate.substr(0, 4), 10);
			var freezeDate_month = parseInt(freezeDate.substr(4, 2), 10);
			var freezeDate_day = parseInt(freezeDate.substr(6, 2), 10);
			var freezeEndDate = _freezeEndDate.replaceAll("-", "");//冻结止期
			var freezeEndDate_year = parseInt(freezeEndDate.substr(0, 4),10);
			var freezeEndDate_month = parseInt(freezeEndDate.substr(4, 2),10);
			var freezeEndDate_day = parseInt(freezeEndDate.substr(6, 2),10);	
			var periodNum = (freezeEndDate_year*12+freezeEndDate_month) - (freezeDate_year*12+freezeDate_month);
			 if(freezeEndDate_year>='9999'){
				$("#freeze_Period", navTab.getCurrentPanel()).attr('disabled', true);
				$("#freeze_Period", navTab.getCurrentPanel()).val("");
				$("#_freezeEndDate", navTab.getCurrentPanel()).hide();
				$("#_freezeEndDate1", navTab.getCurrentPanel()).show();
				$("#_freezeEndDate1", navTab.getCurrentPanel()).attr('disabled', true);
				$("#longDate", navTab.getCurrentPanel()).attr('checked', "checked");
				$("#_freezeEndDate", navTab.getCurrentPanel()).val("9999-12-31");
				$("#_freezeEndDate1", navTab.getCurrentPanel()).val("9999-12-31");
				
			} else {
				if(freezeEndDate_day> freezeDate_day){			
				periodNum = periodNum+1;
				}
				$("#freeze_Period", navTab.getCurrentPanel()).attr('disabled', false);
				$("#freeze_Period", navTab.getCurrentPanel()).val(periodNum);
			} 
					
		}
	}
	</script>