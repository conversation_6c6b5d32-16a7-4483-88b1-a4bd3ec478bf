<%@ page contentType="text/html; charset=utf-8"%>
<%@ taglib uri="/struts-tags" prefix="s"%><%@include file="/udmpCommon.jsp"%>
<%@ taglib uri="http://www.git.com.cn/taglib/udmp/field" prefix="Field"%>
<%@ include file="/cs/pages/common/jsp/common_ctx.jsp"%>
<script type="text/javascript" src="${ctx}/cs/js/cs_common.js"></script>
<link rel="stylesheet" href="${ctx}/cs/css/public.css" type="text/css">
<style type="text/css">
th,td {
	white-space: normal;
}

.searchBar li label {
	width: 60px;
}
</style>
<!-- ********帮助菜单***********begin********* -->
<s:include value="/cs/pages/common/jsp/helpMenu.jsp"></s:include>
<!-- *********帮助菜单***********end********* -->

<div onmousedown="MM_changeProp('holderInfoCmDiv','display','none')" >
	<div id="shtab" class="backgroundCollor" style="height: 620px; overflow: auto;" layOutH='4'>
<s:if test="queryFlag!=1">
<s:include value="entryProgressBar.jsp"></s:include>
</s:if>
	
	
	
	
		<div >
			<div >
				<ul>
				</ul>
			</div>
		</div>
		<div >
			<div >
				<div >
				
				<div class="divfclass">
				<h1 >
				  <img src="cs/img/icon/tubiao.png">关联银行卡 
				</h1>
				</div>
				
					
					<div >
					<s:include value="customerInfo_list.jsp" />
						<div   style="display: none">
							<h1>客户基本信息</h1>
							<div>
								<ul class="searchContent" style="height: auto;">
									<li class="nowrap"><label>客户姓名</label> <input
										id="customerName" type="text" name="csCustomerVO.customerName"
										value="${csCustomerVO.customerName}" readonly="readonly" /></li>
									<li class="nowrap"><label>出生日期</label> <input type="text"
										name="csCustomerVO.customerBirthday"
										value="<s:date format="yyyy-MM-dd" name="csCustomerVO.customerBirthday"></s:date>"
										readonly="readonly" /></li>
								</ul>
								<ul class="searchContent" style="height: auto;">
									<li class="nowrap"><label>证件类型</label> <input
										readonly="readonly" type="text"
										name="csCustomerVO.customerCertType"
										value="<Field:codeValue tableName="APP___PAS__DBUSER.T_CERTI_TYPE" value="${csCustomerVO.customerCertType}"/>" /></li>
									<li class="nowrap"><label>证件号码</label> <input type="text"
										name="customerVO.customerCertiCode"
										value="${csCustomerVO.customerCertiCode}" readonly="readonly" /></li>
									<li class="nowrap"><label style="width: auto;">性别&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
											<input type="text" name="csCustomerVO.customerGender"
											value="<Field:codeValue tableName="APP___PAS__DBUSER.T_GENDER" value="${csCustomerVO.customerGender}"/>"
											readonly="readonly">
									</label></li>
								</ul>
							</div>
						</div>
						<div  >
							
							<div class="divfclass">
							<h1 >
							  <img src="cs/img/icon/tubiao.png">客户银行卡信息 
							</h1>
							</div>
							
							
							
							<div>
								<table class="list" id="" width="100%">
									<thead>
										<tr id="" align="center">
											<th width="30px">选择</th>
											<th>开户银行</th>
											<th>银行代码</th>
											<th>银行帐号</th>
											<th>账户名</th>
											<th>是否已验证</th>
											<th>是否验证通过</th>
										</tr>
									</thead>
									<tbody>
										<s:iterator value="csBankAccountVOs">
											<tr align="center">
												<td><input type="radio" name="accountId"  
													class="myClasss" value="${accountId}" <s:if test="queryFlag==1">disabled="disabled"</s:if>
													onclick="getBankName(this)" /></td>
												<td><input type="hidden" id="bankName"
													disabled="disabled" /> <Field:codeValue tableName="APP___PAS__DBUSER.T_BANK"
														value="${bankCode}" /></td>
												<td><input type="text" value="${bankCode}"
													readonly="readonly" /></td>
												<td><input type="text" value="${bankAccount}"
													readonly="readonly" /></td>
												<td><input type="text" value="${accoName}"
													readonly="readonly" /></td>
												<td><input type="text"
													value="<Field:codeValue tableName="APP___PAS__DBUSER.T_YES_NO" value="${verifiedFlag}"/>"
													readonly="readonly" />
													<input type="hidden"
													value="${verifiedFlag}" />
													</td>
												<td><input type="text"
													value="<Field:codeValue tableName="APP___PAS__DBUSER.T_YES_NO" value="${verifiedPassFlag}"/>"
													readonly="readonly" />
													<input type="hidden"
													value="${verifiedPassFlag}" />
													</td>
											</tr>
										</s:iterator>
									</tbody>
								</table>
								
										<s:if test="queryFlag!=1">
										<div class="formBarButton">	
										<ul>
										<li>
												<button type="button" class="but_blue" onclick="addBankAccount();">新增</button>
										</li>
										<li>
												<button type="button" class="but_blue" onclick="updateBankAccount();">修改</button>
										</li>
										<li>
												<button type="button" class="but_blue" onclick="deleteBankAccount();">删除</button>
										</li>
										</ul>
										</div>
										</s:if>
								
								
								
								
<!-- 								<div style="margin:5px;">
								<table style="width:100%">
								<tbody>
								<tr>
								<td></td>
								<td style="width:150px;">
									<ul>
										<li <s:if test="queryFlag==1">style="display:none"</s:if>>
											<div class="button">
												<div class="buttonContent">
													<button type="button" onclick="addBankAccount()"
														style="width: auto;">新增</button>
												</div>
											</div>
										</li>
										<li <s:if test="queryFlag==1">style="display:none"</s:if>>
											<div class="button">
												<div class="buttonContent">
													<button type="button" onclick="updateBankAccount()"
														style="width: auto;">修改</button>
												</div>
											</div>
										</li>
										<li <s:if test="queryFlag==1">style="display:none"</s:if> >
											<div class="button">
												<div class="buttonContent">
													<button type="button" onclick="deleteBankAccount()"
														style="width: auto;">删除</button>
												</div>
											</div>
										</li>
									</ul>
									</td>
									<td></td>
									</tr>
									</tbody>
									</table>
								</div>
 -->					<div id="bankDiv" >
 
 							<div class="divfclass">
							<h1 >
							  <img src="cs/img/icon/tubiao.png">银行卡信息录入 
							</h1>
							</div>
									<div class="pageFormInfoContent">
										<form
											action="${ctx}/cs/serviceitem_cd/saveBankAccount_PA_csEndorseCDAction.action"
											onsubmit="return navTabSearch(this);" id="bankForm">
											<input type="hidden"  id="accountId" name="csBankAccountVO.accountId" />
											<input type="hidden"  id="acceptId" name="acceptId" value="${acceptId}" /> 
											<input type="hidden"  id="changeId" name="changeId" value="${changeId}" /> 
											<input type="hidden"  id="customerId" name="customerId" value="${customerId}" />
											<!-- 录入完成页面跳转 -->
											<input type="hidden" id="menuId" name="menuId" value="${menuId}" />
											
											<input type="hidden"  name="csBankAccountVO.acceptId" value="${acceptId}" /> 
											<input  type="hidden"  name="csBankAccountVO.changeId" value="${changeId}" /> 
											<input  type="hidden"  name="csBankAccountVO.customerId" value="${customerId}" />
											
												<!-- 修改类型 -->
											<input type="hidden" name="csBankAccountVO.operationType"  value="${csBankAccountVO.operationType}"/>
											<!-- 是否已第三方验证 -->
											<input type="hidden"  name="csBankAccountVO.verifiedFlag" value="${csBankAccountVO.verifiedFlag}"/>
											<!-- 是否已通过第三方验证 -->
											<input type="hidden"  name="csBankAccountVO.verifiedPassFlag" value="${csBankAccountVO.verifiedPassFlag}"/>
											<div >																									<dl>
													<dt>开户银行</dt>
													<dd><Field:codeTable id="bank" 
															nullOption="true" name="csBankAccountVO.issueBankName"
															value="${csBankAccountVO.issueBankName}"
															tableName="APP___PAS__DBUSER.T_BANK" defaultValue=""
															onChange="selectBank()" whereClause="BANK_NAME LIKE 'NTS%'  AND IS_CREDIT_CARD = '0'"/>
															<font color="red">*</font>
													</dd>
													</dl>
													<dl>
													<dt>银行代码</dt>
													<dd>
													<input id="bankCode" type="text"
														name="csBankAccountVO.bankCode" value="${csBankAccountVO.bankCode}" readonly="true" />
														<font color="red">*</font>
													</dd>
													</dl>
													<dl>
													<dt>银行帐号</dt>
													<dd>
													<input id="bankAccount" type="expandBankAccount"  size="100"  class="required doublecheckcap exbankcardNew" maxlength="100"
														name="csBankAccountVO.bankAccount" onchange="checkBankAccount(this)"/><span id="checkaccountDIV"></span>
														<input type="hidden" id="bankAccount1" value="" size="100" maxlength="100"/> 
														<font color="red">*</font>
													</dd>
													</dl>
													<dl>
													<dt>账户名</dt>
													<dd>
														<input id="accoName" type="text" name="csBankAccountVO.accoName"/  maxlength="100" >
														<font color="red">*</font>
													</dd>
													</dl>	
													
													<div class="formBarButton">	
													<div>
													<ul>
													<li>
															<button type="button" class="but_blue" onclick="verification();">第三方验证</button>
															<button type="button" class="but_blue" onclick="saveBank();">保存</button>
													</li>
													</ul>
													
													</div>
													</div>
																							
											</div>
										</form>
									</div>
								</div>
							</div>
						</div>
					</div>
				</div>
				<div class="tabsFooter">
					<div class="tabsFooterContent"></div>
				</div>
				<s:include value="/cs/pages/serviceitem/serviceItemBottomBar.jsp"></s:include>
			</div>
		</div>
	</div>
</div>
<script type="text/javascript">
	//获取银行名称
	function getBankName(obj) {
		$("#bankName").attr('value',
				$(obj).parent().parent().find("td:eq(1)").text().trim());
	}

	//初始化
	$(document).ready(function() {
		$("#bankDiv").hide();
	});
	//新增
	function addBankAccount() {
		
		var $form = $("#bankForm", navTab.getCurrentPanel());
		$form[0].reset();
		$("#accoName").val($("#customerName").val());
		$("#bankDiv").show();
		$("input[name='csBankAccountVO.operationType']").val("1");
		$("input[name='csBankAccountVO.verifiedFlag']").val("0");
		$("input[name='csBankAccountVO.verifiedPassFlag']").val("0");
	};

	// 修改
	function updateBankAccount() {
		
		if ($(".myClasss:checked").val() != null) {
			$("input[name='csBankAccountVO.operationType']").val("2");
			var tdObj = $(".myClasss:checked").parent();
			var trObj = $(tdObj).parent();
			var tds = $(trObj).find("td");
			var accountId = $(tds[0]).find("input").val();
			var issueBankName = $("#bankName").val();
			var bankCode = $(tds[2]).find("input").val();
			var bankAccount = $(tds[3]).find("input").val();
			var accoName = $(tds[4]).find("input").val();
			$("#bank").attr('value', bankCode);
			$("a[name='csBankAccountVO.issueBankName']").text(issueBankName);
			$("#bankCode").val(bankCode);
			$("input[id='bankAccount']").val(bankAccount);
			$("input[name='csBankAccountVO.bankAccount']").val(bankAccount);
			$("#accoName").val(accoName);
			$("#demo").val(bankAccount);
			$("#accountId").val(accountId);
			$("#bankDiv").show();
			$("input[name='csBankAccountVO.verifiedFlag']").val($(tds[5]).find("input").eq(1).val());
			$("input[name='csBankAccountVO.verifiedPassFlag']").val($(tds[6]).find("input").eq(1).val());
		} else {
			alert("请选择要修改的信息记录");
		}
	};

	//删除
	function deleteBankAccount() {
		if ($(".myClasss:checked").val() != null) {
			alertMsg
					.confirm(
							"请确认是否需要删除",
							{
								okCall : function() {
									var accountId = $(
											"input[name='accountId']:checked",
											navTab.getCurrentPanel()).val();

									var acceptId = $("#acceptId").val();
									var changeId = $("#changeId").val();
									var customerId = $("#customerId").val();
									$("input[name='accountId']:checked",
											navTab.getCurrentPanel()).parent()
											.parent().remove();
									$
											.ajax({
												type : "post",
												dataType : "text",
												url : "${ctx}/cs/serviceitem_cd/deleteBankAccount_PA_csEndorseCDAction.action",
												data : 'csBankAccountVO.accountId='
														+ accountId
														+ '&csBankAccountVO.acceptId='
														+ acceptId
														+ '&csBankAccountVO.changeId='
														+ changeId
														+ '&csBankAccountVO.customerId='
														+ customerId,
												success : function() {
													alertMsg.correct("删除成功");
												}
											});
								},
								cancleCall : function() {

								}
							});
		} else {
			alert("请选择要删除的信息记录");
		}

	};

	//保存
	function saveBank() {
		var bankCode=$("#bankCode").val();
		var bankAccount=$("#bankAccount").val();
		var bank=$("#bank").val();
		var accoName = $("#accoName").val();
	 	if(bankCode==""||bankAccount==""||bank==""){
			alertMsg.info("不能进行第三方验证");
			return ;
		}
	 	$("input[name='csBankAccountVO.operationType']").val();
		var customerName=$("#customerName").val();
		if(customerName!=accoName){
			alertMsg.confirm("录入的账户名不是客户姓名，请确认是否继续。",{
				okCall : function(){
					save();
				},
				cancelCall : function() {
				
				}
			});
			
		}else{
			save();
		}
	};
	function save(){
		 var verifiedFlag = $("input[name='csBankAccountVO.verifiedFlag']").val();
		if(verifiedFlag === "0" || verifiedFlag === ""){
			alertMsg.confirm("未进行第三方验证，请确认是否保存。",{
				okCall : function(){
					var $form = $("#bankForm", navTab.getCurrentPanel());
					$form.submit();
					alertMsg.correct("保存成功");
				},
				cancelCall : function() {
				
				}
			});
		}else{
			var $form = $("#bankForm", navTab.getCurrentPanel());
			$form.submit();
			alertMsg.correct("保存成功");
		}
	}
	function verification(){
		$.ajax({
			type : 'POST',
			url : "${ctx }"+ "/cs/serviceitem_cd/verification_PA_csEndorseCDAction.action",
			data : $("#bankForm").serialize(),
			cache : false,
			success : function(response) {
				var json = DWZ.jsonEval(response);
				$("input[name='csBankAccountVO.verifiedFlag']").val("1");
				if(json.statusCode === "200"){//通过
					$("input[name='csBankAccountVO.verifiedPassFlag']").val("1");
				}else{
					$("input[name='csBankAccountVO.verifiedPassFlag']").val("0");
					
					alertMsg.confirm(json.message,{
						okCall : function(){
							saveBank();
						},
						cancelCall : function() {
							
						}
					});
				} 
			}
		});
	}
	function selectBank() {
		var bankCode = $("#bank").val();
		var bankName = $('#bank option:selected').text();
		
		$("#bankCode").val(bankCode);
		var sensor = $('<pre>'+ bankName +'</pre>').css({display: 'none'}); 
        $('body').append(sensor); 
        var width = sensor.width();
		if(width>130){
			$("#bank").css("width",width+30,"px");
		}else{
			$("#bank").css("width",153,"px");
		}
		sensor.remove();
	}
	
	
    function checkBankAccount(obj) {
    	var bankAccount = obj.value;
    	if ($("#bankAccount1").val() == ""
    		|| $("#bankAccount1").val() == null) {
    	    $("#bankAccount1").attr("value", bankAccount);
    	    obj.value = "";
    	    $("#checkaccountDIV").html(
    		    '<font>&nbsp;&nbsp;&nbsp;&nbsp;请再次输入</font>');
    	} else {
    	    if (obj.value == $("#bankAccount1").val()) {
    		$("#bankAccount1").attr("value", "");
    		$("#bankAccount").attr("class", "textInput holder");
    		$("#checkaccountDIV").html("");

    	    } else {
    		$("#bankAccount").attr("class","textInput error");
    		$("#bankAccount").val("");
    		$("#checkaccountDIV").html("");
    		$("#bankAccount1").attr("value", "");
    		alertMsg.warn("两次输入账号不一致");

    	    }
    	}

        }
</script>

<script type="text/javascript">
	//帮助部分动作
	function MM_changeProp(objId, theProp, theValue) {
		var obj = null;
		with (document) {
			if (getElementById) {
				obj = getElementById(objId);
			}
		}
		if (obj) {
			if (theValue == true || theValue == false) {
				eval("obj.style." + theProp + "=" + theValue);
			} else {
				eval("obj.style." + theProp + "='" + theValue + "'");
			}
		}
	};

	//下一步
	function next() {
		var val1 = $("#changeId").val();
		var val2 = $("#acceptId").val();
		var val3 = $("#customerId").val();
		var title = "受理信息录入";
		var tabid = "_aplPermit_M";
		var fresh = eval("true");
		var external = eval("false");
		var url = "${ctx}/cs/csEntry/showAcceptAndArapInfo_PA_csEntryAction.action?&menuId=${menuId}&changeId="
				+ val1 + "&acceptId=" + val2 + "&customerId=" + val3;
		navTab.openTab(tabid, url, {
			title : title,
			fresh : fresh,
			external : external
		});

	}

	
</script>