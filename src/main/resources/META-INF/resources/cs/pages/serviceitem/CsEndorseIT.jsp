<%@ page language="java" contentType="text/html; charset=utf-8" pageEncoding="utf-8"%>
<%@ taglib uri="http://www.git.com.cn/taglib/udmp/field" prefix="Field"%>
<%@ taglib uri="/struts-tags" prefix="s"%>
<%@include file="/udmpCommon.jsp"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<%@ include file="/cs/pages/common/jsp/common_ctx.jsp" %>
<link rel="stylesheet" href="${ctx}/cs/css/public.css" type="text/css">
<script type="text/javascript" src="${ctx}/cs/js/cs_common.js"></script>
<script	src="${ctx }/udmp/plugins/ribbon/jquery.asyncorgtree.js" type="text/javascript"></script>
<script type="text/javascript" src="${ctx}/cs/js/DomZoomZX.js"></script>
<link rel="stylesheet" href="${ctx }/cs/css/cs_common.css" type="text/css">

<script type="text/javascript">
	$(document).ready(function(){
        var D = DomZoomZX('input,select,textarea,td')
	})
</script>
<style type="text/css">
th,td {
	white-space: normal;
}

.searchBar li label {
	width: 90px;
}
</style>
<s:include value="/cs/pages/common/jsp/helpMenu.jsp"></s:include>
<s:include value="csEndorseProgress.jsp" />

<div layoutH="140" >
<div class="divfclass">
	<h1>
		<img src="images/tubiao.png">投连险退保
	</h1>
</div>

	<form  class="required-validate"
		onsubmit="return navTabSearch(this,'showAFPage');"
		id="premSurrendForm">
		<input type="hidden" name="changeId" id="changeId" value="${changeId}" />
		<input type="hidden" name="acceptId" id="acceptId" value="${acceptId}" />
		<input type="hidden" name="policyChgId" id="policyChgId" value="${policyChgId}" />
		<input type="hidden" name="customerId" id="customerId" value="${customerId}" /> 
		<input type="hidden" name="busiItemIds" id="busiItemIds" value="${busiItemIds}" />
		<input type="hidden" name="queryFlag" id="_queryFlag" value="${queryFlag}" />
		
		<div class="pageContent" >
			<!-- 客户信息 -->
			<s:include value="customerInfo_list.jsp" />
			
			
			<s:if test="verifyFlag==1">
				<s:include value="verifyInfo_list.jsp" />
			</s:if>

			<div class="pageContent" >
				<div class="divfclass">
					<h1>
						<img src="cs/img/icon/tubiao.png">保单信息
					</h1>
				</div>
				<div class="tabdivclass">
					<table class="list" id="table1" width="100%" table_saveStatus="1">
						<thead>
							<tr id="" align="center">
								<th>选择</th>
								<th colName="isPCancel" style="display: none;">退保标识</th>
								<th colName="policyId" style="display: none;">policyId</th>
								<th colName="policyChgId" style="display: none;">policyChgId</th>
								<th colName="busiItemId" style="display: none;">险种id</th>
								<th colName="policyCode">保单号</th>
								<th colName="busiPrdCode">险种代码</th>
								<th colName="busiPrdId">险种名称</th>
								<th>险种状态</th>
								<th>保额</th>
								<th>生效日期</th>
								<th>保单年度</th>
								<th>被保险人</th>
							</tr>
						</thead>
						<tbody>
							<s:iterator value="listBfSurrender" id="listBfSurrender">
							<s:if test="prodStatus != 3">
								<tr tr_saveStatus='1'>
									<td> 
										<input <s:if test="checked==1">checked="checked"</s:if> type="checkbox"  class="myClass" 
											<s:if test="#request.queryFlag==1">disabled="disabled"</s:if> 
											onclick="selectItem(this);" value="${busiItemId}" />
										
									</td>
									<td style="display: none;"></td>
									<td style="display: none;">${policyId }</td>
									<td style="display: none;">${policyChgId }</td>
									<td style="display: none;">${busiItemId }</td>
									<td>${policyCode }</td>
									<td>${busiProdCode }</td>
									<td><Field:codeValue tableName="APP___PAS__DBUSER.T_BUSINESS_PRODUCT" value="${busiPrdId}" /></td>
									<td><Field:codeValue tableName="APP___PAS__DBUSER.T_LIABILITY_STATUS" value="${prodStatus}" /></td>
									<td>${productCost }</td>
									<td><s:date name="validatePDay" format="yyyy-MM-dd" /></td>
									<td>${policyDur }</td>
									<td>${insureds }</td>

								</tr>
								</s:if>
							</s:iterator>
						</tbody>
					</table>
				</div>
			</div>
		</div>

		<!-- 投连险账户信息和退保信息界面 -->
		<div id="showIPLPage">
			<s:if test="queryFlag==1 || (csAccountTIVOList!=null&& csAccountTIVOList.size()>0)">
			<s:include value="/cs/pages/serviceitem/CsEndorseIT_ilp.jsp"></s:include>	
			</s:if>	
		</div>

		<!-- 变更后界面 -->
		<div id="showAFPage">
			<s:if test="queryFlag==1 || (listAfSurrender!=null&&listAfSurrender.size() > 0)">
			<s:include value="/cs/pages/serviceitem/CsEndorseIT_after.jsp"></s:include>	
			</s:if>		
		</div>
	</form>
	<!-- 按钮 -->
	<s:include value="/cs/pages/serviceitem/serviceItemBottomBar.jsp"></s:include>
</div>
<!-- js代码  -->
<script type="text/javascript">
	//通过复选框的单击事件，来对退保标识进行赋值
	function selectItem(elem){
		var isCheck = $(elem).parents("tr:first").find("input[id^='itemId']").attr("checked");
				
		//将选中的busiItemId拼接到隐藏域中
		var busiItemIds ="";
		$("#table1 input[type='checkbox']:checked").each(function(){
			if(busiItemIds==""){
				busiItemIds = $(this).val();
			}else{
				busiItemIds = busiItemIds +","+ $(this).val();
			}
		});
		
		$("#busiItemIds").attr('value',busiItemIds);
		
		//查询选中险种的投连账户信息
		var action ="${ctx }/cs/serviceitem_it/showIPLPage_PA_csEndorseITAction.action";
		var onsubmit ="return divSearch(this,'showIPLPage');";
		$("#premSurrendForm").attr('action',action);
		$("#premSurrendForm").attr('onsubmit',onsubmit);		
		$("#premSurrendForm").submit();
	};
	
	//保存退保信息
	function _itSave(){		
		debugger;
		
		var ckNum=$("#table1 input[type='checkbox']:checked").size();
		if(ckNum==0){
			alertMsg.error("请选择要退保的险种信息");
		}
		
		//处理费用详细信息
		var _table = $("#detailTable",navTab.getCurrentPanel());
		var _tableJs = _cs_tableToJson(_table);
		var _jsonBox = $("#feeDetailJson",navTab.getCurrentPanel());
		_jsonBox.val(_tableJs);
		
		var ilpSurrenderCause =  $("#ilpSurrenderCause",navTab.getCurrentPanel()).find("option:selected").val();
		var agentHolderRelation = $("#agentHolderRelation",navTab.getCurrentPanel()).find("option:selected").val();
		
		
		if($("#delayCause",navTab.getCurrentPanel()).size()>0 
				&& $("#delayCause",navTab.getCurrentPanel()).val()==""){
				alertMsg.info("请填写延迟原因！");
				return;
		}
		
		//验证退保原因及投保人与业务员关系
		if($("#ilpSurrenderCause",navTab.getCurrentPanel()).size()>0 
				&& $("#ilpSurrenderCause",navTab.getCurrentPanel()).val()==""){
			alertMsg.info("请填写退保原因！");
			return;
		}
		
		if($("#agentHolderRelation",navTab.getCurrentPanel()).size()>0 
				&& $("#agentHolderRelation",navTab.getCurrentPanel()).val()==""){
			alertMsg.info("请填写投保人与业务员关系！");
			return;
		}

		var busiItemIds ="";
		
		var myClass=$(".myClass");
		for(var i=0;i<myClass.length; i++){
			if(myClass[i].checked){
				if(busiItemIds==""){
					busiItemIds = myClass[i].value;
				}else{
					busiItemIds = busiItemIds +","+ myClass[i].value;
				}
			}
		}
		$("#busiItemIds").attr('value',busiItemIds);
         
         $.ajax({  
             type: "POST",
             url:"${ctx }/cs/serviceitem_it/checkITMsg_PA_csEndorseITAction.action?itflag=1", 
             data: $('#premSurrendForm').serialize(), 
             cache: false,
             success: function(data) {   
            	 debugger;
            	 var json = jQuery.parseJSON(data);	
             	 var msg=json.message;
 				 if (json.statusCode == 200&&msg!='') {
 					alertMsg.confirm(msg, {
	 						okCall : function() {
	 							_sumbitIT();//保存投连退保信息
	 						},cancelCall:function(){
	 							
	 						} 					        
 						});		
 		
 				}else if(json.statusCode == 300&&msg!=''){
 					alertMsg.error(msg);
 				}else{
 					_sumbitIT();//保存投连退保信息
 				}         
             },
             error: function() { 
             	alertMsg.error("保存失败");
             }  
          });
		
	}
	
	function _sumbitIT(){		
		$.ajax({  
            type: "POST",
            url:"${ctx }/cs/serviceitem_it/saveIPLSurrender_PA_csEndorseITAction.action?itflag=1", 
            data: $('#premSurrendForm').serialize(), 
            cache: false,
            success: function(data) {
               $("#showAFPage",navTab.getCurrentPanel()).html(data).initUI();
            },
            error: function() { 
            	alertMsg.error("保存失败");
            }  
         });
		
	}

</script>
