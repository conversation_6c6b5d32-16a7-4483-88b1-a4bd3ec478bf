<%@ page contentType="text/html; charset=utf-8"%>
<%@ taglib uri="/struts-tags" prefix="s"%><%@include
	file="/udmpCommon.jsp"%>
<%@ taglib uri="http://www.git.com.cn/taglib/udmp/field" prefix="Field"%>
<%@ include file="/cs/pages/common/jsp/common_ctx.jsp"%>
<script type="text/javascript" src="${ctx}/cs/js/cs_common.js"></script>
<link rel="stylesheet" href="${ctx}/cs/css/cs_common.css"
	type="text/css">
<link rel="stylesheet" href="${ctx}/cs/css/public.css" type="text/css">

<!-- ********帮助菜单***********begin********* -->
<s:include value="/cs/pages/common/jsp/helpMenu.jsp"></s:include>
<!-- *********帮助菜单***********end********* -->
<div class="pageFormContent" layoutH="100px" style="padding-top: 5px;">
<%-- 进度条 --%>


	<div class="divfclass">
		<h1><img src="cs/img/icon/tubiao.png">客户电话变更批量后台导入（河南）</h1>
		<div class="pageFormContent">
			
			<form id="testss"
				action="${ctx}/cs/serviceitem_ip/exportBatchToExcel_PA_csEndorseIPAction.action"
				method="post" enctype="multipart/form-data" style="margin-top: 10px"
				onsubmit="return iframeCallback(this,testRespons);">
				
				<dl style="width: 350px; margin-top: 10px; margin-top: 3px">
					<dt style="width: 100px;">
						<span>文件</span>
					</dt>
					<dd>
						<input type="file" name="fileName" id="fileName">
					</dd>
				</dl>
				
				<dl>
					<dt>
						<button type="button"  class="but_blue" onclick="sumit()">导入</button>
					</dt>
				</dl>
				<dl style="width: 80px; margin-left: -180px;">
					<dt>
						<!-- <a title="确定要导出这些记录吗?"
							href="${ctx}/cs/serviceitem_ip/exportToExcel_PA_csEndorseIPAction.action"
							target="dwzExport" id="exportExcel" style="display: none;">
						</a>  -->
						<button type="button" class="but_blue" onclick="exportToExcel()">导入模板下载</button>
					</dt>
				</dl>
			</form>
			<!-- 导入模板下载功能 -->
			<form id="exportExcelid" action="${ctx}/cs/serviceitem_ip/exportToExcel_PA_csEndorseIPAction.action" ></form>
			
			
		</div>
		
		<!-- 展示导入数据 -->
		<h1><img src="cs/img/icon/tubiao.png">列表轨迹</h1><br/>
		<div id="result" class="pageContent">
			<s:include value="CsEndorseIP_result.jsp"></s:include>
		</div>
		<!-- 展示错误信息 -->
		<h1><img src="cs/img/icon/tubiao.png">错误信息</h1><br/>
		<div id="error" class="pageContent">
			<s:include value="CsEndorseIP_Error.jsp"></s:include>
		</div>
		
	</div>
	</div>
<script type="text/javascript">
function exportToExcel(){
	//$("#exportExcel").click();
	//修改导出模板报错问题 #106_4502 chenxuan
	alertMsg.confirm("确定要导出这些记录吗?", {
		okCall : function (){
			$("#exportExcelid", navTab.getCurrentPanel()).submit();
		}
	});
	
}
function sumit(){
	var fileName = $("#fileName").val();
	if(fileName == null || '' == fileName){
		alertMsg.info("请选择要导入的文件！");
		return false;
	}
    $("#testss", navTab.getCurrentPanel()).submit();
}
function testRespons(response){
//searchTableAction();
	var json = response;
	if (json!=null && json.statusCode!=null && json.statusCode == DWZ.statusCode.error) {
		alertMsg.error(decodeURI(json.message));
	} else {
		searchExcelAction();
		var $box = $("#testDiv", navTab.getCurrentPanel());
		$box.html(response).initUI();	
	}
	
// 	var json = DWZ.jsonEval(response);	
};
function searchTableAction(){
	$("#queryAppDocCfg", navTab.getCurrentPanel()).submit();
}
function searchExcelAction(){
	$("#queryRecoveryDetailExcel", navTab.getCurrentPanel()).submit();
}
</script>