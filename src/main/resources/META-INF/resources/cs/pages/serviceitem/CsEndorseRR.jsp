<%@ page language="java" contentType="text/html; charset=utf-8" pageEncoding="utf-8"%>
<%@ taglib uri="/struts-tags" prefix="s"%><%@include file="/udmpCommon.jsp"%>
<!-- 引入点击下一步返回保全录入的js -->
<%-- <s:set var="ctx">${pageContext.request.contextPath}</s:set> --%>
<%@ include file="/cs/pages/common/jsp/common_ctx.jsp" %>
<script type="text/javascript" src="cs/pages/common/js/returnAcceptAndPay.js"></script>
<%@ taglib uri="http://www.git.com.cn/taglib/udmp/field" prefix="Field"%>
<script type="text/javascript" src="${ctx}/cs/js/cs_common.js"></script>
<link rel="stylesheet" href="${ctx }/cs/css/cs_common.css" type="text/css">
<input type="hidden" name="menuId" id="menuId" value="${menuId}">
<input type="hidden" name="itemFlag" id="itemFlag" value="mainProd">
<input type="hidden" name="itemName" id="itemName" value="续保险种转换">
<!-- ********帮助菜单***********begin********* -->
<s:include value="/cs/pages/common/jsp/helpMenu.jsp"></s:include>
<s:include value="csEndorseProgress.jsp" />
<!-- *********帮助菜单***********end********* -->
		<div class="pageContent" layoutH="140" ><!-- 动态定义一个域 -->
		<s:include value="customerInfo_list.jsp" />
		
		
		<!--  变更前社保状态-->
		<!-- 151299 start -->
		<s:if test=" multiInsuredFlag==1 ">
		<div class="divfclass" style="max-width:50%;">
			<h1>
				<img src="cs/img/icon/tubiao.png">被保人社保状态
			</h1>
			<div class="divfclass" >
			<form id="customerSocialSecuForm" action="${ctx}/cs/serviceitem_so/saveSocialSecuList_PA_csEndorseSOAction.action" method="post" class="pageForm required-validate"
				  onsubmit="return navTabSearch(this,'addSubBusiProdNew')">
				<input type="hidden" id="changeId" name="csCustomerSocialSecuVO.changeId" value="${csCustomerSocialSecuVO.changeId }"> 
				<input type="hidden" id="acceptId" name="csCustomerSocialSecuVO.acceptId" value="${csCustomerSocialSecuVO.acceptId }"> 
				<input type="hidden" id="isSaved" name="csCustomerSocialSecuVO.isSaved" value="${csCustomerSocialSecuVO.isSaved }">
				<input type="hidden" id="newSocialSecu" name="csCustomerSocialSecuVO.newSocialSecu" value="0">
				<input type="hidden" name="changeId" value="${csCustomerSocialSecuVO.changeId }"> 
				<input type="hidden" name="acceptId" value="${csCustomerSocialSecuVO.acceptId }">
				<input type="hidden" id="resetFlag" name="csCustomerSocialSecuVO.resetFlag" value="${csCustomerSocialSecuVO.resetFlag}">
				<input type="hidden" id="multiInsuredFlag" value="${multiInsuredFlag }"> 
			</form>
				<table class="list" id="csCustomerSocialSecuVOList" table_saveStatus="1">
					<thead>
						<tr>
							<th colName="customerId" inputType="input">被保险人</th>
							<th colName="socialSecu" inputType="input">当前社保状态</th>
							<th colName="newSocialSecu" inputType="select">变更后社保状态</th>
							<th colName="changeId" inputType="input" style="display: none">changeId</th>
							<th colName="acceptId" inputType="input" style="display: none">acceptId</th>
						</tr>
					</thead>
					<tbody id="csCustomerSocialSecuVOListBody">
						<s:iterator value="csCustomerSocialSecuVOList" var="sr"
							status="st1">
							<tr tr_saveStatus="1">
								<td><input type="hidden" id="customerId" name="csCustomerSocialSecuVO.customerId" value="<s:property value="customerId"/>"/>${csCustomerVO.customerName}</td>
								<td><input type="hidden" id="oldSocialSecu" value="${socialSecu}"><Field:codeValue tableName="APP___PAS__DBUSER.T_YES_NO" value="${socialSecu}"/></td>
								<td>
									<s:select list="#{0:'否', 1:'是'}" id="newSocialSecu" name="csCustomerSocialSecuVO.newSocialSecu" listKey="key"
												listValue="value" value="newSocialSecu">
									</s:select>
								</td>
								<td style="display: none"><input type="hidden" id="changeId" name="csCustomerSocialSecuVO.changeId" value="${changeId }"> </td>
								<td style="display: none"><input type="hidden" id="acceptId" name="csCustomerSocialSecuVO.acceptId" value="${acceptId }"> </td>
							</tr>
						</s:iterator>
					</tbody>
				</table>
			
			</div>
			<div  style="margin-left:10%;">
				<table >
					<tr>
						<td style="width: 120px">
							<button class="but_blue" type="button"
								onclick="updateSocialSecusList()">社保状态保存</button>
						</td>
						<td style="width: 120px">
							<button class="but_blue" type="button" onclick="resetSOList()">社保状态重置</button>
						</td>
					</tr>
				</table>
			</div>
		</div>
		<br/>
		</s:if>
		<s:else>
		<!-- 151299 end -->
	 	<div class="divfclass">
		<h1>
			<img src="cs/img/icon/tubiao.png">客户社保状态变更信息
		</h1>
			<div class="panel" style="margin: 10px">
				<h1>客户社保状态信息</h1>
				<div class="pageFormContent">
					<div style="width: 32%;float:left; margin-left:1%;">
						<dl>
							<dt>社保状态变更前</dt>
							<dd>
								<s:select list="#{0:'否', 1:'是',2:''}" id="socialSecu"
									name="csCustomerSocialSecuVO.socialSecu" disabled="true"
									listKey="key" listValue="value"
									value="csCustomerSocialSecuVO.socialSecu">
								</s:select>
							</dd>
						</dl>
					</div>
					<div style="width: 32%;float:left; margin-left:1%;">
						<form id="customerSocialSecuForm"
							action="${ctx}/cs/serviceitem_rr/saveSocialSecuInfo_PA_csEndorseRRAction.action"
							method="post" class="pageForm required-validate"
							onsubmit="return navTabSearch(this,'addSubBusiProdNew')">
							<input type="hidden" id="customerId"
								name="csCustomerSocialSecuVO.customerId"
								value="${csCustomerSocialSecuVO.customerId}"> <input
								type="hidden" id="changeId"
								name="csCustomerSocialSecuVO.changeId"
								value="${csCustomerSocialSecuVO.changeId }"> <input
								type="hidden" id="acceptId"
								name="csCustomerSocialSecuVO.acceptId"
								value="${csCustomerSocialSecuVO.acceptId }"> <input
								type="hidden" id="isSaved" name="csCustomerSocialSecuVO.isSaved"
								value="${csCustomerSocialSecuVO.isSaved }"> <input type="hidden"
								name="changeId" value="${csCustomerSocialSecuVO.changeId }"> <input type="hidden"
								name="acceptId" value="${csCustomerSocialSecuVO.acceptId }">
							<div style="width: 60%">
								<dl>
									<dt>社保状态变更后</dt>
									<dd>
										<s:select list="#{0:'否', 1:'是'}" id="newSocialSecu"
											name="csCustomerSocialSecuVO.newSocialSecu" listKey="key"
											listValue="value"
											value="csCustomerSocialSecuVO.newSocialSecu">
										</s:select>
									</dd>
								</dl>
							</div>
						</form>
					</div>
					<div  style="width: 32%;float:left; margin-left:1%;">
						<table style="width: 100%">
							<tr>
								<td style="width: 120px">
									<button class="but_blue" type="button"
										onclick="updateSocialSecus('customerSocialSecuForm','renewalBusiForm','dataTable')">社保状态保存</button>
								</td>
								<td style="width: 120px">
									<button class="but_blue" type="button" onclick="resetSO()">社保状态重置</button>
								</td>
							</tr>
						</table>
					</div>
				</div>
			</div>
		</div>	 
		<!-- 151299 start -->
		<br/>
		</s:else>
		<!-- 151299 end -->
		
	<!-- 169425：新增告知  start-->
	<form id="informInsured_CsForm" action="" class="pageForm required-validate" method="post" 
	onsubmit="return validateCallback(this,questionaireInfoRRShow);">
		<input type="hidden" name="acceptId" value="${acceptId}"/>
		<input type="hidden" name="changeId" value="${changeId}"/>
		<input type="hidden" name="customerId" value=""/>
		<div id="insuredquestionShow">
			<div class="divfclass">
					<h1><img src="cs/img/icon/tubiao.png"">被保险人健康告知</h1>
		    </div>

			<div class="tabdivclass" id="customerInsured"  >
				<table class="list" width="100%">
					<thead>
						<tr>
							<th>选择</th>
							<th>姓名</th>
							<th>证件类型</th>
							<th>证件号码</th>
							<th>操作</th>
						</tr>
					</thead>
					<tbody id="tableInfo">
						<s:iterator value="csCustomerVOs" id="insuredList" status="st" var="list">
							<tr align="center" >
							<td><input  class = "radioClass" type="radio"  class="Radio2" name="Radio2"
							value="${list.customerId}" /></td>
							<td>${customerName}</td>
							<td >${customerCertType}</td>
							<td>${customerCertiCode}</td>
							<td>
								<button type="button" class="but_blue" onclick="delRRInsured(${list.customerId})">重置被保人健康告知信息</button>
							</td>
							</tr>
						</s:iterator>
					</tbody>
				</table>
			</div>
			<div class="pageFormInfoContent">	
				<dl >
					<dt>告知类型</dt>
					<dd >
						<select id="questionaireSurveyVersion" onchange="queryRRQuestionInfoInsured()">
							<option title="请选择" value="" selected="selected">请选择</option>
							<option title="65版本健康告知" value="65">65-健康告知（UM063/2005D、UA001/2104L、UA001/2303N、UA001/2209M）</option>
						</select>
					</dd>
				</dl>							
			</div>
			
			
			<!--问题描述 开始 -->
			<div id="InsuredquestionDescId_CS" ></div>
			<!--问题描述 结束 -->
			<div>
			&nbsp;
			</div>
			<center><button type="button" class="but_blue" onclick="saveRRImForm();">保存被保人告知信息</button></center>
			<button id="hiddenInsured_New" type="button" value="0"  disabled="none" class="but_blue" style="display: none"
					onclick="InsuredquestionNewShow(this)">隐藏告知信息</button>
 
		</div>
	</form>
	
	<s:if test="csQuestionaireInfoRRVOs!=null && csQuestionaireInfoRRVOs.size > 0">
		<div class="divfclass">
			<h1><img src="cs/img/icon/tubiao.png"">本次告知信息</h1>
		</div>  
		<div id="questionaireInfoRRShow" >
			<s:iterator value="csQuestionaireInfoRRVOs" status="st" var="rrvos">
				<div>
					<div class="divfclass">
						<span>客户：&nbsp;&nbsp; <span>${customerName}</span></span>
						&nbsp;&nbsp;
						<span>告知类型：&nbsp;&nbsp; <span>${surveyVersionName}</span></span>
					</div>
					<div class="tabdivclass">
						<table class="list" width="100%">
							<thead>
								<tr>
									<th>告知编码</th>
									<th>告知内容</th>
									<th>填写内容</th>
									
								</tr>
							</thead>
							<tbody id="tableInfo">
								<s:iterator value="csQuestionaireInfoVOs" status="st" var="list">
									<tr align="center" >
										<td>${surveyCode}</td>
										<td >${questionContent}</td>
										<td>${surveyModuleResult}</td>
									</tr>
								</s:iterator>
							</tbody>
						</table>
					</div>
				</div>
			</s:iterator>
		</div>
		<br/>
	</s:if>

	<!-- 169425：新增告知  end -->		
		
		<div class="panelPageFormContent" >
		<form id="renewalBusiForm"
		action="${ctx}/cs/serviceitem_rr/saveRewalBusiProRes_PA_csEndorseRRAction.action"
		class="pageForm required-validate" method="post"
		onsubmit="return validateCallback(this,loadChangeEndData);">
		<input type="hidden" id="customerId" name="customerId" value="${customerId}"/>
		<input type="hidden" id="acceptId" name="acceptId" value="${acceptId}"/>
		<input type="hidden" id="changeId" name="changeId" value="${changeId}"/>
	    <input type="hidden" id="queryFlag" name="queryFlag" value="${queryFlag}"/>
		<input type="hidden" id="policyAndBusiIds" name="policyAndBusiIds" value=""/>
		<input type="hidden" id="yes" value="是"/>
		<input type="hidden" id="no" value="否"/>
		<!-- 保存类型  1：页面有预约信息，且保留预约信息，保存其他转换信息
		            2：页面无预约信息，保存转换信息 3：页面有预约信息,取消已经预约的信息-->
		<input type="hidden" id="saveType" name="saveType" value="${saveType}"/>
		
		<div class="divfclass">
			<h1>
				<img src="images/tubiao.png" >变更前保单险种列表
			</h1>
		</div>
			<div class="pageFormContent">
				<table id="dataTable" class="list" width="100%">
					<thead>
						<tr>
							<th>序号</th>
							<th colName="policyId" style="display: none">保单ID</th>
							<th colName="policyChgId" style="display: none">保单变更ID</th>
							<th colName="busiItemId" style="display: none">原险种ID</th>
							<th colName="busiPrdId" style="display: none">原险种所属业务ID</th>
							<th colName="policyCode">保单号</th>
							<th>投保人</th>
							<th>被保险人</th>
							<th colName="busiProdCode">险种代码</th>
							<th>险种名称</th>
							<th colName="basicAmount">保额</th>
							<th colName="totalPremAf">保费</th>
							<th>交费对应日</th>
							<th colName="revEndBusiItemCode" inputType="select" >转换后险种代码</th>
							<th>转换后险种名称</th>
							<th>是否已经预约</th>
							<th colName="safeGuardPlan" inputType="select" style="display: none">保障计划</th>
							<th colName="changeAfAmount" inputType="input" style="display: none">转换后保额</th>
							<th colName="changeAfUnit" inputType="input" style="display: none">转换后份数</th>
							<th colName="isRenew" style="display: none;" inputType="select">是否自动续保</th>
							<th colName="isGracePeriod" style="display: none">交费宽限期内</th>
							<th colName="newProductCode" inputType="select">是否包含可选责任</th>
						</tr>
					</thead>
					<tbody>
						<s:iterator value="csEndorseRrVOs" status="st" id="BFList"  var = 'it'>
							<tr align="center" tr_saveStatus="1">
								<td><s:property value="#st.index+1" /></td>
								<td style="display: none">${policyId}</td>
								<td style="display: none">${policyChgId}</td>
								<td style="display: none" name="busiItemId">${busiItemId}</td>
								<td style="display: none">${busiPrdId}</td>
								<td name="policyCode">${policyCode }</td>
								<td>${policyHolderName }</td>
								<td>${insuredName }</td>
								<td name="busiProdCode">${busiProdCode }</td>
								<td><Field:codeValue tableName="APP___PAS__DBUSER.T_BUSINESS_PRODUCT" value="${busiPrdId}"/></td>
								<td name="basicAmount">${basicAmount}</td>
								<td>${totalPremAf}</td>
								<td><s:date name="payDueDate" format="yyyy-MM-dd"></s:date></td>

								<!-- 转换后险种代码 -->
								<td align="left" name="revEndBusiItemCode">
										<select  class="combox" onChange="revBusiItemChange(this)" defaultValueOrigin="">
											<option value="">请选择</option>
											<s:iterator id="productCodeSys" value="businessProductVOs" >
												<option <s:if test=" productCodeSys eq revEndBusiItemCode">selected</s:if>>${productCodeSys}</option>
											</s:iterator>
										</select>
								</td>
								
								<td  id="productDesc">
									<s:if test="isSubscribe==1||isSaved==1" >
										${revEndBusiItemName}
									</s:if>
								</td>
								<td id="_isSubscribe">
									<div style="display: none;">${isSubscribe }</div>
									<s:if test="isSubscribe == 1">
										是
									</s:if>
									<s:if test="isSubscribe == 2">
										否
									</s:if>
								</td>
																
								<s:if test="isSubscribe==1 || isSaved==1 " >
									<td name="planIdSelect" style="display: none;">
									<span>
										<select>
											<s:iterator id="safePlan" value="safeGuardPlans" >
												<option <s:if test=" #safePlan eq safeGuardPlan">selected</s:if> >${safePlan}</option>
											</s:iterator>
										
										</select>		
									</span>
									</td>
								</s:if>
								<s:else>
									<td name="planIdSelect" style="display: none;">
										<span style="display: none;">
											<select></select>
										</span>
									</td>
								</s:else>
								<td name="changeAfAmount" style="display: none;"><span><input value=""></input>&nbsp</span></td>
								<td name="changeAfUnit" style="display: none;"><span> <input value=""></input>&nbsp</span></td>
								<td name="renewTd" style="display: none;"><span ><Field:codeTable  name="isRenew"  tableName="APP___PAS__DBUSER.T_YES_NO" defaultValue="1" value="${isRenew}"/></span></td>
								<td name="isGracePeriod" style="display: none;">${isGracePeriod }</td>
								
								<!-- 是否包含可选责任 -->
								<td name="newProductCode">
									<span>
										<select>
											<option>--</option>
										</select>
									</span>
								</td>
								
							</tr>
						</s:iterator>
					</tbody>
				</table>
				<div style="display:none">
					<dl>
						<label>投保人电子邮箱:</label><input type="text"  style="width: 180px" id="policyHolderEmail" name="policyHolderEmail" value="${csEndorseRrVO.policyHolderEmail }" />
					</dl>
					
				</div>
				<s:if test="secondPolicyHolderStr != null && secondPolicyHolderStr != ''">
					<dl>
						<dt style="font-weight: 900; color: red;width: auto;">第二投保人指定:${secondPolicyHolderStr}</dt>
						
					</dl>
				</s:if>
			</div>
				<div class="pageFormdiv">
					<button type="button" class="but_blue" onclick="saveData('renewalBusiForm','dataTable')">保存</button>
				</div>
				<div id="updateEndDiv">
				<!-- 变更后保单险种列表 -->
				<s:include value="CsEndorseRR_query.jsp"></s:include>
				</div>
	
	</form>
		<s:include value="/cs/pages/serviceitem/serviceItemBottomBar.jsp"></s:include>
	</div>
	</div>
	
	
<script type="text/javascript">
	$(document).ready(function(){
		csHelpMenu();
	});
	
	$(function(){
	  var $trs=$("#dataTable",navTab.getCurrentPanel()).find("tbody tr");
	   $trs.each(function(){
		   debugger;
		  var _busiProd=$(this).find("td[name='revEndBusiItemCode']").find("select");
		  if(_busiProd.val()!=''){
			  revBusiItemChange(_busiProd.get(0));
		  }
		});
		
	});
	
	/**
	* 统一处理th
	*/
	function dynamicLoad(){
		var isCanShow = 0;
		// 每一列只要有一个显示的 一整列都要显示
		$("#dataTable",navTab.getCurrentPanel()).find("tbody tr td[name='planIdSelect']").each(function(){
			if(!$(this).is(":hidden")){
				isCanShow = isCanShow + 1;
			}
		});
		
		if(isCanShow > 0){
			$("#dataTable",navTab.getCurrentPanel()).find("thead tr th[colName='safeGuardPlan']").show();
		}else{
			$("#dataTable",navTab.getCurrentPanel()).find("thead tr th[colName='safeGuardPlan']").hide();
		}
		
		isCanShow = 0;
		$("#dataTable",navTab.getCurrentPanel()).find("tbody tr td[name='changeAfAmount']").each(function(){
			if(!$(this).is(":hidden")){
				isCanShow = isCanShow + 1;
			}
		});
		
		console.info(isCanShow);
		if(isCanShow > 0){
			$("#dataTable",navTab.getCurrentPanel()).find("thead tr th[colName='changeAfAmount']").show();
		}else{
			$("#dataTable",navTab.getCurrentPanel()).find("thead tr th[colName='changeAfAmount']").hide();
		}
		
		isCanShow = 0;
		$("#dataTable",navTab.getCurrentPanel()).find("tbody tr td[name='changeAfUnit']").each(function(){
			if(!$(this).is(":hidden")){
				isCanShow = isCanShow + 1;
			}
		});
		
		if(isCanShow > 0){
			$("#dataTable",navTab.getCurrentPanel()).find("thead tr th[colName='changeAfUnit']").show();
		}else{
			$("#dataTable",navTab.getCurrentPanel()).find("thead tr th[colName='changeAfUnit']").hide();
		}
		
		isCanShow = 0;
		$("#dataTable",navTab.getCurrentPanel()).find("tbody tr td[name='renewTd']").each(function(){
			if(!$(this).is(":hidden")){
				isCanShow = isCanShow + 1;
			}
		});
		if(isCanShow > 0){
			$("#dataTable",navTab.getCurrentPanel()).find("thead tr th[colName='isRenew']").show();
		}else{
			$("#dataTable",navTab.getCurrentPanel()).find("thead tr th[colName='isRenew']").hide();
		}
		
	}
	
	//异步请求  转换后的险种信息
	function revBusiItemChange(obj){
// 		alert(obj);
		var $this = $(obj);
		//获取选中的值
// 		console.info($this.html());
		var optionValue = $this.find('option:selected').text();
		var basicAmountVal = $this.parents("td").parent("tr").find("td[name='basicAmount']").text();
		var busiProdCodeVal = $this.parents("td").parent("tr").find("td[name='busiProdCode']").text();
		var policyCodeVal = $this.parents("td").parent("tr").find("td[name='policyCode']").text();
		var busiItemIdVal = $this.parents("td").parent("tr").find("td[name='busiItemId']").text();
		var csEndorseRrVO={
				productCodeSys:optionValue,
				basicAmount:basicAmountVal,
				busiProdCode:busiProdCodeVal,
				policyCode:policyCodeVal,
				busiItemId:busiItemIdVal
		};
		
		
		console.info(optionValue);
		if(optionValue == '请选择'){
			$this.parents("td").parent().find('td:eq(14)').text("");
// 			$(obj).parents("td").parent("tr").find("td[name='planIdSelect']",navTab.getCurrentPanel()).find("select").html("");
			$(obj).parents("td").parent("tr").find("td[name='planIdSelect'] span",navTab.getCurrentPanel()).hide();
			
// 			if($("#dataTable",navTab.getCurrentPanel()).find("tbody tr td[name='changeAfAmount']").size() == 1){
// 				$("#dataTable",navTab.getCurrentPanel()).find("thead tr th[colName='safeGuardPlan']").hide();
// 				$("#dataTable",navTab.getCurrentPanel()).find("thead tr th[colName='changeAfAmount']").hide();
// 				$("#dataTable",navTab.getCurrentPanel()).find("thead tr th[colName='changeAfUnit']").hide();
// 			}
			$(obj).parents("td").parent("tr").find("td[name='planIdSelect']",navTab.getCurrentPanel()).hide();
			$(obj).parents("td").parent("tr").find("td[name='changeAfAmount']",navTab.getCurrentPanel()).hide();
			$(obj).parents("td").parent("tr").find("td[name='changeAfUnit']",navTab.getCurrentPanel()).hide();
			$(obj).parents("td").parent("tr").find("td[name='renewTd']",navTab.getCurrentPanel()).hide();
			dynamicLoad();
			return false;
		}
		console.info("*****************");
		//使用异步提交的方式   的到 险种代码所对应的名称
		$.ajax({
			type : "post",
			dataType : "text",
			url : "${ctx}/cs/serviceitem_rr/getRevBusiItemName_PA_csEndorseRRAction.action",
			data : 'csEndorseRrVO.productCodeSys=' + optionValue+"&csEndorseRrVO.basicAmount="+basicAmountVal
						+"&csEndorseRrVO.busiProdCode="+busiProdCodeVal+"&csEndorseRrVO.policyCode="+policyCodeVal
						+"&csEndorseRrVO.busiItemId="+busiItemIdVal,
			success : function(data) {
				var adr = jQuery.parseJSON(data);
				if(adr == ""||adr == null){
					alertMsg.error("请选择转换后的险种");
					return false;
				}
				
				var productDesc = adr[0].productDesc;
				var isRenew = adr[0].isRenew;
				console.info(productDesc);
				$this.parents("td").next("td").text(productDesc);
				var safeGuardPlansVal = adr[0].safeGuardPlans;
				console.info(safeGuardPlansVal);
				// 加载页面时 保障计划、转换后的保额、份数默认隐藏
				if(safeGuardPlansVal.length > 0){
					$("#dataTable",navTab.getCurrentPanel()).find("thead tr th[colName='safeGuardPlan']").show();
					// 除本行以外的此列需要显示
					console.info("123");
					$("#dataTable",navTab.getCurrentPanel()).find("tbody tr td[name='safeGuardPlan']",navTab.getCurrentPanel()).show();
					$(obj).parents("td").parent("tr").siblings().find("td[name='planIdSelect']").each(function(){
						$(this).show();
					});
					
					$(obj).parents("td").parent("tr").find("td[name='changeAfAmount'] span",navTab.getCurrentPanel()).hide();
					
					$(obj).parents("td").parent("tr").find("td[name='changeAfUnit'] span",navTab.getCurrentPanel()).hide();
					
					// 同行其他输入项需要显示td 但隐藏span
					if(!$(obj).parents("td").parent("tr").find("td[name='changeAfUnit']").is(":hidden")){
						$(obj).parents("td").parent("tr").find("td[name='changeAfUnit']").show();
						$(obj).parents("td").parent("tr").find("td[name='changeAfUnit'] span",navTab.getCurrentPanel()).hide();
						
					}
					if(!$(obj).parents("td").parent("tr").find("td[name='changeAfAmount'] ").is(":hidden")){
						$(obj).parents("td").parent("tr").find("td[name='changeAfAmount'] ").show();
						$(obj).parents("td").parent("tr").find("td[name='changeAfAmount'] span",navTab.getCurrentPanel()).hide();
						
					}else{
						$(obj).parents("td").parent("tr").find("td[name='changeAfAmount'] ").show();
					}

					$(obj).parents("td").parent("tr").find("td[name='planIdSelect'] ",navTab.getCurrentPanel()).show();
					$(obj).parents("td").parent("tr").find("td[name='planIdSelect'] span",navTab.getCurrentPanel()).show();
					console.info($(obj).parents("td").parent("tr").find("td[name='planIdSelect'] span",navTab.getCurrentPanel()).html());
					// 清空上次的值
					$(obj).parents("td").parent("tr").find("td[name='planIdSelect']",navTab.getCurrentPanel()).find("select").find("option").remove();
					
					$.each(safeGuardPlansVal,function(i,value){
					$(obj).parents("td").parent("tr").find("td[name='planIdSelect'] span",navTab.getCurrentPanel()).find("select")
						 	.append("<option value='" + safeGuardPlansVal[i].code+"'  code_value='"+safeGuardPlansVal[i].code+"'>" + safeGuardPlansVal[i].value + "</option>");
					});

				}else{
					//  有保障计划的不现实 转换后的保额、份数2列
					//  无保障计划(隐藏保障计划一列)的若是按保额卖的则显示 转换后的保额input 份数买的则则显示转换后份数input
					var countWay = adr[0].countWay;
					var isRenew = adr[0].isRenew;
					console.info("countWay :"+countWay);
					console.info("isRenew :"+isRenew);
					// th 显示则td 也要有
					$(obj).parents("td").parent("tr").find("td[name='planIdSelect']",navTab.getCurrentPanel()).find("select").find("option").remove();
					$(obj).parents("td").parent("tr").find("td[name='planIdSelect'] span",navTab.getCurrentPanel()).hide();
					
					if(countWay == '1'){
						$("#dataTable",navTab.getCurrentPanel()).find("thead tr th[colName='changeAfAmount']").show();
						$(obj).parents("td").parent("tr").find("td[name='changeAfAmount']",navTab.getCurrentPanel()).show();
						$(obj).parents("td").parent("tr").find("td[name='changeAfAmount'] span",navTab.getCurrentPanel()).show();
						
						$(obj).parents("td").parent("tr").find("td[name='newCodeTd']",navTab.getCurrentPanel()).show();
						
						// 其他列tr 中 planIdSelect span 是否显示
						var planIdSelectShow = false;
						$(obj).parents("td").parent("tr").siblings().find("td[name='planIdSelect']").filter(function(index) {
						   if(!$(this).is(":hidden")){
							   	planIdSelectShow = true;
								return $(this);
						   }
						});
						if(planIdSelectShow){
							$("#dataTable",navTab.getCurrentPanel()).find("thead tr th[colName='safeGuardPlan']").show();
							$(obj).parents("td").parent("tr").find("td[name='planIdSelect']").show();
						}
					}else{
						// th 显示则td 也要有
						if(!$("#dataTable",navTab.getCurrentPanel()).find("thead tr th[colName='changeAfAmount']").is(":hidden")){
							$(obj).parents("td").parent("tr").find("td[name='changeAfAmount'] span",navTab.getCurrentPanel()).hide();
							
							
						} 
						// 重复性操作，处理保障计划节点的显示，不添加此处代码重复操作，会往前进一列，数据对错列
						if(!$("#dataTable",navTab.getCurrentPanel()).find("thead tr th[colName='safeGuardPlan']").is(":hidden")){	
							var planIdSelectShow = false;
							$(obj).parents("td").parent("tr").siblings().find("td[name='planIdSelect']").filter(function(index) {
							   if(!$(this).is(":hidden")){
								   	planIdSelectShow = true;
									return $(this);
							   }
							});
							if(planIdSelectShow){
								$("#dataTable",navTab.getCurrentPanel()).find("thead tr th[colName='safeGuardPlan']").show();
								$(obj).parents("td").parent("tr").find("td[name='planIdSelect']").show();
							}							
						}
						
						$("#dataTable",navTab.getCurrentPanel()).find("thead tr th[colName='changeAfUnit']").show();
						$(obj).parents("td").parent("tr").find("td[name='changeAfUnit']",navTab.getCurrentPanel()).show();
						//捕获兄弟节点的隐藏子节点，然后填充空白值（后续增加隐藏列需要在此处增加设置，后续增加兄弟节点，得增加兄弟节点的for循环，从循环中捕捉对应子节点）
						$(obj).parents("td").parent("tr").siblings().find("td[name='changeAfUnit']",navTab.getCurrentPanel()).show();
						$(obj).parents("td").parent("tr").siblings().find("td[name='changeAfUnit'] span",navTab.getCurrentPanel()).hide();
						$(obj).parents("td").parent("tr").siblings().find("td[name='renewTd']",navTab.getCurrentPanel()).show();
						$(obj).parents("td").parent("tr").siblings().find("td select[name='isRenew'] span",navTab.getCurrentPanel()).hide();
							}
				}
				
				//169524：当选择的转换后险种代码为 984时，下拉框为是或否，否则为空
				if(optionValue!="00984000"){
					$(obj).parents("td").parent("tr").find("td[name='newProductCode']",navTab.getCurrentPanel()).find("select").find("option").remove();
					$(obj).parents("td").parent("tr").find("td[name='newProductCode'] span",navTab.getCurrentPanel()).find("select")
					.append("<option value='' code_value=''>--</option>");
				}else{
					$(obj).parents("td").parent("tr").find("td[name='newProductCode']",navTab.getCurrentPanel()).find("select").find("option").remove();
					$(obj).parents("td").parent("tr").find("td[name='newProductCode'] span",navTab.getCurrentPanel()).find("select")
					.append("<option value='' code_value=''>请选择</option>");
					$(obj).parents("td").parent("tr").find("td[name='newProductCode'] span",navTab.getCurrentPanel()).find("select")
					.append("<option value='984000' code_value='984000'>否</option>");
					$(obj).parents("td").parent("tr").find("td[name='newProductCode'] span",navTab.getCurrentPanel()).find("select")
					.append("<option value='984001' code_value='984001'>是</option>");
				}
				
				if(isRenew != '0'){
					// 显示th
					$("#dataTable",navTab.getCurrentPanel()).find("thead tr th[colName='isRenew']").show();
					// 将对应的td 显示
					$(obj).parents("td").parent("tr").find("td[name='renewTd']",navTab.getCurrentPanel()).show();
					$(obj).parents("td").parent("tr").find("td select[name='isRenew'] span",navTab.getCurrentPanel()).show();
					
				}
				
				dynamicLoad();
			}
		});
	}
	
	function isCanSaveSaveGurPaln(tableId){
		
	}
	
	function checkInputContext(tableId){
		console.info(" tableId  "+tableId);
		var $dataTableTrs = $("#"+tableId,navTab.getCurrentPanel()).find("tbody").find("tr");
		// 1 .当所有转换后险种对应的 保额/份数/保障计划 都未录入 则阻断！ "信息未录入！！"
		// 2 .只转换部分险种，则此险种的保额/份数/保障计划 必录一项 否则阻断！！ "转换后险种的XXX 未录入！"
		var size = $dataTableTrs.size();
		var changeBusiCount = 0;
		var alertInfo = "";
		$dataTableTrs.each(function(){			
			console.info($(this).find("td[name='revEndBusiItemCode']").find("option:selected").val())
			// 查td显示的
			var $tds =$(this).find("td[name='changeAfAmount'],[name='changeAfUnit'],[name='planIdSelect']").filter(function(index) {
			   if(!$(this).is(":hidden")){
					return $(this);
			   }
			});
			console.info(" tds  "+$tds.size());
			$tds.each(function(){
// 				alert($(this).html());
				var name = $(this).attr("name");
				console.info(name);
				switch(name){
					case "changeAfAmount":
						console.info("  *span input*  "+("" ==$(this).find("span input").val()));
						console.info("  *input hidden ? *  "+($(this).find("span input").is(":hidden")));
						console.info("  *span hidden ? *  "+($(this).find("span").is(":hidden")));
						console.info("  *td hidden ? *  "+($(this).is(":hidden")));
						if(!$(this).find("span").is(":hidden") && 
								("" ==$(this).find("span input").val() || null == $(this).find("span input").val())){
							alertInfo = "保额信息未录入,请确认！！！";
						}
						break;
					case "changeAfUnit":
						if(!$(this).find("span").is(":hidden")  && "" ==$(this).find("span input").val()){
							alertInfo = "份数信息未录入,请确认！！！";
							return;
						}
						break;
					case "planIdSelect":
						if(!$(this).find("span").is(":hidden")  && "" ==$(this).find("span select option:selected").attr("code_value")){
							alertInfo = "保障计划信息未录入,请确认！！！";
							return;
						}
						break;
				}
			});
			
		});
		console.info("alertInfo   "+alertInfo);
		console.info(null != alertInfo && "" != alertInfo);
		if(null != alertInfo && "" != alertInfo){
			alertMsg.error(alertInfo);
			return false;
		}
		return true;
	}
	
	//续保险种转换
	function saveData(formId,tableId){
		debugger;
		var isCan = true;
		var $dataTableTrs = $("#"+tableId,navTab.getCurrentPanel()).find("tbody").find("tr");
		// 输入内容校验
		console.info("--------------输入内容校验----------------");
		isCan = checkInputContext(tableId);
		if(!isCan){
			return false;
		}
		
		var inputInfo = 0;
		$(this).find("td[name='changeAfAmount'] span,[name='changeAfUnit'] span,[name='planIdSelect'] span").filter(function(index) {
			   if(!$(this).is(":hidden")){
				   inputInfo = inputInfo + 1;
					return $(this);
			   }
			});
		if(inputInfo > 0){
			alertMsg.info("请确认转换的保额/保费/保障计划,谢谢！");
			return false;
		}
		
		var selectCount = 0;
		$dataTableTrs.each(function(){
// 			planIdSelect	revEndBusiItemCode
			var basicAmountVal = $(this).find("td[name='basicAmount']").text();
			var changeAfPro = $(this).find("td[name='revEndBusiItemCode']").find("option:selected").val();
// 			var amountVal = $(this).find("td[name='planIdSelect'] span").find("option:selected").attr("code_value");
			var countWayAmount = $(this).find("td[name='changeAfAmount'] span");
			var amountVal;
			$(this).find("td[name='changeAfAmount'] span","td[name='changeAfUnit'] span").filter(function(){
// 				alert($(this).parent().html());
				if(!$(this).parent().is(":hidden")){
					var name = $(this).parent("td").attr("name");
					switch(name){
						case "changeAfAmount":
							amountVal = $(this).find("input").val();
							return ;
							break;
						case "changeAfUnit":
							amountVal = $(this).find("input").val();
							return $(this).find("input").val();
							break;
						case "planIdSelect":
							amountVal = $(this).find("select option:selected").attr("code_value");
							return $(this).find("select option:selected").attr("code_value");
							break;
					}
			   }
			});
			
			console.info( "原始保额   ："+basicAmountVal +"    "+ "新保额  ："+amountVal);
			
			if("" == changeAfPro){
				// 未选择转换后的险种
				selectCount += 1;
			}
		});
		console.info("--------------1----------------");
		console.log("iscan = " + isCan);
		if(!isCan){
			return false;
		}
		
		if(selectCount == $dataTableTrs.size()){
			alertMsg.error("请确认转换信息,谢谢！");
			return false;
		}
		
		// RM94529+关于短期健康险新规应对功能需求（12）续保险种转换增加邮箱录入相关功能  
		var tbrEmail=$("#policyHolderEmail",navTab.getCurrentPanel()).val();
		var reg =/^([a-zA-Z0-9]+[_|\_|\.]?)*[a-zA-Z0-9]+@([a-zA-Z0-9]+[_|\_|\.]?)*[a-zA-Z0-9]+\.[a-zA-Z]{2,3}$/;
		if(tbrEmail!=null && tbrEmail!=""){
			//!reg.test(tbrEmail)
			if(false){
				alertMsg.info("邮箱录入不符合校验规则，请核实");
				return false;
			}
		}
		
		
		var $dataTable = $("#"+tableId,navTab.getCurrentPanel());
		var $formId = $("#"+formId,navTab.getCurrentPanel());
		var _jsons = "";
		var $policyAndBusiIds = $("#policyAndBusiIds",navTab.getCurrentPanel());
		_jsons += _cs_tableToJson($dataTable);
		$policyAndBusiIds.val(_jsons);
		console.info(_jsons);
		var saveType = $("#saveType",navTab.getCurrentPanel());
		var productDesc = $("#productCodeSys",navTab.getCurrentPanel()).val();
		/* if(productDesc == ""||productDesc == null){
			alertMsg.error("请选择转换后的险种");
			return false;
		} */
		//判断页面中预约信息，是否保存
		var flagYes = false;
		var flagNo = false;
		var testTr =  $dataTable.find("tr");
		for(var a = 1; a<testTr.length; a++){
			var testOneTr = $(testTr[a],navTab.getCurrentPanel());
			var oneTrName = testOneTr.find("td[id='_isSubscribe']").find("div").text();
			console.info("oneTrName"+oneTrName);
			if(oneTrName == 1){
				flagYes = true;
			}
			if(oneTrName == 2){
				flagNo = true;
			}
		}
		console.info("--------------2----------------");
		if(flagYes == true){
			//页面存在预约信息
			alertMsg.confirm("转换前的险种存在已经预约的续保险种，是否继续？", {
				okCall: function(){
					//取消已经预约的信息   去后台保存信息
					var $form = $("#renewalBusiForm",navTab.getCurrentPanel());
					$.ajax({
						type : "POST",
						url : "${ctx}/cs/serviceitem_rr/saveRewalBusiProRes_PA_csEndorseRRAction.action",
						data : $form.serialize(),
						success : function(rep) {
							var json = DWZ.jsonEval(rep);
								
								if(json.statusCode === "300"){
									alertMsg.error(json.message);
								}else{
									$.ajax({
										type : "POST",
										url : "${ctx}/cs/serviceitem_rr/loadBeforRevorse_PA_csEndorseRRAction.action?saveType=3&&policyAndBusiIds="
												+_jsons
												+"&&changeId="+$("#changeId",navTab.getCurrentPanel()).val()
												+"&&acceptId="+$("#acceptId",navTab.getCurrentPanel()).val()
												+"&&customerId="+$("#customerId",navTab.getCurrentPanel()).val(),
										success : function(rep) {
											/* var json = DWZ.jsonEval(rep);
											if(json.statusCode === "300"){
												alertMsg.error(json.message);
											}else{
												loadBeforRevorse
											} */
											
											//$("#updateEndDiv").remove();
											$("#updateEndDiv").html(rep).initUI();
										}
									});	
								}
								 
							}
					}); 
					//$formId.submit();
				},
				cancelCall:function(){
					//不取消已经预约的信息  ，提示是否保存没有预约的信息
					/* if(flagNo == true){
						alertMsg.confirm("是否保存除预约信息以外的转换信息？", {
							okCall: function(){
								//保存转换信息
								saveType.val("1");
								$formId.submit();
							},
							cancelCall:function(){
								//不保存转换信息
								return false;
							}
						});
					}else{ */
						return false;
					/* } */
				}
			});
		}else{
			
			/* var action = "${ctx}/cs/serviceitem_rr/saveRewalBusiProRes_PA_csEndorseRRAction.action";
			var onsubmit="return saveCallBack(this,reusltAjaxDone)";
			saveType.val("2");
			$form.attr("onsubmit",onsubmit);
			$formId.attr("action",action);
			$formId.submit(); */
			saveType.val("2");
			$.ajax({
				type : "POST",
				url : "${ctx}/cs/serviceitem_rr/saveRewalBusiProRes_PA_csEndorseRRAction.action?saveType=2&&policyAndBusiIds="
						+_jsons
						+"&&changeId="+$("#changeId",navTab.getCurrentPanel()).val()
						+"&&acceptId="+$("#acceptId",navTab.getCurrentPanel()).val()
						+"&&customerId="+$("#customerId",navTab.getCurrentPanel()).val()
						+"&&policyHolderEmail="+$("#policyHolderEmail",navTab.getCurrentPanel()).val(),
				success : function(rep) {
					var json = DWZ.jsonEval(rep);
					if(json.statusCode === "300"){
						alertMsg.error(json.message);
					}else{
						if('操作成功' != json.message){
							alertMsg.warn(json.message);
						}else{
							alertMsg.correct(json.message);
						}
						$.ajax({
							type : "POST",
							url : "${ctx}/cs/serviceitem_rr/loadBeforRevorse_PA_csEndorseRRAction.action?saveType=2&&policyAndBusiIds="
									+_jsons
									+"&&changeId="+$("#changeId",navTab.getCurrentPanel()).val()
									+"&&acceptId="+$("#acceptId",navTab.getCurrentPanel()).val()
									+"&&customerId="+$("#customerId",navTab.getCurrentPanel()).val(),
							success : function(rep) {
								/* var json = DWZ.jsonEval(rep);
								if(json.statusCode === "300"){
									alertMsg.error(json.message);
								}else{
									loadBeforRevorse
								} */
								
								//$("#updateEndDiv").remove();
								$("#updateEndDiv").html(rep).initUI();
							}
						}); 
					}
				}
			}); 
			
		}
		
		
		//************************************
// 		$formId.submit();
	}
	
	function saveCallBack(obj,resultObj){
		alert("saveCallBack()");
	}
	//上一步
	function upToCsEntry(){
		alertMsg.confirm("请确认是否需要保存变更的信息？",{
			okCall:function(){
				//保存变更信息---返回保全录入主页面
				var onsubmit="return validateCallback(this,reusltAjaxDone)";	
				$("#mainProdRenewalForm",navTab.getCurrentPanel()).attr("onsubmit",onsubmit);
				var action = "${ctx}/cs/serviceitem_mr/mainProdRenewal_PA_csEndorseMRAction.action?flag=2";
				$("#mainProdRenewalForm",navTab.getCurrentPanel()).attr("action",action);
				mainProdRenewal();
			},
			cancelCall:function(){
				$("#gotoCsEntry").click();
			}
		});
	}
	//下一步
	function nextStep(){
		var _jsons = "";
		var $policyAndBusiIds = $("#policyAndBusiIds",navTab.getCurrentPanel());
		var $dataTable = $("#dataTable",navTab.getCurrentPanel());
		_jsons += _cs_tableToJson($dataTable);
		$policyAndBusiIds.val(_jsons);
		var saveType = $("#saveType",navTab.getCurrentPanel());
		var $form = $("#renewalBusiForm",navTab.getCurrentPanel());
		var onsubmit="return validateCallback(this,reusltAjaxDone)";	
		var action = "${ctx}/cs/serviceitem_rr/saveRewalBusiProRes_PA_csEndorseRRAction.action";
		$form.attr("onsubmit",onsubmit);
		$form.attr("action",action);
		saveType.val("2");
		$form.submit();
	}
	//回调函数，跳转页面
	function reusltAjaxDone(json){
		DWZ.ajaxDone(json);
		if (json.statusCode == DWZ.statusCode.ok){
			$("#gotoCsEntry").click();
		}else{
			alertMsg.error("系统异常!");
		}
	}
	//续保装换信息变更后，重新加载数据
	function reLoadData(json){
		DWZ.ajaxDone(json);
		if(json.statusCode == DWZ.statusCode.ok){
			var onsubmit="return navTabSearch(this)";	
			var $form = $("#renewalBusiForm",navTab.getCurrentPanel());
			$form.attr("onsubmit",onsubmit);
			var action = "${ctx}/cs/serviceitem_rr/loadRRPage_PA_csEndorseRRAction.action";
			$form.attr("action",action);
			$form.submit();
		}
	}
	//加载变更后的数据
	function loadChangeEndData(json){
		DWZ.ajaxDone(json);
		if(json.statusCode == DWZ.statusCode.ok){
			var onsubmit="return navTabSearch(this,'updateEndDiv')";
			$("#updateEndDiv",navTab.getCurrentPanel()).show();
			var $form = $("#renewalBusiForm",navTab.getCurrentPanel());
			$form.attr("onsubmit",onsubmit);
			var action = "${ctx}/cs/serviceitem_rr/loadBeforRevorse_PA_csEndorseRRAction.action";
			$form.attr("action",action);
			$form.submit();
		}
	}
	//下一步
	function next() {
		var val1 = $("#changeId").val();
		var val2 = $("#acceptId").val();
		var val3 = $("#customerId").val();
		var title = "受理信息录入";
		var tabid = "_aplPermit_M";
		var fresh = eval("true");
		var external = eval("false");
		var url = "${ctx}/cs/csEntry/showAcceptAndArapInfo_PA_csEntryAction.action?&menuId=${menuId}&changeId="
				+ val1 + "&acceptId=" + val2 + "&customerId=" + val3;
		navTab.openTab(tabid, url, {
			title : title,
			fresh : fresh,
			external : external
		});

	}


</script>
<script type="text/javascript">
/** 保存该保单的社保状态变更信息  */
// 险种信息录入区域--保存按钮--保存录入信息
function updateSocialSecus(formId,saveFormId,saveTableId){
	
	var _changeId=$("#changeId", navTab.getCurrentPanel()).val();
	var _newSocialSecu = $("#newSocialSecu",navTab.getCurrentPanel()).val();
	var _acceptId = $("#acceptId",navTab.getCurrentPanel()).val();
	var _customerId=$("#customerId",navTab.getCurrentPanel()).val();
	var _flag = "1";
	var saveFlag = "update";
	var _socialSecu = $("#socialSecu",navTab.getCurrentPanel()).val();
			 if(_newSocialSecu == _socialSecu){
				 alertMsg.confirm("变更后的社保状态与原社保状态相同，请确认最新社保状态。",{
					 okCall: function(){
						//去变更社保状态
						saveSocialSecuNew(formId,saveFormId,saveTableId);
					 },
					 cancelCall: function(){
					 }
				 });
			 }else{
				//去变更社保状态
				saveSocialSecuNew(formId,saveFormId,saveTableId);
			 };
		
		
} 
//151299 start
function updateSocialSecusList(formId,saveFormId,saveTableId){
	var tip = true;
	var sameCustomers = $("#csCustomerSocialSecuVOListBody").find("select[id='newSocialSecu']");
	sameCustomers.each(function(){
		if($(this).val() != $(this).closest("tr").find("input#oldSocialSecu").val()){
			tip = false;
		}
	});
	if(tip){
		alertMsg.confirm("变更后的社保状态与原社保状态相同，请确认最新社保状态。",{
			 okCall: function(){
				//去变更社保状态
				saveSocialSecuNewList(formId,saveFormId,saveTableId);
			 },
			 cancelCall: function(){
			 }
		 });
	}else{
		//去变更社保状态
		saveSocialSecuNewList(formId,saveFormId,saveTableId);
	}
}
function saveSocialSecuNewList(formId,saveFormId,saveTableId){
	$form=$("#" + formId, navTab.getCurrentPanel());
	var $jsons = $("#csCustomerSocialSecuVOList",navTab.getCurrentPanel());
	var _jsons = _cs_tableToJson($jsons);
   	_jsons = eval('('+ _jsons + ')');
   	_jsons = JSON.stringify(_jsons);
	var sendData = "jsonSocialSecuVOList="+ _jsons;
	$.post("${ctx}/cs/serviceitem_rr/saveSocialSecuInfoList_PA_csEndorseRRAction.action",
			sendData,
			function(data){
				var json = DWZ.jsonEval(data);
				if (json.statusCode == DWZ.statusCode.error) {
					if (json.message && alertMsg){
						alertMsg.error(json.message);
					}
				} else{
					//判断页面中预约信息，是否保存
					var flagYes = false;
					var $dataTable = $("#"+saveTableId,navTab.getCurrentPanel());
					var testTr =  $dataTable.find("tr");
					for(var a = 1; a<testTr.length; a++){
						var testOneTr = $(testTr[a],navTab.getCurrentPanel());
						var oneTrName = testOneTr.find("td[id='_isSubscribe']").find("div").text();
						console.info("oneTrName"+oneTrName);
						if(oneTrName == 1){
							saveSocialData(saveFormId,saveTableId);
						}
					}
					/* $form.submit(); */
					alertMsg.correct("保存成功！");	
			}
		});
}
function resetSOList(){
	alertMsg.confirm("请确认是否需要重置社保状态信息？",{
		okCall:function(){
			var sameCustomers = $("#csCustomerSocialSecuVOListBody").find("select[id='newSocialSecu']");
			sameCustomers.each(function(){
				$(this).val($(this).closest("tr").find("input#oldSocialSecu").val());
			});
			var $jsons = $("#csCustomerSocialSecuVOList",navTab.getCurrentPanel());
			var _jsons = _cs_tableToJson($jsons);
		   	_jsons = eval('('+ _jsons + ')');
		   	_jsons = JSON.stringify(_jsons);
			var sendData = "jsonSocialSecuVOList="+ _jsons;
			$.post("${ctx}/cs/serviceitem_rr/saveSocialSecuInfoList_PA_csEndorseRRAction.action",
				sendData,
				function(data){
				if (json.statusCode == DWZ.statusCode.error) {
					
				} else{
						
				}
			});
		},
	    cancelCall:function(){
	    	
	    }
		
	}); 	
} 
//151299 end

function saveSocialSecuNew(formId,saveFormId,saveTableId){
	$form=$("#" + formId, navTab.getCurrentPanel());
//	$("#"+boxId,navTab.getCurrentPanel()).show();
	 // 开始非阻断校验 
//	debugger;
	$.post("${ctx}/cs/serviceitem_rr/saveSocialSecuInfo_PA_csEndorseRRAction.action",
			$("#customerSocialSecuForm").serialize(),
			function(data){
			var json = DWZ.jsonEval(data);
			if (json.statusCode == DWZ.statusCode.error) {
				if (json.message && alertMsg){
						alertMsg.error(json.message);
					}
				} else{
					//判断页面中预约信息，是否保存
					var flagYes = false;
					var $dataTable = $("#"+saveTableId,navTab.getCurrentPanel());
					var testTr =  $dataTable.find("tr");
					for(var a = 1; a<testTr.length; a++){
						var testOneTr = $(testTr[a],navTab.getCurrentPanel());
						var oneTrName = testOneTr.find("td[id='_isSubscribe']").find("div").text();
						console.info("oneTrName"+oneTrName);
						if(oneTrName == 1){
							saveSocialData(saveFormId,saveTableId);
						}
					}
					$form.submit();
					alertMsg.correct("保存成功！");	
			}
	})
}
function resetSO(){
	alertMsg.confirm("请确认是否需要重置社保状态信息？",{
		okCall:function(){
			var _socialSecu = $("#socialSecu", navTab.getCurrentPanel()).val();//变更前状态
			$("#newSocialSecu", navTab.getCurrentPanel()).val(_socialSecu);//设置成变更前状态
				 $("#customerSocialSecuForm",navTab.getCurrentPanel()).submit();
		},
	    cancelCall:function(){
	    	
	    }
		
	}); 	
} 
//续保险种转换
function saveSocialData(formId,tableId){
	debugger;
	var isCan = true;
	var $dataTableTrs = $("#"+tableId,navTab.getCurrentPanel()).find("tbody").find("tr");
	// 输入内容校验
	console.info("--------------输入内容校验----------------");
	isCan = checkInputContext(tableId);
	if(!isCan){
		return false;
	}
	
	var inputInfo = 0;
	$(this).find("td[name='changeAfAmount'] span,[name='changeAfUnit'] span,[name='planIdSelect'] span").filter(function(index) {
		   if(!$(this).is(":hidden")){
			   inputInfo = inputInfo + 1;
				return $(this);
		   }
		});
	if(inputInfo > 0){
		alertMsg.info("请确认转换的保额/保费/保障计划,谢谢！");
		return false;
	}
	
	var selectCount = 0;
	$dataTableTrs.each(function(){
//			planIdSelect	revEndBusiItemCode
		var basicAmountVal = $(this).find("td[name='basicAmount']").text();
		var changeAfPro = $(this).find("td[name='revEndBusiItemCode']").find("option:selected").val();
//			var amountVal = $(this).find("td[name='planIdSelect'] span").find("option:selected").attr("code_value");
		var countWayAmount = $(this).find("td[name='changeAfAmount'] span");
		var amountVal;
		$(this).find("td[name='changeAfAmount'] span","td[name='changeAfUnit'] span").filter(function(){
//				alert($(this).parent().html());
			if(!$(this).parent().is(":hidden")){
				var name = $(this).parent("td").attr("name");
				switch(name){
					case "changeAfAmount":
						amountVal = $(this).find("input").val();
						return ;
						break;
					case "changeAfUnit":
						amountVal = $(this).find("input").val();
						return $(this).find("input").val();
						break;
					case "planIdSelect":
						amountVal = $(this).find("select option:selected").attr("code_value");
						return $(this).find("select option:selected").attr("code_value");
						break;
				}
		   }
		});
		
		console.info( "原始保额   ："+basicAmountVal +"    "+ "新保额  ："+amountVal);
		
		if("" == changeAfPro){
			// 未选择转换后的险种
			selectCount += 1;
		}
	});
	console.info("--------------1----------------");
	console.log("iscan = " + isCan);
	if(!isCan){
		return false;
	}
	
	if(selectCount == $dataTableTrs.size()){
		alertMsg.error("请确认转换信息,谢谢！");
		return false;
	}
	
	
	var $dataTable = $("#"+tableId,navTab.getCurrentPanel());
	var $formId = $("#"+formId,navTab.getCurrentPanel());
	var _jsons = "";
	var $policyAndBusiIds = $("#policyAndBusiIds",navTab.getCurrentPanel());
	_jsons += _cs_tableToJson($dataTable);
	$policyAndBusiIds.val(_jsons);
	console.info(_jsons);
	var saveType = $("#saveType",navTab.getCurrentPanel());
	var productDesc = $("#productCodeSys",navTab.getCurrentPanel()).val();
	/* if(productDesc == ""||productDesc == null){
		alertMsg.error("请选择转换后的险种");
		return false;
	} */
	//判断页面中预约信息，是否保存
	var flagYes = false;
	var flagNo = false;
	var testTr =  $dataTable.find("tr");
	for(var a = 1; a<testTr.length; a++){
		var testOneTr = $(testTr[a],navTab.getCurrentPanel());
		var oneTrName = testOneTr.find("td[id='_isSubscribe']").find("div").text();
		console.info("oneTrName"+oneTrName);
		if(oneTrName == 1){
			flagYes = true;
		}
		if(oneTrName == 2){
			flagNo = true;
		}
	}
	console.info("--------------2----------------");
	if(flagYes == true){
		//页面存在预约信息
//		alertMsg.confirm("转换前的险种存在已经预约的续保险种，是否继续？", {
//			okCall: function(){
				//取消已经预约的信息   去后台保存信息
				var $form = $("#renewalBusiForm",navTab.getCurrentPanel());
				$.ajax({
					type : "POST",
					url : "${ctx}/cs/serviceitem_rr/saveRewalBusiProRes_PA_csEndorseRRAction.action",
					data : $form.serialize(),
					success : function(rep) {
						var json = DWZ.jsonEval(rep);
							
							if(json.statusCode === "300"){
								alertMsg.error(json.message);
							}else{
								$.ajax({
									type : "POST",
									url : "${ctx}/cs/serviceitem_rr/loadBeforRevorse_PA_csEndorseRRAction.action?saveType=3&&policyAndBusiIds="
											+_jsons
											+"&&changeId="+$("#changeId",navTab.getCurrentPanel()).val()
											+"&&acceptId="+$("#acceptId",navTab.getCurrentPanel()).val()
											+"&&customerId="+$("#customerId",navTab.getCurrentPanel()).val(),
									success : function(rep) {
										/* var json = DWZ.jsonEval(rep);
										if(json.statusCode === "300"){
											alertMsg.error(json.message);
										}else{
											loadBeforRevorse
										} */
										
										//$("#updateEndDiv").remove();
										$("#updateEndDiv").html(rep).initUI();
									}
								});	
							}
							 
						}
				}); 
				//$formId.submit();
//			},
//			cancelCall:function(){
				//不取消已经预约的信息  ，提示是否保存没有预约的信息
				/* if(flagNo == true){
					alertMsg.confirm("是否保存除预约信息以外的转换信息？", {
						okCall: function(){
							//保存转换信息
							saveType.val("1");
							$formId.submit();
						},
						cancelCall:function(){
							//不保存转换信息
							return false;
						}
					});
				}else{ */
//					return false;
				/* } */
//			}
//		});
	}else{
		
		/* var action = "${ctx}/cs/serviceitem_rr/saveRewalBusiProRes_PA_csEndorseRRAction.action";
		var onsubmit="return saveCallBack(this,reusltAjaxDone)";
		saveType.val("2");
		$form.attr("onsubmit",onsubmit);
		$formId.attr("action",action);
		$formId.submit(); */
		saveType.val("2");
		$.ajax({
			type : "POST",
			url : "${ctx}/cs/serviceitem_rr/saveRewalBusiProRes_PA_csEndorseRRAction.action?saveType=2&&policyAndBusiIds="
					+_jsons
					+"&&changeId="+$("#changeId",navTab.getCurrentPanel()).val()
					+"&&acceptId="+$("#acceptId",navTab.getCurrentPanel()).val()
					+"&&customerId="+$("#customerId",navTab.getCurrentPanel()).val(),
			success : function(rep) {
				var json = DWZ.jsonEval(rep);
				if(json.statusCode === "300"){
					alertMsg.error(json.message);
				}else{
					if('操作成功' != json.message){
						alertMsg.warn(json.message);
					}else{
						alertMsg.correct(json.message);
					}
					$.ajax({
						type : "POST",
						url : "${ctx}/cs/serviceitem_rr/loadBeforRevorse_PA_csEndorseRRAction.action?saveType=2&&policyAndBusiIds="
								+_jsons
								+"&&changeId="+$("#changeId",navTab.getCurrentPanel()).val()
								+"&&acceptId="+$("#acceptId",navTab.getCurrentPanel()).val()
								+"&&customerId="+$("#customerId",navTab.getCurrentPanel()).val(),
						success : function(rep) {
							/* var json = DWZ.jsonEval(rep);
							if(json.statusCode === "300"){
								alertMsg.error(json.message);
							}else{
								loadBeforRevorse
							} */
							
							//$("#updateEndDiv").remove();
							$("#updateEndDiv").html(rep).initUI();
						}
					}); 
				}
			}
		}); 
		
	}
	
	
	//************************************
//		$formId.submit();
}

//告知---------------start-----------------
/*查询告知*/
function queryRRQuestionInfoInsured(){
	var customerId = $('#tableInfo input[name="Radio2"]:checked').val();
	var surveyVersionCode = $("#questionaireSurveyVersion").find("option:selected").val();
	if(customerId==null||customerId==''){
		alertMsg.info("请选择填写告知的客户！");
		return false;
	}
	if(surveyVersionCode==null||surveyVersionCode==''){
		alertMsg.info("请选择[告知类型]");
		return false;
	}
	$.ajax({
		type : "post",
		dataType : "html",
		url : "${ctx }" + "/cs/common/questionaireInfo_PA_csNotificAction.action",
		data : "csQuestionaireInfoVO.surveyVersionCode="
			+ surveyVersionCode+"&csQuestionaireInfoVO.customerId="+customerId,
		cache : false,
		success : function(data) {
			$("#InsuredquestionDescId_CS").html(data).initUI();
		}
	});
	
	$('#hiddenInsured_New').removeAttr("disabled");
}

/*重置告知按钮*/
function delRRInsured(customerIdCode){
	var acceptId = $("#informInsured_CsForm input[name='acceptId']").val();
	var customerId = customerIdCode;
	var questionaireObject = "2";//2表示为被保人
	$.ajax({
		type : "post",
		dataType : "html",
		url : "${ctx }" + "/cs/common/del_PA_csNotificAction.action",
		data : {
			"acceptId":acceptId,
			"customerId":customerId,
			"questionaireObject":questionaireObject
			
		},
		cache : false,
		success : function(data) {
			navTab.reload();//刷新
		}
	});
}

//保存告知
function saveRRImForm(){
	var customerId = $('#tableInfo input[name="Radio2"]:checked').val();
	var surveyVersionCode = $("#questionaireSurveyVersion").find("option:selected").val();
	$("#informInsured_CsForm input[name='customerId']").val(customerId);//将选中的客户号赋值给指定的对象，提交保存时使用
	
	if(customerId==null||customerId==''){
		alertMsg.info("请选择填写告知的客户！");
		return false;
	}
	if(surveyVersionCode==null||surveyVersionCode==''){
		alertMsg.info("请选择[告知类型]");
		return false;
	}

	var $form_cs = $("#informInsured_CsForm",navTab.getCurrentPanel());
	var acceptId = $("#informInsured_CsForm input[name='acceptId']").val();
	var changeId = $("#informInsured_CsForm input[name='changeId']").val();
	$.ajax({
		type : "POST",
		url : "${ctx}/cs/common/saveInsuredMessageSurvey_PA_csNotificAction.action",
		data : $form_cs.serialize(),
		success : function(rep) {
			var json = DWZ.jsonEval(rep);
			if(json.statusCode === "300"){
				alertMsg.error(json.message);
			}else{
				if('操作成功' != json.message){
					navTab.reload();//刷新
				}
			}
		}
	});

	
	
}

//告知---------------end-----------------
</script>