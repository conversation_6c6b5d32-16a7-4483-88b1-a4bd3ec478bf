<%@ page contentType="text/html; charset=utf-8"%>
<%@ taglib uri="/struts-tags" prefix="s"%><%@include file="/udmpCommon.jsp"%>
<%@ taglib uri="http://www.git.com.cn/taglib/udmp/field" prefix="Field"%>
<%-- <s:set var="ctx">${pageContext.request.contextPath}</s:set> --%>
<%@ include file="/cs/pages/common/jsp/common_ctx.jsp" %>
<script type="text/javascript" src="${ctx}/cs/js/cs_common.js"></script>
<link rel="stylesheet" href="${ctx }/cs/css/cs_common.css" type="text/css">
<link rel="stylesheet" href="${ctx}/cs/css/public.css" type="text/css">

<script type="text/javascript" src="${ctx}/udmp/plugins/jquery_ui/jquery.ui.core.js"></script>
<script type="text/javascript" src="${ctx}/udmp/plugins/jquery_ui/jquery.ui.widget.js"></script>
<script type="text/javascript" src="${ctx}/udmp/plugins/jquery_ui/assets/prettify.js"></script>
<script type="text/javascript" src="${ctx}/udmp/plugins/jquery_ui/multiselect/jquery.multiselect.js"></script>
<script type="text/javascript" src="${ctx}/udmp/plugins/jquery_ui/multiselect/jquery.multiselect.filter.js"></script>

<link rel="stylesheet" type="text/css" href="${ctx}/udmp/plugins/jquery_ui/jquery-ui.css" />
<link rel="stylesheet" type="text/css" href="${ctx}/udmp/plugins/jquery_ui/assets/prettify.css" />
<link rel="stylesheet" type="text/css" href="${ctx}/udmp/plugins/jquery_ui/multiselect/jquery.multiselect.css" />
<link rel="stylesheet" type="text/css" href="${ctx}/udmp/plugins/jquery_ui/multiselect/jquery.multiselect.filter.css" />

<!-- ********帮助菜单***********begin********* --> 
<s:include value="/cs/pages/common/jsp/helpMenu.jsp"></s:include>
<!-- *********帮助菜单***********end********* -->
<!-- 客户信息 -->
<form id="hiddenForm">
	<!--保全申请号 -->
	<input id="acceptTime" type="hidden" value="" />
	<!--保全受理号 -->
	<input id="changeTime" type="hidden" value="" />
	<!--变更信息的json字符串 -->
	<input name="jsons" type="hidden" value="" />
</form>
<div class="backgroundCollor" style="background: white;" layoutH="100px">
	<%-- 进度条 --%>
	<s:if test="queryFlag!=1">
	<div class="step_header">
	<table width="100%" border="0" cellspacing="0" cellpadding="0">
		<tr>
			<td id="n1"width="2%"><div class="main_n1d">1</div></td>
				<td id="step1"><div class="main_step">受理信息修改</div></td>
				<td id="n2"width="2%"><div class="main_n1d">2</div></td>
				<td id="step2"><div class="main_step">项目信息录入</div></td>
				<td id="n3"width="2%"><div class="main_n2">3</div></td>
				<td id="step3"><div class="main_stepOther">录入完成</div></td>
		</tr>
	</table>
</div>
	</s:if>
	<div class="divfclass">
			<h1>
				<img src="${ctx}/cs/img/icon/tubiao.png" />职业信息变更
			</h1>
		</div>
	<s:include value="customerInfo_list.jsp" />
	<!-- 175909-被变更人信息 -->
	<s:include value="CsEntryChangeCustomer.jsp" />
	
	<div class="divfclass" style="margin:10px;">
	<h1><img src="cs/img/icon/tubiao.png">客户职业信息</h1>
	<div class="pageFormContent" id="" <s:if test="queryFlag==1">disabled="disabled"</s:if> >
		<dl>
			<dt>职业代码及名称</dt>
			<dd>
				<input type="hidden" id="hiddenJobCode" value="${csCustjobCateGoryVO_old.jobCode}"/>
				<input readonly="readonly" type="text" size="30" id="jobCodeOld" name="csCustjobCateGoryVO_old.jobCode" 
				value=' <Field:codeValue tableName="APP___PAS__DBUSER.T_JOB_CODE_DESC"  value="${csCustjobCateGoryVO_old.jobCode}"/>'>
			</dd>
		</dl>
		<dl>
			<dt>职业类别</dt>
			<dd>
				<input type="hidden" id="job_level_old" name="csCustjobCateGoryVO_old.jobLevelCode" value="${csCustjobCateGoryVO_old.jobLevelCode}">
				<input readonly="readonly" id="job_level_oldd" type="text" name="" value="${csCustjobCateGoryVO_old.jobLevelName}"/>
				
			</dd>	
		</dl>
		<dl>
		    <dt></dt>
		    <dd>
		    </dd>
		</dl>		
		<dl> 
		   <dt>上海医保职业代码名称</dt>
		   <dd>
		        <input type="hidden" id="shhiddenJobCode" value="${csCustjobCateGoryVO_old.jobCode}"/>
				<input readonly="readonly" type="text" size="30" id="shJobCodeOldName" name="" 
                  value="${csCustjobCateGoryVO_old.shJobCodeName}"/>
			</dd>		
		</dl>
		<dl>
			<dt>职业类别</dt>
			<dd>
				<input type="hidden" id="shjob_level_old" name="" value="${csCustjobCateGoryVO_old.shJobLevelCode}">
				<input readonly="readonly" id="shjob_level_oldd" type="text" name="" value="${csCustjobCateGoryVO_old.shJobLevelName}"/>
				
			</dd>	
		</dl> 
		<s:if test="secondPolicyHolderStr != null && secondPolicyHolderStr != ''">
		<dl>
			<dt></dt>
			<dd></dd>
		</dl>
		<dl>
			<dt style="font-weight: 900; color: red;width: auto;">第二投保人指定:${secondPolicyHolderStr}</dt>
		</dl>
		</s:if>
		<dl> 
			<dt>工作单位</dt>
			<dd>
				<input readonly="readonly" type="text" size="30" id="companyNameOld" name="csCustjobCateGoryVO_old.companyName" value="${csCustjobCateGoryVO_old.companyName}"/>
			</dd>
		</dl>
	</div>
	</div>
	<div class="divfclass" >
		<h1><img src="cs/img/icon/tubiao.png">保单险种列表信息</h1>
		<div class="divfclass">
			<div class="tabdivclass">
				<table id="basicRemarkTable" class="list" style="width: 100%">
					<thead>
						<tr>
							<th>保单号</th>
							<th>投保人</th>
							<th>被保人</th>
							<th>险种代码</th>
							<th>险种名称</th>
							<th>保额</th>
							<th>保费</th>
							<th>职业加费</th>
							<th>险种状态</th>
						</tr>
					</thead>
					<tbody>
						<s:iterator value="csContractMasterJobCGVOs_old" status="st" var="var">
							<tr align="center" <s:if test="queryFlag==1">readonly="readonly"</s:if>>
								<td><s:property value="policyCode"/></td>
								<td><s:property value="policyHolderName"/></td>
								<td><s:property value="insuredName"/></td>
	<%-- 							<td><s:property value="busiProdCode"/></td> --%>
								<td><s:property value="busiProdCode"/></td>
								<td><Field:codeValue tableName="APP___PAS__DBUSER.T_BUSINESS_PRODUCT" value="${busiPrdId}"/></td>
								<td><s:property value="basicAmount"/></td>
								<td><s:property value="stdPremAf"/></td>
								<td><s:property value="extraPrem"/></td>
								<td><Field:codeValue tableName="APP___PAS__DBUSER.T_LIABILITY_STATUS" value="${liabilityState}"/></td>
							</tr>
						</s:iterator>
					</tbody>
				</table>
			</div>
		</div>
	</div>
	
	<!-- onsubmit="return navTabSearch(this)" onsubmit写成这样后，页面会跳转到原访问页面，如果没有onsubmit的话，页面跳转到新的页面不被原页面包含  -->
	<!--客户职业类别信息变更   onsubmit="return jobCateGoryFormSubmit();"-->
	<form method="post" action="${ctx}/cs/serviceitem_io/custJobCateGoryUpdate_PA_csEndorseIOAction.action" 
	class="required-validate" id="jobCateGoryForm"  onsubmit="return navTabSearch(this,'updateEndInfo')">
		<input type="hidden" name="customerId" id="customerId" value="${customerId}"/>
		<input type="hidden" name="changeId" id="changeId_IO" value="${changeId}"/>
		<input type="hidden" name="acceptId" id="acceptId" value="${acceptId}"/>
		<input type="hidden" name="csCustjobCateGoryVO_new.oldJobCode" value="${csCustjobCateGoryVO_old.jobCode}">
		<div class="divfclass">
			<h1><img src="cs/img/icon/tubiao.png">客户职业信息变更录入</h1>
			<div class="pageFormInfoContent" id="" <s:if test="queryFlag==1">disabled="disabled"</s:if>>
				<dl>
					<dt>职业代码及名称</dt>
					<dd><!--  whereClause="job_nature_code in('')"   onchange="jobCodeOnchange()"-->
						<ul>
							<s:if test="queryFlag==1">							
								 <input type="hidden" id="hiddenJobCode" value="${csCustjobCateGoryVO_new.jobCode}"/> 
								<input readonly="readonly" type="text" id="jobCodenew" name="csCustjobCateGoryVO_new.jobCode" 
								value=' <Field:codeValue tableName="APP___PAS__DBUSER.T_JOB_CODE_DESC" value="${csCustjobCateGoryVO_new.jobCode}"/>'>
							</s:if>
							<s:else>
								<input style="width: 40px;" type="hidden" id="jobCode" name="csCustjobCateGoryVO_new.jobCode" value="${csCustjobCateGoryVO_new.jobCode}"> 
								<Field:codeTable id="jobName" 
								  nullOption="true"
								  name="csFocusCheckVO.organCode" tableName="APP___PAS__DBUSER.T_JOB_CODE" value="${csCustjobCateGoryVO_new.jobCode}" />
							</s:else>
						</ul>
					</dd>
				</dl>
				<dl>
					<dt>职业类别</dt>	
					<dd>
						<s:if test="queryFlag==1">
							<input readonly="readonly" id="job_level_old" type="text" name="" value="${csCustjobCateGoryVO_new.jobLevelCode}"/>
						</s:if>
						<s:else>
							<input type="text" readonly="readonly" id="job_level_newName" name="csCustjobCateGoryVO_new.jobLevelName" value="${csCustjobCateGoryVO_new.jobLevelName}"/>
							<input type="hidden"  id="job_level_new" name="csCustjobCateGoryVO_new.jobLevelCode" value="${csCustjobCateGoryVO_new.jobLevelCode}"/>
						</s:else>
					</dd>
				</dl>
				<dl>
					<dt>职业类别变更开始日期</dt><!-- checkBeginDate() --> <!-- value="<s:date name='csCustjobCateGoryVO_new.jobCateGoryBeginTime' format='yyyy-MM-dd'/>" -->
					<dd>
						<s:if test="queryFlag==1">
							<input class="text" dateFmt="yyyy-MM-dd"  value="<s:date format="yyyy-MM-dd" name="csCustjobCateGoryVO_new.jobCateGoryBeginTime"/>"
								id="jobCateGoryBeginTime" name="csCustjobCateGoryVO_new.jobCateGoryBeginTime" />
						</s:if>
						<s:else>
							<input type="expandDateYMDRO" class="date" dateFmt="yyyy-MM-dd"  value="<s:date format="yyyy-MM-dd" name="csCustjobCateGoryVO_new.jobCateGoryBeginTime"/>"
								id="jobCateGoryBeginTime" name="csCustjobCateGoryVO_new.jobCateGoryBeginTime" />
						</s:else>
					</dd>
				</dl>
				<dl>
					<dt>工作单位</dt>	
					<dd>
						<input type="text" <s:if test="queryFlag==1">readonly="readonly" </s:if> id="companyName" name="csCustjobCateGoryVO_new.companyName" value="${csCustjobCateGoryVO_new.companyName}"/>
					</dd>
				</dl>
				<table <s:if test="queryFlag==1">style="display:none"</s:if><s:else>style="width:100%;padding-top:10px;"</s:else>	>
				    <tbody>
				        <tr>
				            <td style="width:120px">
								<div class="formBarButton">
									<button type="button" class="but_blue" onclick="jobCateGoryFormSubmit()">保 存</button>
								</div> 
				            </td>
				        </tr>
			    	 </tbody>
			    </table>			
			</div>
		</div>
	</form>
	<!-- 变更后保单险种列表信息 -->
	<div id="updateEndInfo" >
		<s:if test="queryFlag eq 1">
			<s:include value="/cs/pages/serviceitem/CsEndorseIOUpdateEnd.jsp"></s:include>
		</s:if>
	</div>
	
</div>
<!-- 按钮 -->
	<s:include value="/cs/pages/serviceitem/serviceItemBottomBar.jsp"></s:include>	

<script type="text/javascript">


//退出按钮功能
function exit(){
	navTab.closeCurrentTab();
}
$(document).ready(function(){
		csHelpMenu();
		//init('jobName', 'jobCode');
		searchjobName();
	});

	function searchjobName(){
		$("#jobName").multiselect({
	        noneSelectedText: "===请选择===",        
	        selectedList:2,
	        multiple:false
	    }).multiselectfilter({
			label: "筛选",
	        placeholder: "请输入",
	        width: 90
		});
	}
	//当选择职业名称时，自定将职业编码填入到页面中
	$("#jobName", navTab.getCurrentPanel()).change(function(){
		var jobCode = $('#jobName').val();
		var changeId = $('#changeId_IO', navTab.getCurrentPanel()).val();
		var acceptId = $('#acceptId', navTab.getCurrentPanel()).val();
		var customerId = $('#customerId', navTab.getCurrentPanel()).val();
			$.ajax({
				type : "POST",
				//data : policyCode,
				url : "${ctx}/cs/serviceitem_io/getJobCode_PA_csEndorseIOAction.action?changeId="+changeId+"&acceptId="+acceptId+"&customerId="+customerId+"&jobCode="+jobCode,
				dataType : "json",
				success : function(json) {
					var csCustjobCateGoryVO = json.csCustjobCateGoryVO;
					$('#job_level_new', navTab.getCurrentPanel()).val(csCustjobCateGoryVO.jobLevelCode);
					$('#jobCode', navTab.getCurrentPanel()).val($("#jobName", navTab.getCurrentPanel()).val());
					$('#job_level_newName').val(csCustjobCateGoryVO.jobLevelName);
					if(csCustjobCateGoryVO.jobLevelCode === "z"){
						alertMsg.error("变更后的职业类别为拒保类职业，本次保全申请终止。");
					}
					
				} 
			});		
		
	});
	$("#jobCode").change(function(){
			var jobCode = $('#jobCode', navTab.getCurrentPanel()).val();
			var changeId = $('#changeId_IO', navTab.getCurrentPanel()).val();
			var acceptId = $('#acceptId', navTab.getCurrentPanel()).val();
			var customerId = $('#customerId', navTab.getCurrentPanel()).val();
				$.ajax({
					type : "POST",
					//data : policyCode,
					url : "${ctx}/cs/serviceitem_io/getJobCode_PA_csEndorseIOAction.action?changeId="+changeId+"&acceptId="+acceptId+"&customerId="+customerId+"&jobCode="+jobCode,
					dataType : "json",
					success : function(json) {
						var result = DWZ.jsonEval(json);
						if(result.statusCode === "300"){
							alertMsg.error(result.message);
						}else{
							var csCustjobCateGoryVO = json.csCustjobCateGoryVO;
							$('#job_level_new', navTab.getCurrentPanel()).val(csCustjobCateGoryVO.jobLevelCode);
							$("a[name='csFocusCheckVO.organCode']", navTab.getCurrentPanel()).attr('value',csCustjobCateGoryVO.jobCode);
							$("a[name='csFocusCheckVO.organCode']", navTab.getCurrentPanel()).text(csCustjobCateGoryVO.class3);
							$('#jobCode', navTab.getCurrentPanel()).attr('value',csCustjobCateGoryVO.jobCode);
							$('#job_level_newName', navTab.getCurrentPanel()).attr('value',csCustjobCateGoryVO.jobLevelName);	
							if(csCustjobCateGoryVO.jobLevelCode === "z"){
								alertMsg.error("变更后的职业类别为拒保类职业，本次保全申请终止。");
							}
						}
					} 
				});		
			
		});
	
	//当职业代码输入到页面时，自动将与之相对应的职业名称选中
	function jobCodeOnchange(){
		var jobCode = $("#jobCode", navTab.getCurrentPanel()).val();
		var jobNameField = $("#jobName", navTab.getCurrentPanel());
		//alert(jobNameField);
		jobNameField.attr("whereClause","job_code in('"+jobCode+"')");
	}
	//判断职业代码是否为空
	function jobCodeIsNull(){
		var jobCode = $("#jobCode", navTab.getCurrentPanel()).val();
		if(jobCode==null || jobCode==""){
			//alertMsg.info("职业代码不能为空！");
			return false;
		}
 		var job_level_newName = $("#job_level_newName", navTab.getCurrentPanel()).val();
 		if(job_level_newName==null||job_level_newName==''){
 			//alertMsg.info("职业类型不能为空。");
 			return false;
 		}
 		var job_level_new = $("#job_level_new", navTab.getCurrentPanel()).val();
 		if(job_level_new == null||job_level_new == ''){
 			//alertMsg.info("职业类别不能为空。");
 			return false;
 		}
		var jobCateGoryBeginTime = $("#jobCateGoryBeginTime", navTab.getCurrentPanel()).val();
		if(jobCateGoryBeginTime==null||jobCateGoryBeginTime==''){
			//alertMsg.info("职业类别变更开始日期不能为空！");
			return false;
		}
		
		return true;
	}
// 	//验证时间
// 	function checkBeginDate(){
// 		var result=false;
// 		var changeId = $("#changeId").val();
// 		var acceptId = $("#acceptId").val();
// 		var jobCateGoryBeginTime = $("#jobCateGoryBeginTime").val();
// 		$.ajax({
// 			url:"cs/serviceitem_io/checkTime_PA_csEndorseIOAction.action",
// 			type:"post",
// 			dataType:'text',
// 			data:"changeId="+changeId+"&acceptId="+acceptId+"&csCustjobCateGoryVO_new.jobCateGoryBeginTime="+jobCateGoryBeginTime,
// 			success:function(data){	
// 				var json = jQuery.parseJSON(data);			
// 				if(json.statusCode==200){
// 					alert();
// 					result=true;
// 				}
// 			},
// 			error:function(){
// 				result=false;
// 			}
// 		});
// 		return result;
// 	}
	function checkTime2(){
		checkBeginDate();
		var acceptTime = $("#acceptTime", navTab.getCurrentPanel()).val();
		var changeTime = $("#changeTime", navTab.getCurrentPanel()).val();
		//alert(acceptTime+"    "+changeTime);
		if(acceptTime==""&&changeTime==""){
			return true;
		}else{
			if(acceptTime!=""){
				alertMsg.info(acceptTime);
				return false;
			}
			if(changeTime!=""){
				alertMsg.info(changeTime);
				return false;
			}
		}
	}
	//执行更改操作
	function jobCateGoryFormSubmit(){
		alertMsg.confirm("请确认是否需要保存录入的信息？",{
			okCall:function(){
				if(!jobCodeIsNull()){
					//jobCodeIsNull();
					alertMsg.info("必填项信息未完整录入，不能受理职业信息变更，请确认");
					return false;
				}
				var jobCode = $("#jobCode", navTab.getCurrentPanel()).val();
				var companyName = $("#companyName", navTab.getCurrentPanel()).val();
				var customerId = $('#customerId', navTab.getCurrentPanel()).val();
				// 工作单位
				$.ajax({
					url:"${ctx}/cs/serviceitem_io/checkBeforeSave_PA_csEndorseIOAction.action",
					type:"post",
					dataType:'text',
					data:"csCustjobCateGoryVO_new.jobCode="+jobCode+"&csCustjobCateGoryVO_new.companyName="+encodeURI(encodeURI(companyName))+"&customerId="+customerId,
					success:function(data) {
						debugger;
						var json = jQuery.parseJSON(data);
						if (json.statusCode == 200) {
							// 增加工作单位非空校验，原处理后移
							jobCateGoryFormSubmit1();
						} else {
							alertMsg.error(json.message);
						}
					}
				});
			},
			cancelCall:function(){
			}
		});
	}
	// 增加工作单位非空校验，原处理后移
	function jobCateGoryFormSubmit1(){
		debugger;
		var jobCode = $("#jobName", navTab.getCurrentPanel()).val();
		$("#jobCode", navTab.getCurrentPanel()).val(jobCode);
		var hiddenJobCode = $("#hiddenJobCode", navTab.getCurrentPanel()).val();
		/* if(hiddenJobCode==jobCode){
			alertMsg.info("录入的职业与变更前的职业相同，请重新录入。");
			return false;
		} */
		//验证时间
		var changeId = $("#changeId_IO", navTab.getCurrentPanel()).val();
		var acceptId = $("#acceptId", navTab.getCurrentPanel()).val();
		var jobCateGoryBeginTime = $("#jobCateGoryBeginTime", navTab.getCurrentPanel()).val();
		$.ajax({
			url:"${ctx}/cs/serviceitem_io/checkTime_PA_csEndorseIOAction.action",
			type:"post",
			dataType:'text',
			data:"changeId="+changeId+"&acceptId="+acceptId+"&csCustjobCateGoryVO_new.jobCateGoryBeginTime="+jobCateGoryBeginTime,
			success:function(data){	
				var json = jQuery.parseJSON(data);			
				if(json.statusCode==200){
					$("#jobCateGoryForm",navTab.getCurrentPanel()).submit();
				}else{
					alertMsg.error(json.message);
				}
			},
			error:function(){
				result=false;
			}
		});
		
		var customerId = $('#customerId', navTab.getCurrentPanel()).val();
		$.ajax({
			url:"${ctx}/cs/serviceitem_io/checkSecondMsg_PA_csEndorseIOAction.action",
			type:"post",
			dataType:'text',
			data:"customerId="+customerId,
			success:function(data){	
				var json = jQuery.parseJSON(data);			
				if(json != null && json.statusCode==200){
					alertMsg.info(json.message);
				}
			},
			error:function(){
				result=false;
			}
		});
	}
	
	//上一步
	function upToCsEntry(){
		alertMsg.confirm("请确认是否需要保存变更的信息？",{
			okCall:function(){
				//保存变更信息---返回保全录入主页面
				var onsubmit="return validateCallback(this,reusltAjaxDone)";	
				$("#jobCateGoryForm",navTab.getCurrentPanel()).attr("onsubmit",onsubmit);
				var action = "${ctx}/cs/serviceitem_io/custJobCateGoryUpdate_PA_csEndorseIOAction.action";
				$("#jobCateGoryForm",navTab.getCurrentPanel()).attr("action",action);
				jobCateGoryFormSubmit();
			},
			cancelCall:function(){
				$("#gotoCsEntry", navTab.getCurrentPanel()).click();
			}
		});
	}
	//下一步
	function nextStep(){
		var onsubmit="return validateCallback(this,reusltAjaxDone)";
		var action = "${ctx}/cs/serviceitem_io/custJobCateGoryUpdate_PA_csEndorseIOAction.action";
		$("#jobCateGoryForm",navTab.getCurrentPanel()).attr("onsubmit",onsubmit);
		$("#jobCateGoryForm",navTab.getCurrentPanel()).attr("action",action);
		jobCateGoryFormSubmit();
	}
	
	function reusltAjaxDone(json){
		DWZ.ajaxDone(json);
		if (json.statusCode == DWZ.statusCode.ok){
			$("#gotoCsEntry", navTab.getCurrentPanel()).click();
		}else{
			alertMsg.error("系统异常!");
		}
	}
	//加载变更后的数据
	function updateEndAjaxDone(json){
		DWZ.ajaxDone(json);
		if (json.statusCode == DWZ.statusCode.ok){
			$("#updateEndInfo",navTab.getCurrentPanel()).show();
			var onsubmit = "return navTabSearch(this,'updateEndInfo')";
			var action = "${ctx}/cs/serviceitem_io/findUpdateEndInfo_PA_csEndorseIOAction.action";
			var $form = $("#jobCateGoryForm",navTab.getCurrentPanel());
			$form.attr("onsubmit",onsubmit);
			$form.attr("action",action);
			$form.submit();
		}else{
			alertMsg.error("系统异常!");
		}
	}
	//下一步
	function next() {
		var val1 = $("#changeId_IO", navTab.getCurrentPanel()).val();
		var val2 = $("#acceptId", navTab.getCurrentPanel()).val();
		var val3 = $("#customerId", navTab.getCurrentPanel()).val();
		var title = "受理信息录入";
		var tabid = "_aplPermit_M";
		var fresh = eval("true");
		var external = eval("false");
		var url = "${ctx}/cs/csEntry/showAcceptAndArapInfo_PA_csEntryAction.action?&menuId=${menuId}&changeId="
				+ val1 + "&acceptId=" + val2 + "&customerId=" + val3;
		navTab.openTab(tabid, url, {
			title : title,
			fresh : fresh,
			external : external
		});

	}
</script>