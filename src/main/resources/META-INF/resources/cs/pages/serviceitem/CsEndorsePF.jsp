<%@ page language="java" contentType="text/html; charset=utf-8"
	pageEncoding="utf-8" import="java.util.*,java.text.*"%>
<%@ taglib uri="/struts-tags" prefix="s"%><%@include file="/udmpCommon.jsp"%>
<%@ include file="/cs/pages/common/jsp/common_ctx.jsp" %><%@ taglib uri="http://www.git.com.cn/taglib/udmp/field" prefix="Field"%>
<script type="text/javascript" src="${ctx}/cs/js/cs_common.js"></script>
<link rel="stylesheet" href="${ctx}/cs/css/cs_common.css" type="text/css">
<link rel="stylesheet" href="${ctx}/cs/css/public.css" type="text/css">
<script type="text/javascript" src="${ctx}/cs/js/DomZoomZX.js"></script>
<script>
	$(document).ready(function(){
        var D = DomZoomZX('input,select,textarea,td')
	})
</script>	
<%-- 分页正文 --%>
<script type="text/javascript">
	function saveChange() {
				alertMsg.confirm("请确认是否需要保存解挂的信息", {
					okCall : function() {
						var  flag=$("#flag1").val();
						if(flag=="1"){
							alertMsg.error("无挂失数据。不能受理。");
							}else{	
								$("#policyUnlockForm", navTab.getCurrentPanel()).submit();
								
							}
					},
								
					cancleCall:function(){
					}
				});
			}
	//下一步
	function next() {
		var  rootPath= getRootPath();
		var val1 = $("#changeId").val();
		var val2 = $("#acceptId").val();
		var val3 = $("#customerId").val();
		var title = "受理信息录入";
		var tabid = "_aplPermit_M";
		var fresh = eval("true");
		var external = eval("false");
		var url = rootPath+"/cs/csEntry/showAcceptAndArapInfo_PA_csEntryAction.action?&menuId=${menuId}&changeId="
				+ val1 + "&acceptId=" + val2 + "&customerId=" + val3;
		navTab.openTab(tabid, url, {
			title : title,
			fresh : fresh,
			external : external
		});
	}
</script>
<%-- 请输入查询条件页面 --%>
<div class="pageContent" layoutH="36px" style="margin: 10px">
<s:include value="customerInfo_list.jsp" />
	<%-- <div class="panel" style="margin: 10px; display: none">
		<h1>客户信息</h1>
		<div class="pageFormContent">
			<dl>
				<dt>姓名</dt>
				<dd>
					<input id="customerName" name="csCustomerVO.customerName"
						type="text" value="${csCustomerVO.customerName}"
						disabled="disabled" />
				</dd>
			</dl>
			<dl>
				<dt>性别</dt>
				<dd>
					<s:select list="#{0:'男', 1:'女', 2:'未知'}" style="width: 135px"
						disabled="true" name="csCustomerVO.customerGender" theme="simple"
						listKey="key" listValue="value"
						value="#{csCustomerVO.customerGender}">
					</s:select>
				</dd>
			</dl>
			<dl>
				<dt>生日</dt>
				<dd>
					<input name="csCustomerVO.customerBirthday" type="text"
						value=<s:date format="yyyy-MM-dd" name="csCustomerVO.customerBirthday"></s:date>
						disabled="disabled" />
				</dd>
			</dl>
			<dl>
				<dt>证件类型</dt>
				<dd>
					<s:select list="#{0:'身份证', 1:'港澳通行证', 2:'其他'}" style="width: 135px"
						disabled="true" name="csCustomerVO.customerCertType"
						theme="simple" listKey="key" listValue="value"
						value="#{csCustomerVO.customerCertType}">
					</s:select>
				</dd>
			</dl>
			<dl>
				<dt>证件号码</dt>
				<dd>
					<input type="expandCertiCode" name="csCustomerVO.customerCertiCode"
						value="${csCustomerVO.customerCertiCode}" disabled="disabled" />
				</dd>
			</dl>
		</div>
	</div> --%>
	<%-- 我的任务列表 onsubmit="return navTabSearch(this);"--%>
	<form id="policyUnlockForm"
		action="${ctx}/cs/serviceitem_pf/saveChange_PA_csEndorsePFAction.action"
		method="post" class="pageForm required-validate"   onsubmit="return validateCallback(this)"
		>
		<div class="panel" style="margin:10px;">
			<h1>保单险种列表信息</h1>
			<div class="pageFormContent">
				<table class="list" width="100%">
					<thead>
						<tr>
							<th>序号</th>
							<th>保单号</th>
							<th>险种代码</th>
							<th>险种名称</th>
							<th>保额</th>
							<th>保费</th>
							<th>生效日期</th>
							<th>保单状态</th>
						</tr>
					</thead>
					<tbody align="center">
						<s:iterator value="policyUnlockUpVOs" status="st">
							<tr align="center" tr_saveStatus="1">
								<td><s:property value="#st.index+1" /></td>
								<td>${policyCode}</td>
								<td>${busiprcode}</td>
								<td><Field:codeValue tableName="APP___PAS__DBUSER.T_BUSINESS_PRODUCT"
										value="${busipid}" /> ${busiItemName}</td>
								<td>${amount}</td>
								<td>${premium }</td>
								<td><s:date format="yyyy-MM-dd" name="validateDay"></s:date></td>
								<td><Field:codeValue tableName="APP___PAS__DBUSER.T_LIABILITY_STATUS"
										value="${policyState}" /></td>
							</tr>
						</s:iterator>

					</tbody>
				</table>
			</div>
			<!--保全申请号 -->
			<input type="hidden" id="customerId" name="customerId"
				value="${customerId}" />
			<!--保全申请号 -->
			<input type="hidden" id="changeId" name="changeId"
				value="${changeId}" />
			<!--保全受理号 -->
			<input type="hidden" id="acceptId" name="acceptId"
				value="${acceptId}" /> <input type="hidden" id="flag1" name="flag1"
				value="${flag1}" />

		</div>
		<div class="formBar" <s:if test="queryFlag==1">style="display:none"</s:if>>
			<table style="width:100%;margin:-10px;">
		        <tbody>
			        <tr>
			            <td></td>
			            <td style="width:100px">
			                <!-- <div class="button">
								<div class="buttonContent">
									<button type="button" onclick="saveChange()">保存</button>
								</div>
							</div> -->
							<div class="pageFormbut">
								<button type="button" class="but_blue" onclick="saveChange()">保存</button>
							</div>
			            </td>
			            <td></td>
			        </tr>
		    	 </tbody>
		    </table>
		</div>
	</form>
	<s:include value="/cs/pages/serviceitem/serviceItemBottomBar.jsp"></s:include>

</div>







