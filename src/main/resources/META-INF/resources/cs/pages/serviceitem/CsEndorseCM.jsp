<!-- 客户重要资料变更页面 -->
<%@ page contentType="text/html; charset=utf-8"%>
<%@ taglib uri="/struts-tags" prefix="s"%>
<%@include file="/udmpCommon.jsp"%>
<%@ taglib uri="http://www.git.com.cn/taglib/udmp/field" prefix="Field"%>
<%@ include file="/cs/pages/common/jsp/common_ctx.jsp"%>
<script type="text/javascript" src="${ctx}/cs/js/cs_common.js"></script>
<link rel="stylesheet" href="${ctx}/cs/css/public.css" type="text/css">

<!-- 帮助菜单-->
<s:include value="/cs/pages/common/jsp/helpMenu.jsp"/>

<!-- 步骤标识 -->
<s:include value="csEndorseProgress.jsp" />

<div class="panelPageFormContent" >
	<div class="divfclass">
		<h1>
			<img src="${ctx}/cs/img/icon/tubiao.png">客户重要资料变更
		</h1>
	</div>
	
	<div class="pageContent" layoutH="150">
		<!-- 客户信息 -->
		<div class="panelPageFormContent">
			<%-- <s:include value="customerInfo.jsp" /> --%>
			<s:include value="customerInfo_list.jsp" />
			<s:if test="verifyFlag==1">
				<s:include value="verifyInfo_list.jsp" />
			</s:if>
		</div>


		<!-- 客户相关保单列表 -->
		<div class="main_tabdiv">
			<div class="panelPageFormContent">
				<div class="divfclass">
					<h1>
						<img src="${ctx}/cs/img/icon/tubiao.png">客户相关保单列表
					</h1>
				</div>
				<div class="tabdivclass">
					<table class="list" width="100%">
						<thead>
							<tr id="" align="center">
								<th>保单号</th>
								<th width="50">投保人</th>
								<th style="display: none;">投保人id</th>
								<th style="display: none;">保单投保日期</th>
								<th width="50">被保人</th>
								<th>险种代码</th>
								<th>险种名称</th>
								<th width="60">被保人投保年龄</th>
								<th>保额</th>
								<th>保费</th>
								<th width="80">交费期限</th>
								<th width="80">下次交费对应日</th>
								<th>领取标准</th>
								<th>保单状态</th>
								<th>保障年期类型</th>
								<th>保障年期</th>
								<th width="80">是否参与本次变更</th>
							</tr>
						</thead>
						<tbody>
							<s:iterator value="custImportInfoUpdateVOS">
								<tr align="center" <s:if test="queryFlag==1">disabled="disabled"</s:if>>
									<td>${policyCode}</td>
									<td>${policyHolder}</td>
									<td style="display: none;" class = "holder">${holderCustomerId}&&${applyDate}&&${policyCode}</td>
									<td>${insuredStr}</td>
									<td>${productCodeSys}</td>
									<td><Field:codeValue tableName="APP___PAS__DBUSER.T_BUSINESS_PRODUCT" value="${productNameSys}" /></td>
									<td>${insuredAgeStr}</td>
									<td>${amout }</td>
									<td>${stdPremAf}</td>
									<td>${chargeYearStr}</td>
									<td><s:date name="payDueDate" format="yyyy-MM-dd" /></td>
									<td>${sam}</td>
									<td><Field:codeValue tableName="APP___PAS__DBUSER.T_LIABILITY_STATUS" value="${abilityState}" /></td>
									<td><Field:codeValue tableName="APP___PAS__DBUSER.T_COVERAGE_PERIOD" value="${coveragePeriod}" /></td>
									<td>${coverageYear}</td>
									<td><s:property value="isChange" /></td>
								</tr>
							</s:iterator>
						</tbody>
					</table>
				</div>
			</div>
		</div>
		
		<!--  客戶--变更后客户信息 -->
		<div  class="main_tabdiv">
			<s:include value="CsEndorseCM_CustomerNew.jsp" />
		</div>

		<!-- 变更后信息  -->		
		<s:include value="CsEndorseCM_ContractMasterNewInfo.jsp" />
		
		<s:include value="/cs/pages/serviceitem/serviceItemBottomBar.jsp"></s:include>

	</div>
</div>

<script type="text/javascript">
	
	//退出按钮功能
	function exit() {
		navTab.closeCurrentTab();
	}

	// 险种信息录入区域--保存按钮--保存录入信息	
	function itemFormSave() {
		alertMsg.confirm("请确认是否需要保存录入的信息？", {
			okCall : function() {
				var $obj = $("#itemForm", navTab.getCurrentPanel());
				action = "${ctx}/serviceitem/saveItemInfo_PA_csEndorseCMAction.action";
				$obj.attr('action', action);
				$obj.submit();
			}
		});

	}

	//帮助部分动作
	function MM_changeProp(objId, theProp, theValue) {
		var obj = null;
		with (document) {
			if (getElementById) {
				obj = getElementById(objId);
			}
		}
		if (obj) {
			if (theValue == true || theValue == false) {
				eval("obj.style." + theProp + "=" + theValue);
			} else {
				eval("obj.style." + theProp + "='" + theValue + "'");
			}
		}
	};

	//上一页
	function up() {
		//系统提示是否保存变更信息，确认--保存，取消--清除
		alertMsg.confirm("请确认是否需要保存录入的信息？", {
			okCall : function() {
				//保存变更信息
				$("#upA").attr("href", 'serviceitem/saveAllCustImportInfo_csEndorseCMAction.action');
			},
			cancelCall : function() {
				//清除变更信息
				$("#upA").attr("href", 'serviceitem/delAllCustImportInfo_csEndorseCMAction.action');
			}
		});
	};
	
</script>