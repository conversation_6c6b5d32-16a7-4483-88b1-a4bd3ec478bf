<%@ page language="java" pageEncoding="utf-8"%>
<%@ taglib uri="http://www.git.com.cn/taglib/udmp/field" prefix="Field"%>
<%@ taglib uri="/struts-tags" prefix="s"%><%@include file="/udmpCommon.jsp"%>
<%@ include file="/cs/pages/common/jsp/common_ctx.jsp" %>
<script type = "text/javascript" src="${ctx}/cs/js/cs_common.js"></script>
<link rel="stylesheet" href="${ctx}/cs/css/public.css" type="text/css">
<script type="text/javascript">
function beforeSubmit(){
	var realPay = $("#realPay",navTab.getCurrentPanel()).val();
	var procode="";
	var arrearPrem=$("#arrearPrem",navTab.getCurrentPanel()).val();
	
	$("#bfchaninfo input[type='radio']:checked").each(function() {
		
		procode=$(this).val();
		
	});
	if(""==procode||null==procode){
		alertMsg.warn('请选择对应保单!');
	
	}else{
	$("#procode",navTab.getCurrentPanel()).attr('value', procode);
	if(""==realPay){
		alertMsg.warn('请输入实缴保费金额');
	}else{
		$("#restorePayForm",navTab.getCurrentPanel()).submit();
	}
	}
}
$(document).ready(function() {
		
		var customerId=$("#customerId",navTab.getCurrentPanel()).val();
		var changeId=$("#changeId",navTab.getCurrentPanel()).val();
		var acceptId=$("#acceptId",navTab.getCurrentPanel()).val();
		var queryFlag=$("#queryFlag",navTab.getCurrentPanel()).val();
		var type="2";
		var rel = $("#insertSurvey",navTab.getCurrentPanel());
		
		rel.loadUrl("${ctx}/cs/common/loadMainPage_PA_csNotificAction.action?customerId="+customerId+"&changeId="+changeId+"&acceptId="+acceptId+"&type="+type+"&queryFlag="+queryFlag);		
	});
	
</script>
<s:include value="/cs/pages/common/jsp/helpMenu.jsp"></s:include>
<%-- 进度条 --%>
<s:if test="queryFlag!=1">
	<s:include value="entryProgressBar.jsp"></s:include>
</s:if>
<div class="backgroundCollor" layoutH="140">
		<s:include value="customerInfo_list.jsp" />
		<div class="divfclass">
			<h1><img src="cs/img/icon/tubiao.png">变更前信息</h1>
			<div class="tabdivclass">
				<table class="list" width="100%" id="bfchaninfo">
					<thead>
						<tr>
						<th>选择</th>
							<th>保单号</th>
							<th>险种代码</th>
							<th>险种名称</th>
							<th>保额</th>
							<th>保费</th>
							<th>下期缴费日</th>
							<th>上次结算日</th>
							<th>账户价值</th>
							<th style="display: none;">所属业务产品</th>
						</tr>
					</thead>
					<tbody>
						<s:iterator value="listBfChangeInfo" status="st" id="BFList" var="pv">
							<tr align="center">
							
							<td><input type="radio" id="itemId" name="itemId"
											class="myClass" value="${busiItemCode}" /></td>
								<td>${policyCode }</td>
								<td>${busiItemCode }</td>
								<td><Field:codeValue tableName="APP___PAS__DBUSER.T_BUSINESS_PRODUCT"
									value="${busiPrdId}" /></td>
								</td>
								<td>${busiAmount }</td>
								<td>${busiPrem }</td>
								 <td>${nextPayDate }</td>
								 <td>${lastCalDate }</td>
								<td>${accountPrice }</td>
								<td style="display: none;">${busiPrdId}</td>
							</tr>
						</s:iterator>
					</tbody>
				</table>
				<div></div>
			</div>
		</div>
		
		<div >
			<!-- 健康告知的引用页面 -->
			<!-- <h1>投保人/被保人健康告知</h1> -->
			<div id="insertSurvey"></div>
		</div>
		<form id="restorePayForm" action="cs/serviceitem_ra/submitPayMent_PA_csEndorseRAAction.action" class="pageForm required-validate" method="post"
		onsubmit="return navTabSearch(this)">

		<input id="customerId" type="hidden" name="customerId" value="${customerId}"/>
		<input id="changeId" type="hidden" name="changeId" value="${changeId}"/>
		<input id="acceptId" type="hidden" name="acceptId" value="${acceptId}"/>
		<input id="procode" type="hidden" name="procode"/>
		<input id="queryFlag" type="hidden" name="queryFlag" value="${queryFlag }"/>
		<div class="divfclass" style="margin: 10px;">
			<h1><img src="cs/img/icon/tubiao.png"">保费信息</h1>
			<div class="panelPageFormContent" >
				<div>
					<dl>
						<dt>欠缴期缴保费：</dt>
						<dd>
							<input type="text" id="arrearPrem" name="arrearPrem" value='${arrearPrem}' readonly="readonly"/>
						</dd>
					</dl>
					<dl>
						<dt>实缴保费金额</dt>
						<dd>
						<s:if test="queryFlag==1">
							<input type="text" id="realPay" disabled="disabled" value="${realPayAmount }"  name="realPayAmount"/>
						</s:if>
						<s:else>
							<input type="text" id="realPay"   name="realPayAmount" value="${realPayAmount }" />
						</s:else>
						</dd>
					</dl>
					
					
					<dl>
			
				
					<s:if test="isSrp==1">
					   <input type="checkbox" id="isSrp" name="isSrp" value='1' checked="checked"/>
					   </s:if>
					   <s:else>
					   <input type="checkbox" id="isSrp" name="isSrp" value='1' />
					  </s:else>
						<dt>是否特殊复缴</dt>
					
					
					</dl>
				</div>
				<table style="width:100%">
			        <tbody>
				        <tr>
				            <td></td>
				            <td style="width:60px">
				                <div class="formBarButton" <s:if test="queryFlag==1">style="display:none"</s:if><s:else>style="margin-top: 5px"</s:else>>
									<button type="button" class="but_blue"  onclick="beforeSubmit();">确定</button>
								</div>
				            </td>
				            <td></td>
				        </tr>
			    	 </tbody>
			    </table>
			</div>
		</div>
		<div class="divfclass" style="margin: 10px;">
			<h1><img src="cs/img/icon/tubiao.png"">变更后信息</h1>
			<div class="tabdivclass">
				<table class="list" width="100%" id="decreaseAFDetail">
					<thead>
						<tr>
							<th>险种代码</th>
							<th>险种名称</th>
							<th>保额</th>
							<th>保费</th>
							<th>补缴保费期数</th>
							<th>下期缴费日</th>
							<th>追加保费</th>
						</tr>
					</thead>
					<tbody id="allInfoContent">
						<s:iterator value="listAfChangeInfo" status="AFst" id="AFList">
							<tr align="center">
								<td>${busiItemCode }</td>
								<td>${busiItemName }</td>
								<td>${busiAmount }</td>
								<td>${busiPrem }</td>
								<td>${rePayPremDurs }</td>
								<td>${nextPayDate }</td>
								<td>${additionalPrem }</td>
							</tr>
						</s:iterator>
					</tbody>
				</table>
			</div>
		</div>
	</form>
</div>
<s:include value="/cs/pages/serviceitem/serviceItemBottomBar.jsp"></s:include>