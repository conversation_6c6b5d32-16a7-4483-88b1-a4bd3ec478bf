<%@ page language="java" contentType="text/html; charset=utf-8" pageEncoding="UTF-8"%><%@ include file="/clm/pages/common/commonJsCss.jsp" %>
<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<%@taglib uri="/struts-tags" prefix="s"%>
<%@ page import="java.util.*"%>

		<title>管理机构查询</title>

	<form id="pagerForm" method="post"
		action="clm/invoice/queryPage_CLM_claimInvoicesPrintAction.action?leftFlag=0&menuId=${menuId }">
		<input type="hidden" name="pageNum" vaule="${currentPage.pageNo} " />
		<input type="hidden" name="numPerPage" value="${currentPage.pageSize}" />
	</form>
	<div class="divfclass">
		<h1><img src="clm/images/tubiao.png">管理机构</h1>
	</div>
	<div layoutH="50" style="width: 100%">
		<form action="clm/invoice/queryPage_CLM_claimInvoicesPrintAction.action?leftFlag=0&menuId=${menuId }"
			method="post" onsubmit="return dialogSearch(this);"
			class="pagerForm required-validate" rel="pagerForm">
			<div class="pageFormInfoContent">
				<dl>
					<dt>管理机构代码</dt>
					<dd>
						<input type="text" name="orgVO.organCode" value="${currentPage.paramObject.organCode}"/>
						<input type="hidden" name=""/>
					</dd>
				</dl>
				<dl>
					<dt>管理机构名称</dt>
					<dd>
						<input type="hidden" name=""/>
						<input type="text" name="orgVO.organName" value="${currentPage.paramObject.organName}"/>
					</dd>
				</dl>
				<div class="formBarButton">
								<button type="submit" class="but_blue">查询</button>   
							</div>
			</div>				
			<div class="tabdivclassbr main_tabdiv">
				<table class="list main_dbottom" width="100%">
					<thead>
						<tr>
							<th nowrap>管理机构代码</th>
							<th nowrap>管理机构名称</th>
							<th nowrap>查找带回</th>
						</tr>
					</thead>
					<tbody>
						<s:if test="imageFlag != null">
							<tr>
								<td colspan="10">
									<div class="noRueryResult">请选择条件查询数据！</div>
								</td>
							</tr>
						</s:if>
						<s:elseif test="currentPage.PageItems == null || currentPage.PageItems.size()==0">
							<tr>
								<td colspan="10">
									<div class="noRueryResult">没有符合条件的查询结果！</div>
								</td>
							</tr>
						</s:elseif>
						<s:iterator value="currentPage.PageItems" status="VOList">
							<tr>
								<td><s:property value="organCode" /></td>
								<td><s:property value="organName" /></td>
								<td>
									<a class="btnSelect" href="javascript:$.bringBack({
										organCode:'<s:property value="organCode"/>',
										organName:'<s:property value="organName"/>'})"
									title="查找带回">选择 </a>
								</td>
							</tr>
						</s:iterator>
					</tbody>
	
				</table>
				<div class="panelBar">
					<div class="pages">
						<span>显示</span>
						<s:select list="#{3:'3',5:'5',10:'10',20:'20',50:'50',100:'100'}"
							name="select" onchange="dialogPageBreak({numPerPage:this.value})"
							value="currentPage.pageSize">
						</s:select>
						<span>条，共${currentPage.total}条</span>
					</div>
					<div class="pagination" targetType="dialog"
						totalCount="${currentPage.total}"
						numPerPage="${currentPage.pageSize}" pageNumShown="10"
						currentPage="${currentPage.pageNo}"></div>
				</div>
			</div>
				<div class="formBarButton">
					<ul>
						<li>
									<button type="button" class="but_gray close">关闭</button>
						</li>
					</ul>
				</div>
		</form>
</div>




