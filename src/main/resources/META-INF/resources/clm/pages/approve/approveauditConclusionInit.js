    //选择赔付明细信息
    $("input[type=radio]", navTab.getCurrentPanel()).click(function(){
    	$obj = $(this).attr("id");
    	var remark=$(this).parent().parent().find("input[name=approve_clmRemark]").val();
    	 
    	var name=remark.split("--");
    	var  values="";
        for(var i=0;i<name.length;i++){
        	values=values+name[i]+"\r";
        	
        }
        $("textarea#"+$obj, navTab.getCurrentPanel()).text(values);
    });
	//收缩按钮 实现
	function show(show){
			if(show.value=="-"){
				document.getElementById(show.hiddenDivId).style.display="none";
				show.value="+";
			}else if (show.value=="+"){
				document.getElementById(show.hiddenDivId).style.display="";
				show.value="-";
			}
	}
	//退出按钮功能
	/*function exit(){
		 alertMsg.confirm("当前页面录入的信息将不被保存，是否确定退出？",{
		 	okCall:function(){
				navTab.closeCurrentTab();
		 	}
		 });
	}*/
