<%@ page language="java" contentType="text/html; charset=utf-8" pageEncoding="UTF-8"%><%@ include file="/clm/pages/common/commonJsCss.jsp" %>
<%@ taglib uri="/struts-tags" prefix="s"%>
<%@ taglib uri="http://www.git.com.cn/taglib/udmp/field" prefix="Field"%>

 
<script type="text/javascript">
if (isApprove == "approve") {
	//审批选中第一个保项结论
	if($("#approve_checkBody tr", navTab.getCurrentPanel()).find("input[type='radio']").length!=0){
		$("#approve_checkBody tr", navTab.getCurrentPanel()).find("input[type='radio']").eq(0).attr("checked","checked");
		queryLiabConclusion();
	}
	//禁用页面操作域
	$("#matchQuery", navTab.getCurrentPanel()).attr("disabled",true);
	$("#matchSave", navTab.getCurrentPanel()).attr("disabled",true);
	
	var obj=$("div#approve_safeProjct", navTab.getCurrentPanel());
	//如果不是  上一步 和 下一步按钮
	obj.find("button").each(function(){
		$(this).attr("disabled",true);
	});
	//输入框 复选框 单选按钮  控制
	obj.find("input").each(function(){
		$(this).attr("disabled",true);
	});
	//下拉框
	obj.find("select").each(function(){
		$(this).attr("disabled",true);
	});
	//a标签
	obj.find("a").each(function(){
		$(this).attr("disabled",true);
	});
	
	//多行文本框控制
    obj.find("textarea").each(function(){
		$(this).attr("disabled",true);
	}); 
	
    var obj=$("div#approve_safeProjctTwo", navTab.getCurrentPanel());
	//如果不是  上一步 和 下一步按钮
	obj.find("button").each(function(){
		var buttonName=$(this).text();
		if(buttonName!="上一步" && buttonName!="下一步"){
			$(this).attr("disabled",true);
		}
	});
}
	$(document).ready(function(){
		// 文档就绪,初始化jQuery插件\
		setTimeout("initTbody()", 100);
		$("#approve_isNormal", navTab.getCurrentPanel()).val("1");
	});
	function initTbody()
	{
		/**
		*避免回掉函数删除 重建 tbody造成 函数执行失败 
		*/
		if(0 == $("#approve_checkBody", navTab.getCurrentPanel()).parents(".grid").size()){
			setTimeout("initTbody()", 100);
			return;
		}
		$("#approve_checkBody", navTab.getCurrentPanel()).changeTbodyAction();

	}
	
	
	(function($){
		$.fn.changeTbodyAction  = function (){
			$trs = $(this).find('>tr');
			$grid = $(this).parents('.grid');
			$trs.each(
				function(index){
					$tr = $(this);
					$tr.unbind('click');
					$tr.click(function(){
						$tr = $(this);
						$trs.filter(".selected").removeClass("selected");
						$tr.addClass("selected");
						$tr.find('input:radio:eq(0)').attr('checked',true);
						//***********************  将每行的单选按钮设置为选中  ************end
						var sTarget = $tr.attr("target");
						if (sTarget) {
							if ($("#"+sTarget, $grid).size() == 0) {
								$grid.prepend('<input id="'+sTarget+'" type="hidden" />');
							}
							$("#"+sTarget, $grid).val($tr.attr("rel"));
						}

					});
					
				
				}
					
			);
			
		}
	
	})(jQuery);
	
	/* //保单条款查看
	function showPolicy(policyCode){
		window.open("clm/audit/getPolicyAddrs_CLM_claimMatchResultAction.action?policyCode="+policyCode);
	} */
  function showPolicy(busiProdCode){
		 
		
		//data.message.replace(/\&/g,"%26")
		var urlValue= "mobcss/policyed/" ;//替换掉&符号
		
		navTab.openTab("queryClaimImages", "clm/common/queryPolicyMsgUrl_CLM_commonQueryAction.action?productCodeOriginal="+busiProdCode+"&urlValue="+urlValue, {title:'保单条款查看'});
     }  
/*function closeAuditMatch(){
		alertMsg.confirm("当前页面录入的信息将不被保存，是否确定退出？",{
			okCall:function(){
				navTab.closeCurrentTab();
			}
		});
 }*/
	function claimMatchNext(id,caseId){
		if (isApprove != "approve") {
			$.ajax({
				'url':"clm/audit/claimMatchNext_CLM_claimMatchResultAction.action?caseId="+caseId,
				'type':'post',
				'success':function(data){
						var data = eval("(" + data + ")");
						if(data.statusCode==300){
							alertMsg.error(data.message);
						}
						if(data.statusCode==200){
							next_approve(id,caseId);
						}
					},
					'error':function(){
						alertMsg.error("系统出险异常，请联系管理员");
					}
			});
		}else{
			next_approve(id,caseId);
		}
	}
	function queryLiabConclusion(){
		var claimLiabId = $("#approve_checkBody tr", navTab.getCurrentPanel()).find("input[name='approve_r']:checked").parents("td").find("input:eq(1)").val();
		$.ajax({
			'url':"clm/audit/queryLiabConclusion_CLM_claimMatchResultAction.action",
			'type':'post',
			'data':{'claimLiabVO.claimLiabId':claimLiabId},
			'async':false,
			'success':function(data){
					var data = eval("(" + data + ")");
					//设置保项赔付结论
					if(data.liabConclusion!=0){
						$("a[name='ClaimLiabVO.liabConclusion']", navTab.getCurrentPanel()).val(data.liabConclusion);
						$("a[name='ClaimLiabVO.liabConclusion']", navTab.getCurrentPanel()).empty();
						$("a[name='ClaimLiabVO.liabConclusion']", navTab.getCurrentPanel()).append($("select#approve_delivery").find("option[value="+data.liabConclusion+"]").attr("title"));
						$("select#approve_delivery", navTab.getCurrentPanel()).attr("value",data.liabConclusion);
						if(data.liabConclusion == 1 || data.liabConclusion == 2){
							$("#approve_isNormal", navTab.getCurrentPanel()).val("1");
							$("#approve_isNormalTwo", navTab.getCurrentPanel()).css("display","none");
							$("#approve_isNormal", navTab.getCurrentPanel()).css("display","block");
						}else if(data.liabConclusion == 3 || data.liabConclusion == 4){
							$("#approve_isNormal", navTab.getCurrentPanel()).val("0");
							$("#approve_isNormalTwo", navTab.getCurrentPanel()).css("display","none");
							$("#approve_isNormal", navTab.getCurrentPanel()).css("display","block");
						}else{
							$("#approve_isNormalTwo", navTab.getCurrentPanel()).css("display","block");
							$("#approve_isNormal", navTab.getCurrentPanel()).css("display","none");
						}
					}
					//设置不可抗辩标识
					if(data.resistFlag==1){
						$("#approve_resistFlag", navTab.getCurrentPanel()).attr("checked",true);
					}
					//设置调整金额
					if(data.adjustPay!=""&&data.adjustPay!="0"){
						$("#approve_adjustMoney", navTab.getCurrentPanel()).attr("value",data.adjustPay);
					}
					//设置调整原因
					if(data.liabAdjustReason!=""){
						$("a[name='ClaimLiabVO.liabAdjustReason']", navTab.getCurrentPanel()).val(data.liabAdjustReason);
						$("a[name='ClaimLiabVO.liabAdjustReason']", navTab.getCurrentPanel()).empty();
						$("a[name='ClaimLiabVO.liabAdjustReason']", navTab.getCurrentPanel()).append($("select#approve_adjustReason").find("option[value="+data.liabAdjustReason+"]").attr("title"));
						$("select#adjustReason", navTab.getCurrentPanel()).attr("value",data.liabAdjustReason);
					}
					//设置调整备注
					$("#approve_adjustRemark", navTab.getCurrentPanel()).empty();
					$("#approve_adjustRemark", navTab.getCurrentPanel()).append(data.adjustRemark);
					
				},
				'error':function(){
					alertMsg.error("系统出险异常，请联系管理员");
				}
		});
	}
</script>

<div class="panelPageFormContent" style="overflow:auto;" id="approve_pagerFormClaimMatchAudit">
	<%-- <form id="pagerForm" method="post" action="clm/audit/queryClaimLiabPage_CLM_claimMatchResultAction.action?leftFlag=0&menuId=${menuId}">
		<input type="hidden" name="pageNum" value="${currentPage.pageNo}" />
		<input type="hidden" name="numPerPage" value="${currentPage.pageSize}" />
		<input name="caseId" type="hidden" value="${claimCaseVO.caseId}"/>
	</form> --%>
	<div class="panelPageFormContent">
		<dl>
			<dt>赔案号</dt>
			<dd>
				<input name="claimCaseVO.caseNo"  value="${claimCaseVO.caseNo}"type="text" size="20" readonly="readonly"/>
		  	    <input id="approve_caseId" type="hidden" value="${claimCaseVO.caseId}"/>
			</dd>
		</dl>
		<dl>
			<dt>事件号</dt>
			<dd>
				<input name="claimAccidentVO.accidentNo" value="${claimAccidentVO.accidentNo}" class="readonly" type="text" size="20" readonly="readonly"/>
			</dd>
		</dl>
		<div class="divfclass">
			<h1>
				<img src="clm/images/tubiao.png">理赔计算信息
			</h1>
		</div>
		<div class="tabdivclassbr">
			<table class="list" width="100%">
				<thead>
					<tr>
						<th nowrap>赔付金额</th>
						<th nowrap>预付金额</th>
						<th nowrap>结算金额</th>
						<th nowrap>最终赔付金额</th>
						<th nowrap>拒赔金额</th>
					</tr>
				</thead>
				<tbody align="center">
					<s:iterator value="%{claimCaseList}" status="st" id="claimCase">
					<tr>
						<td><s:property value="#claimCase.calcPay"/></td>
                        <td><s:property value="#claimCase.advancePay"/></td>
                        <td><s:property value="#claimCase.balancePay"/></td>
                        <td><s:property value="#claimCase.actualPay"/></td>
                        <td><s:property value="#claimCase.rejectPay"/></td>
					</tr>
					</s:iterator>
				</tbody>
			</table>
		</div>
		
		<div class="divfclass">
			<h1>
				<img src="clm/images/tubiao.png">理赔类型计算信息
			</h1>
		</div>
		<div class="tabdivclassbr">
			<table class="list" width="100%">
				<thead>
					<tr>
						<th nowrap>序号</th>
						<th nowrap>理赔类型</th>
						<th nowrap>账单金额</th>
						<th nowrap>保单合计理算金额</th>
						<th nowrap>社保给付</th>
						<th nowrap>第三方给付</th>
						<th nowrap>核赔赔付金额</th>
					</tr>
				</thead>
				<tbody id="approve_claimTypeCalcResult">
					<s:iterator value="%{claimTypeCalcList}" status="st" id="cliamLiabInfo" >
					<tr align="center">
						<td>${st.index+1}</td>
						<td><s:property value="#cliamLiabInfo.claimType"/></td>
                        <td><s:property value="#cliamLiabInfo.sumAmountBill"/></td>
                        <td><s:property value="#cliamLiabInfo.calcPay"/></td>                   
                        <td><s:property value="#cliamLiabInfo.paidAmount"/></td>
                        <td><s:property value="#cliamLiabInfo.sumAmount"/></td>
                        <td><s:property value="#cliamLiabInfo.actualPay"/></td>
					</tr>
					</s:iterator>
				</tbody>
			</table>
		</div>

		<div class="divfclass">
			<h1>
				<img src="clm/images/tubiao.png">保单计算信息
			</h1>
		</div>
		<div class="tabdivclassbr">
			<table class="list" width="100%">
				<thead>
					<tr>
						<th nowrap>序号</th>
						<th nowrap>保单号</th>
						<th nowrap>理赔类型</th>
						<th nowrap>生效日期</th>
						<th nowrap>交至日期</th>
						<th nowrap>险种代码</th>
						<th nowrap>险种名称</th>
						<th nowrap>理算金额</th>
						<th nowrap>账户价值</th>
						<th nowrap>风险保额</th>
						<th nowrap>保单条款</th>
					</tr>
				</thead>
				<tbody>
					<s:iterator value="%{claimProductList}" status="st"  id="claimBusiProd">
					<tr align="center">
						<td>${st.index+1}</td>
						<td><s:property value="#claimBusiProd.policyCode"/></td>
						<td>
						<Field:codeValue tableName="APP___CLM__DBUSER.T_CLAIM_TYPE" value="${claimBusiProd.claimType}"/>
						</td>
						<td><s:date name="#claimBusiProd.validDate" format="yyyy-MM-dd"/></td>
						<td></td>
						<td><s:property value="#claimBusiProd.busiProdCode"/></td>
						<td><s:property value="#claimBusiProd.productNameStd"/> </td>
						<td><s:property value="#claimBusiProd.calcPay"/></td>
						<td><s:property value="#claimBusiProd.accountValue"/></td>
						<td><s:property value="#claimBusiProd.insuredAmount"/></td>
						<td><a href="javascript:showPolicy('${claimBusiProd.busiProdCode}')">查看</a></td>
					</tr>
					</s:iterator>
				</tbody>
			</table>
		</div>

		<div class="divfclass">
			<h1>
				<img src="clm/images/tubiao.png">保项计算信息
			</h1>
		</div>
		<div class="tabdivclassbr">
			<table class="list" width="100%">
				<thead>
					<tr>
						<th nowrap>选择</th>
						<th nowrap>序号</th>
						<th nowrap>保单号</th>
						<th nowrap>保险责任</th>
						<th nowrap>责任起期</th>
						<th nowrap>责任止期</th>
						<th nowrap>宽限天数</th>
						<th nowrap>保额</th>
						<th nowrap>年度红利</th>
						<th nowrap>终了红利</th>
						<th nowrap>理算金额</th>
						<th nowrap>出险期间标识</th>
						<th nowrap>给付代码</th>
						<th nowrap>预算金额</th>
						<th nowrap>调整金额</th>
					</tr>
				</thead>
				<tbody id="approve_checkBody">
					<s:iterator value="%{queryClaimLiabList}"  status="st" id="claimLiab">
					<tr align="center"  target="ListIdInfo" rel="${claimLiabId}">
						<td><input type="radio" name="approve_r" onclick="queryLiabConclusion()"><input type="hidden" value='${claimLiabId}'></td>
						<td> ${st.index+1}</td>
						<td><s:property value="#claimLiab.policyCode"/></td>
						<td><s:property value="#claimLiab.liabName" /></td>
						<td><s:date name="#claimLiab.liabStartDate" format="yyyy-MM-dd"/></td>
						<td><s:date name="#claimLiab.liabEndDate" format="yyyy-MM-dd"/></td>
						<td><s:property value="#claimLiab.gracePeriod" /></td>
						<td><s:property value="#claimLiab.proAmount"/></td>
						<td><s:property value="#claimLiab.proBonusSa"/></td>
						<td><s:property value="#claimLiab.payAmount"/></td>
						<td><s:property value="#claimLiab.calcPay" /><input type="hidden" name="approve_calcPay" value="<s:property value='#claimLiab.calcPay'/>"/></td>
						<td></td>
						<td>
						<s:if test="#claimLiab.liabConclusion == 5">	
							<input type="radio" name="r3${st.index}" value="1" disabled>给
							<input type="radio" name="r3${st.index}" value="2" checked disabled>不给
						</s:if>
						<s:elseif test="#claimLiab.liabConclusion != 5 && #claimLiab.liabConclusion != null">
							<input type="radio" name="r3${st.index}" value="1" checked disabled>给
							<input type="radio" name="r3${st.index}" value="2" disabled>不给
						</s:elseif>
						<s:else>
							<input type="radio" name="r3${st.index}" value="1" checked disabled>给
							<input type="radio" name="r3${st.index}" value="2" disabled>不给
						</s:else>
						</td>
						<td><s:property value="#claimLiab.advancePay"/></td>
						<td><s:property value="#claimLiab.adjustPay"/></td>
					</tr>
					</s:iterator>
				</tbody>
			</table>
		</div>
		<%-- <div class="panelBar" >
			<div class="pages">
				<span>显示</span>
				<s:select  list="#{10:'10',20:'20',50:'50',100:'100',200:'200'}"  name="select" onchange="navTabPageBreak({numPerPage:this.value},'pagerFormClaimMatchAudit')" value="currentPage.pageSize">
	     		</s:select>
				<span>条，共${currentPage.total}条</span>		
			</div>
		<div class="pagination" targetType="navTab" totalCount="${currentPage.total}" numPerPage="${currentPage.pageSize}" pageNumShown="10" currentPage="${currentPage.pageNo}" rel="pagerFormClaimMatchAudit"></div>
		</div> --%>


		<div class="divfclass">
			<h1>
				<img src="clm/images/tubiao.png">保项赔付结论
			</h1>
		</div>
		<div id="approve_safeProjct">
				<div class="" >
					<form id="approve_Form"  onsubmit="return validateCallback(this,navTabAjaxDone);" class="required-validate" action="clm/register/claimMatchResultSave_CLM_claimMatchResultAction.action" method="post" target="navTab" rel="pagerForm2" >
						<input type="hidden" name="ClaimLiabVO.claimLiabId" id="approve_ClaimLiabId">	
						<input type="hidden" name="caseId" id="approve_caseId" value = "${caseId}">
						<!-- <input type="hidden" name="ClaimLiabVO.giveType" id="giveType"> -->
						<dl>
							<dt><font>*</font>赔付结论</dt>
							<dd>
								<Field:codeTable   name="ClaimLiabVO.liabConclusion"
											tableName="APP___CLM__DBUSER.T_CLAIM_LIAB_DECISION" cssClass="notuseflagDelete combox title comboxDD"
											nullOption="false" orderBy="code" id="approve_delivery" onChange = "confirmDeliVery()"/>
							</dd>
						</dl>
						<dl>
							<dt><font>*</font>是否常规给付</dt>
							<dd>
								<Field:codeTable cssClass="combox title"  name="ClaimLiabVO.isCommon"
											tableName="APP___CLM__DBUSER.T_YES_NO"
											nullOption="true" id="approve_isNormal" value="" orderBy="YES_NO desc" />
									    <select class="combox title"  id="approve_isNormalTwo" style="display:none;">
									     <option title="无" value="">无</option>
									    </select>
							</dd>
						</dl>
						<dl>
							<dt>不可抗辩标识</dt>
							<dd><input type = "checkbox" name="ClaimLiabVO.resistFlag" value="1" id="approve_resistFlag">是</dd>
						</dl>
						<dl>
							<dt>调整金额</dt>
							<dd><input type="text" name="ClaimLiabVO.adjustPay" maxlength="18" id="approve_adjustMoney" onchange="addReasonReq()" class="number"></dd>
						</dl>
						<dl>
							<dt>调整原因</dt>
							<dd>
								<Field:codeTable   name="ClaimLiabVO.liabAdjustReason"
											tableName="APP___CLM__DBUSER.T_CLAIM_ADJUST_TYPE" orderBy="substr(code,0,1)" cssClass="notuseflagDelete combox title comboxDD"
											nullOption="true" id="approve_adjustReason" />
							</dd>
						</dl>
						<dl style="width:1000px;height:auto;">
							<dt>调整备注</dt>
							<dd>
								<textarea maxlength="1000" name="ClaimLiabVO.adjustRemark" id="approve_adjustRemark" style="width:280%;height:50px"></textarea>
							</dd>
						</dl>
						<div class="formBarButton">
							<ul>
								<li >
									<button type="button" class="but_blue" id="approve_savaButton" onclick="checkSave()" >保存</button>
								</li>
							</ul>
						</div>
					</form>
				</div>
	</div>
	<div class="formBarButton" id="approve_safeProjctTwo">
		<ul>
			<li>
				<button type="button" class="but_blue" onclick="prev_approve('3','${caseId}')">上一步</button>
			</li>
			<!-- <li><div class="buttonActive"><div id="matchSave" class="buttonContent"><button type="submit">保存</button></div></div></li> -->
			<li><button type="button" class="but_blue" onclick="claimMatchNext('5','${caseId}')">下一步</button></li>
			<li><button type="button" class="but_blue" onclick="exit();">退出</button></li>
		</ul>
	</div>
	</div>
</div>

