<%@ page language="java" contentType="text/html; charset=utf-8"
	pageEncoding="UTF-8"%><%@ include file="/clm/pages/common/commonJsCss.jsp" %>
<%@ taglib uri="/struts-tags" prefix="s"%>
<%@ taglib uri="http://www.git.com.cn/taglib/udmp/field" prefix="Field"%>
<!--add xuyz_wb 分支流程 查询豁免处理页面--> 
<script type="text/javascript">
//-------页面禁止操作--------
		var obj=$("div#approveRemitBranchFlow", navTab.getCurrentPanel());
		//输入框 复选框 单选按钮  控制
		obj.find("input").each(function(){
			$(this).attr("disabled",true);
		});
		//下拉框
		obj.find("select").each(function(){
			$(this).attr("disabled",true);
		});
		//多行文本框控制
	    obj.find("textarea").each(function(){
			$(this).attr("disabled",true);
		});
		$(".waiverManage", navTab.getCurrentPanel()).each(function(){
			$(this).attr("disabled",false);
		});
		//隐藏dwz封装的添加中的input
		function hidentRowNum(){
			obj.find("a").each(function(){
				$(this).attr("disabled",true);
			});
		}
		setTimeout('hidentRowNum()',100);
	//查询豁免详细信息
	function findApproveWaiverManage(obj){
		var caseId = $("#caseId", navTab.getCurrentPanel()).val();
		var value = $(obj).val();
		var policyId = $(obj).parent().parent().find("td").eq(2).find("input").val();
		var busiItemId = $(obj).parent().parent().find("td").eq(3).find("input").val();
		var itemId = $(obj).parent().parent().find("td").eq(4).find("input").val(); 
		if("1" == value){
			$(obj).attr("checked",false);
			$(obj).attr("value","0");
			$("#remitManageId", navTab.getCurrentPanel()).find("input").val(null);
			$("#remitManageId", navTab.getCurrentPanel()).find("select").val(null);
			$("#remitManageId", navTab.getCurrentPanel()).find("textarea").val(null);
		} else {
			$(obj).attr("value","1");
			$.ajax({
				'url':'clm/audit/findWaiverManage_CLM_remitManageAction.action?claimCaseVO.caseId='+caseId+'&claimCaseVO.policyId='+policyId+'&claimCaseVO.busiItemId='+busiItemId+'&claimCaseVO.itemId='+itemId+'&isApprove='+isApprove,
				'type':'post',
				'datatype':'json',
				'success':function(data){
				    var data=eval("("+data+")");
				    //起交日期
					$("#startPayDate",navTab.getCurrentPanel()).val(data.validdateDateString);
					//缴费止期
					$("#paidupdate",navTab.getCurrentPanel()).val(data.paidupString);
				    //缴费频率
					$("#initialTypeId",navTab.getCurrentPanel()).val(data.chargeName);
					//每期保费
					$("#nextPrem",navTab.getCurrentPanel()).val(data.nextPrem);
					//实际保费
					$("#waiverPremAf",navTab.getCurrentPanel()).val(data.nextPrem);
					//累计保费
					$("#totalPremAf",navTab.getCurrentPanel()).val(data.totalPremAf);
					//交至日期
					$("#dueDate",navTab.getCurrentPanel()).val(data.payDueDateString);
					//豁免标志
					$("#waiverFalg",navTab.getCurrentPanel()).val(data.isWaived);
					//豁免起期
					$("#waiverStart",navTab.getCurrentPanel()).val(data.waiveStartString);
					//豁免止期
					$("#waiverEnd",navTab.getCurrentPanel()).val(data.waiveEndString);
					//豁免原因
					$("#waiverReason",navTab.getCurrentPanel()).val(data.waiveReason);
					//豁免期数
					$("#waivePeriod",navTab.getCurrentPanel()).val(data.policyPeriod);
					//总豁免保费
					$("#waiveAmt",navTab.getCurrentPanel()).val(data.waiveAmt);
					//豁免描述
					$("#waiveDesc",navTab.getCurrentPanel()).val(data.waiveDesc);
				}
			});
		}
	}
	//弹出是否
	/* function exit(){
		 alertMsg.confirm("当前页面录入的信息将不被保存，是否确定退出？",{
		 	okCall:function(){
				navTab.closeCurrentTab();
		 	}
		 });
	} */
</script>
<div class="pageContent pageHeader">
	<form method="post">
		<div class="panelPageFormContent">
			<dl>	
				<dt>赔案号</dt>
				<dd>
				 <input name="" type="text" size="17" value="${claimCaseVO.caseNo }" readonly="readonly"/> 
				<input id="caseId" type="hidden" name="claimCaseVO.caseId" value="${claimCaseVO.caseId}">
				<dd>
		    </dl> 
		    <dl>	
				<dt>事件号</dt>
				<dd>
				<input name="accident.accidentNo" type="text" size="17" value="${accident.accidentNo}" readonly="readonly"/>
				</dd> 
		    </dl>
		</div>
		<div id="approveRemitBranchFlow" class="tabdivclassbr main_tabdiv">
		<table class="list" style="width: 100%;">
			<thead>
				<tr>
					<th nowrap>选择</th>
					<th nowrap>序号</th>
					<th nowrap>保单号</th>
					<th nowrap>保单险种号</th>
					<th nowrap>险种代码</th>
					<th nowrap>险种名称</th>
					<th nowrap>险种状态</th>
					<th nowrap>宽限期</th>
					<th nowrap>交至日期</th>
					<th nowrap>缴费止期</th>
				</tr>
			</thead>
			<tbody>
				<s:iterator value="currentPage.pageItems" status="st" var="cp">
					<tr> 
						<td><input type="radio" name="radio" class="waiverManage" onclick="findApproveWaiverManage(this);"  value="0"/></td>
						<td>${st.index+1}</td>
						<td><input type="hidden" value="${cp.policyId}"/>${cp.policyCode}</td>
						<td><input type="hidden" value="${cp.busiItemId}"/>${cp.busiItemId}</td>
						<td><input type="hidden" value="${cp.itemId}"/>${cp.productCodeStd}</td>
						<td>${cp.productNameStd}</td>
						<td>
							<Field:codeValue tableName="APP___CLM__DBUSER.T_LIABILITY_STATUS" value="${cp.liabilityState}"/> 
						</td>
						<td>60</td>
						<td><s:date name='#cp.payDueDate' format='yyyy-MM-dd'/></td>
						<td><s:date name='#cp.paidupDate' format='yyyy-MM-dd'/></td>
					</tr>
				</s:iterator>
			</tbody>

		</table>
		<div class="panelBar">
			<div class="pages" Width="60%">
				<span>显示</span>
				<s:select list="#{10:'10',20:'20',50:'50',100:'100',200:'200'}"
					name="select" onchange="navTabPageBreak({numPerPage:this.value})"
					value="currentPage.pageSize">
				</s:select>
				<span>条，共${currentPage.total}条</span>
			</div>
			<div class="pagination" targetType="navTab"
				totalCount="${currentPage.total}"
				numPerPage="${currentPage.pageSize}" pageNumShown="10"
				currentPage="${currentPage.pageNo}">
			</div>
		</div>
	</div>
	<div>
<!-- 		<table class="list" style="width: 100%;" id="remitManageId"> -->

			<div >
<!-- 				<h1>豁免详细信息11</h1> -->
				<div class="divfclass">
			     	 <h1>
			       		  <img src="clm/images/tubiao.png">豁免详细信息
			   		 </h1>
			    </div>
				<div id="div" class="panelPageFormContent">
					<dl>
						<dt>起交日期</dt>
						<dd>
							<input id="startPayDate" value="" readonly="readonly"/>
						</dd>
					</dl>
					<dl>
						<dt>缴费止期</dt>
						<dd>
							<input name="" id="paidupdate" 
								 value="" readonly="readonly"/>
						</dd>
					</dl>
					<dl>
						<dt>缴费频率
						<dt>
						<dd>
							<input name="" id="initialTypeId"  readonly="readonly">
						</dd>
					</dl>
					<dl>
						<dt>每期保费</dt>
						<dd>
							<input id="nextPrem" value=""  readonly="readonly">
						</dd>
					</dl>
					<dl>
						<dt>实际保费</dt>
						<dd>
							<input name="" id="waiverPremAf" readonly="readonly">
						</dd>
					</dl>
					<dl>
						<dt>累计保费</dt>
						<dd>
							<input name="" id="totalPremAf" readonly="readonly">
						</dd>
					</dl>
					<dl>
						<dt>交至日期</dt>
						<dd>
							<input name="claimCaseVO.dueDate" value="" id="dueDate" readonly="readonly">
						</dd>
					</dl>
					<dl>
						<dt><font class="point" color="red">* </font>免交标志</dt>
						<dd>
						    <Field:codeTable cssClass="combox title"  name="claimCaseVO.isWaived" tableName="APP___CLM__DBUSER.T_YES_NO" value="" nullOption="true" id="waiverFalg"/>
						</dd>
					</dl>
					<dl>
						<dt><font class="point" color="red">* </font>豁免起期</dt>
						<dd>
							<input type="expandDateYMDRO" name="claimCaseVO.waiveStart"
								id="waiverStart" size="17" onPropertychange="updateWaiveStart(this)"/>
						</dd>
					</dl>
					<dl>
						<dt><font class="point" color="red">* </font>豁免止期</dt>
						<dd>
							<input type="expandDateYMDRO" name="claimCaseVO.waiveEnd"
								id="waiverEnd" size="17" onPropertychange="updateWaiveEnd(this)"/>
						</dd>
					</dl>
					<dl>
						<dt>豁免原因<font class="point" color="red">*</font></dt>
						<dd>
							<Field:codeTable cssClass="combox title"  name="claimCaseVO.waiveReason" tableName="APP___CLM__DBUSER.T_EXEMPT_REASON" value="" nullOption="true" id="waiverReason"/> 
						</dd>
					</dl>
					<dl>
						<dt>豁免期数</dt>
						<dd>
							<input id="waivePeriod"  value="" readonly="readonly" />
						</dd>
					</dl>
					<dl>
						<dt>总豁免保费</dt>
						<dd>
							<input type="expandNumber" id="waiveAmt" name="claimCaseVO.waiveAmt" value="" readonly="readonly"/>
						</dd>
					</dl>
					
					<dl style="width: 100%;height: auto;">
						<dt>豁免描述</dt>
						<dd><textarea id="waiveDesc" rows="3" cols="100"></textarea></dd>
					</dl>
				</div>

			</div>
<!-- 		</table> -->
	</div>
	</form>
</div>