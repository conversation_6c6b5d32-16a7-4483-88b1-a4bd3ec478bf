<%@ page language="java" contentType="text/html; charset=utf-8" pageEncoding="UTF-8"%><%@ include file="/clm/pages/common/commonJsCss.jsp" %>
<%@ taglib uri="/struts-tags" prefix="s"%>
<%@ taglib uri="http://www.git.com.cn/taglib/udmp/field" prefix="Field"%>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<meta http-equiv="pragma" content="no-cache">
<meta http-equiv="cache-control" content="no-cache">
<meta http-equiv="expires" content="0"> 

 
<script type="text/javascript" language="javascript" src="clm/pages/approve/approveDatacollect.js"></script>
<script type="text/javascript" >
var taskId = '${taskId}';
if (isApprove == "approve") {
	$("#approve_tbodyIds", navTab.getCurrentPanel()).find("tr").each(function(){
		var now = $(this).find("td:eq(1)").find("input").val();
		if (now.length != 0) {
			var dateNow = new Date(now.replace(/-/g, '/'));
			var birth = $("#approve_accBirthIdsaudit", navTab.getCurrentPanel()).val();
			var dateBirth = new Date(birth.replace(/-/g, '/'));
			var age = (dateNow.getFullYear() - dateBirth.getFullYear() + 1);
			$(this).find("td:eq(2)").find("input").val(age);
			
		}
	});
	//审批 任务号
	if(taskId!=null&&taskId!=""){
		$("#approveTaskId", navTab.getCurrentPanel()).val(taskId);
	}
	//----------------申请人信息DIV-------------------
	var obj=$("div#approve_approveDataCollectApprove", navTab.getCurrentPanel());
	//如果不是  上一步 和 下一步按钮
	obj.find("button").each(function(){
		var buttonName=$(this).text();
		if(buttonName!="上一步" && buttonName!="下一步"){
			$(this).attr("disabled",true);
		}
	});
	//输入框 复选框 单选按钮  控制
	obj.find("input").each(function(){
		if(!$(this).is("[type='radio']")){
			$(this).attr("disabled",true);
		}
	});
	//下拉框
	obj.find("select").each(function(){
		$(this).attr("disabled",true);
	});
	//a标签
	obj.find("a").each(function(){
		$(this).attr("disabled",true);
	});
	//----------------申请信息---------------
	var objApplyMessage=$("div#approve_approveDataCollectApplyMes", navTab.getCurrentPanel());
	//输入框 复选框 单选按钮  控制
	objApplyMessage.find("input").each(function(){
		$(this).attr("disabled",true);
	});
	//下拉框
	objApplyMessage.find("select").each(function(){
		$(this).attr("disabled",true);
	});
	//a标签
	objApplyMessage.find("a").each(function(){
		$(this).attr("disabled",true);
	});
	//-------------------受托人信息DIV---------------
	var objTrustee=$("div#approve_approveDataCollectTrustee", navTab.getCurrentPanel());
	//输入框 复选框 单选按钮  控制
	objTrustee.find("input").each(function(){
		$(this).attr("disabled",true);
	});
	//下拉框
	objTrustee.find("select").each(function(){
		$(this).attr("disabled",true);
	});
	//a标签
	objTrustee.find("a").each(function(){
		$(this).attr("disabled",true);
	});
	
	//----------------出险信息DIV---------------
	var objCustomer=$("div#approve_approveDataCollectCustomer", navTab.getCurrentPanel());
	//如果不是  上一步 和 下一步按钮
	objCustomer.find("button").each(function(){
		var buttonName=$(this).text();
		if(buttonName!="上一步" && buttonName!="下一步"){
			$(this).attr("disabled",true);
		}
	});
	//输入框 复选框 单选按钮  控制
	objCustomer.find("input").each(function(){
		$(this).attr("disabled",true);
	});
	//下拉框
	objCustomer.find("select").each(function(){
		$(this).attr("disabled",true);
	});
	//a标签
	objCustomer.find("a").each(function(){
		$(this).attr("disabled",true);
	});
	//多行文本框控制
    objCustomer.find("textarea").each(function(){
		$(this).attr("disabled",true);
	}); 
	
	//---------------按钮控制--------------
    var objButton=$("div#approve_auditDataCollectButton", navTab.getCurrentPanel());
    //如果不是  上一步 和 下一步按钮
	objButton.find("button").each(function(){
		var buttonName=$(this).text();
		if(buttonName=="保存"){
			$(this).attr("disabled",true);
		}
	});
	setTimeout(function() {
		$("#approve_addTable", navTab.getCurrentPanel()).siblings(".button").find("button").attr("disabled","disabled");
		$("#approve_tableId", navTab.getCurrentPanel()).siblings(".button").find("button").attr("disabled","disabled");
	},1);
}
dwz_comboxDD_myarray = new Array('province2','comboxDD_city2','comboxDD_area2');
dwz_comboxDD_mybox = navTab.getCurrentPanel();
//隐藏dwz封装的添加中的input
function hidentRowNum(){
	$("[name='dwz_rowNum']").css("display","none");
}
//延迟dwz
setTimeout('hidentRowNum()',100);
</script>

<div onload="javascript:" >
	<!-- 页面带进来的赔案号，事件号，只读 -->
	<div class="panelPageFormContent" >  
		<dl>
			<dt>赔案号：</dt>
			<dd><input name="" type="text" size="17" value="${claimCaseVO.caseNo }" readonly="readonly"/> </dd>
		</dl>
		<dl>
			<dt>事件号：</dt>
			<dd><input id="approve_ccidentNoId" name="accident.accidentNo" type="text" size="17" value="${accident.accidentNo}" readonly="readonly"/> 
				<input type="hidden" id="approve_flagIdI" name="accident.flag" disabled="disabled"/>
			</dd>
		</dl>
		 <input name="caseId" type="hidden" size="17" value="${caseId }" readonly="readonly"/>
		 <input id="approve_caseId" type="hidden" size="17" value="${caseId }" />
	     <input id="approve_caseNo" type="hidden" value="${claimCaseVO.caseNo }" />
	   </div>  
		 <div  style="background:#fafafa;" id="approve_approveDataCollectApprove">
				<div class="divfclass">
					<h1>
						<img src="clm/images/tubiao.png">申请人信息
					</h1>
				</div>
				<div class="panelPageFormContent">
				<table  class ="list" id = "approve_table"  style="width:100%;">
			   		 <thead>
						 <tr >
						   <th>选择</th>
						   <th>序号</th>
						   <th>申请人姓名</th>
						   <th>证件号码</th>
						   <th>手机</th>
						   <th>地址</th>
						   <th>操作</th>
						</tr>
			 		 </thead>
				    <tbody id="approve_auditregister" class="tbody" >
                     <s:iterator value="applicantVOlist" status="st">
						<tr >
							<td>
								<input type="radio" name="123" flag="approve_radioflag" id="approve_radio${listId}" 
									onclick="findMessage(${listId});" value="0"
									<s:if test="#st.index==0">checked
									</s:if> />
									<script type="text/javascript">
										findMessage("${listId}");
									</script>
							</td>
							<td>
								<div align="center" >${st.index+1}</div>
							</td>
							<td>
								<div align="center"><s:property value="clmtName"></s:property></div>
							</td>
							<td>
								<div align="center"><input type="hidden" id="approve_auditClmtCertiNo" size="20" name="auditClmtCertiNo"  value="${clmtCertiNo }" /><s:property value="clmtCertiNo"></s:property></div>
							</td>
							<td>
								<div align="center"><input type="hidden" id="approve_auditPhoneNumber" size="20" name="auditPhoneNumber"  value="${clmtMp }" /><s:property value="clmtMp"></s:property></div>
							</td>
							<td>
								<div align="center"><s:property value="clmtDistreactCode"></s:property></div>
							</td>
							<td>
								<a class="btnDel" href="javascript:;" onclick="todeleteApplicant(${listId})" ref="current">删除</a>
							</td>
						</tr>
					</s:iterator>
                   </tbody>																		
				</table>
				
		 <form method="post" class="pageForm required-validate"  
		 		onsubmit ="return validateCallback(this, dialogAjaxDone);" novalidate="novalidate">					
			   <div class="panelPageFormContent">	
				 <input type="hidden" name="claimCaseVO.caseId" value="${claimCaseVO.caseId}" id="approve_claimCaseVOcASEID" />
				 <div class="pageFormContent" id="approve_appliacntMessage">
                  <input name="applicant.listId" id="approve_listId" value="" type="hidden"> 
					<dl>
						<dt>申请人姓名<font class="point" color="red">*</font></dt>
						<dd><input name="applicant.clmtName"  type="text" id="approve_clmtName" value="${applicantVOlist[0].clmtName}"/></dd>
					</dl>
					<dl>
						<dt>性别<font class="point" color="red">*</font></dt>
						<dd>
							 <Field:codeTable cssClass="combox title"  id="approve_clmtSex" name="applicant.clmtSex" tableName="APP___CLM__DBUSER.T_GENDER" whereClause="gender_code in(1,2)"  value="${applicantVOlist[0].clmtSex}" /> 
						</dd>
					</dl>
					<dl>
						<dt>与出险人关系<font class="point" color="red">*</font></dt>
						<dd>
						    
							 <Field:codeTable   name="applicant.clmtInsurRelation" tableName="APP___CLM__DBUSER.T_LA_PH_RELA"  id="approve_clmtInsurRelation" value="${applicantVOlist[0].clmtInsurRelation}" cssClass="notuseflagDelete combox title selectChange"/>  
						</dd>
					</dl>
					<dl>
						<dt>证件类型<font class="point" color="red">*</font></dt>
						<dd>
							 <Field:codeTable cssClass="combox title"  name="applicant.clmtCertiType" tableName="APP___CLM__DBUSER.T_CERTI_TYPE" defaultValue="0"  id="clmtCertiType" value="${applicantVOlist[0].clmtCertiType}"
							 		whereClause="code in ('0','5','2','b','4','1','8')"  orderBy="decode(code,'0','001','5','002','2','003','b','004','4','005','1','006','8','007', code)" />
						</dd>
					</dl>
					<dl>
						<dt>证件号码<font class="point" color="red">*</font></dt>
						<dd><input name="applicant.clmtCertiNo" id="approve_clmtCertiNo" value="${applicantVOlist[0].clmtCertiNo}" /></dd>
					</dl>
					<dl>
						<dt>证件有效期<font class="point" color="red">*</font></dt>
						<dd>
							<input type="expandDateYMD" name="applicant.clmtCertiValidDate" id="approve_clmtCertiValidDate"  size="17" readonly="readonly"/>
							<a class="inputDateButton" id="approve_isLongValids" href="javascript:;">选择</a>
							 <input name="applicant.isLongValid" id="approve_isLongValid" type="checkbox" value="0"/>长期 
						</dd>
					</dl>
					<dl>
						<dt>与投保险人关系<font class="point" color="red">*</font></dt>
						<dd>
						 
                        <Field:codeTable  name="applicant.clmtHolderRelation" tableName="APP___CLM__DBUSER.T_LA_PH_RELA"  id="approve_clmtHolderRelation" value="${applicantVOlist[0].clmtHolderRelation}" cssClass="notuseflagDelete combox title selectChange"/>  
						</dd>
					</dl>
					<dl>
						<dt>手 机<font class="point" color="red">*</font></dt>
						<dd><input id="approve_clmtMp" size="20" type="expandMobile" name="applicant.clmtMp" value="${applicantVOlist[0].clmtMp}" onkeyup="this.value=this.value.replace(/\D/g,'')" /></dd>
					</dl>
					<dl>
						<dt>固定电话<font class="point" color="red">*</font></dt>
						<dd><input id="approve_clmtTel" size="20" name="applicant.clmtTel" value="${applicantVOlist[0].clmtTel}" /></dd>
					</dl>
					
				  <dl>
					<dt>电子邮件</dt>
					<dd><input name="applicant.clmtMail" id="approve_clmtMail" size="20" class="email" class="required" value="${applicantVOlist[0].clmtMail}"/></dd>
				  </dl>
				  <dl>
					<dt>职业</dt>
					<dd>
					<input name="applicant.clmtProfession" id="approve_clmtProfession" type="text" size="20" />
					<%-- <Field:codeTable cssClass="selectToInput"  name="applicant.clmtProfession" tableName="APP___CLM__DBUSER.T_JOB_CODE"  id="clmtProfession" />   --%>
					</dd>
				  </dl>
				  <dl>
					<dt>国籍</dt>
					<dd>
					<Field:codeTable cssClass="combox title"  name="applicant.clmtNation" tableName="APP___CLM__DBUSER.T_COUNTRY"  id="approve_clmtNation" defaultValue="CHN" value="${applicantVOlist[0].clmtNation}" whereClause="1=1"  orderBy="decode(country_code,'CHN','A',country_code)"/>  
					</dd>
				  </dl>
				   <dl>
					<dt>领款人银行</dt>
					<dd>
						 <Field:codeTable cssClass="combox title"  name="applicant.clmtBank" tableName="APP___CLM__DBUSER.T_BANK" id="approve_clmtBank" value="${applicantVOlist[0].clmtBank}" />
					</dd>
				  </dl> 
				  <dl>
					<dt>银行户名</dt>
					<dd><input name="applicant.clmtAccountName" id="approve_clmtAccountName"class="" type="text" size="20" value="${applicantVOlist[0].clmtAccountName}"  /></dd>
				  </dl>
				  <dl>
					<dt>银行账号</dt>
					<dd><input type="expandBankAccount" name="applicant.clmtAccount" id="approve_clmtAccount" size="17" style="width:127px" value="${applicantVOlist[0].clmtAccount}" /></dd>
				  </dl>
				  <table style="width:80%;">
						<dl>
						   <td>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;联系地址 <font class="point" color="red">*</font></td>
            			   <td>省/直辖市</td>
						   <td>
							 <s:select cssClass="notuseflagDelete selectToInput  comboxDD" id="clmtProvince" list="provinceMap"
									listKey="code" listValue="name"
									name="applicant.clmtProvince"  ref="clmtCity"
									refUrl="clm/sign/province_CLM_dataCollectAction.action?districtVO.code={value}"
									initval="%{province1}" >
							</s:select> 
            			   </td>
                           <td>市</td>							
               			   <td>
                              <%-- <select class="selectToInput comboxDD"  class="comboxDD" id="clmtCity"
									name="applicant.clmtCity" ref="clmtDistreact"
									refUrl="clm/sign/getCity_CLM_dataCollectAction.action?districtVO.code={value}" >
									<!-- <option value="110100">北京市市辖区</option> -->
								</select> --%>
								<select class="selectToInput comboxDD"  class="comboxDD" id="clmtDistreact"
									name="applicant.clmtCity">
									<option value="${applicant.clmtCity }">${applicant.clmtCityName }</option>
								</select> 
                           </td>
                          <td>区/县</td>
               			   <td>
                              <select class="selectToInput comboxDD"  class="comboxDD" id="clmtDistreact"
									name="applicant.clmtDistreact">
									<option value="${applicant.clmtDistreact }">
									${applicant.clmtDistreactName } 
									</option>
								</select> 
								<%-- <select class="selectToInput"  id="clmtDistreact" name="applicant.clmtDistreact" >
								<option value="applicant.clmtDistreact"></option>
									</select>       --%>                                               
                          </td>
                          <td>乡镇/街道</td>
                          <td>
                              <input name="applicant.clmtStreet"  id="clmtStreet" value="${applicantVOlist[0].clmtStreet}" />                           
                          </td>
               		  </dl>
               	  </table>
					<!-- <input type="hidden" name="claimApplicantVO.tabJson" id="tabJson"> -->
					<div class="formBarButton" >
						<ul>
							<li>
								<button type="button"  class="but_gray" onclick="addApplicant1()">添加申请人</button>
							</li>
						</ul>
					</div>
			  </div>
			 </div>
		 </form>
		</div>
		</div>
		
		<form method="post" id = "approve_Forms" class="pageForm required-validate"  
				onsubmit ="return validateCallback(this, dialogAjaxDoneAudit);" novalidate="novalidate">      
				<input type="hidden" name="claimCaseVO.caseId" value="${claimCaseVO.caseId}" id="approve_claimCaseVOcASEID" />
				<input type="hidden" name="claimCaseVO.caseNo" size="30" value="${claimCaseVO.caseNo}" /> 
				<input type="hidden" name="accident.accidentNo" size="30" value="${accident.accidentNo}" id="approve_cidentNoIdId"/> 
				<input type="hidden" name="claimCaseVO.accidentId" value="${claimCaseVO.accidentId}" size="20" />
				<input type="hidden" name="claimCaseVO.insuredId" value="${claimCaseVO.insuredId}" size="20" id="approve_insuredidiD"/>
				<input type="hidden" id="approve_expiryDate" name="claimCaseVO.expiryDate" value="${claimCaseVO.expiryDate}" size="20" /> 
			    <input type="hidden" id="approve_acceptTimeIdaudit" name="claimCaseVO.acceptTime" value="<s:date name='claimCaseVO.acceptTime' format='yyyy-MM-dd'/>" size="20" /> 
			    <input type="hidden" id="approve_sysTimeIdaudit" name="claimCaseVO.systemDateString" value="${claimCaseVO.systemDateString}"/>
				<div class="panelPageFormContent" id="approve_approveDataCollectApplyMes">
				 <dl>
					<dt>申请类型</dt>
					<dd>
						 <Field:codeTable cssClass="combox title"  name="claimCaseVO.caseApplyType"
								tableName="APP___CLM__DBUSER.T_APPLY_TYPE" 
								value="${claimCaseVO.caseApplyType}" />
					</dd >
				  </dl>	
				  <dl>
					<dt>申请日期</dt>
					<dd>
						<input type="expandDateYMD" name="claimCaseVO.applyDate"
							value="<s:date name='claimCaseVO.applyDate' format='yyyy-MM-dd'/>" readonly="readonly"/>
						<a class="inputDateButton" href="javascript:;">选择</a>
					</dd>
				  </dl>
				  <dl>
					<dt>受理时间</dt>
					<dd>
						<input type="expandDateYMD" name="claimCaseVO.acceptTime"
							value="<s:date name='claimCaseVO.acceptTime' format='yyyy-MM-dd'/>" readonly="readonly"/>
						<a class="inputDateButton" href="javascript:;">选择</a>
					</dd>
				</dl>
				  <dl>
					<dt>管理机构</dt>
					<dd>
						<input id="" type="text" name="claimCaseVO.organCode" value="86" size="1" readOnly /> 
						<input id="" type="text" value="${claimCaseVO.organName}" readOnly size="46" />
					</dd>
				</dl>
				 <%-- <dl>
					<dt>受理人</dt>
					<dd>
							${claimCaseVO.userRealName}
						<input name="claimCaseVO.acceptorId"
							value="${claimCaseVO.acceptorId}" id="" type="hidden">
					</dd>
				</dl> --%>
				<dl >
					<dt >签收人</dt>
					<dd >
							${claimCaseVO.signerCode}&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;${claimCaseVO.signerName}
					</dd>
				</dl>
				<dl >
					<dt >立案人</dt>
					<dd >
						${claimCaseVO.registerCode}&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;${claimCaseVO.registerName}
					</dd>
				</dl>
			 </div>
		 <div class="panelPageFormContent" id="approve_approveDataCollectTrustee">
	        <div class="divfclass">
				<h1>
					<img src="clm/images/tubiao.png">受托人信息
				</h1>
			</div>
		<div>
			<div class="panelPageFormContent" >
                 <dl>
					<dt>受托人类型</dt>
					<dd>
						<Field:codeTable cssClass="combox title"  id="trusteeTypeSign"
							name="claimCaseVO.trusteeType" tableName="APP___CLM__DBUSER.T_TRUSTEE_TYPE"
							value="${claimCaseVO.trusteeType}" nullOption="true" />
					</dd>
				</dl>
               <dl>
					<dt>受托人代码</dt>
					<dd>
						<input name="claimCaseVO.trusteeCode" type="text" id=""
							value="${claimCaseVO.trusteeCode}" />
					</dd>
				</dl>
			    <dl>
					<dt>受托人姓名</dt>
					<dd>
						<input name="claimCaseVO.trusteeName" type="text"
							value='<s:property value="claimCaseVO.trusteeName"/>' id="" />
					</dd>
				</dl>
			    <dl>
				   <dt>证件类型</dt>
				   <dd>
       			 	<Field:codeTable cssClass="combox title"  name="claimCaseVO.trusteeCertiType"
 						tableName="APP___CLM__DBUSER.T_CERTI_TYPE" defaultValue="0"  value="${claimCaseVO.trusteeCertiType}" 
 						whereClause="code in ('0','5','2','4','1','e','h','i','d','f','g','8')"  
									orderBy="decode(code,'0','001','5','002','2','003','4','004','1','005','e','006','h','007','i','008','d','009','f','010','g','011','8','012', code)" />
			       </dd>
			    </dl>
			    <dl>
					<dt>手机</dt>
					<dd>
						<input name="claimCaseVO.trusteeMp" type="expandMobile" id="approve_assignMobile"
							value="${claimCaseVO.trusteeMp}" onkeyup="this.value=this.value.replace(/\D/g,'')" />
					</dd>
				</dl>
			    <dl>
				   <dt>固定电话</dt>
				   <dd><input name="claimCaseVO.trusteeTel" type="expandPhone" value="${claimCaseVO.trusteeTel}" /></dd>
			    </dl>
			    <dl>
				   <dt>证件号码</dt>
				   <dd><input name="claimCaseVO.trusteeCertiCode" id="trusteeCertiCode"   value="${claimCaseVO.trusteeCertiCode}"/></dd>
			    </dl>
			   <dl>
					<dt>上门签收日期</dt>
					<dd>
						<input type="expandDateYMD" name="claimCaseVO.doorSignTime" class="date"
							id=""
							value="<s:date name='claimCaseVO.doorSignTime' format='yyyy-MM-dd'/>" readonly="readonly"/>
						<a class="inputDateButton" href="javascript:;">选择</a>
					</dd>
				</dl>
			</div>
		</div>
		</div> 
		
		
		<div class="panelPageFormContent" id="approve_approveDataCollectInsured">
        <div class="divfclass">
			<h1>
				<img src="clm/images/tubiao.png">出险人信息
			</h1>
		</div>
		<div>
			<div class="panelPageFormContent" >
				<table class ="list" style="width:100%;">
					<thead>
						<tr>
							<th>出险人姓名</th>
							<th>客户号</th>
							<th>证件号码</th>
							<th>性别</th>
							<th>出生日期</th>
						</tr>
					</thead>
					<tbody id="approve_claimAccidentCUSInfo" align="center">
						<tr>
							<td>${customerVO.customerName }</td>
							<td>${customerVO.customerId }</td>
							<td>${customerVO.customerCertiCode }</td>
							<td>
								<s:if test="customerVO.customerGender == 1">
									<input type="hidden" name="customerVO.customerGender" id="" size="20" value="1" />
									        男
								</s:if>
								<s:if test="customerVO.customerGender == 2">
									<input type="hidden" name="customerVO.customerGender" id="" size="20"
										value="2">
										女
								</s:if>
								<s:if test="customerVO.customerGender==9">
									<input type="hidden" size="20" value="9"
										name="customerVO.customerGender" />
										未知
								</s:if>
							</td> 
							<td><s:date name='customerVO.customerBirthday' format='yyyy-MM-dd' /></td>
						</tr>
					</tbody>
				</table>
			</div>
		</div>
		</div>

	<div class="panelPageFormContent" id="approve_approveDataCollectCustomer">
        <div class="divfclass">
			<h1>
				<img src="clm/images/tubiao.png">出险信息
			</h1>
		</div>
		<div>
			<div class="panelPageFormContent" >
				<dl>
					<dt>出险人姓名</dt>
					<dd>
							${customerVO.customerName}
						 <input id="claimAccInsuredId" name="customerVO.customerId"
							value="${customerVO.customerId}" type="hidden" /> 
					</dd>
				</dl>
			    <dl>
							<dt>证件号码</dt>
							<dd>
									${customerVO.customerCertiCode }
									<input id="approve_accBirthIdsaudit" type="hidden" name="customerVO.customerBirthday"
									value="<s:date name='customerVO.customerBirthday' format='yyyy-MM-dd' />" class="date" />
							</dd>
				</dl>
			    <dl>
				   <dt>性别</dt>
				   <dd>
						 <s:if test="customerVO.customerGender == 1">
								男
						</s:if>
						<s:if test="customerVO.customerGender == 2">
							           女
						</s:if>
						<s:if test="customerVO.customerGender==9">
							          未知
						</s:if> 
				   </dd>
			    </dl>
			       <dl>
				   <dt>事故日期<font class="point" color="red">*</font></dt>
				   <dd>
				   		 <%--  <input type="expandDateYMD" name="accident.accDate" id="accident.accDate"
								value="<s:date name='accident.accDate' format='yyyy-MM-dd'/>" />
				   		  <a class="inputDateButton" href="javascript:;">选择</a> --%>
				   		 <input onclick="changeReasoninfo();" id="approve_accidentTimeIdsaudit" type="expandDateYMD" class="date" name="accident.accDate" value="<s:date name='accident.accDate' format='yyyy-MM-dd'/>"
							 	postField="keyword" suggestFields="accDate" suggestUrl="clm/report/pages/report/queryExistedEvent.jsp"
								 lookupGroup="accident"> 
						<a class="btnLook" href="clm/report/queryExistedEventInit_CLM_insertReportInfoInputAction.action"
								 lookupGroup="accident" onClick="myReportOption(this);">查找带回</a> 
				   </dd>
			    </dl>
			  <dl>
							<dt>治疗情况</dt>
							<dd>
								<Field:codeTable cssClass="combox title"  id="approve_cureStatusId" name="claimCaseVO.cureStatus"
									value="${claimCaseVO.cureStatus}" tableName="APP___CLM__DBUSER.T_CURE_STATUS" orderBy="substr(code,1,2)"/>
							</dd>
						</dl>
			    
			     <dl>
							<dt>治疗医院</dt>
							<dd>
								<input id="approve_inputHospital" name="claimCaseVO.cureHospital" value="${claimCaseVO.cureHospital}" size="10" type="hidden" /> 
								<input id="approve_cureHospital" name="claimCaseVO.hospitalName" value="${hospitalServiceVO.hospitalName}" type="text" postField="keyword"  
									suggestFields="hospitalLevel,hospitalName"
									suggestUrl="clm/report/pages/report/bringBackHospital.jsp"
									lookupGroup="claimCaseVO" /> 
									<a class="btnLook" href="clm/report/showHospital_CLM_hospitalInfoQueryAction.action?leftFlag=0&menuId=${menuId}" 
									lookupGroup="claimCaseVO">查找带回</a>
							</dd>
						</dl>
			     <dl>
							<dt>出险原因<font class="point" color="red">*</font></dt>
							<dd>
								<Field:codeTable cssClass="combox title"  id="approve_accReasonId" name="accident.accReason"
									value="${accident.accReason}" tableName="APP___CLM__DBUSER.T_CLAIM_NATURE" whereClause="code in (1,2)" onChange="accidentReason();" orderBy="code"/>
							</dd>
						</dl>
			    <dl>
							<dt>意外细节</dt>
							<dd>
								<input id="approve_findaccidentDetails" name="claimCaseVO.accidentDetail"
									value="${claimCaseVO.accidentDetail}" type="hidden" />
								<input id="approve_accidentDetail" name="claimCaseVO.DetailDesc" 
									value="${accidentDetailVO.detailDesc}" type="text" readonly
									 postField="keyword" 
									suggestFields="accDetailCode,accDetailDesc"
									suggestUrl="clm/report/pages/report/findAccidentDetails.jsp"
									lookupGroup="claimCaseVO" /> <a class="btnLook"
									href="clm/report/queryPage_CLM_accidentDetailsAction.action?leftFlag=0&menuId=${menuId}"
									lookupGroup="claimCaseVO">查找带回</a>
							</dd>
						</dl> 
			    
			     <dl>
							<dt>重大疾病</dt>
							<dd>
								<Field:codeTable cssClass="combox title"  id="seriousDiseaseId"
									name="claimCaseVO.seriousDisease"
									value="${claimCaseVO.seriousDisease}" nullOption="true" tableName="APP___CLM__DBUSER.t_la_type" whereClause="1 = 1" orderBy="code"></Field:codeTable>
							</dd>
				</dl>
			    <dl>
				   <dt>医生姓名</dt>
				   <dd> <input name="claimCaseVO.doctorName" id="approve_doctorName" value="${claimCaseVO.doctorName}" type="text" /></dd>
			    </dl>
			    <dl>
				   <dt>科室</dt>
				   <dd>
					  <Field:codeTable   name="claimCaseVO.medDept" tableName="APP___CLM__DBUSER.T_MEDICAL_DEPT" nullOption="true" cssClass="notuseflagDelete combox title comboxDD" defaultValue=" " showInput="true" showInputId="showInput" value="${claimCaseVO.medDept }" orderBy="code"/>
					  <%-- <input id="approve_showInput" type="text" value="${medicalDeptVO.name }"> --%>
				   </dd>
			    </dl>
			    
			       <table id="approve_accAddress">
						<tr>
						   <td>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;出险地点 <font class="point" color="red">*</font></td>
            			   <td>&nbsp;&nbsp;&nbsp;&nbsp;省/直辖市</td>
						   <td>
							 <s:select cssClass="notuseflagDelete selectToInput comboxDD" id="approve_province2" list="provinceMap"
									listKey="code" listValue="name"
									name="accident.accProvince" ref="comboxDD_city2"
									refUrl="clm/sign/province_CLM_dataCollectAction.action?districtVO.code={value}"
									initval="%{province2}" >
							</s:select> 
            			   </td>
                           <td>市</td>							
               			   <td>
                              <select class="selectToInput comboxDD"   class="comboxDD" id="approve_comboxDD_city2"
									name="accident.accCity" ref="comboxDD_area2"
									refUrl="clm/sign/getCity_CLM_dataCollectAction.action?districtVO.code={value}">
									<option value="110100">北京市市辖区</option>
								</select>
                           </td>
                           <td>区/县</td>
               			   <td>
                               <select class="selectToInput comboxDD"  class="comboxDD" id="approve_comboxDD_area2"
									name="accident.accDistreact">
									<option value="110101">北京市东城区</option>
								</select>                                                          
                                                                                             
                          </td>
                          <td>乡镇/街道</td>
                          <td>
                              <input name="accident.accStreet"  id="approve_accStreet" value="${accident.accStreet}"/>                              
                          </td>
               		  </tr>
               	  </table>

			    <dl>
              				<dt>事故描述<font class="point" color="red">*</font></dt>
              				<dd><textarea  class="required" name="accident.accDesc" id="accidentDescId" rows="2" cols="100" maxlength="1000" readonly>${accident.accDesc }</textarea></dd>
                   </dl> 

			</div>
		</div>
		
		
		<div class="panelPageFormContent">
			<div class="panelPageFormContent" >
			 <input name="claimAccidentResultVO.caseId" value="${caseId}" type="hidden">
			 	  <div class="tabdivclass" >
			 	  		<table  class="list nowrap itemDetail" addButton="添加出险结果" id="approve_addTable" style="width:100%;">
						<thead>
							<tr>
								<th type="text" name="items[#index#].itemInt" readonly defaultVal="#index#" size="9" width="0%" fieldClass="digits">序号</th>
								<th type="enum" name="claimAccidentResultVO.accResult1"
									enumUrl="clm/pages/html/accidentResultOne.jsp?accType=" size="12">出险结果1</th>
								<th type="enum" name="claimAccidentResultVO.accResult2"
									enumUrl="clm/pages/html/accidentResultTwo.jsp" size="12">出险结果2</th>
								<th type="del" width="60">操作</th>
							</tr>
						</thead>
						<tbody class="tbodys">
							<s:if test="listResultVo==null||listResultVo.size()==0">
								<tr class="unitBox">
									<td>
										<input name="items[0].itemInt" class="digits textInput focus" type="text" size="9"  value="1" readonly="readonly"/>
									</td>
									<td >
										<select class="combox title accResult1"  id="approve_accResult1" name="claimAccidentResultVO.accResult1" onchange="change(this);"  >
											<option value="" class=" ">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; </option> 
										 	<s:iterator value="accident1vos" var="accident1">
										 		<option value="<s:property value="#accident1.code"/>" class="<s:property value="#accident1.name"/>" ><s:property value="#accident1.code"/>-<s:property value="#accident1.name"/></option>
										 	</s:iterator>
										 </select>
										 <input name="accident1vo.name" id="approve_accResult1Name" value="${accident1vo.name}" type="text"  /> 
									</td>
									<td >	
										<select class="combox title accResult2"   id="approve_accResult2" name="claimAccidentResultVO.accResult2" onchange="change2(this);"  >
											 <option value="" class=" ">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; </option> 
										</select>
										<input name="accident2vo.name" value="${accident2vo.name}" type="text" id="accResult2Name" />
									</td>
									<td><a class="btnDel"></a></td>
								</tr>
							</s:if>
							<s:else>
								<s:iterator value="listResultVo" var="rlist" status="st">
									<tr>
										<td>
											<input name="items[0].itemInt" class="digits textInput focus" type="text" size="9"  value="${st.index+1}" readonly="readonly"/>
										</td>
										<td >
											<select class="combox title accResult1"  id="approve_accResult1" name="claimAccidentResultVO.accResult1"  onchange="change(this);"   >
											 	<option value="<s:property value="#rlist.accResult1"/>"  ><s:property value="#rlist.accResult1"/>-<s:property value="#rlist.accResultDesc1"/></option>
											 </select>
											 <input name="claimAccidentResultVO.accResultDesc1" id="accResult1Name" value="${rlist.accResultDesc1}" type="text"  /> 
										</td>
										<td >	
											<select class="combox title accResult2"   id="approve_accResult2" name="claimAccidentResultVO.accResult2" onchange="change2(this);"  >
												 <option value="<s:property value="#rlist.accResult2"/>"  ><s:property value="#rlist.accResult2"/>-<s:property value="#rlist.accResultDesc2"/></option>
											</select>
											<input name="claimAccidentResultVO.accResultDesc2" value="${rlist.accResultDesc2}" type="text" id="accResult2Name" />
										</td>
										<td><a class="btnDel"></a></td>
									</tr>
								</s:iterator>
							</s:else>
						</tbody>
					</table>
			 	  </div>
				</div>
			<div>
			
			<input name="claimSubCaseVO.caseId" value="${caseId}" type="hidden" />
			<div class="tabdivclass" >
				<table id="approve_tableId" style="width: 100%" id="approve_flag"
						class="list nowrap itemDetail" addButton="添加理赔类型" width="100%">
						<thead>
							<tr>
								<th type="enum" name="claimSubCaseVO.claimType"
									enumUrl="clm/pages/html/claimType.jsp" size="12">理赔类型</th>
								<th type="enum" name="claimSubCaseVO.claimDate"
									enumUrl="clm/pages/html/accidentTime.html" size="12">出险日期</th>
								<th type="enum" name="claimSubCaseVO.accAgeString"
									enumUrl="clm/pages/html/accidentAge.html" size="12">出险年龄</th>
								<th type="del" width="60">操作</th>
							</tr>
						</thead>
						<tbody id="approve_tbodyIds">
							<input type="hidden" id="approve_tclaimJson" name="claimSubCaseVO.clmJson" value=""/>
							<s:if test="subCaseVoList==null||subCaseVoList.size()==0">
								<tr>
									<td >
										<Field:codeTable cssClass="combox title"  name="claimSubCaseVO.claimType"  id="approve_claimType" whereClause="1=1"
											value="${claimType}" tableName="APP___CLM__DBUSER.T_CLAIM_TYPE"  orderBy=" decode(code,'08',1,'03',2,'10',3,'04',4,'02',5,'01',6,'11',7,'06',8,'07',9)"></Field:codeTable>
									</td>
									<td>
										<input id="approve_accTimeIdaudit" type="expandDateYMD" name="claimSubCaseVO.claimDate" value="" 
											class="date" onPropertychange="countAge(this)" readonly="readonly"/>
										<a class="inputDateButton" href="javascript:;">选择</a>
									</td>
									
									<td>
										<input type="text" name="claimSubCaseVO.accAgeString"
										readonly="readonly" value="${subCase.accAgeString}" />
									</td>
									<td>
										<a class="btnDel"></a>
									</td>
								</tr>
							</s:if>
							<s:else> 
							<s:iterator value="subCaseVoList" status="sub" var="subCase">
								<tr>
									<td>
										<Field:codeTable cssClass="combox title"  name="claimSubCaseVO.claimType" 
										value='${subCase.claimType}' tableName="APP___CLM__DBUSER.T_CLAIM_TYPE" whereClause="code not in (11)"   orderBy=" decode(code,'08',1,'03',2,'10',3,'04',4,'02',5,'01',6,'11',7,'06',8,'07',9)"></Field:codeTable>
										
									</td>
										
									 <td>
										<input id="accTimeIdaudit" type="expandDateYMD" name="claimSubCaseVO.claimDate" value="<s:date name='#subCase.claimDate' format='yyyy-MM-dd'/>"
											class="date" onPropertychange="countAge(this)" readonly="readonly"/>
										<a class="inputDateButton" href="javascript:;">选择</a>
									</td> 
									
									<td>
										<input type="text" name="claimSubCaseVO.accAgeString" readonly="readonly"
											value="${subCase.accAgeString}" />
									</td>
									<td>
										<a class="btnDel"></a>
									</td>
								</tr>
							</s:iterator>
						 </s:else> 
					</tbody>
				</table>
			</div>
		
		</div>
		</div>
	</form>	
	
		<!-- 页面底部：保存、下一步、退出 -->
		<div class="formBarButton" id="approve_auditDataCollectButton">								
			<ul>
			   <li><div class="button"><div class="buttonContent"><button type="button" class="button" onclick="autoAudit()">自动审核处理</button></div></div></li>
			   <li>
					<div class="button">
						<div class="buttonContent">
							<a href="clm/audit/claimRiskReportConfigInfo_CLM_claimRiskReportConfigAction.action?caseId=${caseId}&caseNo=${claimCaseVO.caseNo}&insuredId=${claimCaseVO.insuredId}&accidentId=${claimCaseVO.accidentId}" target="dialog" rel="page2" type="button" width="1000" height="550"><span>产生风险评估报告</span></a>
						</div>
					</div>
				</li>
			    <li>
			    	<div class="button">
			    		<div class="buttonContent">
			    			<a href="clm/report/checkContractHangUp_CLM_contractHangUpAction.action?caseId=${caseId}" rel="page2"  target="dialog"  type="button" width="1000" height="550">
			    			<span>保单挂起/解挂</span> </a>
			    		</div>
			    	</div>
				</li>
			    <li>
					<div class="button">
						<div class="buttonContent">
							<a id="auditClaimCheckListIni" href=" clm/audit/auditClaimCheckListIni_CLM_auditClaimCheckListAction.action?caseId=${caseId}&caseNo=${claimCaseVO.caseNo}" target="dialog" rel="page2" type="button" width="1000" height="550"><span>查看单证</span></a>
						</div>
					</div>
				</li>
				<li><div class="buttonActive"><div class="buttonContent"><button type="button" onclick="approve_autoMatchClacLogClick()">保存</button></div></div></li>

				<li><div class="button"><div class="buttonContent">
				<button type="button" id="nestbuttonDataOne" class="button" onclick="next_approve('2',${caseId});" >下一步</button>
				</div></div></li>
				<li><div class="button" id="exit"><div class="buttonContent"><button type="button" onclick="exit()" class="close">退出</button></div></div></li>
			</ul>	
		</div>   

	</div>
</div>
					
						