	
	//这里是涉案险种的点击事件，通过点击会显示它的终止结论
	$(function() {
		$("input[name='claimBusiProdVO.claimBusiProdId']").live("click",function (){
			var dealConclusion = $(this).next().val();
			$("#approve_contractDealPage", navTab.getCurrentPanel()).find("#approve_BusiProdDealConclusion").val(dealConclusion);
			AutoPolicyBalance($(this).next().next().next().val(),$(this).next().next().val());
		});
	});


	//时间转换
	function date2str(x,y){
		var z={y:x.getFullYear(),
               M:x.getMonth()+1,
               d:x.getDate(),
               h:x.getHours(),
               m:x.getMinutes(),
               s:x.getSeconds()
		};
		return y.replace(/(y+|M+|d+|h+|m+|s+)/g,
				function(v){return ((v.length>1?"0":"")+eval('z.'+v.slice(-1))).slice(-(v.length>2?v.length:2))});
	};
	//保单的点击事件，通过点击保单刷新它的涉案保单，同时显示它的终止结论
	function trendsValueApprove(node){
		var busiProdDeal = $("#approve_busiProdDeal", navTab.getCurrentPanel());
		var policyDeal = $("#approve_policyDeal", navTab.getCurrentPanel());
		var claimBusiProd = $("#approve_claimBusiProd", navTab.getCurrentPanel());
		var dealConclusion = $(node).next().val();
	 	$("#approve_contractDealPage", navTab.getCurrentPanel()).find("#approve_PolicyDealConclusion").val(dealConclusion);
	 	claimFireEvent($("#approve_contractDealPage", navTab.getCurrentPanel()).find("#approve_PolicyDealConclusion"));
 
	 	$.ajax({
			'url':"clm/audit/queryClaimBusiProdByPolicyId_CLM_contractDealAction.action",
			'data':{'claimPolicyVO.policyId':$(node).next().next().val(),'claimPolicyVO.caseId':$("#approve_caseId").attr("value")},
			'datatype':'Json',
			 'type':'post',
			 'success':function(data){
				var data=eval("("+data+")");
				var claimBusiProdVOs = data["claimBusiProdVOs"];
				var claimPolicyDealConclVOs = data["claimPolicyDealConclVOs"];
				var claimBusiProdDealConclVOs = data["claimBusiProdDealConclVOs"];
				claimBusiProd.empty();
				//涉案险种
				for(var i=0;i<claimBusiProdVOs.length;i++){
					var expireDate = '';
					if(claimBusiProdVOs[i].expireDate!=null&&claimBusiProdVOs[i].expireDate!='null'){
						expireDate=new Date(claimBusiProdVOs[i].expireDate.time);
						expireDate=date2str(expireDate,"yyyy-MM-dd");
					}
					var BusiProd = "<tr align='center'>"+
										"<td width='5%;'>"+
											"<input type='radio' name='claimBusiProdVO.claimBusiProdId' value="+claimBusiProdVOs[i].claimBusiProdId+" />"+
											"<input type='hidden' name='' value="+claimBusiProdVOs[i].dealConclusion+" />"+
											"<input type='hidden' name='' value="+claimBusiProdVOs[i].busiItemId+" />"+
											"<input type='hidden' name='' value="+$(node).next().next().val()+" />"+
										"</td>"+
										"<td width='5%;''>"+i+"</td>"+
										"<td width='10%;'>"+claimBusiProdVOs[i].policyCode+"</td>"+
										"<td width='10%;'>"+claimBusiProdVOs[i].busiItemId+"</td>"+
										"<td width='10%;'>"+claimBusiProdVOs[i].busiProdCode+"</td>"+
										"<td width='10%;'>"+claimBusiProdVOs[i].businessProduct.productNameSys+"</td>"+
										"<td width='10%;'>"+claimBusiProdVOs[i].statusName+"</td>"+
										"<td width='10%;'></td>"+
										"<td width='10%;'>"+expireDate+"</td>"+
									"</tr>";
					claimBusiProd.append(BusiProd);
					var busiDealConclusion = $("input[name='claimBusiProdVO.claimBusiProdId']", navTab.getCurrentPanel()).eq(0).next().val();
					$("input[name='claimBusiProdVO.claimBusiProdId']", navTab.getCurrentPanel()).eq(0).attr("checked",true);
					$("#approve_contractDealPage", navTab.getCurrentPanel()).find("#approve_BusiProdDealConclusion").val(busiDealConclusion);
					
					claimFireEvent($("#approve_contractDealPage", navTab.getCurrentPanel()).find("#approve_BusiProdDealConclusion"));
					 
				}
				//合同处理结果  保单层
				policyDeal.empty();
				for(var i=0;i<claimPolicyDealConclVOs.length;i++){
					var BusiProd = "<tr align='center'>"+
										"<td>"+(i+1)+"</td>"+
										"<td>"+claimPolicyDealConclVOs[i].policyCode+"</td>"+
										"<td>"+claimPolicyDealConclVOs[i].liabilityStatusStr+"</td>"+
										"<td>"+claimPolicyDealConclVOs[i].dealConclusionStr+"</td>"+
										"<td>"+date2str(new Date(claimPolicyDealConclVOs[i].expiryDate.time),'yyyy-MM-dd')+"</td></tr>";
					policyDeal.append(BusiProd);				
				}
				//合同处理结果  险种层
				busiProdDeal.empty();
				for(var i=0;i<claimBusiProdDealConclVOs.length;i++){
					var BusiProd = "<tr align='center'>"+
					"<td>"+(i+1)+"</td>"+
					"<td>"+claimBusiProdDealConclVOs[i].policyCode+"</td>"+
					"<td>"+claimBusiProdDealConclVOs[i].busiItemId+"</td>"+
					"<td>"+claimBusiProdDealConclVOs[i].statusName+"</td>"+
					"<td>"+claimBusiProdDealConclVOs[i].dealConclusionStr+"</td>"+
					"<td>"+date2str(new Date(claimBusiProdDealConclVOs[i].expireDate.time),'yyyy-MM-dd')+"</td></tr>";
					busiProdDeal.append(BusiProd);				
				}
				//加载合同结算结果
				AutoPolicyBalance($(node).next().next().val(),claimBusiProdVOs[0].busiItemId);
				//getServivalType($(this).next().next().next().val(),$(this).next().next().val());
			 },
			 'error':function(){
					alertMsg.error("系统出险异常，请联系管理员");
			}
		});
	};
	//退出
	/*function closeAuditcContractDeal(){
		 alertMsg.confirm("当前页面录入的信息将不被保存，是否确定退出？",{
		 	okCall:function(){
				navTab.closeCurrentTab();
		 	}
		 });
	}*/
	
	var AutoPolicyBalance = function getAutoPolicyBalance(policyId,busiItemId){
		var caseId= $("#approve_caseId", navTab.getCurrentPanel());
		$.ajax({
			'url':"clm/audit/getAutoPolicyBalance_CLM_contractDealAction.action",
			'data':{'claimAdjustBusiVO.policyId':policyId,'claimAdjustBusiVO.busiItemId':busiItemId,'claimAdjustBusiVO.caseId':caseId.val()},
			'datatype':'Json',
			 'type':'post',
			 'success':function(data){
				 var data=eval("("+data+")");
				 var claimAdjustBusi = $("#approve_claimAdjustBusi", navTab.getCurrentPanel());
				 claimAdjustBusi.empty();
				 for(var i=0;i<data.length;i++){
					 $("#approve_dealAdjustReason", navTab.getCurrentPanel()).attr("value",data[i].busiAdjustReason);
					var adjustBusi = "<tr align='center'>"+
										"<td width='10%;'>"+data[i].adjustTypeName+"<input type='hidden' name='claimAdjustBusiVOs["+i+"].adjustBusiId' value="+data[i].adjustBusiId+" />"+
										"</td>"+
										"<td width='10%;'>"+data[i].feeAmount+"</td>"+
										"<td width='10%;'><input type='text' name='claimAdjustBusiVOs["+i+"].adjAmount' value='"+data[i].adjAmount+"' size='8'/></td>"+
										"<td width='5%;'>" +
											"<select class='combox' name='claimAdjustBusiVOs["+i+"].busiAdjustReason'>"+
												$("#approve_dealAdjustReason", navTab.getCurrentPanel()).html()+
											"</select>"+
										"</td>"+
										"<td width='10%;'><input type='text' name='claimAdjustBusiVOs["+i+"].remarks' value='"+data[i].remarks+"'/></td>"+
									"</tr>";
					claimAdjustBusi.append(adjustBusi);
				}
				 if (isApprove == "approve") {
					 var obj=$("div#approve_contractDealPage",navTab.getCurrentPanel());
						//如果不是  上一步 和 下一步按钮
						obj.find("button").each(function(){
							if($(this).text()!="下一步"&&$(this).text()!="上一步"){
								$(this).attr("disabled",true);
							}
						});
						//a标签
						obj.find("a").each(function(){
							$(this).attr("disabled",true);
						});
						
						//textarea标签
						obj.find("textarea").each(function(){
							$(this).attr("disabled",true);
						});
						obj.find("input").each(function(){
							if(!$(this).is("[type=radio]")){
								$(this).attr("disabled",true);
							}
						});
						obj.find("select").each(function(){
							$(this).attr("disabled",true);
						});
				}
			 },
			 'error':function(){
				 alertMsg.error("系统出险异常，请联系管理员");
			}
		});
	}