<%@ page language="java" contentType="text/html; charset=utf-8"
	pageEncoding="UTF-8"%><%@ include file="/clm/pages/common/commonJsCss.jsp" %>
<%@ taglib uri="http://www.git.com.cn/taglib/udmp/field" prefix="Field"%>

<%@ taglib uri="/struts-tags" prefix="s"%>
<% String path=request.getContextPath();  String basePath=path+"/"; %>
 
<script type="text/javascript" src="clm/pages/approve/toApproveConclusionInit.js"></script>
<script type="text/javascript" charset="UTF-8">
//既往赔案限制页面按钮
$(function(){
    var queryAlwaysClaim= $("#queryAlwaysClaim",navTab.getCurrentPanel()).attr("value") ;
	if(queryAlwaysClaim==1){
		//如果是 则不能保存
		$("#saveAndAffirm",navTab.getCurrentPanel()).attr("disabled","disabled");
		$("#saveAndAffirm2",navTab.getCurrentPanel()).attr("disabled","disabled");
	}
});
	//页面加载完毕执行的方法
	if (isclaimQuery == "yes") {
		//OneDIV
		var objOne=$("div#approveConclusionOne", navTab.getCurrentPanel());
		//多行文本框控制
	    objOne.find("textarea").each(function(){
			$(this).attr("disabled",true);
		}); 
	    //TwoDIV
		var objTwo=$("div#approveConclusionTwo", navTab.getCurrentPanel());
		//输入框 复选框 单选按钮  控制
		objTwo.find("input").each(function(){
			$(this).attr("disabled",true);
		});
		//下拉框
		objTwo.find("select").each(function(){
			$(this).attr("disabled",true);
		});
		//ThreeDIV
		var objThree=$("div#approveConclusionThree", navTab.getCurrentPanel());
		objThree.find("button").each(function(){
			var buttonName=$(this).text();
			if(buttonName!="上一步"){
				$(this).attr("disabled",true);
			}
		});
	}
	//反馈质检结果集成将页面禁用除了上一步
	$(function(){
		var flag="${param.inspectAfterFlag}";
		if(flag!=""){
			$("#approveRemark",navTab.getCurrentPanel()).attr("disabled",true);
			$("#approveDecision",navTab.getCurrentPanel()).attr("disabled",true);
			$("#approveCaseFlag",navTab.getCurrentPanel()).attr("disabled",true);
			$("#approveRejectReason",navTab.getCurrentPanel()).attr("disabled",true);
			$("[name='claimCaseVO.overCompFlag']",navTab.getCurrentPanel()).attr("disabled",true);
			$("button", navTab.getCurrentPanel()).each(function(){
				if($(this).text()!="上一步"){
					$(this).attr("disabled",true);
				}
			});
		}
	})
</script>

<body>
	<div id="toApproveConclusionInit" class="panelPageFormContent">
	<input type="hidden" id="queryAlwaysClaim" value="${param.queryAlwaysClaim}">
	<form action="clm/audit/saveApproveConclusion_CLM_addApproveConclusionAction.action?menuId=${menuId }" method="post"  
		id="toAuditConclusionInitform" class="pageForm required-validate" 
		onsubmit="return validateCallback(this, navTabAjaxDone)"  novalidate="novalidate">
		<dl>
			<dt>赔案号</dt>
			<dd>
				<input name="sn" type="text" size="30"   value="${claimCaseVO.caseNo}"
					readonly="readonly" />
				<input type="hidden" name="claimCaseVO.caseId"
					value="${claimCaseVO.caseId}" id="claimCaseVOcASEID" />
				<input type="hidden" value="${claimCaseVO.caseId}" id="caseId" />
			</dd>
		</dl>
		<dl>
			<dt>事 件 号</dt>
			<dd>
				<input name="sn" type="text" size="30" value="${claimAccidentVO.accidentNo}"
					readonly="readonly" />
			</dd>
		</dl>
	
		<div class="panelPageFormContent" id="approveConclusionOne">	
			<dl style="width:1000px;height:auto;">
				<dt ><font>*</font>审批意见（包括符号最多700汉字）</dt>
				<dd>
					<textarea id="approveRemark" onpropertychange="if(value.length>1000) value=value.substring(0,700)" style="width: 500px;" rows="2" cols="3" name="claimCaseVO.approveRemark">${claimCaseVO.approveRemark}</textarea>
				</dd>
			</dl>
		</div>
		
		<div class="panelPageFormContent" id="approveConclusionTwo">	
			<dl>
				<dt><font>*</font>审批结论</dt>
				<dd>
					<Field:codeTable cssClass="combox title"  id="approveDecision"
						name="claimCaseVO.approveDecision" tableName="APP___CLM__DBUSER.T_CLAIM_APPROVE_DECISION"
						value="${claimCaseVO.approveDecision}" nullOption="true" />
				</dd>
			</dl>
	
			<dl>
				<dt><font>*</font>案件标识</dt>
				<dd>
					<Field:codeTable cssClass="combox title"  id="approveCaseFlag"
						name="claimCaseVO.caseFlag" tableName="APP___CLM__DBUSER.T_CASE_LEVEL"
						value="${claimCaseVO.caseFlag}" nullOption="true" orderBy="code" whereClause="code in(1,3)"/>
				</dd>
			</dl>
			<dl id="ss_flag" >
			<dt >诉讼案件</dt>
				<dd >
					<select class="combox title"  name="claimCaseVO.isLawsuits" id="isLawsuits"  onchange="isLawsuitsChange()">
						<option value="0" <s:if test="claimCaseVO.isLawsuits == 0">selected</s:if>>否</option>
						<option value="1" <s:if test="claimCaseVO.isLawsuits == 1">selected</s:if>>是</option>
					</select>
				</dd>
			</dl>
			<dl>
				<dt>
					<input type="checkbox" size="5px" name="claimCaseVO.overCompFlag" value="1" <s:if test="claimCaseVO.overCompFlag==1">checked="checked"</s:if>/>超期补偿
				</dt>
				<dd ></dd>
			</dl>
			<dl>
				<dt><font>*</font>不通过原因</dt>
				<dd>
					<Field:codeTable cssClass="combox title"  id="approveRejectReason"
						name="claimCaseVO.approveRejectReason" tableName="APP___CLM__DBUSER.T_CLAIM_APPROVE_REJECT"
						value="${claimCaseVO.approveRejectReason}" nullOption="true" />
				</dd>
			</dl>
		</div>
		<s:if test="claimCaseVO.relatedNo!=null">
			<div class="divfclass">
				<h1>
					<img src="clm/images/tubiao.png">追偿款信息
				</h1>
			</div>				
				<div class="tabdivclassbr" >
					<table class="list" style="width: 100%;">
						<thead>
							<tr>
								<th nowrap>领款人</th>
							
								<th nowrap>通知书号</th>
							
								<th nowrap>应收金额</th>
							
								<th nowrap>收费方式</th>
							
								<th nowrap>费用状态</th>
							
								<th nowrap>收费日期</th>
							</tr>
						</thead>
						<tbody style="margin:0 auto;">
							<s:iterator value="claimPayVOs" var="claimPayVO">
								<tr align="center">
									<td><s:property value="#claimPayVO.claimPayeeVO.payeeName"/></td>
									<td></td>
									<td><s:property value="#claimPayVO.payAmount"/></td>
									<td>现金</td>
									<td>未收取</td>
									<td></td>
								</tr>
							</s:iterator>
						</tbody>
					</table>
				</div>
				<div >
						<div class="divfclass">
							<h1>
								<img src="clm/images/tubiao.png">消息提醒
							</h1>
						</div>	
					<div class="tabdivclassbr" >
						发送给业务员<br>
						<table class="list" class="table" width="100%">
							<thead>
								<tr align="center">
									<th nowrap>业务员代码</th>
									<th nowrap>业务员姓名</th>
									<th nowrap>短信发送</th>
									<th nowrap>邮件发送</th>
								</tr>
							</thead>
							<tbody>
								<tr>
						     		<td>
										<input type="text" name="" value="00001"/>	
						     		</td>
						       		<td>
										<input type="text" name="" value="张艾迪"/>	
									</td>
									<td>
										<input name="" type="checkbox" checked="checked"/>
										<input name="" type="text" maxlength="11" value="13692737392"/>
									</td>
									<td>
										<input name="" type="checkbox" checked="checked"/>
										<input name="" maxlength="100" type="text" value="<EMAIL>"/> 
									</td>
							     </tr>
								<tr>
						     		<td>
										<input type="text" name="" value="00002"/>	
						     		</td>
						       		<td>
										<input type="text" name="" value="陈桥"/>	
									</td>
									<td>
										<input name="" type="checkbox" checked="checked"/>
										<input name="" type="text" maxlength="11" value="18937474838"/>
									</td>
									<td>
										<input name="" type="checkbox" checked="checked"/>
										<input name="" maxlength="100" type="text" value="<EMAIL>"/> 
									</td>
							     </tr>
							</tbody>
						</table>
						<input name=""  type="checkbox" checked="checked"/>
						<input name="signConfirmationVO.isSendCustomer"  type="hidden"  value=""/>发送给客户
						<table class="list" class="table" width="50%">
							<thead>
								<tr align="center">
									<th width="10%" nowrap>姓名</th>
									<th width="15%" nowrap>短信发送</th>
									<th width="20%" nowrap>邮件发送</th>
								</tr>
							</thead>
							<tbody>
								<tr>
						     		<td>				     			
										<input type="text" maxlength="100" name="" value="47363839" />								
						     		</td>
						       		<td>
										<input name="" type="checkbox" checked="checked"/>
										<input name="" type="text" value="13692737392"/>
									</td>
									<td>			
										<input name="flag"  type="checkbox" checked="checked"/>
										<input name="" value="<EMAIL>"/>							
									</td>
						     	</tr>
							</tbody>
						</table>	
					</div>
				</div>
		</s:if>
		<div class="panelPageFormContent" id="approveConclusionThree">
			<div class="formBarButton">
				<ul>
					<li>
								<button type="button" class="but_blue" id="last" onclick="approveUpStep(${caseId});">上一步</button>
					</li>
				 
					<li >
						<button type="button" class="but_blue"  id="saveAndAffirm" onclick="approveDoSave();">保存</button>
					</li>
					<li >
						<button type="button" class="but_blue"  id="saveAndAffirm2" onclick="approveDoUpdate();">审批确认</button>
					</li>
					<li>
						<button type="button" class="but_blue"  onclick="exit()">退出 </button>
					</li>
				</ul>
			</div>
		</div>
	</form>
	</div>
</body>
