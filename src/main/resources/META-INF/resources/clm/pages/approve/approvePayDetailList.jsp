<%@ page language="java" pageEncoding="UTF-8"%><%@ include file="/clm/pages/common/commonJsCss.jsp" %>
<%@ taglib uri="/struts-tags" prefix="s"%>
<%@ taglib uri="http://www.git.com.cn/taglib/udmp/field" prefix="Field"%>
<!-- 赔付金额明细（医疗类） -->
<script type="text/javascript" >
</script>
<s:if test="payDetailListMedical.size!=0">
<ul class="main_ul">
	<li class="clearfix">
				<h5 hasborder="false"><b id="three" class="main_minuss"></b><span>医疗类</span></h5>
<div class="main_foldContent">
		<div class="main_bqtabdivbr">
				<!-- 医疗类 -->
<div class="panelPageFormContent" id="payDocumentDivTwoOne">
<div class="tabdivclassbr">
<table class="list" style="width: 100%;">
	<thead>
		<tr align="center">
			<td nowrap>选择</td>
			<td nowrap>序号</td>
			<td nowrap>保单号</td>
			<td nowrap>险种名称</td>
			<td nowrap>保险责任名称</td>
			<td nowrap>账单金额</td>
			<td nowrap>实际住院天数</td>
			<td nowrap>预付金额</td>
			<td nowrap>合同结算金额</td>
			<td nowrap>超期补偿金额</td>
			<td nowrap>实际赔付金额</td>
			<td nowrap>剩余有效保额</td>
			<td nowrap>合同状态</td>
		</tr>
	</thead>
	<tbody id="approve_payDetailListMedicalTbody">
		<s:iterator value="payDetailListMedical" var="status" status="var">
			<tr align="center">
				<td><input type="radio" name="r1" id="MedBody"/></td>
				<td><s:property value="#var.index+1"></s:property></td>
				<td><s:property value="policyCode"></s:property></td>
				<td><s:property value="productNameSys"></s:property></td>
				<td><s:property value="liabName"></s:property></td>
				<td><s:property value="sumAmount"></s:property><s:if test="sumAmount eq null">0</s:if></td>
				<td><s:property value="reallyDay"></s:property><s:if test="reallyDay eq null">0</s:if></td>
				<td><s:property value="advancePay"></s:property><s:if test="advancePay eq null">0</s:if></td>
				<td><s:property value="freeAmount"></s:property><s:if test="freeAmount eq null">0</s:if></td>
				<td>
						<s:property value="overdueMoney"></s:property><s:if test="overdueMoney eq null">0</s:if>
				</td>
				<td><s:property value="actualPay"></s:property><s:if test="actualPay eq null">0</s:if></td>
				<td><s:property value="remainEffPay"></s:property><s:if test="remainEffPay eq null">0</s:if></td>
				<td><Field:codeValue tableName="APP___CLM__DBUSER.T_LIABILITY_STATUS" value="${liabilityState}"/>
				<input id="clmRemark"  type="hidden" name="clmRemark" value="${clmRemark}" />
			    <input name="liabId"  value="${liabId}" type="hidden" title="<Field:codeValue value='${liabId}'  tableName='APP___CLM__DBUSER.t_liability' />" />
				
				</td>
			</tr>
		</s:iterator>
	</tbody>
</table>
</div>
<div class="panelPageFormContent main_tabdiv">
<dl style="width: 100%;height: auto">
				<dt>备注信息</dt>
				<dd >
					<textarea maxlength="5000" id="MedBody" name=""  rows="3" cols="70" readonly></textarea>
				</dd>	
			</dl>
		</div>	
</div>
			</div>
		</div>
	</li>
</ul>	 
</s:if>
                  
                   		

<s:if test="payDetailListUnMedical.size!=0">
<ul class="main_ul">
<li class="clearfix">
	<h5 hasborder="false"><b id="three" class="main_minuss"></b><span>非医疗类</span></h5>
<div class="main_foldContent">
	<div class="main_bqtabdivbr">
<!-- 非医疗类 -->
 <div class="panelPageFormContent" id="payDocumentDivTwoTwo">
<div >
	<div>
	<div class="panelPageFormContent" id="nomedical">
	<div class="tabdivclassbr" >
	
	<table class="list" style="width: 100%;">
<thead>
	<tr align="center">
		<td nowrap>选择</td>
		<td nowrap>序号</td>
		<td nowrap>保单号</td>
		<td nowrap>险种名称</td>
		<td nowrap>保险责任名称</td>
		<td nowrap>保险金额</td>
		<td nowrap>年度红利</td>
		<td nowrap>终了红利</td>
		<td nowrap>合同结算金额</td>
        <td nowrap>超期补偿金额</td>
		<td nowrap>实际赔付金额</td>
		<td nowrap>剩余有效保额</td>
		<td nowrap>合同状态</td>
	</tr>
</thead>
<tbody id="approve_payDetailListUnMedicalTbody">
	<s:iterator value="payDetailListUnMedical" var="status" status="var" >
	<tr>
		<td><input type="radio" name="r2" id="UnMedBody"/></td>
		<td><s:property value="#var.index+1"></s:property></td>
		<td><s:property value="policyCode"></s:property></td>
		<td><s:property value="productNameSys"></s:property></td>
		<td><s:property value="liabName"></s:property></td>
		<td><s:property value="amount"></s:property></td>
		<td><s:property value="bonusSa"></s:property><s:if test="bonusSa eq null">0</s:if></td>
		<td><s:property value="bonusSaEnd"></s:property><s:if test="bonusSaEnd eq null">0</s:if></td>
		<td><s:property value="freeAmount"></s:property><s:if test="freeAmount eq null">0</s:if></td>
		<td>
			<s:property value="overdueMoney"></s:property><s:if test="overdueMoney eq null">0</s:if>
		</td>
		<td><s:property value="actualPay"></s:property><s:if test="actualPay eq null">0</s:if></td>
        <td><s:property value="remainEffPay"></s:property><s:if test="remainEffPay eq null">0</s:if></td>
        <td><Field:codeValue tableName="APP___CLM__DBUSER.T_LIABILITY_STATUS" value="${liabilityState}"/>
		<input id="clmRemark" name="clmRemark" value="${clmRemark}" type="hidden"/>
                    <input name="liabId"  value="${liabId}" type="hidden" title="<Field:codeValue value='${liabId}'  tableName='APP___CLM__DBUSER.t_liability' />" />
		</td>
	</tr>
</s:iterator>
	</tbody>
</table>
</div>
<div class="panelPageFormContent main_tabdiv">
<dl  style="width: 100%;height: auto">
				<dt>备注信息</dt>
				<dd>
					<textarea maxlength="5000" id="UnMedBody" name=""  rows="3" cols="70" readonly>
					</textarea>
				</dd>	
			</dl>
			</div>	
		    </div>
            </div>
		</div>
   </div>
                            				
	</div>
</div>	
</li>
</ul>
</s:if>