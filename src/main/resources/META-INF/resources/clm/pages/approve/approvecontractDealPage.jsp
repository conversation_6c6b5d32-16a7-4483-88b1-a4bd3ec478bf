<%@ page language="java" contentType="text/html; charset=utf-8"
	pageEncoding="UTF-8"%><%@ include file="/clm/pages/common/commonJsCss.jsp" %>
<%@ taglib uri="http://www.git.com.cn/taglib/udmp/field" prefix="Field"%>
<%@ taglib uri="/struts-tags" prefix="s"%>
 

<script type="text/javascript" charset="UTF-8" src="clm/pages/audit/contractDealPage.js">
</script>
<script type="text/javascript">
//禁用两个下拉框
$("#contractDealPage", navTab.getCurrentPanel()).find("#BusiProdDealConclusion").setMyComboxDisabled(true);
$("#contractDealPage", navTab.getCurrentPanel()).find("#PolicyDealConclusion").setMyComboxDisabled(true);
var readOnly='${readOnly}';

function contractDealPagePrevAudit(caseId){
	//判断保单是否涉及豁免责任 如果没有豁免责任直接跳到匹配理算页面
	var isWaivedFlag = 0;
	$.ajax({
		'url':"clm/audit/queryLiabConclusionIsWaived_CLM_claimMatchResultAction.action",
		'type':'post',
		'data':{'claimCaseVO.caseId':caseId},
		'async':false,
		'success':function(data){
			if(data!=""){
				var objdata = eval("(" + data + ")");
				if(objdata.isWaived == 1){
					isWaivedFlag = 1;
				}
			}
		},
		'error':function(){
			alertMsg.error("系统出现异常，请联系管理员");
		}
		});
	if(isWaivedFlag == 0){
		prev_audit('4',caseId);
	}else{
		prev_audit('5',caseId);
	}
}
</script>
<body>
	<div id="contractDealPage" class="pageContent pageHeader"  layoutH="36">
			<div class="panelPageFormContent">
				<dl>
					<dt>赔案号</dt>
					<dd>
					<input name="claimCaseVO.caseNo" type="text" size="30" value="${claimCaseVO.caseNo}" readonly="readonly" />
					<input id="caseId" type="hidden" value="${caseId}" />
					</dd>
				</dl>
				<dl>
					<dt>事 件 号</dt>
					<dd>
					<input name="claimAccidentVO.accidentNo" type="text" size="30" value="${claimAccidentVO.accidentNo}" readonly="readonly" />
					</dd>
				</dl>
			</div>
		 <form
			action="clm/audit/saveContractMsg_CLM_contractDealAction.action?menuId=898989"
			method="post" id="contractDealForm1"
			class="pageForm required-validate"
			onsubmit="return validateCallback(this, navTabAjaxDone)">
			<input id="caseId" name="claimPolicyVO.caseId" type="hidden" value="${caseId}" />
			<div class="panelPageFormContent">
				<div class="divfclass">
					<h1>
						<img src="clm/images/tubiao.png">涉案保单
					</h1>
				</div>
				<div class="tabdivclassbr">
					<table class="list" style="width: 100%;">
						<thead>
							<tr align="center">
								<td nowrap>选择</td>
								<td nowrap>序号</td>
								<td nowrap>保单号</td>
								<td nowrap>状态</td>
								<td nowrap>二核结论</td>
								<td nowrap>终止生效日</td>
							</tr>
						</thead>
						<tbody>
							<s:iterator value="claimPolicyVOs" var="status"
								status="var">
								<tr align="center">
									<td >
										<input type="radio" name="claimPolicyVO.listId" value="<s:property value="listId"></s:property>" onclick="trendsValue(this)" />
										<input type="hidden" name="" value="<s:property value="dealConclusion"></s:property>" />  
										<input type="hidden" name="" value="<s:property value="policyId"></s:property>"/>
									</td>
									<td ><s:property value="#var.index+1"></s:property></td>
									<td ><s:property value="policyCode"></s:property></td>
									<td ><Field:codeValue value="${liabilityStatus}" tableName="APP___CLM__DBUSER.T_LIABILITY_STATUS" /></td>
									<td ><Field:codeValue value="${policyDecision}" tableName="APP___CLM__DBUSER.T_POLICY_DECISION" /></td>
									<td ><s:date name="expiryDate" format="yyyy-MM-dd"></s:date></td>
								</tr>
							</s:iterator>
						</tbody>
					</table>
				</div>
				<div class="panelPageFormContent">

						<input name="contractDealProdVO.listId"
							id="contractDealProdListId" value="" type="hidden" /> <input
							name="contractDealProdVO.flag"  id="contractDealProdFlag"
							value="${contractDealProdVO.flag }" type="hidden" />
						
						<dl>
							<dt>终止结论</dt>
							<dd>
								<Field:codeTable cssClass="combox title"  name="claimPolicyVO.dealConclusion" id="PolicyDealConclusion" tableName="APP___CLM__DBUSER.T_TERMINATION" nullOption="true"/>
							</dd>
						</dl>	
						
						<div class="pageFormdiv"><button disabled class="but_blue" type="button" onclick="contractDealSubmit('contractDealForm1')" id="contractDealSubmit1">保存</button></div>	
						
<!-- 						<table> -->
<!-- 						<tr> -->
<!-- 						<td width="120">终止结论</td> -->
<%-- 						<td width="200"><Field:codeTable cssClass="selectToInput"  name="claimPolicyVO.dealConclusion" id="PolicyDealConclusion" tableName="APP___CLM__DBUSER.T_TERMINATION" nullOption="true"/></td> --%>
						 
<!-- 						<td width="200"> -->
<!-- 						</td> -->
<!-- 						<td width="120"> -->
<!-- 							<div class="button"> -->
<!-- 								<div class="buttonContent"> -->
<!-- 									<button type="button" onclick="contractDealSubmit('contractDealForm1')" id="contractDealSubmit1">保存</button> -->
<!-- 								</div> -->
<!-- 							</div> -->
<!-- 						</td> -->
<!-- 						</tr> -->
<!-- 						</table> -->
				</div>
			</div>
			</form>
        <form
			action="clm/audit/saveContractMsg_CLM_contractDealAction.action?menuId=898989"
			method="post" id="contractDealForm2"
			class="pageForm required-validate"
			onsubmit="return validateCallback(this, navTabAjaxDone)">
			<input id="caseId" name="claimBusiProdVO.caseId" type="hidden" value="${caseId}" />
			<div class="panelPageFormContent">
				<div class="divfclass">
					<h1>
					<img src="clm/images/tubiao.png">涉案保单险种
					</h1>
				</div>
				
				<div class="tabdivclassbr">
					<table class="list" style="width: 100%;">
						<thead>
							<tr align="center">
								<td nowrap>选择</td>
								<td nowrap>序号</td>
								<td nowrap>保单号</td>
								<td nowrap>保单险种号</td>
								<td nowrap>险种代码</td>
								<td nowrap>险种名称</td>
								<td nowrap>状态</td>
								<td nowrap>二核结论</td>
								<td nowrap>终止生效日</td>
							</tr>
						</thead>
						<tbody id="claimBusiProd">
							<!-- 这里是显示涉案保单的，通过点击保单然后通过ajax刷新出来 -->
						</tbody>
					</table>
				</div>
         
				<div class="panelPageFormContent">
						<input name="contractDealProdVO.listId"
							id="contractDealProdListId" value="" type="hidden" /> 
						<input
							name="contractDealProdVO.flag" id="contractDealProdFlag"
							value="" type="hidden" />
						<dl>
							<dt>终止结论：</dt>
							<dd>
								<Field:codeTable cssClass="combox title"  name="claimBusiProdVO.dealConclusion" id="BusiProdDealConclusion" tableName="APP___CLM__DBUSER.T_PROD_DEAL_DECISION" nullOption="true"/>
							</dd>
						</dl>
						<div class="pageFormdiv"><button disabled class="but_blue" type="button" onclick="contractDealSubmit('contractDealForm2')" id="contractDealSubmit2">保存</button></div>
<!-- 						<dl> -->
<!-- 							<dd> -->
<!-- 								<div class="button"> -->
<!-- 									<div class="buttonContent"> -->
<!-- 										<button type="button" onclick="contractDealSubmit('contractDealForm2')" id="contractDealSubmit2">保存</button> -->
<!-- 									</div> -->
<!-- 								</div> -->
<!-- 							</dd> -->
<!-- 						</dl> -->
				</div>
			</div>
			</form>
			<div class="divider"></div>
			<div class="panelPageFormContent">
				<div class="divfclass">
					<h1>
					<img src="clm/images/tubiao.png">合同处理结果
					</h1>
				</div>
				<div class="tabdivclassbr">
					<h2 style="margin:10px 0px 0px 10px;">保单处理结果</h2>
					<div class="divider"></div>
	
					<table class="list" style="width: 100%;">
						<thead>
							<tr align="center">
								<td nowrap>序号</td>
								<td nowrap>保单号</td>
								<td nowrap>状态</td>
								<td nowrap>合同处理结论</td>
								<td nowrap>终止时间</td>
							</tr>
						</thead>
						<tbody id = "policyDeal">
							<s:iterator value="claimPolicyDealConclVOs" var="status"
								status="var">
								<tr align="center">
									<td><s:property value="#var.index+1"></s:property></td>
									<td><s:property value="policyCode"></s:property></td>
									<td><Field:codeValue value="${liabilityStatus}" tableName="APP___CLM__DBUSER.T_LIABILITY_STATUS" /></td>
									<td><Field:codeValue value="${dealConclusion}" tableName="APP___CLM__DBUSER.T_TERMINATION" /></td>
									<td><s:date name="expiryDate" format="yyyy-MM-dd"></s:date></td>
								</tr>
							</s:iterator>
						</tbody>
					</table>
					<h2 style="margin:10px 0px 0px 10px;">险种处理结果</h2>
					<div class="divider"></div>
					<table class="list" style="width: 100%;">
						<thead>
							<tr align="center">
								<td nowrap>序号</td>
								<td nowrap>保单号</td>
								<td nowrap>保单险种号</td>
								<td nowrap>状态</td>
								<td nowrap>合同处理结论</td>
								<td nowrap>终止时间</td>
							</tr>
						</thead>
						<tbody id="busiProdDeal">
							<s:iterator value="claimBusiProdDealConclVOs" var="status"
								status="var">
								<tr align="center">
									<td><s:property value="#var.index+1"></s:property></td>
									<td><s:property value="policyCode"></s:property></td>
									<td><s:property value="busiItemId"></s:property></td>
									<td><Field:codeValue value="${liabilityStatus}" tableName="APP___CLM__DBUSER.T_LIABILITY_STATUS" /></td>
									<td><Field:codeValue value="${dealConclusion}" tableName="APP___CLM__DBUSER.T_PROD_DEAL_DECISION" /></td>
									<td><s:date name="expireDate" format="yyyy-MM-dd"></s:date></td>
								</tr>
							</s:iterator>
						</tbody>
					</table>
				</div>
			</div>
			<div class="divider"></div>
			<div style="display:none"><Field:codeTable cssClass="combox title"  name="" tableName="APP___CLM__DBUSER.T_CLAIM_ADJUST_REASON" id="dealAdjustReason"/></div>
			<input type="hidden" value="0" id="amountcheck">
			<form
			action="clm/audit/saveContractMsg_CLM_contractDealAction.action?menuId=898989"
			method="post" id="contractDealForm3"
			class="pageForm required-validate"
			onsubmit="return validateCallback(this, navTabAjaxDone)">
			<div class="panelPageFormContent">
				<div class="divfclass">
					<h1>
					<img src="clm/images/tubiao.png">合同结算结果
					</h1>
				</div>
				<div class="tabdivclassbr">
					<table class="list" style="width: 100%;">
						<thead>
							<tr align="center">
								<td nowrap>合同结算结果</td>
								<td nowrap>理算金额</td>
								<td nowrap>调整后金额</td>
								<td nowrap>调整原因</td>
								<td nowrap>备注</td>
							</tr>
						</thead>
						<tbody id="claimAdjustBusi">
						</tbody>
					</table>
<!-- 					<div class="pageFormdiv"><button class="but_blue" type="button" onclick="contractDealSubmit('contractDealForm3')" id="contractDealSubmit3">保存</button></div> -->
<!-- 					<div style="width: 70%"> -->
<!-- 						<div class="button" style="float:right;"> -->
<!-- 							<div class="buttonContent"> -->
<!-- 								<button type="button" onclick="contractDealSubmit('contractDealForm3')" id="contractDealSubmit3">保存</button> -->
<!-- 							</div> -->
<!-- 						</div> -->
<!-- 					</div>				 -->
				</div>
			</div>
			</form>
			
			<div class="formBarButton">
			<ul>
				<li>
					<button class="but_blue" type="button" onclick="prev_approve('5','${caseId}')">上一步</button>
				</li>
				<li>
					<button disabled class="but_blue" type="button" onclick="contractDealSubmit('contractDealForm3')" id="contractDealSubmit3">保存</button>
				</li>
				<li>	
					<button class="but_blue" type="button" onclick="next_approve('7','${caseId}')">下一步</button>
				</li>
				<li>	
					<button class="but_gray" onclick="exit();" id="closeAuditcContractDeal">退出</button>
				</li>
				</ul>
			</div>
	</div>
</body>
