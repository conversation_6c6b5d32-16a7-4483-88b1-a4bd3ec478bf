﻿<%@ page language="java" contentType="text/html; charset=utf-8"
	pageEncoding="UTF-8"%>
<%@include file="/udmpCommon.jsp"%>
<%@ taglib uri="/struts-tags" prefix="s"%>
<%@ taglib uri="http://www.git.com.cn/taglib/udmp/field" prefix="Field"%>
<script type="text/javascript" language="javascript">
//======================================初始化加载，查看时，自动显示勾选过的项目和录入过的数据====================================================//
var jsonShowMap = '${jsonShowMap}'; //判断页面展示数据使用
var caseId = $("#caseId", $.pdialog.getCurrent()).attr("value");
var isMigration = '${claimCaseVO.isMigration}';
var isHospitalFlag = true;
//职业给付系数  年龄误告处理  医疗单证录入 特种费用  社保第三方给付  伤残 //特定手术/疾病/给付录入信息
if(jsonShowMap.length > 2){
	jsonShowMap = jsonShowMap.substring(1,jsonShowMap.length - 1);
	for (var j = 0; j < jsonShowMap.split(",").length; j++) {
	    var show =jsonShowMap.split(",")[j];
	    var checkboxid = "",checkboxname = "";
	    checkboxid = show.split("=")[0].trim();
	    checkboxname = show.split("=")[1].trim();
	    //职业给付系数
		if(checkboxid == "claimOccupationRate" && checkboxname == "show"){
			$("input[hiddenDivId='approveJobCateNews']", $.pdialog.getCurrent()).attr("checked",true);
			$("#approveJobCateNews", $.pdialog.getCurrent()).show("3000");
		}
		//年龄误告处理
		if(checkboxid == "claimAdjustAge" && checkboxname == "show"){
			$("input[hiddenDivId='approveAgeNews']", $.pdialog.getCurrent()).attr("checked",true);
			$("#approveAgeNews", $.pdialog.getCurrent()).show("3000");
		}
	    //医疗单证录入 
		if(checkboxid == "claimBill" && checkboxname == "show"){
			var rel = $("#approveHonsp", $.pdialog.getCurrent());
			rel.loadUrl("clm/register/queryRegisterHonsp_CLM_tClaimDutyRegisterAction.action?caseId="+caseId);
			$("#approveHonsp", $.pdialog.getCurrent()).show("3000");
			//延迟复选框勾选
			setTimeout('approveHonspcheckbox()',900);
		 
		}
	    //伤残录入信息
		if(checkboxid=="claimInjury" && checkboxname == "show"){
			var rel = $("#approveMaimNews", $.pdialog.getCurrent());
			rel.loadUrl("clm/register/queryRegisterMaimNews_CLM_tClaimDutyRegisterAction.action?caseId="+caseId);
			$("#approveMaimNews", $.pdialog.getCurrent()).show("3000");
			//延迟复选框勾选
			setTimeout('approveMaimNewscheckbox()',900);
		}
	    //特定手术/疾病/给付录入信息
		if(checkboxid=="claimSurgery" && checkboxname == "show"){
			var rel = $("#approveOpsNews", $.pdialog.getCurrent());
			rel.loadUrl("clm/register/queryRegisterOpsNews_CLM_tClaimDutyRegisterAction.action?caseId="+caseId);
			$("#approveOpsNews", $.pdialog.getCurrent()).show("3000");
			//延迟复选框勾选
			setTimeout('approveOpsNewscheckbox()',900);
		}
	    //特种费用录入信息
		if(checkboxid=="claimSpecial" && checkboxname == "show"){
			var rel = $("#approveSpeCostNews", $.pdialog.getCurrent());
			rel.loadUrl("clm/register/queryRegisterSpeCostNews_CLM_tClaimDutyRegisterAction.action?caseId="+caseId);
			$("#approveSpeCostNews", $.pdialog.getCurrent()).show("3000");
			//延迟复选框勾选
			setTimeout('approveSpeCostNewscheckbox()',900);
		}
	    //社保第三方给付录入
		if(checkboxid=="claimBillPaid" && checkboxname == "show"){
			var rel = $("#approveThirdPayNews",$.pdialog.getCurrent());
			rel.loadUrl("clm/register/queryRegisterThirdPayNews_CLM_tClaimDutyRegisterAction.action?caseId="+caseId);
			$("#approveThirdPayNews", $.pdialog.getCurrent()).show("3000");
			//延迟复选框勾选
			setTimeout('approveThirdPayNewscheckbox()',900);
		}
	    //一般失能信息录入
		if(checkboxid=="claimDisabilityGeneral" && checkboxname == "show"){
			var rel = $("#approveGeneralNews",$.pdialog.getCurrent());
			rel.loadUrl("clm/register/queryRegisterGeneralNews_CLM_tClaimDutyRegisterAction.action?caseId="+caseId);
			$("#approveGeneralNews", $.pdialog.getCurrent()).show("3000");
			//延迟复选框勾选
			setTimeout('approveGeneralNewscheckbox()',900);
		}
	    //重度失能
		if(checkboxid=="claimDisabilityGreat" && checkboxname == "show"){
			var rel = $("#approveGreatNews",$.pdialog.getCurrent());
			rel.loadUrl("clm/register/queryRegisterGreatNews_CLM_tClaimDutyRegisterAction.action?caseId="+caseId);
			$("#approveGreatNews", $.pdialog.getCurrent()).show("3000");
			//延迟复选框勾选
			setTimeout('approveGreatNewscheckbox()',900);
		}
		//同时出险（连生保单）
		if(checkboxid=="claimDisabilityTogether" && checkboxname == "show"){
			var rel = $("#approveClaimTogetherNews",navTab.getCurrentPanel());
			rel.loadUrl("clm/register/queryRegisterClaimTogetherNews_CLM_tClaimDutyRegisterAction.action?caseId="+caseId+"&readOnly="+readOnly);
			$("#approveClaimTogetherNews", navTab.getCurrentPanel()).show("3000");
			//延迟复选框勾选
			setTimeout('approveTogetherNewscheckbox()',800);
		}
	}
}
//勾选复选框
function approveHonspcheckbox(){
	$("input[hiddenDivId='approveHonsp']", $.pdialog.getCurrent()).attr("checked",true);
}
function approveMaimNewscheckbox(){
	$("input[hiddenDivId='approveMaimNews']", $.pdialog.getCurrent()).attr("checked",true);
}
function approveOpsNewscheckbox(){
	$("input[hiddenDivId='approveOpsNews']", $.pdialog.getCurrent()).attr("checked",true);
}
function approveSpeCostNewscheckbox(){
	$("input[hiddenDivId='approveSpeCostNews']", $.pdialog.getCurrent()).attr("checked",true);
}
function approveThirdPayNewscheckbox(){
	$("input[hiddenDivId='approveThirdPayNews']", $.pdialog.getCurrent()).attr("checked",true);
}
function approveGeneralNewscheckbox(){
	$("input[hiddenDivId='approveGeneralNews']", $.pdialog.getCurrent()).attr("checked",true);
}
function approveGreatNewscheckbox(){
	$("input[hiddenDivId='approveGreatNews']", $.pdialog.getCurrent()).attr("checked",true);
}
function approveTogetherNewscheckbox(){
	$("input[hiddenDivId='approveClaimTogetherNews']", navTab.getCurrentPanel()).attr("checked",true);
}

//=============================================================================================//
queryClaimSubCase();
setTimeout("readOnly('approveDuty')",1500);
//readOnly("approveDuty");
/**
 * 根据理赔类型 ==生成左侧复选框
 */
function queryClaimSubCase(){
	var caseId = $("#caseId", $.pdialog.getCurrent()).val();
	$.ajax({
		'url':'clm/register/queryClaimType_CLM_tClaimDutyRegisterAction.action?caseId='+caseId,
		'type':'post',
		'data':{},'datatype':'json','success':function(data){
			var data = eval("(" + data + ")");
			for(var i=0;i<data.length;i++){
				var claimType=data[i].claimType;
				if(claimType.match('02')||claimType.match('04')){
					// 04:高残、02:伤残 ====>伤残
					if($("[hiddenDivId='approveMaimNews']", $.pdialog.getCurrent()).html()==null){
						$("#approveClaimSubCase", $.pdialog.getCurrent()).append("<input type='checkbox' name='claimTypecode[2]' value='2' hiddenDivId='approveMaimNews' ChangeTypecode='claimTypecode' onclick='smallChange(this)' disabled>伤残 </br>");
					}
				}else if(claimType.match('08')){
					//08:医疗====>医疗单证录入、社保第三方给付、特种费用
					if($("[hiddenDivId='approveHonsp']", $.pdialog.getCurrent()).html()==null){
						$("#approveClaimSubCase", $.pdialog.getCurrent()).append("<input type='checkbox' name='claimTypecode[1]' value='1' hiddenDivId='approveHonsp' ChangeTypecode='claimTypecode' onclick='smallChange(this)' disabled>医疗单证录入 <br>");
					}
					if($("[hiddenDivId='approveSpeCostNews']", $.pdialog.getCurrent()).html()==null){
						$("#approveClaimSubCase", $.pdialog.getCurrent()).append("<input type='checkbox' name='claimTypecode[4]' value='4' hiddenDivId='approveSpeCostNews' ChangeTypecode='claimTypecode' onclick='smallChange(this)' disabled>特种费用 <br>");
					}
					if($("[hiddenDivId='approveThirdPayNews']", $.pdialog.getCurrent()).html()==null){
						$("#approveClaimSubCase", $.pdialog.getCurrent()).append("<input type='checkbox' name='claimTypecode[5]' value='5' hiddenDivId='approveThirdPayNews' ChangeTypecode='claimTypecode' onclick='smallChange(this)' disabled>社保第三方给付 <br>");
					}
				}else if (claimType.match('10')){
					//10:特种疾病=====>特定手术/疾病/给付、特种费用
					if($("[hiddenDivId='approveOpsNews']", $.pdialog.getCurrent()).html()==null){
						$("#approveClaimSubCase", $.pdialog.getCurrent()).append("<input type='checkbox' name='claimTypecode[3]' value='3' hiddenDivId='approveOpsNews' ChangeTypecode='claimTypecode' onclick='smallChange(this)' disabled>特定手术/疾病/给付 <br>");
					}
					if($("[hiddenDivId='approveSpeCostNews']").html()==null){
						$("#approveClaimSubCase", $.pdialog.getCurrent()).append("<input type='checkbox' name='claimTypecode[4]' value='4' hiddenDivId='approveSpeCostNews' ChangeTypecode='claimTypecode' onclick='smallChange(this)' disabled>特种费用 <br>");
					}
					
				}else if (claimType.match('06')){
					//06:一般失能======>一般失能
					if($("[hiddenDivId='approveGeneralNews']", $.pdialog.getCurrent()).html()==null){
						$("#approveClaimSubCase", $.pdialog.getCurrent()).append("<input type='checkbox' name='claimTypecode[7]' value='7' hiddenDivId='approveGeneralNews' ChangeTypecode='claimTypecode' onclick='smallChange(this)' disabled>一般失能 <br>");
					}
				}else if (claimType.match('07')){
					//07:重度失能======>重度失能
					if($("[hiddenDivId='approveGreatNews']", $.pdialog.getCurrent()).html()==null){
						$("#approveClaimSubCase", $.pdialog.getCurrent()).append("<input type='checkbox' name='claimTypecode[8]' value='8' hiddenDivId='approveGreatNews' ChangeTypecode='claimTypecode' onclick='smallChange(this)' disabled>重度失能 <br>");
					}
				}
				else if (claimType.match('01')){
					//01:身故======>同时出险（连生保单）
					if($("[hiddenDivId='approveClaimTogetherNews']", navTab.getCurrentPanel()).html()==null){
						$("#approveClaimSubCase", navTab.getCurrentPanel()).append("<input type='checkbox' name='claimTypecode[16]' value='16' hiddenDivId='approveClaimTogetherNews' ChangeTypecode='claimTypecode' onclick='smallChange(this)'>同时出险（连生保单） <br>");
					}
				}
	  		}
		}
	});
}

//点击左侧复选框显示右边详细信息
function smallChange(obj){
	//页面加载完之后获取queryAlwaysClaim判断是否为1,说明是 从既往赔案 过来的  liutao
	var queryAlwaysClaim= $("#queryAlwaysClaim",$.pdialog.getCurrent()).attr("value") ;
	if(queryAlwaysClaim==1){
		//如果是 直接返回  并且不能选中
		 $(obj).attr("checked",false);
		 return ;
	}
	var caseId = $("#caseId", $.pdialog.getCurrent()).attr("value");
	var id = $(obj).attr("hiddenDivId");
	if($("#"+id, $.pdialog.getCurrent()).html()!=''){
		if($(obj).is(":checked")){
			$("#"+id, $.pdialog.getCurrent()).show("3000");
		}else{
			$("#"+id, $.pdialog.getCurrent()).hide("3000");
		}
	}else{
		if(id == "approveHonsp"){
			var rel = $("#approveHonsp", $.pdialog.getCurrent());
			rel.loadUrl("clm/register/queryRegisterHonsp_CLM_tClaimDutyRegisterAction.action?caseId="+caseId);
			$("#approveHonsp", $.pdialog.getCurrent()).show("3000");
		}
		if(id=="approveMaimNews"){
			var rel = $("#approveMaimNews", $.pdialog.getCurrent());
			rel.loadUrl("clm/register/queryRegisterMaimNews_CLM_tClaimDutyRegisterAction.action?caseId="+caseId);
			$("#approveMaimNews", $.pdialog.getCurrent()).show("3000");
		}
		if(id=="approveOpsNews"){
			var rel = $("#approveOpsNews", $.pdialog.getCurrent());
			rel.loadUrl("clm/register/queryRegisterOpsNews_CLM_tClaimDutyRegisterAction.action?caseId="+caseId);
			$("#approveOpsNews", $.pdialog.getCurrent()).show("3000");
		}
		if(id=="approveSpeCostNews"){
			var rel = $("#approveSpeCostNews", $.pdialog.getCurrent());
			rel.loadUrl("clm/register/queryRegisterSpeCostNews_CLM_tClaimDutyRegisterAction.action?caseId="+caseId);
			$("#approveSpeCostNews", $.pdialog.getCurrent()).show("3000");
		}
		if(id=="approveThirdPayNews"){
			var rel = $("#approveThirdPayNews",$.pdialog.getCurrent());
			rel.loadUrl("clm/register/queryRegisterThirdPayNews_CLM_tClaimDutyRegisterAction.action?caseId="+caseId);
			$("#approveThirdPayNews", $.pdialog.getCurrent()).show("3000");
		}
		if(id=="approveGeneralNews"){
			var rel = $("#approveGeneralNews",$.pdialog.getCurrent());
			rel.loadUrl("clm/register/queryRegisterGeneralNews_CLM_tClaimDutyRegisterAction.action?caseId="+caseId);
			$("#approveGeneralNews", $.pdialog.getCurrent()).show("3000");
		}
		if(id=="approveGreatNews"){
			var rel = $("#approveGreatNews",$.pdialog.getCurrent());
			rel.loadUrl("clm/register/queryRegisterGreatNews_CLM_tClaimDutyRegisterAction.action?caseId="+caseId);
			$("#approveGreatNews", $.pdialog.getCurrent()).show("3000");
		}
		if(id=="approveClaimTogetherNews"){
			var rel = $("#registerClaimTogetherNews",navTab.getCurrentPanel());
			rel.loadUrl("clm/register/queryRegisterClaimTogetherNews_CLM_tClaimDutyRegisterAction.action?caseId="+caseId+"&readOnly="+readOnly);
			$("#approveClaimTogetherNews", navTab.getCurrentPanel()).show("3000");
		} 
		
	}
}
//根据直接给付代码 查询职业类别和职业给付系数
function queryJobLevelByJobCode(obj){
	var jobCode=$(obj).attr("value");
	 $.ajax({
		'type':'post',
		'url':'clm/register/queryOccupationRateByCode_CLM_tClaimDutyRegisterAction.action',
		'data':{'jobCodeVO.jobCode':jobCode},
		'datatype':'json',
		'success':function(data){
			var data = eval("(" + data + ")");
			 $(obj).closest("dl").next().find("input[name='claimOccupationRateVO.jobCategory']").attr("value",data.jobCategory);
			 $(obj).closest("dl").next().next().find("input").attr("value",data.jobName);
			 $(obj).closest("dl").next().find("input[name='data']").attr("value",data.jobCategoryName);
			 $(obj).closest("dl").next().next().find("input").attr("value",data.jobUwLevel);
			 $("#oldJobCode", navTab.getCurrentPanel()).attr("value",data.oldJobCode);
			 $("#oldJobCategoryName", navTab.getCurrentPanel()).attr("value",data.oldJobCategoryName);
			 $("#oldJobUwLevel", navTab.getCurrentPanel()).attr("value",data.oldJobUwLevel);
		},
	});
}
//页面加减号的显示和隐藏
function showDiv(show){
	if(show.value=="-"){
		$("#"+show.hiddenDivId, $.pdialog.getCurrent()).hide("3000");
		show.value="+";
	}else if (show.value=="+"){
		$("#"+show.hiddenDivId, $.pdialog.getCurrent()).show("3000");
		show.value="-";
	}
}
$(".btnDel").die().live("click",function(){
	$(this).closest("tr").remove();
});
//页面操作域禁用
function readOnly(obj){
	$("#"+obj, $.pdialog.getCurrent()).find("input").each(function(){
		$(this).attr("disabled",true);
	});
	$("#"+obj, $.pdialog.getCurrent()).find("select").each(function(){
		$(this).attr("disabled",true);
	});
	$("#"+obj, $.pdialog.getCurrent()).find("a").each(function(){
		if(!$(this).attr("id") == "directDetail"){
			$(this).attr("disabled",true);
		}
	});
	$("#"+obj, $.pdialog.getCurrent()).find("textarea").each(function(){
		$(this).attr("disabled",true);
	});
	$("#"+obj, $.pdialog.getCurrent()).find("button").each(function(){
		if(!($(this).html()=="下一步"||$(this).html()=="上一步")){
			$(this).attr("disabled",true);
		}
	});
	//单选按钮可以触发 用于显示详情
	$("#"+obj, $.pdialog.getCurrent()).find("input[type=radio]").each(function(){
		$(this).attr("disabled",false);
	});
}
</script>

<form method="post" id="registerFeeForms" class="pageForm required-validate"
	onsubmit="return validateCallback(this, navTabAjaxDone);"></form>
	<!--  -->
<input type="hidden" id="queryAlwaysClaim" value="${param.queryAlwaysClaim}">
<div class="pageContent pageHeader" layoutH="36">
	<div>
		<form method="post" class="pageForm required-validate"
			action = "clm/register/saveClaimDutyRegister_CLM_tClaimDutyRegisterAction.action"
			onsubmit="return validateCallback(this, navTabAjaxDone);"
			style="background-color: #EEF4F5">
			<input id="caseId" name="caseId" type="hidden" value="${caseId}">
			<input id="claimAccReasonId" type="hidden" value="${accident.accReason}">
			<input type='hidden' name="" id='claimDate' value='<s:date name='claimSubCaseVO.claimDate' format='yyyy-MM-dd' />' />
		 	<input type='hidden' name="" id='accDate'  value='<s:date name='accident.accDate' format='yyyy-MM-dd' />' />
		 	<input type='hidden' name="" id='accResult1' value=' <s:property value='claimAccidentResultVO.accResult1'/>' />
		 	<input type='hidden' name="" id='accResult2' value='<s:property value='claimAccidentResultVO.accResult2' />' />
			<input type="hidden" id="auditClaimCheckListInit" value="auditClaimCheckListInit">
			<input type="hidden" value="${claimCaseVO.reportMode }" id="reportMode"/>
			<input type="hidden" value="${directClaimItemDetail }" id="directClaimItemDetail"/>
			
			<div style="width: 100%; height: 100%;"  id="approveDuty">
				<div id="approveClaimSubCase" align="left"
					style="float: left; width: 100px; height: 2300px; border-right: 10px solid #CCC;">
					<input type="checkbox" name="claimTypecode[6]" id="" value="6" 
						hiddenDivId="approveJobCateNews"
						onclick="smallChange(this)">职业给付系数
					<input type="checkbox" name="claimTypecode[9]" id="" value="9"
						hiddenDivId="approveAgeNews"
						onclick="smallChange(this)"> 年龄误告处理
				</div>
				<div align="left" >
					<div class="pageFormContent">
						<!-- 医疗单证录入   div  开始 -->
						<div id="approveHonsp" style="display: none"></div>
						<!--伤残录入信息  div start  -->
						<div id="approveMaimNews" class="pageFormContent" style="display: none">
						</div>

						<!-- 特定手术/疾病/给付录入信息  div start  -->
						<div id="approveOpsNews" class="pageFormContent" style="display: none">
						</div>

						<!-- 特种费用录入信息  div start  -->
						<div id="approveSpeCostNews" class="pageFormContent"
							style="display: none"></div>

						<!--社保第三方给付录入  div start  -->
						<div id="approveThirdPayNews" class="pageFormContent"
							style="display: none"></div>

						<!--职业给付系数录入信息  div start  -->
						<div id="approveJobCateNews" class="pageFormContent"
							style="display: none">
							<div>
								<input type="button" hiddenDivId="jobCateNewsInt"
									onclick="showDiv(this)" value="-">职业给付系数录入
							</div>
							<div id="jobCateNewsInt" class="panel">
								<h1>职业给付系数信息录入</h1>
								<!--职业给付系数   div start  -->
								<div class="pageFormContent">
									<dl style="width:32%; float: left; ">
										<dt style="width: 32%">
											职业代码 <font class="point" color="red">*</font>
										</dt>
										<dd style="width: 60%; float: left; " >
											<input type="hidden" value="${claimOccupationRateVO.jobCode}">
											<%--  <Field:codeTable name="claimOccupationRateVO.occupationCode" tableName="APP___CLM__DBUSER.T_JOB_CODE" nullOption="true" cssClass="combox" defaultValue=" " showInput="true" showInputId="jobId" onChange="occupationChange(this);"/> --%>
											<Field:codeTable 
												name="claimOccupationRateVO.jobCode" tableName="APP___CLM__DBUSER.T_JOB_CODE"
												nullOption="true" cssClass="combox title" defaultValue=" "
												showInput="true" showInputId="registerJobId"
												value="${claimOccupationRateVO.jobCode}"
												onChange="queryJobLevelByJobCode(this);" />
											<input id="registerJobId" type="text"
												value="${claimOccupationRateVO.jobName}">
										</dd>
									</dl>

									<dl style="width: 32%">
										<dt style="width: 32%">
											职业类型 <font class="point" color="red">*</font>
										</dt>
										<dd style="width: 60%">
											<!-- <input type="text" name="claimOccupationRateVO.jobCategory"  readonly="readonly"/> -->
											<input type="hidden" name="claimOccupationRateVO.jobCategory"
												value="${claimOccupationRateVO.jobCategory}" /> <input
												type="text" name="data"
												value="<Field:codeValue tableName="APP___CLM__DBUSER.T_JOB_CATEGORY" value="${claimOccupationRateVO.jobCategory}"/>"
												readonly="readonly" />
										</dd>
									</dl>

									<dl style="width: 32%">
										<dt style="width: 32%">
											职业级别 <font class="point" color="red">*</font>
										</dt>
										<dd style="width: 60%">
											<!-- <input type="text" readonly="readonly" name="claimOccupationRateVO.occupationRate" /> -->
											<input type="text"
												
												value="${claimOccupationRateVO.jobUwLevel}"
												readonly="readonly" />
										</dd>
									</dl>
									<br></br>
									<br></br>
									<dl style="width: 32%"> 
										<dt style="width: 32%">
											旧职业代码 
										</dt>
										<dd style="width: 60%">
											<input type="text" id="oldJobCode"
												value="${claimOccupationRateVO.oldJobCode}"
												readonly="readonly" />
										</dd>
									</dl>
									<dl style="width: 32%"> 
										<dt style="width: 32%">
											旧职业类型 
										</dt>
										<dd style="width: 60%">
											<input type="text" id="oldJobCategoryName"
												value="${claimOccupationRateVO.oldJobCategoryName}"
												readonly="readonly" />
										</dd>
									</dl>
									<dl style="width: 32%"> 
										<dt style="width: 32%">
											旧职业级别
										</dt>
										<dd style="width: 60%">
											<input type="text" id="oldJobUwLevel"
												value="${claimOccupationRateVO.oldJobUwLevel}"
												readonly="readonly" />
										</dd>
									</dl>
									<dl
										style="float: none; margin: 50px 0 0px 0; display: block; width: 100%;">
										<dt style="width: 10%;">备注</dt>
										<dd style="width: 60%;">
											<textarea name="claimOccupationRateVO.occupationRemark"
												rows="2" cols="100"
												onpropertychange="if(value.length>1000) value=value.substring(0,1000)">${claimOccupationRateVO.occupationRemark}</textarea>
										</dd>
									</dl>
								</div>
							</div>
							<!--职业给付系数录入信息 div  end  -->
						</div>

						<!--一般失能信息录入  div start  -->
						<div id="approveGeneralNews" class="pageFormContent"
							style="display: none"></div>

						<!--重度失能   div start  -->
						<div id="approveGreatNews" class="pageFormContent" style="display: none">
						</div>
			
						<!--同时出险（连生保单）   div start  -->
						<div id="approveClaimTogetherNews" class="pageFormContent" style="display: none"></div>
						
						<!--一年龄误告处理  div start  -->
						<div id="approveAgeNews" class="pageFormContent" style="display: none">
							<div>
								<input type="button" hiddenDivId="approveAgeNewsInt"
									onclick="showDiv(this)" value="-">年龄误告处理
							</div>
							<div id="approveAgeNewsInt" class="panel">
								<h1>年龄误告处理</h1>
								<div class="pageFormContent">

									<dl style="width: 500px">
										<dt style="width: 35%">出险人当前出生日期：</dt>
										<dd style="width: 60%">
											<input name="claimAdjustAgeVO.insuBirth"
												value="<s:date name='customerVO.customerBirthday' format='yyyy-MM-dd' />"
												type="text" readonly="readonly" />
										</dd>
									</dl>

									<dl style="width: 500px">
										<dt style="width: 32%">
											出险人真实出生日期：<font class="point" color="red">*</font>
										</dt>
										<dd style="width: 60%">
											<input type="expandDateYMD"
												name="claimAdjustAgeVO.insuRealBirth" class="date"
												value="<s:date name='claimAdjustAgeVO.insuRealBirth' format='yyyy-MM-dd' />" />
											<a class="inputDateButton" href="javascript:;">选择</a>
										</dd>
									</dl>
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>
		</form>
	</div>
</div>


