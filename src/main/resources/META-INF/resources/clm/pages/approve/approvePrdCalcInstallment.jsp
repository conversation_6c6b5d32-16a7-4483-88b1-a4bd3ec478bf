<%@ page language="java" pageEncoding="UTF-8"%>
<%@include file="/udmpCommon.jsp"%>
<%@ taglib uri="/struts-tags" prefix="s"%>
<%@ taglib uri="http://www.git.com.cn/taglib/udmp/field" prefix="Field"%>
<s:set var="ctx">${pageContext.request.contextPath}</s:set>


<link href="clm/css/jquery.dataTables_themeroller.css" rel="stylesheet" type="text/css" media="screen"/>
<link href="clm/css/jquery.dataTables.css" rel="stylesheet" type="text/css" media="screen"/>
<link href="clm/css/jquery.dataTables.min.css" rel="stylesheet" type="text/css" media="screen"/>
<script type="text/javascript" src="clm/js/jquery.dataTables.js"></script>
<script type="text/javascript">
	  $("#prdCalcInstallmentTable", navTab.getCurrentPanel()).dataTable({
		"bFilter" : false,
		"bLengthChange" : false,//自定义显示页数
		"iDisplayLength" : 10,
		"bPaginate" : true,
		"oLanguage" : {
			"sUrl" : "clm/js/jquery.dataTable.cn.txt"
		}
	});  
</script>
<!-- 分期支付计划页面 -->
<dl>
	<dt>计划开始时间</dt>
	<dd>
		<s:date name='calcInstallmentVO.installmentStartDate'
			format='yyyy-MM-dd' />
	</dd>
</dl>
<dl>
	<dt>分期类型</dt>
	<dd>
		<Field:codeValue tableName="APP___CLM__DBUSER.T_INSTALMENT_TYPE"
			value="${calcInstallmentVO.installmentType }" />
		<input type="hidden" name="installmentTypeHidden"
			id="installmentTypeHidden"
			value="${calcInstallmentVO.installmentType }" />
	</dd>
</dl>
<dl>
	<dt>支付/豁免频率</dt>
	<dd>
		<Field:codeValue tableName="APP___CLM__DBUSER.T_PAY_TYPE"
			value="${calcInstallmentVO.claimReveFrequency }" />
	</dd>
</dl>
<dl>
	<dt>支付/豁免次数</dt>
	<dd>${calcInstallmentVO.claimReveCount }</dd>
</dl>
<dl>
	<dt>生调频率</dt>
	<dd>
		<Field:codeValue tableName="APP___CLM__DBUSER.T_SURVERY_FREQ"
			value="${calcInstallmentVO.surveryFreqCode }" />
	</dd>
</dl>
<dl>
	<dt>下次生调期数</dt>
	<dd>${calcInstallmentVO.surveryPeriods }</dd>
</dl>
<div class="divider" style="border-style: none;"></div>
<div id="prdCalc" class="tabdivclassbr">
	<table id="prdCalcInstallmentTable" class="list" style="width: 100%;">
		<thead>
			<tr align="center">
				<th nowrap>序号</th>
				<th nowrap>支付日期</th>
				<th nowrap>分期类型</th>
				<th nowrap>支付金额</th>
				<th nowrap>支付状态</th>
				<th nowrap>生调标识</th>
			</tr>
		</thead>
		<tbody id="prdCalcInstallmentTbody">
			<s:iterator value="claimInstalmentList" status="st" id="task">
				<tr>
					<td>${st.index+1}</td>
					<td><s:date name='payDueDate' format='yyyy-MM-dd' /></td>
					<td><Field:codeValue tableName="APP___CLM__DBUSER.T_INSTALMENT_TYPE" value="${instalType}" /></td>
					<td>${principal}</td>
					<td><s:if test="0==instalStatus">未支付</s:if></td>
					<td><input type='checkbox' id='surveyFlag'
						value="${surveyFlag }" <s:if test="1==surveyFlag">checked</s:if>
						disabled></td>
				</tr>
			</s:iterator>
		</tbody>
	</table>
</div>
<%-- <div class="panelBar">
	<div class="pages">
		<span>显示</span>
		<s:select list="#{5:'5',10:'10',20:'20',50:'50',100:'100',200:'200'}"
			name="select" onchange="navTabPageBreak({numPerPage:this.value},'approve_instalment')"
			value="currentPage.pageSize">
		</s:select>
		<span>条，共${currentPage.total}条</span>
	</div>
	<div class="pagination" targetType="navTab"
		totalCount="${currentPage.total}" numPerPage="${currentPage.pageSize}"
		pageNumShown="10" currentPage="${currentPage.pageNo}" rel="approve_instalment"></div>
</div> --%>