<%@ page language="java" contentType="text/html; charset=utf-8"
	pageEncoding="UTF-8"%><%@ include file="/clm/pages/common/commonJsCss.jsp" %>
<%@ taglib uri="http://www.git.com.cn/taglib/udmp/field" prefix="Field"%>

<%@ taglib uri="/struts-tags" prefix="s"%>
<% String path=request.getContextPath();  String basePath=path+"/"; %>
<!--add xuyz_wb 分支流程 审批结论 页面-->
<script type="text/javascript" charset="UTF-8">
	//页面禁用
	var obj=$("div#toApproveConclusionInitBranchFlow", navTab.getCurrentPanel());
	//输入框 复选框 单选按钮  控制
	obj.find("input").each(function(){
		$(this).attr("disabled",true);
	});
	//下拉框
	obj.find("select").each(function(){
		$(this).attr("disabled",true);
	});
	//a标签
	obj.find("a").each(function(){
		$(this).attr("disabled",true);
	});
	//多行文本框控制
	obj.find("textarea").each(function(){
		$(this).attr("disabled",true);
	});

	//隐藏dwz封装的添加中的input
	function hidentRowNum(){
		$("[name='dwz_rowNum']").css("display","none");
	}
	setTimeout('hidentRowNum()',100);
</script>

<body>
	<div class="pageHeader">
		<div class="pageFormContent">
			<dl style="width: 40%;">
				<dt>赔案号</dt>
				<dd>
					<input name="sn" type="text" size="30"   value="${claimCaseVO.caseNo}"
						readonly="readonly" />
					<input type="hidden" name="claimCaseVO.caseId"
						value="${claimCaseVO.caseId}" id="claimCaseVOcASEID" />
					<input type="hidden" value="${claimCaseVO.caseId}" id="caseId" />
				</dd>
			</dl>
			<dl style="width: 40%;">
				<dt>事 件 号</dt>
				<dd>
					<input name="sn" type="text" size="30" value="${claimAccidentVO.accidentNo}"
						readonly="readonly" />
				</dd>
			</dl>
		</div>
		<div id="toApproveConclusionInitBranchFlow">
		<div class="pageFormContent">	
			<dl style="width: 100%;">
				<dt style="width: 30%">审批意见（包括符号最多700汉字）<font class="point" style="color:red;">*</font></dt>

			</dl>
			<dl style="width: 100%;">
				<dd style="height: 10px;">
					<textarea id="approveRemark"  style="width: 500px;" rows="2" cols="3" name="claimCaseVO.approveRemark">${claimCaseVO.approveRemark}</textarea>
				</dd>
			</dl>
		</div>
		<div class="pageFormContent">	
			<dl style="width:32%">
				<dt style="width: 32%">审批结论<font class="point" style="color:red;">*</font></dt>
				<dd style="width: 32%">
					<Field:codeTable cssClass="combox title"  id="approveDecision"
						name="claimCaseVO.approveDecision" tableName="APP___CLM__DBUSER.T_CLAIM_APPROVE_DECISION"
						value="${claimCaseVO.approveDecision}" nullOption="true" />
				</dd>
			</dl>
	
			<dl style="width: 32%">
				<dt style="width: 32%">案件标识<font class="point" style="color:red;">*</font></dt>
				<dd style="width: 32%">
					<Field:codeTable cssClass="combox title"  id="approveCaseFlag"
						name="claimCaseVO.caseFlag" tableName="APP___CLM__DBUSER.T_CASE_LEVEL"
						value="${claimCaseVO.caseFlag}" nullOption="true" whereClause="code in(1,2,3)"/>
				</dd>
			</dl>
			<dl style="width: 32%">
				<dt style="width: 32%">
					<input type="checkbox" size="5px" name="claimCaseVO.overCompFlag" value="1" <s:if test="claimCaseVO.overCompFlag==1">checked="checked"</s:if>/>超期补偿
				</dt>
				<dd ></dd>
			</dl>
			<dl style="width: 32%">
				<dt style="width: 32%">不通过原因<font class="point" style="color:red;">*</font></dt>
				<dd style="width: 32%">
					<Field:codeTable cssClass="combox title"  id="approveRejectReason"
						name="claimCaseVO.approveRejectReason" tableName="APP___CLM__DBUSER.T_CLAIM_APPROVE_REJECT"
						value="${claimCaseVO.approveRejectReason}" nullOption="true" />
				</dd>
			</dl>
		</div>
		</div>
	</div>
</body>
