<%@ page language="java" contentType="text/html; charset=utf-8" pageEncoding="UTF-8"%><%@ include file="/clm/pages/common/commonJsCss.jsp" %>
<%@ taglib uri="/struts-tags" prefix="s"%>
<%@ taglib uri="http://www.git.com.cn/taglib/udmp/field" prefix="Field"%>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<meta http-equiv="pragma" content="no-cache">
<meta http-equiv="cache-control" content="no-cache">
<meta http-equiv="expires" content="0"> 
 <script type="text/javascript" language="javascript"
	src="clm/js/commonMianBox.js">
</script>
<style>
.spanbox1 span {width:22%}
.spanbox2 span {width:22%}
</style>
<script type="text/javascript" >
$(document).ready(function(){
	//重大疾病显示格式
	var zdjb = $("select#approve_seriousDiseaseId",navTab.getCurrentPanel()).val();
	if(zdjb!=""){
		var name = $("select#approve_seriousDiseaseId",navTab.getCurrentPanel()).find("option[value="+zdjb+"]").text();
		$("#approve_seriousDiseaseIdInput",navTab.getCurrentPanel()).text(zdjb+"-"+name);
	}
	//轻度疾病
	var qdjb = $("select#approve_seriousDiseaseId1",navTab.getCurrentPanel()).val();
	if(qdjb!=""){
		var name = $("select#approve_seriousDiseaseId1",navTab.getCurrentPanel()).find("option[value="+qdjb+"]").text();
		$("#approve_seriousDiseaseIdInput1",navTab.getCurrentPanel()).text(qdjb+"-"+name);
	}
	//中度疾病
	var zhongjb = $("select#approve_seriousDiseaseId2",navTab.getCurrentPanel()).val();
	if(zhongjb!=""){
		var name = $("select#approve_seriousDiseaseId2",navTab.getCurrentPanel()).find("option[value="+zhongjb+"]").text();
		$("#approve_seriousDiseaseIdInput2",navTab.getCurrentPanel()).text(zhongjb+"-"+name);
	}
	//审批选中第一个保项结论
	if($("#approve_checkBody tr",navTab.getCurrentPanel()).find("input[type='radio']").length!=0){
		$("#approve_checkBody tr",navTab.getCurrentPanel()).find("input[type='radio']").eq(0).attr("checked","checked");
		queryLiabConclusion();
	}
});

//当理赔类型中包含身故时，显示其他身故原因
var tableInfo = "";
var countDie = 0 ;
var tableObj = document.getElementById("clmType");
for (var i = 0; i < tableObj.rows.length; i++) {
	tableInfo = tableObj.rows[i].cells[0].innerText.trim(); 
	if(tableInfo == "身故"){
		++countDie;
	}   
 }
if(countDie>0){
    document.getElementById("otherDieReasonMain").style.display="";
}

function queryLiabConclusion(){
	var midAfterFlag = '${middleThingFlag}';
	var afterFlag = '${afterFlag}';
	var navTabFlag = navTab.getCurrentPanel();
	if(midAfterFlag == "1" || afterFlag == "1"){
		navTabFlag = $.pdialog.getCurrent();
	}
	var claimLiabId = $("#approve_checkBody tr",navTabFlag).find("input[name='approve_r']:checked").parents("td").find("input:eq(1)").val();
	$.ajax({
		'url':"clm/audit/queryLiabConclusion_CLM_claimMatchResultAction.action",
		'type':'post',
		'data':{'claimLiabVO.claimLiabId':claimLiabId},
		'async':false,
		'success':function(json){
				var data = eval("(" + json + ")");
				//设置保项赔付结论
				if(data.liabConclusion!=0){
					if(data.liabConclusion==5){
						$("#unrefectDiv", navTab.getCurrentPanel()).hide();
						$("#refectDiv", navTab.getCurrentPanel()).show();
						var dataStr1 = data.rejectCode.split(',');
						for (i=0;i<dataStr1.length ;i++ )
						{	
							if(dataStr1[i]=='01'||dataStr1[i]==01){
//	 							$("#rejectCode1", navTab.getCurrentPanel()).val(dataStr1[i]);
								if(data.liabConclusion=="5"){//如果不给为拒付原因赋值
//			 						$("select#rejectCode", navTab.getCurrentPanel()).attr("value",data.rejectCode);
									$("#rejectCode1", navTab.getCurrentPanel()).attr("checked",true);
								}
							}
							if(dataStr1[i]=='02'||dataStr1[i]==02){
//	 							$("#rejectCode2", navTab.getCurrentPanel()).val(dataStr1[i]);
								if(data.liabConclusion=="5"){//如果不给为拒付原因赋值
//			 						$("select#rejectCode", navTab.getCurrentPanel()).attr("value",data.rejectCode);
									$("#rejectCode2", navTab.getCurrentPanel()).attr("checked",true);
								}
							}
							if(dataStr1[i]=='03'||dataStr1[i]==03){
//	 							$("#rejectCode3", navTab.getCurrentPanel()).val(dataStr1[i]);
								if(data.liabConclusion=="5"){//如果不给为拒付原因赋值
//			 						$("select#rejectCode", navTab.getCurrentPanel()).attr("value",data.rejectCode);
									$("#rejectCode3", navTab.getCurrentPanel()).attr("checked",true);
								}
							}
							if(dataStr1[i]=='04'||dataStr1[i]==04){
//	 							$("#rejectCode4", navTab.getCurrentPanel()).val(dataStr1[i]);
								if(data.liabConclusion=="5"){//如果不给为拒付原因赋值
//			 						$("select#rejectCode", navTab.getCurrentPanel()).attr("value",data.rejectCode);
									$("#rejectCode4", navTab.getCurrentPanel()).attr("checked",true);
								}
							}
							if(dataStr1[i]=='05'||dataStr1[i]==05){
//	 							$("#rejectCode5", navTab.getCurrentPanel()).val(dataStr1[i]);
								if(data.liabConclusion=="5"){//如果不给为拒付原因赋值
//			 						$("select#rejectCode", navTab.getCurrentPanel()).attr("value",data.rejectCode);
									$("#rejectCode5", navTab.getCurrentPanel()).attr("checked",true);
								}
							}
							if(dataStr1[i]=='06'||dataStr1[i]==06){
//	 							$("#rejectCode6", navTab.getCurrentPanel()).val(dataStr1[i]);
								if(data.liabConclusion=="5"){//如果不给为拒付原因赋值
//			 						$("select#rejectCode", navTab.getCurrentPanel()).attr("value",data.rejectCode);
									$("#rejectCode6", navTab.getCurrentPanel()).attr("checked",true);
								}
							}
							if(dataStr1[i]=='07'||dataStr1[i]==07){
//	 							$("#rejectCode7", navTab.getCurrentPanel()).val(dataStr1[i]);
								if(data.liabConclusion=="5"){//如果不给为拒付原因赋值
//			 						$("select#rejectCode", navTab.getCurrentPanel()).attr("value",data.rejectCode);
									$("#rejectCode7", navTab.getCurrentPanel()).attr("checked",true);
								}
							}
							if(dataStr1[i]=='08'||dataStr1[i]==08){
//	 							$("#rejectCode8", navTab.getCurrentPanel()).val(dataStr1[i]);
								if(data.liabConclusion=="5"){//如果不给为拒付原因赋值
//			 						$("select#rejectCode", navTab.getCurrentPanel()).attr("value",data.rejectCode);
									$("#rejectCode8", navTab.getCurrentPanel()).attr("checked",true);
								}
							}
							if(dataStr1[i]=='99'||dataStr1[i]==99){
//	 							$("#rejectCode99", navTab.getCurrentPanel()).val(dataStr1[i]);
								if(data.liabConclusion=="5"){//如果不给为拒付原因赋值
//			 						$("select#rejectCode", navTab.getCurrentPanel()).attr("value",data.rejectCode);
									$("#rejectCode99", navTab.getCurrentPanel()).attr("checked",true);
									var $isChecked = $("#rejectCode99").is(":checked");
									if($isChecked){
										$("#reject99", navTab.getCurrentPanel()).val(data.otherReason);
									}
								}
							}
						}
						if(data.specialRemark!=null&&data.specialRemark!=""){
						$("#matchSpecialRemark", navTab.getCurrentPanel()).val(data.specialRemark);
						}
					}else{
						$("#refectDiv", navTab.getCurrentPanel()).hide();
						$("#unrefectDiv", navTab.getCurrentPanel()).show();
					}
					$("a[name='ClaimLiabVO.liabConclusion']", navTabFlag).val(data.liabConclusion);
					$("a[name='ClaimLiabVO.liabConclusion']", navTabFlag).empty();
					$("a[name='ClaimLiabVO.liabConclusion']", navTabFlag).append($("select#approve_delivery",navTabFlag).find("option[value="+data.liabConclusion+"]").attr("title"));
					$("select#approve_delivery", navTabFlag).attr("value",data.liabConclusion);
					var liabConclusionName = $("select#approve_delivery", navTabFlag).find("option[value="+data.liabConclusion+"]").text();
					$("#approve_deliveryInput", navTabFlag).val(liabConclusionName); //给input赋值
					var isNormalName;
					if(data.liabConclusion == 1 || data.liabConclusion == 2){
						$("#approve_isNormal", navTabFlag).val("1");
						$("#approve_isNormalTwo", navTabFlag).css("display","none");
						$("#approve_isNormal", navTabFlag).css("display","block");
						isNormalName = $("select#approve_isNormal",navTabFlag).find("option[value='1']").text();
					}else if(data.liabConclusion == 3 || data.liabConclusion == 4){
						$("#approve_isNormal", navTabFlag).val("0");
						$("#approve_isNormalTwo", navTabFlag).css("display","none");
						$("#approve_isNormal", navTabFlag).css("display","block");
						isNormalName = $("select#approve_isNormal",navTabFlag).find("option[value='0']").text();
					}else{
						$("#approve_isNormalTwo", navTabFlag).css("display","block");
						$("#approve_isNormal", navTabFlag).css("display","none");
						isNormalName = $("select#approve_isNormalTwo",navTabFlag).find("option[value='']").text();
					}
					$("#approve_isNormalInput", navTabFlag).val(isNormalName); //给input赋值
				}
				//设置不可抗辩标识
				if(data.resistFlag==1){
					$("#approve_resistFlag", navTabFlag).attr("checked",true);
				}
				if(data.liabRiskLabel == 1){ //有风险
					$("#isRiskMatchResult", navTab.getCurrentPanel()).val("有风险");
				}
				if(data.liabRiskLabel == 0){ //无风险
					$("#isRiskMatchResult", navTab.getCurrentPanel()).val("无风险");
				}
				//设置调整金额
				if(data.adjustPay!=""&&data.adjustPay!="0"){
					$("#approve_adjustMoney", navTabFlag).attr("value",data.adjustPay);
				}
				//设置调整原因
				if(data.liabAdjustReason!=""){
					$("a[name='ClaimLiabVO.liabAdjustReason']", navTabFlag).val(data.liabAdjustReason);
					$("a[name='ClaimLiabVO.liabAdjustReason']", navTabFlag).empty();
					$("a[name='ClaimLiabVO.liabAdjustReason']", navTabFlag).append($("select#approve_adjustReason",navTabFlag).find("option[value="+data.liabAdjustReason+"]").attr("title"));
					$("select#approve_adjustReason", navTabFlag).val(data.liabAdjustReason);
					var adjustReasonName = $("select#approve_adjustReason",navTabFlag).find("option[value="+data.liabAdjustReason+"]").text();
					$("#approve_adjustReasonInput", navTabFlag).val(adjustReasonName); //给input赋值
				}
				//设置调整备注
				$("#approve_adjustRemark", navTabFlag).empty();
				$("#approve_adjustRemark", navTabFlag).append(data.adjustRemark);
				
			},
			'error':function(){
				alertMsg.error("系统出险异常，请联系管理员");
			}
	});
}
//收缩按钮 实现
function show(show){
		if(show.value=="-"){
			document.getElementById(show.hiddenDivId).style.display="none";
			show.value="+";
		}else if (show.value=="+"){
			document.getElementById(show.hiddenDivId).style.display="";
			show.value="-";
		}
}
//计算出险年龄
function approveCountAge(obj) {
	var now = $(obj).val();
	if (now.length != 0) {
		var dateNow = new Date(now.replace(/-/g, '/'));
		var birth = $("#approve_accBirthIdsaudit",$.pdialog.getCurrent()).val();
		var dateBirth = new Date(birth.replace(/-/g, '/'));
		var age=0;
		var years = dateNow.getFullYear()-dateBirth.getFullYear();
		var months = dateNow.getMonth()-dateBirth.getMonth();
		var days = dateNow.getDate()-dateBirth.getDate();
		if (months>0) {
			age=years;
		}else if (months<0) {
			age=years-1;
		}else if (days>=0) {
			age=years;
		}else {
			age=years-1;
		}
		$(obj).parent().next().html(age);
	}
}
//保单条款查看
function showPolicy(busiProdCode){
	var urlValue= "mobcss/policyed/" ;//替换掉&符号
	navTab.openTab("queryClaimImages", "clm/common/queryPolicyMsgUrl_CLM_commonQueryAction.action?productCodeOriginal="+busiProdCode+"&urlValue="+urlValue, {title:'保单条款查看'});
 }  
//退出
/* function exit(){
	  alertMsg.confirm("当前页面录入的信息将不被保存，是否确定退出？",{ 
	 	okCall:function(){
			navTab.closeCurrentTab();
	 	}
	 });
} */
</script>
<div onload="javascript:">
	<!-- 页面带进来的赔案号，事件号，只读 -->
	<div class="panelPageFormContent main_tabdiv">
		<dl>	
			 <dt>赔案号</dt>
			 <dd><input name="" type="text" size="17" value="${claimCaseVO.caseNo }" readonly="readonly"/></dd> 
		</dl> 
		
		<input name="caseId" type="hidden" size="17" value="${caseId }" readonly="readonly"/>
		<input id="approve_caseId" type="hidden" size="17" value="${caseId }" />
	    <input id="approve_caseNo" type="hidden" value="${claimCaseVO.caseNo }" />
	     
		<dl>	
			<dt>事件号</dt>
			<dd><input id="approve_ccidentNoId" name="accident.accidentNo" type="text" size="17" value="${accident.accidentNo}" readonly="readonly"/></dd> 
		</dl>
		<input type="hidden" id="approve_flagIdI" name="accident.flag" disabled="disabled"/>
   </div>
        
        <div class="main_box">
				<div class="main_heading"><h1><img src="clm/images/tubiao.png">申请人信息
					<b class="maim_lpask"></b></h1></div>
					<div class="main_lptwo">
					<p><div id="applicantUser">
		<div class="tabdivclassbr" >
			<table  class ="list" style="width:100%;" >
			   		 <thead>
						 <tr >
						   <th nowrap>序号</th>
						   <th nowrap>申请人姓名</th>
						   <th nowrap>与出险人关系</th>
						   <th nowrap>申请人电话</th>
						   <th nowrap>电子邮件</th>
						   <th nowrap>联系地址</th>
						</tr>
			 		 </thead>
				    <tbody id="approve_auditregister" class="tbody" >
                     <s:iterator value="applicantVOlist" status="st">
						<tr >
							<td align="center">
								${st.index+1}
							</td>
							<td align="center">
								<s:property value="clmtName"></s:property>
							</td>
							<td align="center">
							   <Field:codeValue tableName="APP___CLM__DBUSER.T_LA_PH_RELA" value="${clmtInsurRelation}"/>
							</td>
							<td align="center">
								<s:property value="clmtMp"></s:property>
							</td>
							<td align="center">
								<s:property value="clmtMail"></s:property>
							</td>
							<td align="left">
								<s:property value="clmtStreet"></s:property>
							</td>
						</tr>
					</s:iterator>
                   </tbody>																		
				</table>
		</div>
		
		<div class="panelPageFormContent main_tabdiv">
					<dl >
						<dt >申请类型</dt>
						<dd >
							<div class="main_datawhite"><Field:codeValue tableName="APP___CLM__DBUSER.T_APPLY_TYPE" value="${claimCaseVO.caseApplyType}"/></div>
						</dd>
					</dl>
					<dl>
				      <dt>申请渠道</dt>
				      <dd>
				         <div class="main_datawhite"><Field:codeValue tableName="APP___CLM__DBUSER.T_ACCEPT_CHANNEL" value="${claimCaseVO.channelCode}" /></div>
				      </dd>
				    </dl>
					<dl >
						<dt >申请日期</dt>
						<dd >
							<div class="main_datawhite"><s:date name='claimCaseVO.applyDate' format='yyyy-MM-dd'/></div>
						</dd>
					</dl>
					<dl >
						<dt >受理时间</dt>
						<dd >
							<div class="main_datawhite"><s:date name='claimCaseVO.acceptTime' format='yyyy-MM-dd'/></div>
						</dd>
					</dl>
					<dl >
						<dt >管理机构</dt>
						<dd >
							<div class="main_datawhite" title="${claimCaseVO.organCode}-${claimCaseVO.organName}">${claimCaseVO.organCode}-${claimCaseVO.organName}</div>
						</dd>
					</dl>
					<%-- <dl >
						<dt >受理人</dt>
						<dd >
							<div class="main_datawhite">${claimCaseVO.userRealName}</div>
						</dd>
					</dl> --%>
					<dl >
						<dt >签收人</dt>
						<dd >
							${claimCaseVO.signerCode}&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;${claimCaseVO.signerName}
						</dd>
					</dl>
					<dl >
						<dt >立案人</dt>
						<dd >
						${claimCaseVO.registerCode}&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;${claimCaseVO.registerName}
						</dd>
					</dl>
					<dl >
						<dt >绩优等级</dt>
						<s:if test="claimCaseVO.greenFlag == 0 || claimCaseVO.greenFlag ==null ">
						  <dd >
							<div class="main_datawhite"><Field:codeValue tableName="APP___CLM__DBUSER.T_CLAIM_GREEN_FLAG" value="0"/></div>
						  </dd>
						</s:if>
						<s:else>
						  <dd >
							<div class="main_datawhite"><Field:codeValue tableName="APP___CLM__DBUSER.T_CLAIM_GREEN_FLAG" value="${claimCaseVO.greenFlag}"/></div>
						  </dd>
					    </s:else>
					</dl>
				</div>
		
		
	 </div></p>				
				</div>
			</div>
	 
		    <!-- 受托人信息 -->
	<s:if test="claimCaseVO.trusteeCode != null">
	
		<div class="main_box">
				<div class="main_heading"><h1><img src="clm/images/tubiao.png">受托人信息
					<b class="maim_lpask"></b></h1></div>
					<div class="main_lptwo">
					<p><div id="trusteeUser">
				<div class="panelPageFormContent" >
	                 <dl style="width: 32%">
						<dt>受托人类型</dt>
						<dd>
						    <input type="text" style="border:0px;" value="<Field:codeValue tableName="APP___CLM__DBUSER.T_TRUSTEE_TYPE" value="${claimCaseVO.trusteeType}"/>" readonly/>
						</dd>
					</dl>
	               <dl>
						<dt>受托人代码</dt>
						<dd>
							<input type="text" style="border:0px;" value="${claimCaseVO.trusteeCode}" readonly/>
						</dd>
					</dl>
				    <dl>
						<dt>受托人姓名</dt>
						<dd>
							<input type="text" style="border:0px;" value='<s:property value="claimCaseVO.trusteeName"/>' readonly/>
						</dd>
					</dl>
				    <dl>
						<dt>手机</dt>
						<dd>
							<input type="text" style="border:0px;" value="${claimCaseVO.trusteeMp}" readonly/>
						</dd>
					</dl>
				    <dl>
					   <dt>固定电话</dt>
					   <dd>
					   <input type="text" style="border:0px;" value="${claimCaseVO.trusteeTel}" readonly/>
					   </dd>
				    </dl>
				    <dl>
					   <dt>证件类型</dt>
					   <dd>
					    <input type="text" style="border:0px;" value="<Field:codeValue tableName="APP___CLM__DBUSER.T_CERTI_TYPE" value="${claimCaseVO.trusteeCertiType}"/>" readonly/>
				       </dd>
				    </dl>
				    <dl>
					   <dt>证件号码</dt>
					   <dd>
					   <input type="text" style="border:0px;" value="${claimCaseVO.trusteeCertiCode}" readonly/>
					   </dd>
				    </dl>
				   <dl>
						<dt>上门签收日期</dt>
						<dd>
						    <input type="text" style="border:0px;" value="<s:date name='claimCaseVO.doorSignTime' format='yyyy-MM-dd'/>" readonly/>
						</dd>
					</dl>
				</div>
	        </div></p>				
				</div>
			</div>
	
<!-- 		<div class="divfclass"> -->
<!-- 			<h1> -->
<!-- 				<button onclick="show(this)" type="button" style="width:20px;height:20px;text-align:center;" hiddenDivId="trusteeUser" value="-">-</button>受托人信息 -->
<!-- 			</h1> -->
<!-- 		</div> -->
<!-- 	    <div id="trusteeUser"> -->
<!-- 				<div class="panelPageFormContent" > -->
<!-- 	                 <dl style="width: 32%"> -->
<!-- 						<dt>受托人类型</dt> -->
<!-- 						<dd> -->
<%-- 						    <input type="text" style="border:0px;" value="<Field:codeValue tableName="APP___CLM__DBUSER.T_TRUSTEE_TYPE" value="${claimCaseVO.trusteeType}"/>" readonly/> --%>
<!-- 						</dd> -->
<!-- 					</dl> -->
<!-- 	               <dl> -->
<!-- 						<dt>受托人代码</dt> -->
<!-- 						<dd> -->
<%-- 							<input type="text" style="border:0px;" value="${claimCaseVO.trusteeCode}" readonly/> --%>
<!-- 						</dd> -->
<!-- 					</dl> -->
<!-- 				    <dl> -->
<!-- 						<dt>受托人姓名</dt> -->
<!-- 						<dd> -->
<%-- 							<input type="text" style="border:0px;" value='<s:property value="claimCaseVO.trusteeName"/>' readonly/> --%>
<!-- 						</dd> -->
<!-- 					</dl> -->
<!-- 				    <dl> -->
<!-- 						<dt>手机</dt> -->
<!-- 						<dd> -->
<%-- 							<input type="text" style="border:0px;" value="${claimCaseVO.trusteeMp}" readonly/> --%>
<!-- 						</dd> -->
<!-- 					</dl> -->
<!-- 				    <dl> -->
<!-- 					   <dt>固定电话</dt> -->
<!-- 					   <dd> -->
<%-- 					   <input type="text" style="border:0px;" value="${claimCaseVO.trusteeTel}" readonly/> --%>
<!-- 					   </dd> -->
<!-- 				    </dl> -->
<!-- 				    <dl> -->
<!-- 					   <dt>证件类型</dt> -->
<!-- 					   <dd> -->
<%-- 					    <input type="text" style="border:0px;" value="<Field:codeValue tableName="APP___CLM__DBUSER.T_CERTI_TYPE" value="${claimCaseVO.trusteeCertiType}"/>" readonly/> --%>
<!-- 				       </dd> -->
<!-- 				    </dl> -->
<!-- 				    <dl> -->
<!-- 					   <dt>证件号码</dt> -->
<!-- 					   <dd> -->
<%-- 					   <input type="text" style="border:0px;" value="${claimCaseVO.trusteeCertiCode}" readonly/> --%>
<!-- 					   </dd> -->
<!-- 				    </dl> -->
<!-- 				   <dl> -->
<!-- 						<dt>上门签收日期</dt> -->
<!-- 						<dd> -->
<%-- 						    <input type="text" style="border:0px;" value="<s:date name='claimCaseVO.doorSignTime' format='yyyy-MM-dd'/>" readonly/> --%>
<!-- 						</dd> -->
<!-- 					</dl> -->
<!-- 				</div> -->
<!-- 	        </div> -->
	    </s:if> 
	        
	    <!-- 出险人信息 -->
	    
	    <div class="main_box">
				<div class="main_heading"><h1><img src="clm/images/tubiao.png">出险人信息
					<b class="maim_lpask"></b></h1></div>
					<div class="main_lptwo">
					<p><div id="customerUser">
			<div id="approve_approveDataCollectInsured">
				<div class="tabdivclassbr" >
					<table class="list" style="width:100%;" >
						<thead>
							<tr>
								<th nowrap>出险人姓名</th>
								<th nowrap>证件号码</th>
								<th nowrap>性别</th>
								<th nowrap>出生日期</th>
							</tr>
						</thead>
						<tbody id="approve_claimAccidentCUSInfo" align="center">
							<tr>
								<td>${customerVO.customerName }</td>
								<td>${customerVO.customerCertiCode }</td>
								<td><Field:codeValue tableName="APP___CLM__DBUSER.T_GENDER" value="${customerVO.customerGender}"/></td> 
								<td><s:date name='customerVO.customerBirthday' format='yyyy-MM-dd' /></td>
							</tr>
						</tbody>
					</table>
				</div>
			</div>
	    </div></p>				
				</div>
			</div>
	        
        <!-- 出险信息 -->
        
        <div class="main_box">
				<div class="main_heading"><h1><img src="clm/images/tubiao.png">出险信息
					<b class="maim_lpask"></b></h1></div>
					<div class="main_lptwo">
					<p><div id="customerMessage">
			<div id="approve_approveDataCollectCustomer">
				<div class="panelPageFormContent" >
					<dl>
						<dt>出险人姓名</dt>
						<dd>
							<div class="main_datawhite">${customerVO.customerName}</div>
<%-- 							 <input type="text" style="border:0px;" value="${customerVO.customerName}" readonly/> --%>
						</dd>
					</dl>
				    <dl>
						<dt>证件号码</dt>
						<dd>
							<div class="main_datawhite">${customerVO.customerCertiCode }</div>
<%-- 							<input type="text" style="border:0px;" value="${customerVO.customerCertiCode }" readonly/> --%>
							<input id="approve_accBirthIdsaudit" type="hidden" value="<s:date name='customerVO.customerBirthday' format='yyyy-MM-dd' />" class="date" />
						</dd>
					</dl>
				    <dl>
					   <dt>性别</dt>
					   <dd>
					   		<div class="main_datawhite"><Field:codeValue tableName="APP___CLM__DBUSER.T_GENDER" value="${customerVO.customerGender}"/></div>
<%-- 					        <input type="text" style="border:0px;" value="<Field:codeValue tableName="APP___CLM__DBUSER.T_GENDER" value="${customerVO.customerGender}"/>" readonly/> --%>
					   </dd>
				    </dl>
				    
				    <div class="mian_site">
		                <dl>
		                   <dt>出险地点</dt>
		                </dl>
		                <div class="main_detail">
		                    <dl><dd><div class="main_datawhite" title="<Field:codeValue tableName="APP___CLM__DBUSER.T_COUNTRY" value="${accident.accCountryCode}"/>"><Field:codeValue tableName="APP___CLM__DBUSER.T_COUNTRY" value="${accident.accCountryCode}"/></div></dd></dl>
		                    <dl><dd><div class="main_datawhite" title="<Field:codeValue tableName="APP___CLM__DBUSER.T_DISTRICT" value="${accident.accProvince }"/>"><Field:codeValue tableName="APP___CLM__DBUSER.T_DISTRICT" value="${accident.accProvince }"/></div><span>&nbsp;&nbsp;-&nbsp;&nbsp;</span></dd></dl>
		                    <dl><dd><div class="main_datawhite" title="<Field:codeValue tableName="APP___CLM__DBUSER.T_DISTRICT" value="${accident.accCity }"/>"><Field:codeValue tableName="APP___CLM__DBUSER.T_DISTRICT" value="${accident.accCity }"/></div> <span>&nbsp;&nbsp;-&nbsp;&nbsp;&nbsp;</span></dd></dl>
		                    <dl><dd><div class="main_datawhite" title="<Field:codeValue tableName="APP___CLM__DBUSER.T_DISTRICT" value="${accident.accDistreact }"/>"><Field:codeValue tableName="APP___CLM__DBUSER.T_DISTRICT" value="${accident.accDistreact }"/></div> <span>&nbsp;&nbsp;-&nbsp;&nbsp;&nbsp;</span></dd></dl>
		                    <dl><dd><div class="main_datawhite" title="${accident.accStreet}" style="width:280px;">${accident.accStreet}</div><span>-</span></dd></dl>
		                </div>
		            </div>
	               	<dl>
						<dt>出险原因</dt>
						<dd>
							<div class="main_datawhite"><Field:codeValue tableName="APP___CLM__DBUSER.T_CLAIM_NATURE" value="${accident.accReason }"></Field:codeValue></div>
<%-- 						    <input type="text" style="border:0px;" value="<Field:codeValue tableName="APP___CLM__DBUSER.T_CLAIM_NATURE" value="${accident.accReason }"/>" readonly/> --%>
						</dd>
					</dl>					
					<dl id="otherDieReasonMain" style="display: none;">
						<dt>其他身故原因</dt>
						<dd>
							<div class="main_datawhite"><Field:codeValue tableName="APP___CLM__DBUSER.T_CLAIM_DIE_REASON" value="${accident.otherDieReason }"></Field:codeValue></div>
						</dd>
					</dl>
				    <dl>
					   <dt>事故日期</dt>
					   <dd>
					   		<div class="main_datawhite"><s:date name='accident.accDate' format='yyyy-MM-dd'></s:date></div>
<%-- 					       <input type="text" style="border:0px;" value="<s:date name='accident.accDate' format='yyyy-MM-dd'/>" readonly/> --%>
					   </dd>
				    </dl>
				    <dl>
						<dt>确诊时间</dt>
						<dd>
							<div class="main_datawhite"><s:date name='claimCaseVO.diagnosisTime' format='yyyy-MM-dd'></s:date></div>
						</dd>							 
					</dl>
					<dl>
						<dt>轻度疾病</dt>
						<dd>
						    <div class="main_datawhite" id="approve_seriousDiseaseIdInput1"></div>
						    <div style="display:none;">
						    <Field:codeTable cssClass="combox title"  id="approve_seriousDiseaseId1"
									name="claimCaseVO.seriousDisease1"
 									value="${claimCaseVO.seriousDisease1}" nullOption="true" tableName="APP___CLM__DBUSER.t_la_type" whereClause="1 = 1" orderBy="code"></Field:codeTable>
						    </div>
						</dd>
					</dl>
					<dl>
						<dt>中度疾病</dt>
						<dd>
						    <div class="main_datawhite" id="approve_seriousDiseaseIdInput2"></div>
						    <div style="display:none;">
						    <Field:codeTable cssClass="combox title"  id="approve_seriousDiseaseId2"
									name="claimCaseVO.seriousDisease2"
 									value="${claimCaseVO.seriousDisease2}" nullOption="true" tableName="APP___CLM__DBUSER.t_la_type" whereClause="1 = 1" orderBy="code"></Field:codeTable>
						    </div>
						</dd>
					</dl>
				    <dl>
						<dt>重大疾病</dt>
						<dd>
<!-- 						    <input type="text" id="approve_seriousDiseaseIdInput" style="border:0px;" size="100" value="-" readonly/> -->
						    <div class="main_datawhite" id="approve_seriousDiseaseIdInput"></div>
						    <div style="display:none;">
						    <Field:codeTable cssClass="combox title"  id="approve_seriousDiseaseId"
									name="claimCaseVO.seriousDisease"
 									value="${claimCaseVO.seriousDisease}" nullOption="true" tableName="APP___CLM__DBUSER.t_la_type" whereClause="1 = 1" orderBy="code"></Field:codeTable>
						    </div>
						</dd>
					</dl>
					<dl>
						<dt>治疗医院</dt>
						<dd>
							<div class="main_datawhite" title="${claimCaseVO.cureHospital} - ${hospitalServiceVO.hospitalName}">${claimCaseVO.cureHospital} - ${hospitalServiceVO.hospitalName}</div>
<%-- 							<input type="text" style="border:0px;" value="${claimCaseVO.cureHospital} - ${hospitalServiceVO.hospitalName}" readonly/> --%>
						</dd>
				    </dl>
				    <dl>
						<dt>治疗情况</dt>
						<dd>
							<div class="main_datawhite"><Field:codeValue tableName="APP___CLM__DBUSER.T_CURE_STATUS" value="${claimCaseVO.cureStatus }"/></div>
<%-- 						    <input type="text" style="border:0px;" 50 value="<Field:codeValue tableName="APP___CLM__DBUSER.T_CURE_STATUS" value="${claimCaseVO.cureStatus }"/>" readonly/> --%>
						</dd>
				    </dl>
				    <dl>
						<dt>认可医院标识</dt>
						<dd>
							<s:if test='hospitalServiceVO.isDesignated == 0'>
								<div id="hospitalSignHtmlInput" class="main_datawhite" style="color: #FF0000">否</div>
							</s:if>
							<s:elseif test='hospitalServiceVO.isDesignated == 1'>
								<div id="hospitalSignHtmlInput" class="main_datawhite">是</div>
							</s:elseif>
							<s:else>
								<div id="hospitalSignHtmlInput" class="main_datawhite"></div>
							</s:else>
						</dd>
					</dl>					 
				    
				     <dl>
						<dt>社保状态</dt>
						<dd>
							<div class="main_datawhite"><s:if test='sociSecu == "0"'>否</s:if><s:elseif test='sociSecu == "1"'>是</s:elseif></div>
<%-- 						<input type="text" style="border:0px;" value="<s:if test='sociSecu == "0"'>否</s:if><s:elseif test='sociSecu == "1"'>是</s:elseif>" readonly/> --%>
						</dd>
				    </dl>
				    <dl>
						<dt>意外细节</dt>
						<dd>
							<div class="main_datawhite" title="${claimCaseVO.accidentDetail}-${accidentDetailVO.detailDesc}">${claimCaseVO.accidentDetail}-${accidentDetailVO.detailDesc}</div>
<%-- 							<input type="text" style="border:0px;" value="${claimCaseVO.accidentDetail} - ${accidentDetailVO.detailDesc}" readonly/> --%>
						</dd>
					</dl>
					<dl>
					   <dt>是否在商业保司理赔</dt>
					   <dd>
					   		<div class="main_datawhite"><Field:codeValue tableName="APP___CLM__DBUSER.T_YES_NO" value="${claimCaseVO.insurancePaidFlag}"/></div>
<%-- 					        <input type="text" style="border:0px;" value="<Field:codeValue tableName="APP___CLM__DBUSER.T_GENDER" value="${customerVO.customerGender}"/>" readonly/> --%>
					   </dd>
				    </dl>
					</div>
				    <div class="tabdivclassbr main_tabdiv">
					    <table class="list" style="width:100%">
							<thead>
								<tr>
									<th nowrap>序号</th>
									<th nowrap>诊断编码</th>
									<th nowrap>诊断名称</th>
									<th nowrap>诊断时间</th>
									<th nowrap>诊断医生姓名</th>
									<th nowrap>归属科室</th>
									<th nowrap>中西医标识</th>
									<th nowrap>就诊类型</th>
									<th nowrap>最后一次修改人</th>
								</tr>
							</thead>
							<tbody id="approve_claimDoctor" align="center">
								<s:iterator value="claimDoctorVOList" var="claimDoctorlist" status="st">
									<tr>
										<td>${st.index+1}</td>
										<td>${diagnosisCode}</td>
										<td>${diagnosisDesc}</td>
										<td><s:date name='diagTime' format='yyyy-MM-dd' /></td>
										<td>${doctorName}</td>
										<td><Field:codeValue tableName="APP___CLM__DBUSER.T_MEDICAL_DEPT" value="${code}" /></td>
										<td>
											<s:if test="#claimDoctorlist.tcmFlag == 0">
											 	中
											 </s:if>
											 <s:if test="#claimDoctorlist.tcmFlag == 1">
											 	西
											 </s:if>
										</td>
										<td>
										<s:if test="#claimDoctorlist.medicalType == 0">
										 	门诊
										 </s:if>
										 <s:if test="#claimDoctorlist.medicalType == 1">
										 	住院
										 </s:if>
										</td>
										<td>${updateName}</td>
									</tr>
								</s:iterator>
							</tbody>
						</table>
					</div>
				    <dl>
					   <dt></dt>
					   <dd>
					    &nbsp;   
					   </dd>
				    </dl>
				<div class="tabdivclassbr main_tabdiv">
					<table class="list" style="width:100%">
						<thead>
						<tr>
							<th nowrap>序号</th>
							<th nowrap>鉴定机构名称</th>
							<th nowrap>统一社会信用代码</th>
							<th nowrap>委托人/委托单位</th>
							<th nowrap>委托鉴定事项</th>
							<th nowrap>受理日期</th>
							<th nowrap>鉴定日期</th>
							<th nowrap>鉴定报告出具日期</th>
							<th nowrap>司法鉴定人姓名</th>
							<th nowrap>执业证号</th>
							<th nowrap>最后一次修改人</th>
						</tr>
						</thead>
						<tbody id="approve_claimIdentifyInfo" align="center">
						<s:iterator value="claimIdentifyInfoVOList" var="claimIdentifyInfoList" status="st">
							<tr>
								<td>${st.index+1}</td>
								<td>${claimIdentifyInfoList.accreditingBodyName}</td>
								<td>${claimIdentifyInfoList.sociolCreditCode}</td>
								<td>${claimIdentifyInfoList.lient}</td>
								<td>${claimIdentifyInfoList.entrustingAppraisalMatter}</td>
								<td><s:date name='#claimIdentifyInfoList.acceptanceDate' format='yyyy-MM-dd' /></td>
								<td><s:date name='#claimIdentifyInfoList.appraisalDate' format='yyyy-MM-dd' /></td>
								<td><s:date name='#claimIdentifyInfoList.acceptanceDate' format='yyyy-MM-dd' /></td>
								<td>${claimIdentifyInfoList.appraiserName}</td>
								<td>${claimIdentifyInfoList.unitCertiCode}</td>
								<td>${claimIdentifyInfoList.endUpdateByName}</td>
							</tr>
						</s:iterator>
						</tbody>
					</table>
				</div>
				<dl>
					<dt></dt>
					<dd>
						&nbsp;
					</dd>
				</dl>
				    <div class="tabdivclassbr" >
				    <table class="list" style="width:100%">
						<thead>
							<tr>
								<th nowrap>序号</th>
								<th nowrap>出险结果1</th>
								<th nowrap>出险结果2</th>
								<th nowrap>出险结果3</th>
								<th nowrap>最后一次修改人</th>
							</tr>
						</thead>
						<tbody class="tbodys">
							<s:iterator value="listResultVo" var="rlist" status="st">
								<tr>
									<td>
										${st.index+1}
									</td>
									<td>
									${rlist.accResult1}-${rlist.accResultDesc1}
									</td>
									<td>
									${rlist.accResult2}-${rlist.accResultDesc2}
									</td>
									<td>
									${rlist.accResult3}-${rlist.accResult3Name}
									</td>
									<td>${updateName}</td>
								</tr>
							</s:iterator>
						</tbody>
					</table>
					</div>
				    <dl>
					   <dt></dt>
					   <dd>
					    &nbsp;   
					   </dd>
				    </dl>
				    <div class="tabdivclassbr" >
				    	<table class="list" style="width:100%;" id = "clmType">
							<thead>
								<tr>
									<th nowrap>理赔类型</th>
									<th nowrap>出险日期</th>
									<th nowrap>出险时年龄</th>
								</tr>
							</thead>
							<tbody id="approve_tbodyIds">
								<s:iterator value="subCaseVoList" status="sub" var="subCase">
									<tr>
										<td>
											<Field:codeValue tableName="APP___CLM__DBUSER.T_CLAIM_TYPE" value="${subCase.claimType }"/>
<%-- 										    <input type="text" style="border:0px;" size="20" value="<Field:codeValue tableName="APP___CLM__DBUSER.T_CLAIM_TYPE" value="${subCase.claimType }"/>" readonly/> --%>
										</td>	
										<td>
											<s:date name="#subCase.claimDate" format='yyyy-MM-dd'/>
											<%-- <input type="hidden" style="border:0px;" value="<s:date name='#subCase.claimDate' format='yyyy-MM-dd'/>" onPropertychange="approveCountAge(this)" readonly/> --%>
										</td> 
										<td>
											<input type="text" style="border:0px;" value="${subCase.accAgeString}" readonly/>
										</td>
									</tr>
								</s:iterator>
						   </tbody>
					    </table>
				    </div>
				    <div class="panelPageFormContent main_tabdiv" >
					    <dl style="width: 100%;height: auto;">
		                   	<dt>事故描述</dt>
		                   	<dd><textarea rows="3" cols="100" maxlength="1000" readonly>${accident.accDesc }</textarea></dd>
		                </dl>	
					</div>
	          </div>
            </div></p>				
				</div>
			</div>
        
            
            <!-- 赔案计算信息 -->
            <div class="main_box">
				<div class="main_heading"><h1><img src="clm/images/tubiao.png">赔案计算信息
					<b class="maim_lpask"></b></h1></div>
					<div class="main_lptwo">
					<p><div id="claimCasePay">
				<div class="tabdivclassbr">
					<table class="list" width="100%">
						<thead>
							<tr>
								<th nowrap>赔付金额</th>
								<th nowrap>预付金额</th>
								<th nowrap>结算金额</th>
								<th nowrap>最终赔付金额</th>
								<th nowrap>拒赔金额</th>
							</tr>
						</thead>
						<tbody align="center">
							<s:iterator value="%{claimCaseList}" status="st" id="claimCase">
							<tr>
								<td><s:property value="#claimCase.calcPay"/></td>
		                        <td><s:property value="#claimCase.advancePay"/></td>
		                        <td><s:property value="#claimCase.balancePay"/></td>
		                        <td><s:property value="#claimCase.actualPay"/></td>
		                        <td><s:property value="#claimCase.rejectPay"/></td>
							</tr>
							</s:iterator>
						</tbody>
					</table>
				</div>
	        </div></p>				
				</div>
			</div>
	        <!--理赔类型计算信息 -->
	        
	        <div class="main_box">
				<div class="main_heading"><h1><img src="clm/images/tubiao.png">理赔类型计算信息
					<b class="maim_lpask"></b></h1></div>
					<div class="main_lptwo">
					<p> <div id="claimTypeCalc">
					<div class="tabdivclassbr">
						<table class="list" width="100%">
							<thead>
								<tr>
									<th nowrap>序号</th>
									<th nowrap>理赔类型</th>
									<th nowrap>账单金额</th>
									<th nowrap>保单合计理算金额</th>
									<th nowrap>社保给付</th>
									<th nowrap>第三方给付</th>
									<th nowrap>核赔赔付金额</th>
									<th nowrap>住院账单金额</th>
									<th nowrap>门诊账单金额</th>
								</tr>
							</thead>
							<tbody id="approve_claimTypeCalcResult">
								<s:iterator value="%{claimTypeCalcList}" status="st" id="cliamLiabInfo" >
								<tr align="center">
									<td>${st.index+1}</td>
									<td><s:property value="#cliamLiabInfo.claimType"/></td>
			                        <td><s:property value="#cliamLiabInfo.sumAmountBill"/></td>
			                        <td><s:property value="#cliamLiabInfo.calcPay"/></td>                   
			                        <td><s:property value="#cliamLiabInfo.paidAmount"/></td>
			                        <td><s:property value="#cliamLiabInfo.sumAmount"/></td>
			                        <td><s:property value="#cliamLiabInfo.actualPay"/></td>
			                        <td><s:property value="#cliamLiabInfo.hospitalizedFeeAmount"/></td>
	                        		<td><s:property value="#cliamLiabInfo.outpatientFeeAmount"/></td>
								</tr>
								</s:iterator>
							</tbody>
						</table>
					</div>
	        </div></p>				
				</div>
			</div>
	        <!--保单计算信息 -->
	        
	        <div class="main_box">
				<div class="main_heading"><h1><img src="clm/images/tubiao.png">保单计算信息
					<b class="maim_lpask"></b></h1></div>
					<div class="main_lptwo">
					<p><div id="claimProductCalc">
					<div class="tabdivclassbr">
						<table class="list" width="100%">
							<thead>
								<tr>
									<th nowrap>序号</th>
									<th nowrap>保单号</th>
									<th nowrap>理赔类型</th>
									<th nowrap>生效日期</th>
									<th nowrap>交至日期</th>
									<th nowrap>险种代码</th>
									<th nowrap>险种名称</th>
									<th nowrap>理算金额</th>
									<th nowrap>账户价值</th>
									<th nowrap>风险保额</th>
									<!-- <th nowrap>保单条款</th> -->
								</tr>
							</thead>
							<tbody>
								<s:iterator value="%{claimProductList}" status="st"  id="claimBusiProd">
								<tr align="center">
									<td>${st.index+1}</td>
									<td><s:property value="#claimBusiProd.policyCode"/></td>
									<td>
									<Field:codeValue tableName="APP___CLM__DBUSER.T_CLAIM_TYPE" value="${claimBusiProd.claimType}"/>
									</td>
									<td><s:date name="#claimBusiProd.validDate" format="yyyy-MM-dd"/></td>
									<td><s:date name="#claimBusiProd.dueDate" format="yyyy-MM-dd"/></td>
									<td><s:property value="#claimBusiProd.busiProdCode"/></td>
									<td><s:property value="#claimBusiProd.productNameStd"/> </td>
									<td><s:property value="#claimBusiProd.actualPay"/></td>
									<td><s:property value="#claimBusiProd.accountValue"/></td>
									<td><s:property value="#claimBusiProd.insuredAmount"/></td>
									<%-- <td><a href="javascript:showPolicy('${claimBusiProd.busiProdCode}')">查看</a></td> --%>
								</tr>
								</s:iterator>
							</tbody>
						</table>
					</div>
	        </div></p>				
				</div>
			</div>
	        <!--保项计算信息 -->
	        <div class="main_box">
				<div class="main_heading"><h1><img src="clm/images/tubiao.png">保项计算信息
					<b class="maim_lpask"></b></h1></div>
					<div class="main_lptwo">
					<p> <div id="queryClaimLiabCalc">
					<div class="tabdivclassbr">
						<table class="list" width="100%">
							<thead>
								<tr>
									<th nowrap>选择</th>
									<th nowrap>序号</th>
									<th nowrap>保单号</th>
									<th nowrap>险种代码</th>
									<th nowrap>保险责任</th>
									<th nowrap>责任起期</th>
									<th nowrap>责任止期</th>
									<th nowrap>宽限天数</th>
									<th nowrap>保额</th>
									<th nowrap>年度红利</th>
									<th nowrap>终了红利</th>
									<th nowrap>理算金额</th>
									<th nowrap>出险期间标识</th>
									<th nowrap>给付代码</th>
									<th nowrap>预付金额</th>
									<th nowrap>调整金额</th>
								</tr>
							</thead>
							<tbody id="approve_checkBody">
								<s:iterator value="%{queryClaimLiabList}"  status="st" id="claimLiab">
								<s:if test="#claimLiab.liabConclusion != 6">
									<tr align="center"  target="ListIdInfo" rel="${claimLiabId}">
										<td><input type="radio" name="approve_r" onclick="queryLiabConclusion()"><input type="hidden" value='${claimLiabId}'></td>
										<td> ${st.index+1}</td>
										<td><s:property value="#claimLiab.policyCode"/></td>
										<td><s:property value="#claimLiab.busiProdCode"/></td>
										<td><s:property value="#claimLiab.liabName" /></td>
										<td><s:date name="#claimLiab.liabStartDate" format="yyyy-MM-dd"/></td>
										<td><s:date name="#claimLiab.liabEndDate" format="yyyy-MM-dd"/></td>
										<td><s:property value="#claimLiab.gracePeriod" /></td>
										<td><s:property value="#claimLiab.proAmount"/></td>
										<td><s:property value="#claimLiab.proBonusSa"/></td>
										<td><s:property value="#claimLiab.payAmount"/></td>
										<td><s:property value="#claimLiab.calcPay" /><input type="hidden" name="approve_calcPay" value="<s:property value='#claimLiab.calcPay'/>"/></td>
										<td></td>
										<td>
										<s:if test="#claimLiab.liabConclusion == 5">	
											<input type="radio" name="r3${st.index}" value="1" disabled>给
											<input type="radio" name="r3${st.index}" value="2" checked disabled>不给
										</s:if>
										<s:elseif test="#claimLiab.liabConclusion != 5 && #claimLiab.liabConclusion != null">
											<input type="radio" name="r3${st.index}" value="1" checked disabled>给
											<input type="radio" name="r3${st.index}" value="2" disabled>不给
										</s:elseif>
										<s:else>
											<input type="radio" name="r3${st.index}" value="1" checked disabled>给
											<input type="radio" name="r3${st.index}" value="2" disabled>不给
										</s:else>
										</td>
										<td><s:property value="#claimLiab.advancePay"/></td>
										<td><s:property value="#claimLiab.adjustPay"/></td>
									</tr>
								</s:if>
								</s:iterator>
							</tbody>
						</table>
					</div>
	        </div></p>				
				</div>
			</div>
            <!--保项赔付结论 -->
            
            <div class="main_box">
				<div class="main_heading"><h1><img src="clm/images/tubiao.png">保项赔付结论
					<b class="maim_lpask"></b></h1></div>
					<div class="main_lptwo">
					<p><div id="claimLiabConclusion" >
	           <div id="approve_safeProjct">
	           <dl>
	           	<dt></dt>
	           	
	           </dl>
<!-- 				<fieldset> -->
					<div class="panelPageFormContent" >
								<input type="hidden" name="ClaimLiabVO.claimLiabId" id="approve_ClaimLiabId">	
								<input type="hidden" name="caseId" id="approve_caseId" value = "${caseId}">
								
									<dl>
										<dt>赔付结论</dt>
										<dd>
										    <input type="text" style="border:0px;" size="20" id="approve_deliveryInput" value="" readonly/>
										    <div style="display:none;">
											<Field:codeTable   name="ClaimLiabVO.liabConclusion"
												tableName="APP___CLM__DBUSER.T_CLAIM_LIAB_DECISION" cssClass="notuseflagDelete combox title comboxDD"
												nullOption="false" orderBy="code" id="approve_delivery"/>
											</div>
										</dd>
									</dl>	
									<dl>
										<dt>是否常规给付</dt>
											<dd>
											    <input type="text" style="border:0px;" size="20" id="approve_isNormalInput" value="" readonly/>
											    <div style="display:none;">
												<Field:codeTable cssClass="combox title"  name="ClaimLiabVO.isCommon"
													tableName="APP___CLM__DBUSER.T_YES_NO"
													nullOption="true" id="approve_isNormal" value="" orderBy="YES_NO desc" />
											    <select class="combox title"  id="approve_isNormalTwo" style="display:none;">
											     <option title="无" value="">无</option>
											    </select>
											    </div>
										</dd>
									</dl>
									<dl>
										<dt>不可抗辩标识</dt>
										<dd><input type = "checkbox" name="ClaimLiabVO.resistFlag" value="1" id="approve_resistFlag" disabled>是</dd>
									</dl>	
									<dl>
										<dt>保项欺诈风险标签</dt>
										<dd>
											<input id="isRiskMatchResult" type="text" style="border:0px;" readonly/>
										</dd>
									</dl>	
									<div id="unrefectDiv" style="display: none;">
									<dl>
										<dt>调整金额</dt>
										<dd><input type="text" style="border:0px;" size="20" id="approve_adjustMoney" readonly></dd>
									</dl>	
									<dl>
										 <dt>调整原因</dt>
										<dd>
										    <input type="text" style="border:0px;" size="20" id="approve_adjustReasonInput" value="" readonly/>
										    <div style="display:none;">
											<Field:codeTable   name="ClaimLiabVO.liabAdjustReason"
												tableName="APP___CLM__DBUSER.T_CLAIM_ADJUST_TYPE" orderBy="substr(code,0,1)" cssClass="notuseflagDelete combox title comboxDD"
												nullOption="true" id="approve_adjustReason" />
											</div>
										</dd>
									</dl>
									<dl style="width: 100%;height: auto">
										<dt>调整备注</dt>
										<dd>
											<textarea id="approve_adjustRemark" rows="3" cols="100" disabled></textarea>
										</dd>
									</dl>
									</div>
									<div id="refectDiv" style="display: none;">
									<dl style="width: 100%;height: auto">
										<dt><font>* </font>拒付原因</dt> 
										 <dd  style="width: 80%;">
											<%-- <Field:codeTable id="rejectCode" name="claimLiabVO.rejectCode" tableName="APP___CLM__DBUSER.T_CLAIM_AUDIT_REJECT" cssClass="combox title notuseflagDelete"
													nullOption="true" whereClause="code in ('01','02','03','04','05','06','07','08','99')" orderBy="decode(code,'01','001','02','002','03','003','04','004','05','005','06','006','07','007','08','008','99','009', code)"/> --%>
											<span class="spanbox1" style="width: 100%;">
												<span>
													<input type="checkbox"
													id="rejectCode1" name="claimLiabVO.rejectCode" value="01" disabled="disabled"/>故意不如实告知
												</span>
												<span>
													<input type="checkbox"
													id="rejectCode2" name="claimLiabVO.rejectCode" value="02" disabled="disabled"/>重大过失不如实告知
												</span>
												<span>
													<input  type="checkbox"
													id="rejectCode3" name="claimLiabVO.rejectCode" value="03" disabled="disabled"/>合同约定的责任免除项
												</span>
												<span>
													<input type="checkbox"
													id="rejectCode4" name="claimLiabVO.rejectCode" value="04" disabled="disabled"/>未达到合同约定的保险金赔付标准
												</span>
												<%-- <span>
													<input type="checkbox"
													id="rejectCode5" name="claimLiabVO.rejectCode" value="05" disabled="disabled"/>非保险合同保障对象
												</span>
												<span>
													<input type="checkbox"
													id="rejectCode6" name="claimLiabVO.rejectCode" value="06" disabled="disabled"/>不在有效保障期内
												</span>
												<span>
													<input  type="checkbox"
													id="rejectCode7" name="claimLiabVO.rejectCode" value="07" disabled="disabled"/>不在保险责任范围内
												</span>
												<span>
													<input  type="checkbox"
													id="rejectCode8" name="claimLiabVO.rejectCode" value="08" disabled="disabled"/>索赔单证不齐备或无效
												</span> --%>
											</span>
											<span class="spanbox2" style="width: 100%;">
												<span>
													<input type="checkbox"
													id="rejectCode99" name="claimLiabVO.rejectCode" value="99" disabled="disabled" onclick="rejectClick()" /> 其他
												</span>
												<span>
													其他原因
												</span>
												<span>
													<input style="margin-left:-100px;width: 500px" type="text" id="reject99" name="claimLiabVO.otherReason" disabled="disabled">
												</span>
											</span>
										</dd>
									</dl>
<!-- 									<dl style="width: 100%;height: auto"> -->
<!-- 										<dt>拒付依据</dt> -->
<!-- 										<dd> -->
<!-- 										<input id="rejectProof" type="text" name="claimLiabVO.rejectProof" maxlength="100" id=""> -->
<!-- 										<textarea rows="3" cols="80" maxlength="1000" name="claimLiabVO.rejectProof" id="rejectProof" ></textarea> -->
<!-- 										</dd> -->
<!-- 								    </dl> -->
									<dl style="width: 100%;height: auto">
										<dt>特殊备注</dt>
										<dd><textarea rows="3" cols="80" maxlength="1000" name="claimLiabVO.specialRemark" id="matchSpecialRemark" ></textarea>
										</dd>
									</dl>
								</div>
								
						</div>
<!-- 				</fieldset> -->
				</div>
	        </div></p>				
				</div>
			</div>
      <div>
		<!-- 页面底部按钮-->
		<div class="formBarButton">
					<ul>
						<li>
						   <a  href="clm/register/toApproveDataCollectInit_CLM_tClaimDutyRegisterAction.action?caseId=${caseId}&isApprove=approveInit" 
								class="but_blue main_buta"    target="navTab"   title="责任明细"  ><span>责任明细</span></a>
						</li>
						<li>
						   <s:if test='isShowButton == "1"'>
						   <a href="clm/audit/otherOperinit_CLM_otherOperAction.action?caseId=${caseId}&isApprove=approveInit" 
								class="but_blue main_buta" title="调查/协谈/二核/合议" target="navTab" ><span>调查/协谈/二核/合议</span></a>
						   </s:if>
						   <s:else>
							<a href="clm/audit/otherOperinit_CLM_otherOperAction.action?caseId=${caseId}&isApprove=approveInit" 
								class="but_blue main_buta" disabled="disabled" target="navTab" title="调查/协谈/二核/合议" ><span>调查/协谈/二核/合议</span></a>
						   </s:else>
						</li>
						<li>
							<a href="clm/report/checkContractHangUp_CLM_contractHangUpAction.action?caseId=${caseId}&isApprove=approveInit"
										class="but_blue main_buta" width="1100" height="600" title="保单挂起/解挂" lookupGroup=""> <span>保单挂起/解挂</span></a>
						</li>
						<li>
							<a id="auditClaimCheckListIni" href="clm/audit/auditClaimCheckListIni_CLM_auditClaimCheckListAction.action?caseId=${caseId}&caseNo=${claimCaseVO.caseNo}&isApprove=approveInit"
								class="but_blue main_buta"  width="1100" height="600" title="查看单证" lookupGroup="" ><span>查看单证</span></a>
						</li>
						<li>
							<button type="button" id="nestbuttonDataOne" class="but_blue" 
										onclick="next_approve('2',${caseId});">下一步</button>	
						</li>
						<li>
							<s:if test="#request.qryQuitFlag == null || #request.qryQuitFlag== ''">
							<button type="button" onclick="exit()" class="but_gray">退出</button>	
							</s:if>	
						</li>
					</ul>
				</div>
	</div>
</div>
					
						