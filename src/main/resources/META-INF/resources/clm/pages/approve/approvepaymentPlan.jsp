<%@ page language="java" pageEncoding="UTF-8"%><%@ include file="/clm/pages/common/commonJsCss.jsp" %>
<%@ taglib uri="/struts-tags" prefix="s"%>
<%@ taglib uri="http://www.git.com.cn/taglib/udmp/field" prefix="Field" %>
<s:set var="ctx">${pageContext.request.contextPath}</s:set>

 
<script type="text/javascript" src="${ctx}/clm/pages/approve/approvepaymentPlan.js" ></script>
<!-- 支付计划页面 --> 
<script type="text/javascript" >
//设置页面为只读状态
if (isApprove == "approve") {
	//将审批的标识存放到隐藏域input
	$("#approve_isApprovePaymentPlan", navTab.getCurrentPanel()).val(isApprove);
	//整体的DIV
	var obj=$("div#approve_paymentplanDiv", navTab.getCurrentPanel());
	//如果不是  上一步 和 下一步按钮
	obj.find("button").each(function(){
		var buttonName=$(this).text();
		if(buttonName!="上一步" && buttonName!="下一步"){
			$(this).attr("disabled",true);
		}
	});
	//输入框 复选框 单选按钮  控制
	obj.find("input").each(function(){
		if(!$(this).is("[type=radio]")){
			$(this).attr("disabled",true);
		}
	});
	//下拉框
	obj.find("select").each(function(){
		$(this).attr("disabled",true);
	});
	//a标签
	obj.find("a").each(function(){
		$(this).attr("disabled",true);
	});
	$(".btnEdit", navTab.getCurrentPanel()).removeAttr("disabled");
}


/* $(".paymentPlanVOValue", navTab.getCurrentPanel()).removeAttr("disabled");

$(".btnEdit", navTab.getCurrentPanel()).removeAttr("disabled");
$("#prdCalcInstallment", navTab.getCurrentPanel()).removeAttr("disabled");
$("#claimBeneTbody tr", navTab.getCurrentPanel()).each(function() {
	
 $(this).find("input#isInstalment").removeAttr("disabled");
 
}); */
 
</script>

<style type="text/css">
    /* 删除按钮样式 */
	a.btnDelPrivate{
		display:block; width:22px; height:20px; 
		text-indent:-1000px; overflow:hidden; 
		float:left; margin-right: 3px}
		a.btnDelPrivate{
		background-position: -23px 0;
		background-repeat: no-repeat;
		background-color: transparent;
		background-image: url("clm/images/imgX.gif");
		}
</style>

<div class="panelPageFormContent" id="approve_paymentplanDiv">
	<dl>
		<dt>赔案号</dt>
		<dd>
			 <input type="text" name="paymentPlanVO.caseNo" id="approve_caseNo" value="${claimCaseVO.caseNo }" readonly="readonly" />
			 <input id="approve_caseId" type="hidden" value="${paymentPlanVO.caseId }" />
		</dd>
	</dl>
	<dl>
		<dt>事件号</dt>
		<dd>
			<input type="text" name="paymentPlanVO.accidentNo" id="approve_accidentNo" value="${claimAccidentVO.accidentNo }" readonly="readonly"/> 
		</dd>
	</dl>
	
	<input type="hidden" name="isApprovePaymentPlan" id="approve_isApprovePaymentPlan">
		<div class="tabdivclassbr" >
			<table class="list" style="width: 100%;" >
				<thead>
					<tr align="center">
					    <th nowrap>选择</th>
						<th nowrap>保单号</th>
						<th nowrap>险种代码</th>
						<th nowrap>险种名称</th>
						<th nowrap>赔付金额</th>
						<th nowrap>分配情况</th>
						<th nowrap>结算项目</th>
					</tr>
				</thead>
				<tbody id="approve_paymentPlanTbody">
					<s:iterator value="listPaymentPlanVO" status="st">
						<tr align="center">
							 <td>
							    <input type="hidden" name="policyId" id="approve_policyId" value="${policyId }">
							 	<input type="radio" name="approve_caseId" onclick="queryBenePay(this);" value="${caseId }:${policyId}:${busiItemId}">
							 </td>
							 <td>${policyCode }</td>
							 <td>${busiProdCode }</td>
							 <td>${productNameSys }</td>
							 <td><s:if test="adjustType == '理赔金'">${actualPay }</s:if>
							     <s:if test="adjustType != null">${payAmount }</s:if></td>
<%-- 							 <td><Field:codeValue tableName="APP___CLM__DBUSER.T_YES_NO" value="${assignFlag }"/></td> --%>
 							 <td><s:if test="assignFlag == 0">未分配</s:if>
 								 <s:if test="assignFlag == 1">已分配</s:if>
 								 <s:if test="assignFlag == 2">未分配</s:if>
 								 <s:if test="assignFlag == 3"></s:if></td>
							 <td>
								 <Field:codeValue tableName="APP___CLM__DBUSER.T_ADJUST_TYPE" value="${adjustType}"/>
								 <input type="hidden" id="approve_adjustTypeHidden" value="${adjustType }">
								 <input type="hidden" id="approve_adjustBusiIdHidden" value="${adjustBusiId }">
								 <s:if test="adjustType == '理赔金'">理赔金</s:if>
							 </td>
						</tr>
					</s:iterator>
				</tbody>
			</table>
		</div>
			
			
			<div id="approve_claimBene">
				<div id="approve_claimBeneDiv">
					<div class="tabdivclassbr" >
						<table id="approve_claimBeneTable" class="list" style="width: 100%;" >
							<thead>
								<tr align="center">
								    <th nowrap>受益人姓名</th>
									<th nowrap>领款人姓名</th>
									<th nowrap>受益金额</th>
									<th nowrap>受益比例</th>
									<th nowrap>支付方式</th>
									<th nowrap>分期给付</th>
									<th nowrap>操作</th>
								</tr>
							</thead>
							<tbody id="approve_claimBeneTbody">
							</tbody>
						</table>
					</div>
					
				</div>
			</div>
			<div class="formBarButton">
				<ul>
					<li >
						<button type="button" class="but_blue" id="approve_addBenePayee" disabled="disabled" onclick="addBene();">添加受益人</button>
					</li>
				</ul>
			</div>		
			
			<div id="approve_benePayeeInfo">
			<form id="approve_benePayeeInfoForm" action="clm/paymentplan/saveBenePayeeInfo_CLM_paymentPlanAction.action" method="post" class="pageForm required-validate"
	  			  onsubmit="return validateCallback(this, idAjaxDone)">
	  			  <input type="hidden" name="claimPayVO.caseId" id="approve_hiddenCaseId" value="">
	  			  <input type="hidden" name="claimPayVO.policyId" id="approve_hiddenPolicyId" value="">
	  			  <input type="hidden" name="claimPayVO.busiItemId" id="approve_hiddenBusiItemId" value="">
	  			  <input type="hidden" name="claimPayVO.policyCode" id="approve_hiddenPolicyCode" value="">
	  			  <input type="hidden" name="claimPayVO.busiProdCode" id="approve_hiddenbusiProdCode" value="">
	  			  <input type="hidden" name="claimPayVO.adjustBusiId" id="approve_hiddenadjustBusiId" value="">
	  			  <input type="hidden" name="claimPayVO.isInstalment" id="approve_hiddenIsInstalment" value="0">
			      <input type="hidden" name="claimPayVO.advanceFlag" id="approve_hiddenAdvanceFlag" value="0">
	  			  <input type="hidden" name="beneVO.beneId" id="approve_hiddenBeneId" value="">
			      <input type="hidden" name="payeeVO.payeeId" id="approve_hiddenPayeeId" value="">
			     <div class="divfclass">
					<h1>
						<img src="clm/images/tubiao.png">受益人信息
					</h1>
				</div>
			    <dl>
					<dt>受益人与被保人关系</dt>
					<dd>
					   <Field:codeTable   name="beneVO.beneRelation" id="approve_beneRelation" value="${beneVO.beneRelation}" tableName="APP___CLM__DBUSER.T_LA_PH_RELA" nullOption="true" onChange="querySelf(this);" cssClass="notuseflagDelete combox title comboxDD required selectChange"/>
					   <font color="red">*</font>
					</dd>
				</dl>
				<dl>
					<dt>姓名</dt>
					<dd>
					   <input type="text" name="beneVO.beneName" id="approve_beneName" value="${beneVO.beneName }" class="required"/>
						<a href="clm/common/queryCheckIdentity_CLM_commonQueryAction.action" target="navTab" id="approve_beneCheck" rel="beneCheck" ><font style="color: red;">*验真</font></a>
					</dd>
				</dl>
				<dl>
					<dt>性别</dt>
					<dd>
					   <Field:codeTable  name="beneVO.beneSex" id="approve_beneSex" value="${beneVO.beneSex}" tableName="APP___CLM__DBUSER.T_GENDER" nullOption="true" cssClass="notuseflagDelete combox title comboxDD required"/>
					   <font color="red">*</font>
					</dd>
				</dl>
				<dl>
					<dt>出生日期</dt>
					<dd>
					   <input type="expandDateYMD" name="beneVO.beneBirth" id="approve_beneBirthAudit" value="<s:date name="beneVO.beneBirth" format="yyyy-MM-dd"/>" class="required"/>
					   <a class="inputDateButton" href="javascript:;">选择</a>
					</dd>
				</dl>
				<dl>
					<dt>证件类型</dt>
					<dd>
					   <Field:codeTable  name="beneVO.beneCertiType" id="approve_beneCertiType" value="${beneVO.beneCertiType}" tableName="APP___CLM__DBUSER.T_CERTI_TYPE" defaultValue="0" nullOption="true" cssClass="notuseflagDelete combox title comboxDD required"
					   whereClause="code in ('0','5','2','4','1','e','h','i','d','f','g','8')"  
									orderBy="decode(code,'0','001','5','002','2','003','4','004','1','005','e','006','h','007','i','008','d','009','f','010','g','011','8','012', code)" />
					   <font color="red">*</font>
					</dd>
				</dl>
				<dl>
					<dt>证件号码</dt>
					<dd>
					   <input type="text" name="beneVO.beneCertiNo" id="approve_beneCertiNo" value="${beneVO.beneCertiNo }" onblur="reInputBeneCertiID(this);" class="required"/>
					</dd>
				</dl>
				<dl>
					<dt>受益人国籍</dt>
					<dd>
					   <Field:codeTable   name="beneVO.beneNation" id="approve_beneNation" value="${beneVO.beneNation}" tableName="APP___CLM__DBUSER.T_COUNTRY" nullOption="true" cssClass="notuseflagDelete combox title comboxDD required" whereClause="1=1" defaultValue="CHN"  orderBy="decode(country_code,'CHN','A',country_code)"/>
					   <font color="red">*</font>
					</dd>
				</dl>
				<dl>
					<dt>证件有效起期</dt>
					<dd>
					   <input type="expandDateYMD" name="beneVO.beneCertiStart" id="approve_beneCertiStart" value="<s:date name="beneVO.beneCertiStart" format="yyyy-MM-dd"/>" class="required"/>
					   <a class="inputDateButton" href="javascript:;">选择</a>
					</dd>
				</dl>
				<dl>
					<dt>证件有效止期</dt>
					<dd>
					   <input type="expandDateYMD" name="beneVO.beneCertiEnd" id="approve_beneCertiEnd" gt="beneCertiStart" value="<s:date name="beneVO.beneCertiEnd" format="yyyy-MM-dd"/>" class="required"/>
					   <a class="inputDateButton" href="javascript:;">选择</a>
					</dd>
				</dl>
				<dl>
					<dt>剩余比例</dt>
					<dd>
						<input type="text" name="beneRate" id="approve_beneRate" readonly="readonly">
					</dd>
				</dl>
				<dl>
					<dt>受益分子</dt>
					<dd>
					   <input type="text" name="claimPayVO.payMole" min="1" id="approve_payMole" onblur="computePayAmount();" value="${claimPayVO.payMole }" class="digits required"/>
					</dd>
				</dl>
				<dl>
					<dt>受益分母</dt>
					<dd>
					   <input type="hidden" name="claimPayVO.claimPayId" id="approve_claimPayIdHidden" value="${claimPayVO.claimPayId }">
					   <input type="hidden" name="claimPayVO.beneRate" id="approve_beneRateHidden">
					   <input type="text" name="claimPayVO.payDeno" min="1" id="approve_payDeno" onblur="computePayAmount();" value="${claimPayVO.payDeno }" class="digits required"/>
					</dd>
				</dl>
				<dl>
					<dt>受益金额</dt>
					<dd>
						<input type="text" name="claimPayVO.payAmount" id="approve_payAmount" readonly="readonly">
					</dd>
				</dl>
			      
			    <div class="divfclass">
					<h1>
						<img src="clm/images/tubiao.png">领款人信息
					</h1>
				</div>  
			    <dl>
					<dt>领款人与受益人关系</dt>
					<dd>
                    
						<Field:codeTable  name="payeeVO.payeeRelation" id="approve_payeeRelation" value="${payeeVO.payeeRelation}" tableName="APP___CLM__DBUSER.T_LA_PH_RELA" nullOption="true" onChange="queryPayeeRelation(this)" cssClass="notuseflagDelete combox title comboxDD required selectChange"/>
						<font color="red">*</font>
					</dd>
				</dl>
				<dl>
					<dt>姓名</dt>
					<dd>
					   <input type="text" name="payeeVO.payeeName" id="approve_payeeName" value="${payeeVO.payeeName }" class="required"/>
						<a href="clm/common/queryCheckIdentity_CLM_commonQueryAction.action" target="navTab" id="approve_payeeCheck" rel="payeeCheck"><font style="color: red;">*验真</font></a>
					</dd>
				</dl>
				<dl>
					<dt>性别</dt>
					<dd>
						<Field:codeTable   name="payeeVO.payeeSex" id="approve_payeeSex" value="${payeeVO.payeeSex}" tableName="APP___CLM__DBUSER.T_GENDER" nullOption="true" cssClass="notuseflagDelete combox title comboxDD required"/>
						<font color="red">*</font>
					</dd>
				</dl>
				<dl>
					<dt>出生日期</dt>
					<dd>
					   <input type="expandDateYMD" name="payeeVO.payeeBirth" id="approve_payeeBirth" value="<s:date name="payeeVO.payeeBirth" format="yyyy-MM-dd"/>" class="required"/>
					   <a class="inputDateButton" href="javascript:;">选择</a>
					</dd>
				</dl>
				<dl>
					<dt>证件类型</dt>
					<dd>
					   <Field:codeTable   name="payeeVO.payeeCertiType" id="approve_payeeCertiType" value="${payeeVO.payeeCertiType}" tableName="APP___CLM__DBUSER.T_CERTI_TYPE" defaultValue="0" nullOption="true" cssClass="notuseflagDelete combox title comboxDD required"
					   whereClause="code in ('0','5','2','4','1','e','h','i','d','f','g','8')"  
									orderBy="decode(code,'0','001','5','002','2','003','4','004','1','005','e','006','h','007','i','008','d','009','f','010','g','011','8','012', code)" />
					   <font color="red">*</font>
					</dd>
				</dl>
				<dl>
					<dt>证件号码</dt>
					<dd>
					   <input type="text" name="payeeVO.payeeCertiNo" id="approve_payeeCertiNo" value="${payeeVO.payeeCertiNo }" onblur="reInputPayeeCertiID(this)" class="required"/>
					</dd>
				</dl>
				<dl>
					<dt>领款人国籍</dt>
					<dd>
					   <Field:codeTable   name="payeeVO.payeeNation" id="approve_payeeNation" value="${payeeVO.payeeNation}" tableName="APP___CLM__DBUSER.T_COUNTRY" nullOption="true" cssClass="notuseflagDelete combox title comboxDD required" whereClause="1=1" defaultValue="CHN"  orderBy="decode(country_code,'CHN','A',country_code)"/>
					   <font color="red">*</font>
					</dd>
				</dl>
				<dl>
					<dt>证件有效起期</dt>
					<dd>
					   <input type="expandDateYMD" name="payeeVO.payeeCertiStart" id="approve_payeeCertiStart" value="<s:date name="payeeVO.payeeCertiStart" format="yyyy-MM-dd"/>" class="required"/>
					   <a class="inputDateButton" href="javascript:;">选择</a>
					</dd>
				</dl>
				<dl>
					<dt>证件有效止期</dt>
					<dd>
					   <input type="expandDateYMD" name="payeeVO.payeeCertiEnd" id="approve_payeeCertiEnd" gt="payeeCertiStart" value="<s:date name="payeeVO.payeeCertiEnd" format="yyyy-MM-dd"/>" class="required"/>
					   <a class="inputDateButton" href="javascript:;">选择</a>
					</dd>
				</dl>
				<dl>
					<dt>支付方式</dt>
					<dd>
					   <Field:codeTable   name="payeeVO.payMode" id="approve_payMode" value="${payeeVO.payMode}" whereClause="code in(10,32,20,34,42,18)" orderBy="decode(code,'32',1,'34',2,'20',3,'10',4,'42',5,'18',6)" tableName="APP___CLM__DBUSER.T_PAY_MODE" nullOption="true" cssClass="notuseflagDelete combox title comboxDD required"/>
					   <font color="red">*</font>
					</dd>
				</dl>
				<dl>
					<dt>银行账户名</dt>
					<dd>
					   <input type="text" name="payeeVO.accountName" id="approve_accountName" value="${payeeVO.accountName }" class="required"/>
					</dd>
				</dl>
				<dl>
					<dt>银行账号</dt>
					<dd>
					   <input type="expandBankAccount" name="payeeVO.accountNo" id="approve_accountNo" value="${payeeVO.accountNo }" onblur="reInputAccount(this);" class="required"/>
					</dd>
				</dl>

				<div class="formBarButton">
					<ul>
						<li >
							<button type="button" class="but_blue" id="approve_saveBenePayee" disabled="disabled" onclick="saveJudge();">保存受益人和领款人信息</button>
						</li>
					</ul>
				</div>		
			</form>
			</div>
			
			<div id="approve_policyHolder" style="display: none;">
			<form id="approve_holderForm" action="" method="post" class="pageForm required-validate"
	  			  onsubmit="return validateCallback(this, nvaTabAjaxDone)">
			        <div class="divfclass">
						<h1>
							<img src="clm/images/tubiao.png">投保人信息
						</h1>
					</div>
		        	<dl>
						<dt></dt>
						<dd>
							<input type="hidden" id="approve_holderCustomerId" name="holderCustomerId">
						   <input type="checkbox" id="approve_checkHolder"/>退还投保人
						</dd>
					</dl>
					<dl>
						<dt>投保人姓名</dt>
						<dd>
						   <input type="text" name="holderName" id="approve_holderName" value="" readonly="readonly" />
						</dd>
					</dl>
					<dl>
						<dt>证件类型</dt>
						<dd>
							<Field:codeTable cssClass="combox title"  name="holderCerti" id="approve_holderCerti" value="" tableName="APP___CLM__DBUSER.T_CERTI_TYPE" defaultValue="0" nullOption="true" disabled="true"
							whereClause="code in ('0','5','2','b','4','1','8')"  orderBy="decode(code,'0','001','5','002','2','003','b','004','4','005','1','006','8','007', code)" />
						</dd>
					</dl>
					<dl>
						<dt>证件号码</dt>
						<dd>
						   <input type="text" name="holderCertiCode" id="approve_holderCertiCode" value="" readonly="readonly" />
						</dd>
					</dl>
					<dl>
						<dt>收费方式</dt>
						<dd>
						   <Field:codeTable   name="holderMode" id="approve_holderMode" tableName="APP___CLM__DBUSER.T_PAY_MODE" whereClause="code in(10,32,20,34,42,18)" orderBy="decode(code,'32',1,'34',2,'20',3,'10',4,'42',5,'18',6)" onChange="bankAccount();" nullOption="true" cssClass="notuseflagDelete combox title required" />
						</dd>
					</dl>
					<dl id="approve_bankAccountDL" style="display: none;">
						<dt>银行账号</dt>
						<dd>
						   <input type="expandBankAccount" name="holderBankAccount" id="approve_holderBankAccount"/>
						</dd>
					</dl>
		        </form>
			</div>
			
			<div id="approve_instalment" style="display: none;">
<!-- 			<form id="pagerForm" method="post" -->
<!-- 				action="clm/paymentplan/prdCalcInstallment_CLM_paymentPlanAction.action"> -->
<%-- 				<input type="hidden" name="pageNum" vaule="${currentPage.pageNo} " /> --%>
<%-- 				<input type="hidden" name="numPerPage" value="${currentPage.pageSize}" /> --%>
<!-- 			</form> -->
			
			<form id="approve_instalmentForm" action="clm/paymentplan/prdCalcInstallment_CLM_paymentPlanAction.action" method="post" class="pageForm required-validate"
	  			  onsubmit="return divSearch(this, 'approve_instalment')">
				<div class="divfclass">
					<h1>
						<img src="clm/images/tubiao.png">分期计划
					</h1>
				</div>
					<dl>
						<dt>总金额</dt>
						<dd>
						  <input class="paymentPlanVOValue" type="hidden" name="paymentPlanVO.caseId" id="approve_instalmentCaseId">
			  			  <input class="paymentPlanVOValue" type="hidden" name="paymentPlanVO.policyId" id="approve_instalmentPolicy">
			  			  <input class="paymentPlanVOValue" type="hidden" name="paymentPlanVO.claimBusiProdId" id="approve_instalmentBusiPrd">
			  			  <input class="paymentPlanVOValue" type="hidden" name="paymentPlanVO.claimPayId" id="approve_instalmentClaimPay">
			  			  <input class="paymentPlanVOValue" type="hidden" name="calcInstallmentVO.liabId" id="approve_calcInstallmentVOliabId" value="${calcInstallmentVO.liabId }">
						   <input type="text" name="totalAmout" id="approve_totalAmout" value="${calcInstallmentVO.totalAmout }" readonly="readonly" />
						</dd>
					</dl>
					<dl>
						<dt>计划开始时间</dt>
						<dd>
						   <input type="text" name="installmentStartDate" id="approve_installmentStartDate" value="<s:date name='calcInstallmentVO.installmentStartDate' format='yyyy-MM-dd'/>" readonly="readonly"/>
						</dd>
					</dl>
					<dl>
						<dt>支付频率</dt>
						<dd>
						   <Field:codeValue tableName="APP___CLM__DBUSER.T_PAY_TYPE" value="${calcInstallmentVO.claimReveFrequency }"/>
						</dd>
					</dl>
					<dl>
						<dt>每次支付金额</dt>
						<dd>
						   <input type="text" name="eachPayAmount" id="eachPayAmount" value="${calcInstallmentVO.eachPayAmount }" readonly="readonly"/>
						</dd>
					</dl>
					<dl>
						<dt>支付次数</dt>
						<dd>
						   <input type="text" name="payTimes" id="approve_payTimes" value="${calcInstallmentVO.claimReveCount }" readonly="readonly"/>
						</dd>
					</dl>

		        <div class="buttonActive" >
					<div class="buttonContent">
						<button type="submit" id="prdCalcInstallment">计算分期支付计划</button>
					</div>
				 </div>
				 
				 <div id="approve_prdCalc" class="tabdivclassbr">
					<table id="approve_prdCalcInstallmentTable" class="list" style="width: 100%;" >
						<thead>
							<tr align="center">
							    <th nowrap>序号</th>
								<th nowrap>支付日期</th>
								<th nowrap>分期类型</th>
								<th nowrap>分期金额</th>
								<th nowrap>调整金额</th>
								<th nowrap>支付状态</th>
								<th nowrap>生调标识</th>
								<th nowrap>生调日期</th>
							</tr>
						</thead>
						<tbody id="prdCalcInstallmentTbody">
							<s:iterator value="claimInstalmentVOList" id="st">
								<tr align='center'>
								  <td></td>
					   				<td><s:date name='payDueDate' format='yyyy-MM-dd'/></td>
								   <td>分期类型</td>	
								   <td></td>
								   <td>调整金额</td>
								   <td><s:if test="0==instalStatus">未支付</s:if></td>
								   <td><input type='checkbox' id='surveyFlag' value="${surveyFlag }" <s:if test="1==surveyFlag">checked</s:if>></td>
								   <td>${surveyDate }</td>
							    </tr>
							</s:iterator>
						</tbody>
					</table>
					<div class="panelBar" >
						<div class="pages">
							<span>显示</span>
							<s:select  list="#{10:'10',20:'20',50:'50',100:'100',200:'200'}"  name="select" onchange="navTabPageBreak({numPerPage:this.value})" value="currentPage.pageSize">
				     		</s:select>
							<span>条，共${currentPage.total}条</span>		
						</div>
					    <div class="pagination" targetType="navTab" totalCount="${currentPage.total}" numPerPage="${currentPage.pageSize}" pageNumShown="10" currentPage="${currentPage.pageNo}"></div>
					</div>
				</div>
				</form>
			</div>
			
			<div>
				<dl>
					<dt></dt>
					<dd><input type="checkbox" id="approve_annuConvert" onclick="annuConvert();" disabled="disabled">年金转换</dd>
				</dl>
				<dl>
					<dt>转换为年金产品</dt>
					<dd>
						<select class="combox title"  id="approve_claimLiabAnnuId"></select>
					</dd>
				</dl>
			</div>
			<div class="buttonActive" >
				<div class="buttonContent">
					<button type="button" id="approve_annuDrawPlan" disabled="disabled" onclick="annuDrawPlan();">计算年金领取计划</button>
				</div>
			</div>
			 <div id="" class="tabdivclassbr" >
				<table id="approve_annuDrawPlanTable" class="list" style="width: 100%;" >
					<thead>
						<tr align="center">
						    <th nowrap>序号</th>
							<th nowrap>受益人年龄</th>
							<th nowrap>年领金额</th>
						</tr>
					</thead>
					<tbody id="approve_annuDrawPlanTbody">
					</tbody>
				</table>
		</div>
	
	<div class="formBarButton" id="formBarPaymentPlan">
		<ul>
			<li><button type="button" class="but_blue" onclick="prev_approve('6','${paymentPlanVO.caseId }')">上一步</button></li>
			<li><button type="button" class="but_blue" id="saveInfo" onclick="savePaymentPlanInfo();">保存</button></li>
			<li><button type="button" class="but_blue" onclick="next_approve('8','${paymentPlanVO.caseId }');">下一步</button></li>
			<li><button type="button" class="but_blue" id="closeApprovePayment" onclick="exit();">退出</button></li>
		</ul>
	</div>
	
</div>
