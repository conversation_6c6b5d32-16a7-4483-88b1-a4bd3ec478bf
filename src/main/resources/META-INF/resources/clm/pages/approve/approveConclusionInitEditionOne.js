/**
 * 点击保存时校验必录项
 */
var clickFlag=true;//liupengit1 52637
//超期原因引起的保存阻断
function approveDoSaveFlag(){
	var isOverComp=$("#isOverCompSP", navTab.getCurrentPanel()).val(); //超期
    var overReason=$("#overReason", navTab.getCurrentPanel()).val(); //超期原因 
	if(isOverComp==1 && overReason==2){
		alertMsg.confirm("请上传告知客户超期风险并获其认可的书面证明材料。",{
			okCall:function(){
				approveDoSave();
			},
			cancelCall : function() {
				return;
						}
					});
	}else{
		approveDoSave();
	}
}
function approveDoSave(){
	var flagBoolean = true;
	clickFlag=false;//liupengit1 52637
	var isOverComp=$("#isOverCompSP", navTab.getCurrentPanel()).val(); //超期
	var overReason=$("#overReason", navTab.getCurrentPanel()).val(); //超期原因 
	if($("#approveRemark", navTab.getCurrentPanel()).val()==null || $("#approveRemark", navTab.getCurrentPanel()).val()==""){
		alertMsg.info("审批意见不能为空!"); 
		$("#alertMsgBox .toolBar .button").off().on("click",function(){
		    $("#approveRemark", navTab.getCurrentPanel()).focus();
	    });   
		return;
	}else if($("#approveDecision", navTab.getCurrentPanel()).val()==null || $("#approveDecision", navTab.getCurrentPanel()).val()==""){
		alertMsg.info("请选择审批结论!");
		$("#alertMsgBox .toolBar .button").off().on("click",function(){ 
			$("#approveDecision", navTab.getCurrentPanel()).focus(); 
		  });  
		return;
	}else if($("#approveCaseFlag", navTab.getCurrentPanel()).val()==null || $("#approveCaseFlag", navTab.getCurrentPanel()).val()==""){
		alertMsg.info("请选择案件标识!");
		$("#alertMsgBox .toolBar .button").off().on("click",function(){
	       $("#approveCaseFlag", navTab.getCurrentPanel()).focus();
		  });     
		return;
	}else if($("#approveDecision", navTab.getCurrentPanel()).val()==2 && $("#approveRejectReason", navTab.getCurrentPanel()).val()==""){
		alertMsg.info("请选择不通过原因!");
		$("#alertMsgBox .toolBar .button").off().on("click",function(){
	          $("#approveRejectReason",navTab.getCurrentPanel()).focus();
		  });  
		return;
	}else if($("#approveDecision", navTab.getCurrentPanel()).val() == 1 && $("#approveRejectReason", navTab.getCurrentPanel()).val()!=""){
		alertMsg.info("审批结论为通过，不应选择不通过原因!");
		$("#alertMsgBox .toolBar .button").off().on("click",function(){
	          $("#approveRejectReason", navTab.getCurrentPanel()).focus();
		  });  
		return;
	}
	if(isOverComp=="1"&&(overReason==null||overReason=="")){
    	alertMsg.info("该赔案存在超期补偿，请录入超期原因！");
    	return;
    }
	// liupengit1 52637
	checkMisrepresentationsFactor();
	//64370风险标签只读
//	if ($("#isRisk", navTab.getCurrentPanel()).val() == "1"){ //有风险时进行校验
//		//判断是否一级标签的数据选择
//		  if($("#riskLabelNineAudit", navTab.getCurrentPanel()).attr("checked") != "checked" && $("#riskLabelTwoAudit", navTab.getCurrentPanel()).attr("checked") != "checked" && $("#riskLabelFourAudit", navTab.getCurrentPanel()).attr("checked") != "checked"
//			  && $("#riskLabelSixAudit", navTab.getCurrentPanel()).attr("checked") != "checked" && $("#riskLabelTenAudit", navTab.getCurrentPanel()).attr("checked") != "checked"  && $("#riskLabelOneAudit", navTab.getCurrentPanel()).attr("checked") != "checked"){
//			  	alertMsg.error("请在一级标签中至少勾选一项欺诈风险选项。");
//			  	return;
//		  }
//		 //当赔案欺诈风险为“有风险”时，欺诈案件信息都是必填项
//		  //作案性质校验
//		  if($("#committingNatureOneAudit", navTab.getCurrentPanel()).attr("checked") != "checked" && $("#committingNatureTwoAudit", navTab.getCurrentPanel()).attr("checked") != "checked"){
//			  alertMsg.error("当赔案欺诈风险为“有风险”时，作案性质必选！");
//			  return;
//		  }
//		  //公检法立案校验
//		  if($("#inspectionRegisterFlagOneAudit", navTab.getCurrentPanel()).attr("checked") != "checked" && $("#inspectionRegisterFlagTwoAudit", navTab.getCurrentPanel()).attr("checked") != "checked"){
//			  alertMsg.error("当赔案欺诈风险为“有风险”时，公检法立案必选！");
//			  return;
//		  }
//		  //公检法立案日期校验(公检法立案为是时必填。)
//		  if($("#inspectionRegisterFlagOneAudit", navTab.getCurrentPanel()).attr("checked") == "checked"){
//			  if($("#inspectionRegisterTimeIdAudit", navTab.getCurrentPanel()).val().trim() == "" || $("#inspectionRegisterTimeIdAudit", navTab.getCurrentPanel()).val().trim() == null ){
//				  alertMsg.error("当赔案欺诈风险为“有风险”时，公检法立案日期必填！");
//				  return;
//			  }
//		  }
//		  
//		  //欺诈识别途径校验
//		  if($("#cheatDistinguishChannelIdAudit", navTab.getCurrentPanel()).val().trim() == "" || $("#cheatDistinguishChannelIdAudit", navTab.getCurrentPanel()).val().trim() == null ){
//			  alertMsg.error("当赔案欺诈风险为“有风险”时，欺诈识别途径必选！");
//			  return;
//		  }
//		  //欺诈实施人员校验
//		  if($("#cheatImplementationPersonnelOneAudit", navTab.getCurrentPanel()).attr("checked") != "checked" && $("#cheatImplementationPersonnelTwoAudit", navTab.getCurrentPanel()).attr("checked") != "checked" && $("#cheatImplementationPersonnelThreeAudit", navTab.getCurrentPanel()).attr("checked") != "checked" 
//			  && $("#cheatImplementationPersonnelFourAudit", navTab.getCurrentPanel()).attr("checked") != "checked" && $("#cheatImplementationPersonnelFiveAudit", navTab.getCurrentPanel()).attr("checked") != "checked" && $("#cheatImplementationPersonnelSixAudit", navTab.getCurrentPanel()).attr("checked") != "checked" 
//				  && $("#cheatImplementationPersonnelSevenAudit", navTab.getCurrentPanel()).attr("checked") != "checked"){
//			  alertMsg.error("当赔案欺诈风险为“有风险”时，欺诈实施人员必选！");
//			  return;
//		  }
//		//选择故意欺诈风险选项为其他时文本框必填
//		  if($("#riskLabelEightAudit", navTab.getCurrentPanel()).attr("checked") == "checked"){
//			  if($.trim($("#riskOtherReasonAudit", navTab.getCurrentPanel()).val())==""){
//				  alertMsg.error("当赔案欺诈风险选项为其他时,文本框必填!");
//				  return;
//			  }
//		  }
//		  //选择欺诈识别途径为其他时，文本框必填
//		  if($("#cheatDistinguishChannelIdAudit", navTab.getCurrentPanel()).val() == "09"){
//			  if($.trim($("#otherCheatOptionIdAudit", navTab.getCurrentPanel()).val())==""){
//				  alertMsg.error("欺诈识别途径为其他时,文本框必填!");
//				  return;
//			  }
//		  }
//		  //选择欺诈实施人员为其他人员时，文本框必填
//		  if($("#cheatImplementationPersonnelSevenAudit", navTab.getCurrentPanel()).attr("checked") == "checked"){
//			  if($.trim($("#otherImplementationPersonnelAudit", navTab.getCurrentPanel()).val())==""){
//				  alertMsg.error("欺诈实施人员为其他人员时,文本框必填!");
//				  return;
//			  }
//		  }
//		  $("#toAuditConclusionInitform",navTab.getCurrentPanel()).submit();
//		} else {
//			 $.ajax({
//					'type':'post',
//					'url':'clm/audit/queryLiabRiskLableFlag_CLM_auditConclusionAction.action?caseId='+caseIdNum,
//					'datatype':'json', 
//					'success':function(data){
//						var data = eval("(" + data + ")");
//						if(data.liabRiskLabel == 1){
//							$("#isRisk", navTab.getCurrentPanel()).selectMyComBox("1");
//							$("#riskDiv", navTab.getCurrentPanel()).show();
//							alertMsg.error("保项欺诈标签与赔案欺诈标签不一致，请确认。");
//						} else {
//							$("#toAuditConclusionInitform",navTab.getCurrentPanel()).submit();
//						}
//					},
//					'error':function(){
//						alert("出错了！");
//					}
//				}); 
//		}
	if(flagBoolean){
		$("#toAuditConclusionInitform",navTab.getCurrentPanel()).submit();
	}
}

/**
 * 52637 liupengit1 Start
 */
function checkMisrepresentationsFactor(){
	var caseIdNum = $("#caseId", navTab.getCurrentPanel()).val();
	if($("#rejectCode1", $.pdialog.getCurrent()).is(":checked")||$("#rejectCode2", $.pdialog.getCurrent()).is(":checked")){
		$.ajax({
			'url':"clm/audit/claimCheckMisrepresentations_CLM_claimMatchResultAction.action",
			'type':'post',
			'data':{'caseId':caseIdNum}, 
			'success':function(json){
					var data = eval("("+json+")");
					if (data.statusCode == 300) {
							alertMsg.info(data.message);
					}
			}
		});
	}
	
}
//52637 liupengit1 End

//故意欺诈风险
function riskChange(k){
	var caseIdNum = $("#caseId", navTab.getCurrentPanel()).val();
	var risk =  $(k).val();
	if(risk==1){
//		var  caseIdNum=$("#caseId",navTab.getCurrentPanel()).val();
		//当赔案层选择有风险时,查看保项层是否都是无风险,如果都是无风险则阻断。否则不阻断
		$.ajax({
			'type':'post',
			'url':'clm/audit/queryLiabRiskLableFlag_CLM_auditConclusionAction.action?caseId='+caseIdNum,
			'datatype':'json', 
			'success':function(data){
				var data = eval("(" + data + ")");
				if(data.liabRiskLabel == 0){
					$("#isRisk", navTab.getCurrentPanel()).selectMyComBox("0");
					alertMsg.error("请在保项结论部分勾选保项层欺诈风险标签。");
				} else {
					$("#riskDiv", navTab.getCurrentPanel()).show();
				}
			},
			'error':function(){
				alert("出错了！");
			}
		}); 
		
	}else{
		$("#riskDiv", navTab.getCurrentPanel()).hide();
	}
}
//故意欺诈风险校验50字符
function checkLen(){
	var str  = $("#riskOtherReason", navTab.getCurrentPanel()).val();
	var len = 0;
	for(var i = 0 ; i < str.length; i++){
		var a = str.charAt(i);
		if (a.match(/[^\x00-\xff]/ig) != null){
            len += 2;
     }else{
        	len += 1;
        }

	}
	if(len>50){
		alertMsg.error("请录入50个字符！");
	}
}

//拒付通知书验证
function notNote(){
	var caseId = $("#caseId", navTab.getCurrentPanel()).val();
	var auditDecision="";
	$.ajax({
		 'url':"clm/audit/queryClaimLiabConclusion_CLM_auditConclusionAction.action?caseId="+caseId,
	     'type':'post',
	     'datatype':'json',
	     'async':false,
	     'success':function(data){
	    	var json=eval("("+data+")");
	    	auditDecision=json.liabConclusion;
	     }
	});
	auditDecision = "3";
	if(auditDecision=="3"||auditDecision=="6"){
		//拒付通知书预览
		$.ajax({
			 'url':"clm/audit/auditConclusionNote_CLM_auditConclusionAction.action?caseId="+caseId,
		     'type':'post',
		     'datatype':'json',
		     'async':true,
		     'success':function(data){
		    	var json=eval("("+data+")");
		    	
		    	if(json.jobId!="" && json.error!=""){
		    		alertMsg.error("操作失败");
		    	}else{
		    		var object = "<object  type=\"application/x-java-applet\">"+
		    		"<param name=\"code\" value=\"SipRpcltApplet.class\"/>"+
		    		"<param name=\"archive\" value=\"SipRpclt.jar\"/>"+
		    		"<param name=\"reqId\" value='"+json.jobId+"'/>"+
		    		"<param name=\"server\" value='"+json.serviceIp+"'/>"+
		    		"<param name=\"operation\" value='"+json.printType+"'/>"+"</object>";
		    		$("div#printShow", navTab.getCurrentPanel()).html(object);
		    	}
		     },
		     'error':function(){
		    	 alertMsg.error("操作失败");
		     }
		   });
	}else{
		alertMsg.info("保项结论有一项拒付才能预览拒付通知书");
	  	return;
	}
	
}

//合同解除通知书
function contractRelievePrit(){
	var caseId = $("#caseId", navTab.getCurrentPanel()).val();
	
	//合同解除通知书预览
	$.ajax({
		 'url':"clm/audit/auditTerCon_CLM_auditConclusionAction.action?caseId="+caseId,
	     'type':'post',
	     'datatype':'json',
	     'async':true,
	     'success':function(data){
	    	var json=eval("("+data+")");
	    	if(json.jobId!="" && json.error!=""){
	    		 alertMsg.error("操作失败");
	    	}else{
	    		var object = "<object  type=\"application/x-java-applet\">"+
	    		"<param name=\"code\" value=\"SipRpcltApplet.class\"/>"+
	    		"<param name=\"archive\" value=\"SipRpclt.jar\"/>"+
	    		"<param name=\"reqId\" value='"+json.jobId+"'/>"+
	    		"<param name=\"server\" value='"+json.serviceIp+"'/>"+
	    		"<param name=\"operation\" value='"+json.printType+"'/>"+"</object>";
	    		$("div#printShow", navTab.getCurrentPanel()).html(object);
	    	}
	     },
	     'error':function(){
	    	 alertMsg.error("操作失败");
	     }
	   });
	
}
//超期原因引起的保存阻断
function checkConfirmIfInfoFlag(){
	//超期补偿标识和超期补偿核定控制，审批意见中<超期>标识为勾选状态时，显示否则不显示
	var isOverComp=$("#isOverCompSP",navTab.getCurrentPanel()).val();
	var overReason=$("#overReason",navTab.getCurrentPanel()).val();
	if(isOverComp==1 && overReason==2){
		alertMsg.confirm("请上传告知客户超期风险并获其认可的书面证明材料。",{
			okCall:function(){
				checkConfirmIfInfo();
			},
			cancelCall : function() {
				return;
						}
					});
	}else{
		checkConfirmIfInfo();
	}
}
function checkConfirmIfInfo(){
	if(clickFlag){
		checkMisrepresentationsFactor();
	}//liupengit1 52637 
	var caseId = $("#caseId", navTab.getCurrentPanel()).val();
	var approveConclusion = $("#approveDecision",navTab.getCurrentPanel()).val();
	var isFlag = true;
	var confirmKay = "";
	
	var itCheckFlag = false;
	$.ajax({//校验赔案是否存在没有回销的IT问题件
			url : "clm/memo/checkAllItProbCase_CLM_memoAction.action?caseId="+caseId,
			type : "POST",
			dataType : "json",
			async : false,
			success : function (l){
			if (l.statusCode != DWZ.statusCode.ok) {//存在没有回销的IT问题件，进行阻断提示
				alertMsg.error("该赔案存在未回销的问题件，请核实");
				itCheckFlag = false;
				return false;
			}else{
				itCheckFlag = true;
			}
		}	
	});
	 if (!itCheckFlag) {
			return false;
	}
	
	$.ajax({
	    'type':'post',
	    'url':'clm/register/confirmIfInfo_CLM_claimMatchCalcAction.action?caseId='+caseId,
	    'async':false,
	    'datatype':'json', 
	    'success':function(data){
	    	//校验是否预览
			$.ajax({
	  			'type':'post',
	  			'url':'clm/audit/lookFlag_CLM_auditConclusionAction.action?caseId='+caseIdNum,
	  			'datatype':'json', 
	  			async: false,
	  			'success':function(data){
	  				var data = eval("(" + data + ")");
	  				for(var kay in data){
	  					if(kay=="relieveFlag" && approveConclusion == "1"){
	  						alertMsg.error("请先预览合同解除通知书，再操作【审批确认】");
	  						isFlag = false;
	  						return;
	  					}
	  					if(kay=="noticeFlag" && approveConclusion == "1"){
	  						alertMsg.error("请先预览理赔决定通知书（拒付），再操作【审批确认】");
	  						isFlag = false;
	  						return;
	  					}
					}
	  			},
	  			'error':function(){
	  				alert("出错了！");
	  			}
	  		});
			//53418超链接校验
			$.ajax({
				'url' : "clm/common/queryLinkLookYes_CLM_commonQueryAction.action?claimCaseVO.caseId="+caseId,
				'type' : "POST",
				'datatype' : "json",
				'async' : false,
				'success' : function (json){
					var data = eval("(" + json + ")");
	  				for(var kay in data){
	  					if (kay=="1") {
	  						isFlag = false;
	  						alertMsg.error(data[kay]);
	  						return false;
						}
	  					if (kay=="2") {
	  						confirmKay = kay;
//	  						alertMsg.info(data[kay]);
						}
					}
				}
			});
			
	      var data = eval("(" + data + ")");
	      for(var kay in data){
	        if (kay=="5") {
	        	isFlag = false;
	        	alertMsg.confirm(data[kay],{
					okCall:function(){
						isFlag = true;
						approveDoUpdate();
					}
				});
	        }
	      }
	    },
	    'error':function(){
	      alert("出错了！");
	    }
	});
	
	if(confirmKay == "2"){
		alertMsg.confirm("该赔案被理赔反欺诈关联图谱平台识别为可疑案件，请确保已登录平台查看详细情况。",{
			okCall:function(){
				isFlag = true;
				approveDoUpdate();
			}
		});
	}else{
		if(isFlag){
			approveDoUpdate();
		}
	}
}


/**
 * 点击审批确认时校验必录项
 */
function approveDoUpdate(){
	
	if(JudgeTimeOutCLM()){
		return false;
	};
//	var isRisk = $('#isRisk option:selected', navTab.getCurrentPanel()).val();
//	var riskOtherReason = $('#riskOtherReason', navTab.getCurrentPanel()).val();
//	var riskStr = null;
//	var id_array=new Array();
//	$('input[name="claimCaseVO.riskLabel"]:checked').each(function(){  
//	    id_array.push($(this).val());//向数组中添加元素  
//	});
//	if(id_array!=null&&id_array!=""){
//		riskStr=id_array.join(',');//将数组元素连接起来以构建一个字符串  
//	}
	
	/*if(!checkConfirmIfInfo()){
		return false;
	};*/
	var isOverComp=$("#isOverCompSP", navTab.getCurrentPanel()).val(); //超期
	var overReason=$("#overReason", navTab.getCurrentPanel()).val(); //超期原因 
	if(isOverComp=="1"&&(overReason==null||overReason=="")){
    	alertMsg.info("该赔案存在超期补偿，请录入超期原因！");
    	return;
    }
	if($("#approveRemark", navTab.getCurrentPanel()).val()==null || $("#approveRemark", navTab.getCurrentPanel()).val()==""){
		alertMsg.info("审批意见不能为空!");
		
		$("#alertMsgBox .toolBar .button").off().on("click",function(){
		    $("#approveRemark", navTab.getCurrentPanel()).focus();
	    });    
		 return;
	}else if($("#approveDecision", navTab.getCurrentPanel()).val()==null || $("#approveDecision", navTab.getCurrentPanel()).val()==""){
		alertMsg.info("请选择审批结论!");
		$("#alertMsgBox .toolBar .button").off().on("click",function(){ 
			$("#approveDecision", navTab.getCurrentPanel()).focus(); 
		  });  		
		 return;
	}else if($("#approveCaseFlag", navTab.getCurrentPanel()).val()==null || $("#approveCaseFlag", navTab.getCurrentPanel()).val()==""){
		alertMsg.info("请选择案件标识!");
		$("#alertMsgBox .toolBar .button").off().on("click",function(){
	       $("#approveCaseFlag", navTab.getCurrentPanel()).focus();
		  });    
		 return;
	}else if($("#approveDecision", navTab.getCurrentPanel()).val()==2 && $("#approveRejectReason", navTab.getCurrentPanel()).val()==""){
		alertMsg.info("请选择不通过原因!");
		$("#alertMsgBox .toolBar .button").off().on("click",function(){
	          $("#approveRejectReason", navTab.getCurrentPanel()).focus();
		  });  
		 return;
	}else if($("#approveDecision", navTab.getCurrentPanel()).val() == 1 && $("#approveRejectReason", navTab.getCurrentPanel()).val()!=""){
		alertMsg.info("审批结论为通过，不应选择不通过原因!");
		$("#alertMsgBox .toolBar .button").off().on("click",function(){
	          $("#approveRejectReason", navTab.getCurrentPanel()).focus();
		  });  
		 return;
	}else{
		
		var beforeActionUrl=$("#toAuditConclusionInitform", navTab.getCurrentPanel()).attr("action");
		var beforeSubmit=$("#toAuditConclusionInitform", navTab.getCurrentPanel()).attr("onsubmit");
		$("#toAuditConclusionInitform", navTab.getCurrentPanel()).attr("action","clm/audit/updateApproveConclusion_CLM_addApproveConclusionAction.action?menuId=${menuId}&taskId="+$("#approveTaskId", navTab.getCurrentPanel()).val()+"&caseId="+$("#claimCaseVOcASEID",navTab.getCurrentPanel()).val());
		$("#toAuditConclusionInitform", navTab.getCurrentPanel()).attr("onsubmit","return validateCallback(this, approveConfirmAjaxDone)");
		$("#toAuditConclusionInitform",navTab.getCurrentPanel()).submit();
	    //恢复form
		$("#toAuditConclusionInitform", navTab.getCurrentPanel()).attr("action",beforeActionUrl );
		$("#toAuditConclusionInitform", navTab.getCurrentPanel()).attr("onsubmit", beforeSubmit);
		
	} 
	//64370风险标签只读
//	if ($("#isRisk", navTab.getCurrentPanel()).val() == "1"){ //有风险时进行校验
//		//判断是否一级标签的数据选择
//		  if($("#riskLabelNineAudit", navTab.getCurrentPanel()).attr("checked") != "checked" && $("#riskLabelTwoAudit", navTab.getCurrentPanel()).attr("checked") != "checked" && $("#riskLabelFourAudit", navTab.getCurrentPanel()).attr("checked") != "checked"
//			  && $("#riskLabelSixAudit", navTab.getCurrentPanel()).attr("checked") != "checked" && $("#riskLabelTenAudit", navTab.getCurrentPanel()).attr("checked") != "checked"  && $("#riskLabelOneAudit", navTab.getCurrentPanel()).attr("checked") != "checked"){
//			  	alertMsg.error("请在一级标签中至少勾选一项欺诈风险选项。");
//			  	return;
//		  }
//		 //当赔案欺诈风险为“有风险”时，欺诈案件信息都是必填项
//		  //作案性质校验
//		  if($("#committingNatureOneAudit", navTab.getCurrentPanel()).attr("checked") != "checked" && $("#committingNatureTwoAudit", navTab.getCurrentPanel()).attr("checked") != "checked"){
//			  alertMsg.error("当赔案欺诈风险为“有风险”时，作案性质必选！");
//			  return;
//		  }
//		  //公检法立案校验
//		  if($("#inspectionRegisterFlagOneAudit", navTab.getCurrentPanel()).attr("checked") != "checked" && $("#inspectionRegisterFlagTwoAudit", navTab.getCurrentPanel()).attr("checked") != "checked"){
//			  alertMsg.error("当赔案欺诈风险为“有风险”时，公检法立案必选！");
//			  return;
//		  }
//		  //公检法立案日期校验(公检法立案为是时必填。)
//		  if($("#inspectionRegisterFlagOneAudit", navTab.getCurrentPanel()).attr("checked") == "checked"){
//			  if($("#inspectionRegisterTimeIdAudit", navTab.getCurrentPanel()).val().trim() == "" || $("#inspectionRegisterTimeIdAudit", navTab.getCurrentPanel()).val().trim() == null ){
//				  alertMsg.error("当赔案欺诈风险为“有风险”时，公检法立案日期必填！");
//				  return;
//			  }
//		  }
//		  
//		  //欺诈识别途径校验
//		  if($("#cheatDistinguishChannelIdAudit", navTab.getCurrentPanel()).val().trim() == "" || $("#cheatDistinguishChannelIdAudit", navTab.getCurrentPanel()).val().trim() == null ){
//			  alertMsg.error("当赔案欺诈风险为“有风险”时，欺诈识别途径必选！");
//			  return;
//		  }
//		  //欺诈实施人员校验
//		  if($("#cheatImplementationPersonnelOneAudit", navTab.getCurrentPanel()).attr("checked") != "checked" && $("#cheatImplementationPersonnelTwoAudit", navTab.getCurrentPanel()).attr("checked") != "checked" && $("#cheatImplementationPersonnelThreeAudit", navTab.getCurrentPanel()).attr("checked") != "checked" 
//			  && $("#cheatImplementationPersonnelFourAudit", navTab.getCurrentPanel()).attr("checked") != "checked" && $("#cheatImplementationPersonnelFiveAudit", navTab.getCurrentPanel()).attr("checked") != "checked" && $("#cheatImplementationPersonnelSixAudit", navTab.getCurrentPanel()).attr("checked") != "checked" 
//				  && $("#cheatImplementationPersonnelSevenAudit", navTab.getCurrentPanel()).attr("checked") != "checked"){
//			  alertMsg.error("当赔案欺诈风险为“有风险”时，欺诈实施人员必选！");
//			  return;
//		  }
//		//选择故意欺诈风险选项为其他时文本框必填
//		  if($("#riskLabelEightAudit", navTab.getCurrentPanel()).attr("checked") == "checked"){
//			  if($.trim($("#riskOtherReasonAudit", navTab.getCurrentPanel()).val())==""){
//				  alertMsg.error("当赔案欺诈风险选项为其他时,文本框必填!");
//				  return;
//			  }
//		  }
//		  //选择欺诈识别途径为其他时，文本框必填
//		  if($("#cheatDistinguishChannelIdAudit", navTab.getCurrentPanel()).val() == "09"){
//			  if($.trim($("#otherCheatOptionIdAudit", navTab.getCurrentPanel()).val())==""){
//				  alertMsg.error("欺诈识别途径为其他时,文本框必填!");
//				  return;
//			  }
//		  }
//		  //选择欺诈实施人员为其他人员时，文本框必填
//		  if($("#cheatImplementationPersonnelSevenAudit", navTab.getCurrentPanel()).attr("checked") == "checked"){
//			  if($.trim($("#otherImplementationPersonnelAudit", navTab.getCurrentPanel()).val())==""){
//				  alertMsg.error("欺诈实施人员为其他人员时,文本框必填!");
//				  return;
//			  }
//		  }
//		  
//		    var beforeActionUrl=$("#toAuditConclusionInitform", navTab.getCurrentPanel()).attr("action");
//			var beforeSubmit=$("#toAuditConclusionInitform", navTab.getCurrentPanel()).attr("onsubmit");
//			$("#toAuditConclusionInitform", navTab.getCurrentPanel()).attr("action","clm/audit/updateApproveConclusion_CLM_addApproveConclusionAction.action?menuId=${menuId}&taskId="+$("#approveTaskId", navTab.getCurrentPanel()).val()+"&caseId="+$("#claimCaseVOcASEID",navTab.getCurrentPanel()).val());
//			$("#toAuditConclusionInitform", navTab.getCurrentPanel()).attr("onsubmit","return validateCallback(this, approveConfirmAjaxDone)");
//			$("#toAuditConclusionInitform",navTab.getCurrentPanel()).submit();
//		    //恢复form
//			$("#toAuditConclusionInitform", navTab.getCurrentPanel()).attr("action",beforeActionUrl );
//			$("#toAuditConclusionInitform", navTab.getCurrentPanel()).attr("onsubmit", beforeSubmit);
//		  
//		} else {
//			 $.ajax({
//					'type':'post',
//					'url':'clm/audit/queryLiabRiskLableFlag_CLM_auditConclusionAction.action?caseId='+caseIdNum,
//					'datatype':'json', 
//					'success':function(data){
//						var data = eval("(" + data + ")");
//						if(data.liabRiskLabel == 1){
//							$("#isRisk", navTab.getCurrentPanel()).selectMyComBox("1");
//							$("#riskDiv", navTab.getCurrentPanel()).show();
//							alertMsg.error("保项欺诈标签与赔案欺诈标签不一致，请确认。");
//						} else {
//							var beforeActionUrl=$("#toAuditConclusionInitform", navTab.getCurrentPanel()).attr("action");
//							var beforeSubmit=$("#toAuditConclusionInitform", navTab.getCurrentPanel()).attr("onsubmit");
//							$("#toAuditConclusionInitform", navTab.getCurrentPanel()).attr("action","clm/audit/updateApproveConclusion_CLM_addApproveConclusionAction.action?menuId=${menuId}&taskId="+$("#approveTaskId", navTab.getCurrentPanel()).val()+"&caseId="+$("#claimCaseVOcASEID",navTab.getCurrentPanel()).val());
//							$("#toAuditConclusionInitform", navTab.getCurrentPanel()).attr("onsubmit","return validateCallback(this, approveConfirmAjaxDone)");
//							$("#toAuditConclusionInitform",navTab.getCurrentPanel()).submit();
//						    //恢复form
//							$("#toAuditConclusionInitform", navTab.getCurrentPanel()).attr("action",beforeActionUrl );
//							$("#toAuditConclusionInitform", navTab.getCurrentPanel()).attr("onsubmit", beforeSubmit);
//						}
//					},
//					'error':function(){
//						alert("出错了！");
//					}
//				}); 
//		}
	
}

//审批确认回调函数
function approveConfirmAjaxDone(json) {
	if (json.statusCode == DWZ.statusCode.ok) {
		alertMsg.correct(json.message);
		//若审批确认成功，则刷新访问理赔工作台
		//关闭审核页面
		navTab.closeCurrentTab();
		if (typeof(navTab._getTab("20902"))!='undefined') {
			navTab.closeTab("20902");
			var url = "clm/clmWorkPlatform/claimApproveManage_CLM_clmWorkPlatformAction.action";
			navTab.openTab("20902", url, {title:'理赔审批'});
		}
		
//		//先关闭访问理赔工作台页面
//		navTab.closeTab("20345");
//		//重新打开理赔工作台页面
//		var url = "clm/clmWorkPlatform/clmWorkPlatformInitialize_CLM_clmWorkPlatformAction.action";
//		navTab.openTab("20345", url, {title:'访问理赔工作台'});
		$("a[title='访问理赔工作台']").find("span").unbind().click(function(){
	     	queryWorkMsgData();  
//	     	if($("#approveTask", navTab.getCurrentPanel()).html()!=null){
	     		var selectPriorityClaimVal = $("#priorityClaimListId", navTab.getCurrentPanel()).val();
//		     	var selectVal = $("#problemCheckListId", navTab.getCurrentPanel()).val();
//		     	$.ajax({
//		     		'url':'clm/clmWorkPlatform/problemCheckListCount_CLM_clmWorkPlatformAction.action?problemVal='+selectVal,
//		     		'type':'post',
//		     		'datatype':'json',
//		     		'success':function(data){
//		     			var count = eval("("+data+")");
//		     			$("#auditTaskId", navTab.getCurrentPanel()).text("审核("+count+")");
//		     		}
//		     	});
	     		if(selectPriorityClaimVal != "" && selectPriorityClaimVal == "undefined"){
	     			selectPriorityClaimVal = "";
	     		}
	     	    var rel = $("#personalTaskDetail", navTab.getCurrentPanel());
	        	rel.loadUrl("clm/clmWorkPlatform/queryApproveTaskMessages_CLM_clmWorkPlatformAction.action?difficultFlag=0&priorityClaimVal="+selectPriorityClaimVal);
//	     	} 
       });
       $("a[title='访问理赔工作台']").find("span").click();
	} else {
		alertMsg.error(json.message);
	}
}


/**
 * 退出
 */
/*function approveExit(){
	 alertMsg.confirm("当前页面录入的信息将不被保存，是否确定退出？",{
		 okCall:function(){
			navTab.closeCurrentTab();
		 }
	 });
}*/

/**
 * 上一步
 * @param caseId
 */
function preSaveStep(caseId){
/*	alert("aa");
	if($("#approveRemark", navTab.getCurrentPanel()).val()!="" && $("#approveDecision", navTab.getCurrentPanel()).val()!=""
	   && $("#approveCaseFlag", navTab.getCurrentPanel()).val()!="" && $("#approveRejectReason", navTab.getCurrentPanel()).val()!=""
	   && $("#approveRejectReason", navTab.getCurrentPanel()).val()!=""){
		presave = 1;
		alert("先保存再上一步的步骤！");
		approveDoUpdate();
	}else{
		alert("直接上一步");*/
		prev_approve('2',caseId);
	//}
	
}

//回调函数
/*function navTabAjaxDone(json){
	alert("presave标识为:"+presave);
	 if(json.statusCode == DWZ.statusCode.error) {
		  if(json.message && alertMsg){ 
		 	  alertMsg.error(json.message);
		  }
	 } else if (json.statusCode == DWZ.statusCode.timeout) {
			  DWZ.loadLogin(json.flag);
	 } else {
			//表示为保存
		 	if(presave == 1){
		 		presave = 0;
		 		prev_approve('2',$("#claimCaseVOcASEID",navTab.getCurrentPanel()).val());
		 	}else(json.data.flag == 0){
				alertMsg.correct(json.message);
			}
	 }
}*/

function changeResean(obj){
	if($(obj).val() == 1){ 
		 $("#approveRejectReason", navTab.getCurrentPanel()).selectMyComBox("");
//		 $("#approveRejectReason", navTab.getCurrentPanel()).setMyComboxDisabled(true); 
		 $("#approveRejectReason", navTab.getCurrentPanel()).prev().attr("disabled","disabled");
	}
	if($(obj).val() == 2){
		$("#approveRejectReason", navTab.getCurrentPanel()).setMyComboxDisabled(false);
	}
}

 
setTimeout("changeReseanInit()",500);
function changeReseanInit(){ 
	var obj=$("#approveDecision",navTab.getCurrentPanel());
	changeResean(obj); 
}
//判断赔案欺诈二级标签其他是否勾选 
function riskLabelEightAudit(obj){
	if($(obj).attr("checked")!="checked"){
		$("#riskOtherReasonAudit",navTab.getCurrentPanel()).hide();
		$("#riskOtherReasonAudit",navTab.getCurrentPanel()).val("");
	} else {
		$("#riskOtherReasonAudit",navTab.getCurrentPanel()).show();
	}
}
//判断赔案欺诈二级标签其他是否勾选 
function cheatImplementationPersonnelSeven(obj){
	if($(obj).attr("checked")!="checked"){
		$("#otherImplementationPersonnelAudit",navTab.getCurrentPanel()).hide();
		$("#otherImplementationPersonnelAudit",navTab.getCurrentPanel()).val("");
	} else {
		$("#otherImplementationPersonnelAudit",navTab.getCurrentPanel()).show();
	}
}


//初始化对公支付选中状态，和法人信息录入是否可点击状态
$(function(){
	var beneLegalPersonId = $("#beneLegalPersonId", navTab.getCurrentPanel()).val();
	if(beneLegalPersonId.length > 0){
		$("#legalPersonQueryBeneStyle", navTab.getCurrentPanel()).attr("disabled",false);
		$("#legalPersonQueryBeneStyleButton", navTab.getCurrentPanel()).attr("disabled",false);
	}else{
		$("#legalPersonQueryBeneStyle", navTab.getCurrentPanel()).attr("disabled",true);
		$("#legalPersonQueryBeneStyleButton", navTab.getCurrentPanel()).attr("disabled",true);
	}
	var contraryPayFlag = $("#contraryPayFlag", navTab.getCurrentPanel()).val();
	if(contraryPayFlag == 1){
		$("#contraryPayFlagCheckBoxId", navTab.getCurrentPanel()).attr("checked","checked");
		$("#legalPersonQueryPayeeStyle", navTab.getCurrentPanel()).attr("disabled",false);
		$("#legalPersonQueryPayeeStyleButton", navTab.getCurrentPanel()).attr("disabled",false);
	}else{
		$("#contraryPayFlagCheckBoxId", navTab.getCurrentPanel()).removeAttr("checked");
		$("#legalPersonQueryPayeeStyle", navTab.getCurrentPanel()).attr("disabled",true);
		$("#legalPersonQueryPayeeStyleButton", navTab.getCurrentPanel()).attr("disabled",true);
	}
});

//查询受益人法人录入信息
function queryLegalPersonInfoBene() {
	var caseId = $("#hiddenCaseId", navTab.getCurrentPanel()).val();
	var policyId = $("#hiddenPolicyId", navTab.getCurrentPanel()).val();
	var busiItemId = $("#hiddenBusiItemId", navTab.getCurrentPanel()).val();
	var policyCode = $("#hiddenPolicyCode", navTab.getCurrentPanel()).val();
	var listId = $("#beneLegalPersonId", navTab.getCurrentPanel()).val();
	var beneId = $("#beneId",navTab.getCurrentPanel()).val();
	var operationFlag = 'look';
	var personFlag = 'bene';
	var url = "clm/register/legalPersonInfoInit_CLM_legalPersonAction.action?operationFlag="+operationFlag+"&personFlag="+personFlag+"&legalPersonInfoVO.listId="+listId+"&relaListId="+beneId;
	$("#legalPersonQueryBene", navTab.getCurrentPanel()).attr("href", url).click();
}

//查询领款人法人录入信息
function queryLegalPersonInfoPayee() {
	var caseId = $("#hiddenCaseId", navTab.getCurrentPanel()).val();
	var policyId = $("#hiddenPolicyId", navTab.getCurrentPanel()).val();
	var busiItemId = $("#hiddenBusiItemId", navTab.getCurrentPanel()).val();
	var policyCode = $("#hiddenPolicyCode", navTab.getCurrentPanel()).val();
	var listId = $("#payeeLegalPersonId", navTab.getCurrentPanel()).val();		
	if(listId.length == 0){
		listId = $("#beneLegalPersonId", navTab.getCurrentPanel()).val();
	}
	var payeeId = $("#payeeId",navTab.getCurrentPanel()).val();
	var operationFlag = 'look';
	var personFlag = 'payee';
	var url = "clm/register/legalPersonInfoInit_CLM_legalPersonAction.action?operationFlag="+operationFlag+"&personFlag="+personFlag+"&legalPersonInfoVO.listId="+listId+"&relaListId="+payeeId;
	$("#legalPersonQueryPayee", navTab.getCurrentPanel()).attr("href", url).click();
}