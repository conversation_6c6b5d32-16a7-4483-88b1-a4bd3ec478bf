<%@ page language="java" contentType="text/html; charset=utf-8"
	pageEncoding="utf-8"%>
<%@include file="/udmpCommon.jsp"%>
<%@ include file="/clm/pages/common/commonJsCss.jsp"%>
<%@ taglib uri="http://www.git.com.cn/taglib/udmp/field" prefix="Field"%>
<%@ taglib uri="/struts-tags" prefix="s"%>
<script type="text/javascript" language="javascript"
	src="clm/js/commonMianBox.js">
</script>
<script type="text/javascript">

	//协谈 合议  显示详情操作
	$(document)
			.ready(
					function() {
						$("input[name='approve_r1']", $.pdialog.getCurrent())
								.on(
										'click',
										function(e) {
											$("#approve_treatyDesc",$.pdialog.getCurrent()).text($(this).parents("tr").find("#approve_Descra").val());
											$("#approve_treatyConclusion",$.pdialog.getCurrent()).text($(this).parents("tr").find("#approve_Conclusionra").val());
											$("#approve_organConclusion",$.pdialog.getCurrent()).text($(this).parents("tr").find("#organ_OConclusionra").val());
										});
						$("input[name='approve_Discuss']", $.pdialog.getCurrent()).die()
								.live(
										'click',
										function(e) {
											var discussFinalConclusion = $(this)
													.next().val();
											var discussContent = $(this).next()
													.next().val();
											$(
													"#approve_discussFinalConclusion",
													$.pdialog.getCurrent())
													.text(
															discussFinalConclusion);
											$("#approve_discussContent",
													$.pdialog.getCurrent())
													.text(discussContent);
											$
													.ajax({
														'type' : 'post',
														'url' : 'clm/audit/findAllSubDiscuss_CLM_claimDiscussAction.action?claimSubDiscussVO.discussId='
																+ $(this).val(),
														'datatype' : 'json',
														'success' : function(
																data) {
															var data = eval("("
																	+ data
																	+ ")");
															if (data.length != 0) {
																$(
																		"#approve_discussSubTBody",
																		$.pdialog
																				.getCurrent())
																		.empty();
																for (var i = 0; i < data.length; i++) {
																	var html = '<tr align="center">'
																			+ '<td><input type="radio" id="" name="subTBody" onclick="showSubDiscussReplyApp(this);" /></td>'
																			+ '<input type="hidden" id="appSubDiscussReply" value="'+data[i].subDiscussReply+'" />'
																			+ '<td>'
																			+ (i + 1)
																			+ '</td>'
																			+ '<td>'
																			+ data[i].subDiscussByName
																			+ '</td>'
																			+ '<td>'
																			+ data[i].subDiscussStatusName
																			+ '</td>'
																			+ '<td>'
																			+ data[i].subDiscussConclusionName
																			+ '</td>'
																			+ '<td>'
																			+ data[i].subDiscussString
																			+ '</td>'
																			+ '</tr>';
																	$(
																			"#approve_discussSubTBody",
																			$.pdialog
																					.getCurrent())
																			.append(
																					html);
																}
																$(
																		"#approve_discussSubTable",
																		$.pdialog
																				.getCurrent())
																		.css(
																				'display',
																				'block');
																$(
																		"#approve_discussSubDL",
																		$.pdialog
																				.getCurrent())
																		.css(
																				'display',
																				'block');
															} else {
																$(
																		"#approve_discussSubTable",
																		$.pdialog
																				.getCurrent())
																		.css(
																				'display',
																				'none');
																$(
																		"#approve_discussSubDL",
																		$.pdialog
																				.getCurrent())
																		.css(
																				'display',
																				'none');
															}
														}
													});
										});
					});
	function showSubDiscussReplyApp(ok) {
		var appSubDiscussReply = $(ok).parents("tr").find("#appSubDiscussReply").val();
		$("#approve_subDiscussReply", $.pdialog.getCurrent()).val(appSubDiscussReply);
	}
	//调查结论查询
	function surveyApplyConclusionApprove(applyId) {
		var url = "clm/survey/querySurveyClm_CLM_surveyConclusionQueryAction.action?applyId="
				+ applyId+"&dialogFlag=1";
		$("#approve_surveyApplyConclusionQuery", $.pdialog.getCurrent()).attr(
				"href", url).click();
	}
	//收缩按钮 实现
	function show(show) {
		if (show.value == "-") {
			document.getElementById(show.hiddenDivId).style.display = "none";
			show.value = "+";
		} else if (show.value == "+") {
			document.getElementById(show.hiddenDivId).style.display = "";
			show.value = "-";
		}
	}
	
	 // 查看/处理二核按钮控制，根据有无二核对查看/处理二核按钮进行控制 二核状态不为空以及不为已撤销时，按钮可用。
	if($("#approve_insuredlistpolicys").find("#claimUwStatusTD").text() != null && $("#approve_insuredlistpolicys").find("#claimUwStatusTD").text() != "" 
			&& $("#approve_insuredlistpolicys").find("#claimUwStatusTD").text() != "undefined"){
		$("#insuredlistpolicys").find("#claimUwStatusTD").each(function(i,obj){
			  if($(this).text() != null && $(this).text() != "" && $(this).text() != "已撤销"){
				  $("#dealUw", navTab.getCurrentPanel()).removeAttr("disabled", "disabled");
				  return false;
			  }else{
				  $("#dealUw", navTab.getCurrentPanel()).attr("disabled", "disabled");
			  }
		});
	}else{
		$("#dealUw", navTab.getCurrentPanel()).attr("disabled", "disabled");
	}
</script>
<body>
	<div layoutH="35">
		<div class="panelPageFormContent">
			<dl>
				<dt>赔案号</dt>
				<dd>
					<input name="caseNo" value="${claimCaseVO.caseNo}" type="text"
						readOnly />
				</dd>
			</dl>
			<dl>
				<dt>事件号</dt>
				<dd>
					<input name="accidentNo" value="${claimAccidentVO.accidentNo}"
						type="text" readOnly />
				</dd>
			</dl>
			</div>
			
			<div class="main_box">
				<div class="main_heading"><h1><img src="clm/images/tubiao.png">查看调查
					<b class="maim_lpask"></b></h1></div>
					<div class="main_lptwo">
					<p><div id="approve_medicalSurvey" class="tabdivclassbr main_tabdiv">
				<table class="list" style="width: 100%;"
					id="approve_surveyApplyOperTable">
					<thead>
						<tr align="center">
							<th nowrap>调查批次号</th>
							<th nowrap>提起阶段</th>
							<th nowrap>调查类型</th>
							<th nowrap>调查原因</th>
							<th nowrap>调查机构</th>
							<th nowrap>完成标志</th>
							<th nowrap>阳性结论</th>
							<th nowrap>重疾慰问先赔标识</th>
						</tr>
					</thead>
					<tbody>
						<s:iterator value="surveyApplyVOList" var="status" status="st">
							<tr align="center">
								<td><a href="javascript:void(0);"
									id="approve_surveyApplyConclusion"
									onclick="surveyApplyConclusionApprove('${applyId}')"><s:property
											value="surveyCode" /></a> <a
									id="approve_surveyApplyConclusionQuery" href="#"
									style="display: none;" width="1200" height="600" lookupGroup="">调查结论查询</a>
								</td>
								<td><Field:codeValue
										tableName="APP___CLM__DBUSER.T_SURVEY_SECTION"
										value="${applySection}" /></td>
								<td><Field:codeValue
										tableName="APP___CLM__DBUSER.T_SURVEY_TYPE"
										value="${surveyType}" /></td>
								<td><Field:codeValue
										tableName="APP___CLM__DBUSER.T_SURVEY_REASON"
										value="${surveyReason}" /></td>
								<td><Field:codeValue
										tableName="APP___CLM__DBUSER.T_UDMP_ORG" value="${surveyOrg}" /></td>
								<td>
									<s:if test="surveyStatus eq 1">已申请</s:if>
									<s:if test="surveyStatus eq 4">进行中</s:if>
									<s:if test="surveyStatus eq 3">撤销</s:if>
									<s:if test="surveyStatus eq 2">完成</s:if>
								</td>
								<td>
								<s:if test="surveyType == 6">
									<s:if test="positiveFlag == 1">已垫付</s:if> <s:if test="positiveFlag == 0">未垫付</s:if>
								</s:if> 
								<s:if test="surveyType != 6">
									<s:if test="positiveFlag eq 1">阳性</s:if><s:if test="positiveFlag eq 0">非阳性</s:if> 
								</s:if>
								</td>
								<td>
									<Field:codeValue tableName="APP___CLM__DBUSER.T_YES_NO" value="${priorityClaim }" />
								</td>
							</tr>
						</s:iterator>
					</tbody>
				</table>
			</div></p>				
				</div>
			</div>
			
			<form id="approve_uwTwiceconfirmForms"
				class="pagerForm required-validate" novalidate="novalidate"
				method="post" onsubmit="return navTabSearch(this)" action="">
				<div id="approve_uwAgain" class="panelPageFormContent">
			<div class="main_box">
				<div class="main_heading"><h1><img src="clm/images/tubiao.png">查看二核
					<b class="maim_lpask"></b></h1></div>
					<div class="main_lptwo">
					<div id="main_1" class="main_borderbg">
                                    <ul class="main_ul">
                                       <li class="clearfix">
					<div class="main_foldContent">
					<div class="main_bqtabdivbr" style="margin-left: 10px;">
					<div class="main_bqtitle">
                         <h1><img src="clm/images/three.png">出险人下的保单</h1>
                     </div>
<div class="">
					<div class="main_bqtabdiv">
						<table class="list" style="width: 100%;"
							id="approve_insuredlistpolicys">
							<thead>
								<tr align="center">
									<!-- <th nowrap>选择</th> -->
									<th nowrap>序号</th>
									<th nowrap>保单号码</th>
									<th nowrap>生效日期</th>
									<th nowrap>投保人姓名</th>
									<th nowrap>被保人姓名</th>
									<th nowrap>险种名称</th>
									<th nowrap>管理机构</th>
									<!-- <th nowrap>赔案相关标志</th> -->
									<th nowrap>二核类型</th>
									<th nowrap>二核状态</th>
									<!-- <th nowrap>核保次数</th> -->
								</tr>
							</thead>
							<tbody id="approve_uwRefreshPolicyList">
								<s:iterator value="contractMasterVos" var="status" status="st">
									<tr align="center">
										<%-- <td><input type="checkbox" flag="uwflag"
											value="${claimCaseVO.caseNo}" name="contractMasterVos[${st.index }].isChecked" id="uwcheck"
											></td> --%>
										<td>${st.index+1}<input type="hidden"
											name="contractMasterVos[${st.index }].caseId"
											value="${claimCaseVO.caseId}" /> <input type="hidden"
											name="contractMasterVos[${st.index }].caseNo"
											value="${claimCaseVO.caseNo}" /> <input type="hidden"
											name="contractMasterVos[${st.index }].policyId"
											value="${policyId}"> <input type="hidden"
											name="contractMasterVos[${st.index }].claimUwConculsion"
											value="${claimUwConculsion }">
										</td>
										<td><input type="hidden"
											name="contractMasterVos[${st.index }].policyCode"
											value="${policyCode}">${policyCode}</td>
									    <td><input type="hidden"
											name="contractMasterVos[${st.index }].validdateDate"
											value="${validdateDate}"><div align="center"><s:date name="validdateDate" format="yyyy-MM-dd"/></div></td>
										<td><input type="hidden"
											name="contractMasterVos[${st.index }].holderId"
											value="${holderId}">${holderName}</td>
										<td><input type="hidden"
											name="contractMasterVos[${st.index }].insuredId"
											value="${insuredId}">${insuredName}</td>
										<td><input type="hidden"
											name="contractMasterVos[${st.index }].productAbbrName"
											value="${productAbbrName}">${productAbbrName}</td>
										<td><input type="hidden"
											name="contractMasterVos[${st.index }].organCode"
											value="${organCode}">${organCodeName}</td>
										<%-- <td><input type="hidden"
											name="contractMasterVos[${st.index }].caseFlag"
											value="${caseFlag}">${caseFlagName}</td> --%>
										<td><input type="hidden"
											name="contractMasterVos[${st.index }].claimUwType"
											value="${claimUwType}">${claimUwTypeName}</td>
										<td id = "claimUwStatusTD"><input type="hidden"
											name="contractMasterVos[${st.index }].claimUwStatus"
											value="${claimUwStatus}">${claimUwStatusName}</td>
										<%-- <td><input type="hidden"
										name="contractMasterVos[${st.index }].undwerCount"
										value="${undwerCount}">${undwerCount}</td> --%>
									</tr>
								</s:iterator>
							</tbody>
						</table>
					</div>
					</div>
					</div>
					</div>
					</li></ul>
					</div>	
					<div class="pageFormdiv  main_tabdiv"">
													<a class="but_blue main_buta" id="dealUw"
														href="clm/audit/claimUnderTwiceInit_CLM_claimUnderTwiceAction.action?flagUw=2&caseId=${claimCaseVO.caseId}&caseNo=${claimCaseVO.caseNo}"
														target="dialog" rel="dlg_page1" max="true" mask="true"
														title="理赔核保" >查看二核</a>
												</div>
					<%-- <s:if test="claimUwVOs == null || claimUwVOs.size() == 0">
                     	<div id="main_2">
                            <ul class="main_ul">
                                <li class="clearfix fold">
                                    <h5 hasborder="true"><b id="two" class="main_plus"></b><span>已发起核保信息列表</span></h5>
                                    <div class="main_foldContent" style="display: none;">
                                       <div class="main_bqtabdivbr">
											<div> 
							
												<table class="list" width="100%">
													<thead>
														<tr align="center">
															<th nowrap>发起核保次数</th>
															<th nowrap>未告知情况</th>
															<th nowrap>备注信息</th>
														</tr>
													</thead>
													<tbody>
														<s:iterator value="claimUwVOs" var="status" status="st">
															<tr align="center">
																<td>${uwTimes}</td>
																<td>${notInformSituation}</td>
																<td>${remark}</td>
															</tr>
														</s:iterator>
													</tbody>
												</table>
											</div>
                     				</div>
                     			</div>
                     			</li>
                     		</ul>
                     	</div>
                     </s:if>
                     <s:else>
                     		<div id="main_2" class="main_borderbg">
                            <ul class="main_ul">
                                <li class="clearfix">
                                    <h5 hasborder="true"><b id="two" class="main_minus"></b><span>已发起核保信息列表</span></h5>
                                    <div class="main_foldContent">
                                       <div class="main_bqtabdivbr">
											<div> 
							
												<table class="list" width="100%">
													<thead>
														<tr align="center">
															<th nowrap>发起核保次数</th>
															<th nowrap>未告知情况</th>
															<th nowrap>备注信息</th>
														</tr>
													</thead>
													<tbody>
														<s:iterator value="claimUwVOs" var="status" status="st">
															<tr align="center">
																<td>${uwTimes}</td>
																<td>${notInformSituation}</td>
																<td>${remark}</td>
															</tr>
														</s:iterator>
													</tbody>
												</table>
											</div>
                     				</div>
                     			</div>
                     			</li>
                     		</ul>
                     	</div>
                     </s:else> --%>
                     
				</div>
			</div>
					
				</div>
			</form>
			<div class="main_box">
				<div class="main_heading"><h1><img src="clm/images/tubiao.png">查看合议
					<b class="maim_lpask"></b></h1></div>
					<div class="main_lptwo">
					
					
					<div id="main_1" class="main_borderbg">
                                    <ul class="main_ul">
                                       <li class="clearfix">
					<div class="main_foldContent">
					<div class="main_bqtabdivbr" style="margin-left: 10px;">
					<div class="main_bqtitle">
                         <h1><img src="clm/images/three.png">出险人下的保单</h1>
                     </div>
                     	<div id="approve_dsicussMedical">
				<div class="main_bqtabdiv">
					<table class="list" style="width: 100%;">
						<thead>
							<tr align="center">
								<td nowrap>选择</td>
								<td nowrap>序号</td>
								<td nowrap>合议主管</td>
								<td nowrap>已分配人数</td>
								<td nowrap>已回复人数</td>
								<td nowrap>合议最终结论</td>
								<td nowrap>任务分配结论</td>
								<td nowrap>发起日期</td>
								<td nowrap>期望回复日期</td>
								<td nowrap>最终结论回复日期</td>
							</tr>
						</thead>
						<tbody id="approve_otherOperTBody">
							<s:iterator value="claimDiscussVOs" var="claimDiscussVO"
								status="st">
								<tr align="center">
									<td><input type="radio" id="approve_Discuss${st.index+1}"
										name="approve_Discuss" value="${discussId}" /> <input
										type="hidden" value="${discussDesc}" /> <input type="hidden"
										value="${discussContent}" /> <input type="hidden"
										value="${discussStatus}" /></td>
									<td><s:property value="#st.index+1"></s:property></td>
									<td>${discussManageName}</td>
									<td>${allotNumber}</td>
									<td>${replyNumber}</td>
									<td><Field:codeValue value="${discussConclusion}"
											tableName="APP___CLM__DBUSER.T_CLAIM_AUDIT_DECISION" /></td>
									<td><Field:codeValue value="${assignConclusion}"
											tableName="APP___CLM__DBUSER.T_DISCUSS_ASSIGN_DECI" /></td>
									<td><s:date name='applyTime' format='yyyy-MM-dd' /></td>
									<td><s:date name='expectReturnTime' format='yyyy-MM-dd' />
									</td>
									<td><s:date name='discussBackTime' format='yyyy-MM-dd' />
									</td>
								</tr>
							</s:iterator>
						</tbody>
					</table>
				</div>
				<div class="panelPageFormContent main_tabdiv">
					<dl style="height: auto;">
						<dt>最终结论描述</dt>
						<dd>
							<textarea readOnly id="approve_discussFinalConclusion" rows="3"
								cols="55"></textarea>
						</dd>
					</dl>
				</div>
				<div class="tabdivclassbr main_tabdiv">
					<table id="approve_discussSubTable" class="list"
						style="width: 100%;">
						<thead>
							<tr align="center">
								<td nowrap>选择</td>
								<td nowrap>序号</td>
								<td nowrap>合议人</td>
								<td nowrap>状态</td>
								<td nowrap>合议结论</td>
								<td nowrap>合议回复日期</td>
							</tr>
						</thead>
						<tbody id="approve_discussSubTBody">
						</tbody>
					</table>
				</div>
				<div class="panelPageFormContent main_tabdiv">
					<dl id="approve_discussSubDL" style="height: auto;width: 100%">
						<dt>合议回复</dt>
						<dd>
							<textarea readOnly id="approve_subDiscussReply" rows="3"
								cols="55"></textarea>
						</dd>
					</dl>
					<dl style="height: auto; width: 100%;">
						<dt>合议内容</dt>
						<dd>
							<textarea readOnly id="approve_discussContent" rows="3" cols="55"></textarea>
						</dd>
					</dl>
				</div>
			</div>				
                     
                     </div>
                     </div>
                     </li>
                     </ul>
                     </div>
				</div>
			</div>
			
<!-- 			<div id="approve_dsicussMedical"> -->
<!-- 				合议历史 -->
<!-- 							<fieldset style="border-style: solid;"> -->
<!-- 								<legend style="border-style: solid;">合议历史</legend> -->
<!-- 				<div class="divfclass"> -->
<!-- 					<h1> -->
<!-- 						<img src="clm/images/tubiao.png">合议历史 -->
<!-- 					</h1> -->
<!-- 				</div> -->
<!-- 				<div class="tabdivclassbr"> -->
<!-- 					<table class="list" style="width: 100%;"> -->
<!-- 						<thead> -->
<!-- 							<tr align="center"> -->
<!-- 								<td nowrap>选择</td> -->
<!-- 								<td nowrap>序号</td> -->
<!-- 								<td nowrap>合议主管</td> -->
<!-- 								<td nowrap>已分配人数</td> -->
<!-- 								<td nowrap>已回复人数</td> -->
<!-- 								<td nowrap>合议最终结论</td> -->
<!-- 								<td nowrap>任务分配结论</td> -->
<!-- 								<td nowrap>发起日期</td> -->
<!-- 								<td nowrap>期望回复日期</td> -->
<!-- 								<td nowrap>最终结论回复日期</td> -->
<!-- 							</tr> -->
<!-- 						</thead> -->
<!-- 						<tbody id="approve_otherOperTBody"> -->
<%-- 							<s:iterator value="claimDiscussVOs" var="claimDiscussVO" --%>
<%-- 								status="st"> --%>
<!-- 								<tr align="center"> -->
<%-- 									<td><input type="radio" id="approve_Discuss${st.index+1}" --%>
<%-- 										name="approve_Discuss" value="${discussId}" /> <input --%>
<%-- 										type="hidden" value="${discussDesc}" /> <input type="hidden" --%>
<%-- 										value="${discussContent}" /> <input type="hidden" --%>
<%-- 										value="${discussStatus}" /></td> --%>
<%-- 									<td><s:property value="#st.index+1"></s:property></td> --%>
<%-- 									<td>${discussManageName}</td> --%>
<%-- 									<td>${allotNumber}</td> --%>
<%-- 									<td>${replyNumber}</td> --%>
<%-- 									<td><Field:codeValue value="${discussConclusion}" --%>
<%-- 											tableName="APP___CLM__DBUSER.T_CLAIM_AUDIT_DECISION" /></td> --%>
<%-- 									<td><Field:codeValue value="${assignConclusion}" --%>
<%-- 											tableName="APP___CLM__DBUSER.T_DISCUSS_ASSIGN_DECI" /></td> --%>
<%-- 									<td><s:date name='applyTime' format='yyyy-MM-dd' /></td> --%>
<%-- 									<td><s:date name='expectReturnTime' format='yyyy-MM-dd' /> --%>
<!-- 									</td> -->
<%-- 									<td><s:date name='discussBackTime' format='yyyy-MM-dd' /> --%>
<!-- 									</td> -->
<!-- 								</tr> -->
<%-- 							</s:iterator> --%>
<!-- 						</tbody> -->
<!-- 					</table> -->
<!-- 				</div> -->
<!-- 				<div class="panelPageFormContent main_tabdiv"> -->
<!-- 					<dl style="height: auto;"> -->
<!-- 						<dt>最终结论描述</dt> -->
<!-- 						<dd> -->
<!-- 							<textarea readOnly id="approve_discussFinalConclusion" rows="3" -->
<!-- 								cols="61"></textarea> -->
<!-- 						</dd> -->
<!-- 					</dl> -->
<!-- 				</div> -->
<!-- 				<div class="tabdivclassbr main_tabdiv"> -->
<!-- 					<table id="approve_discussSubTable" class="list" -->
<!-- 						style="width: 100%;"> -->
<!-- 						<thead> -->
<!-- 							<tr align="center"> -->
<!-- 								<td nowrap>选择</td> -->
<!-- 								<td nowrap>序号</td> -->
<!-- 								<td nowrap>合议人</td> -->
<!-- 								<td nowrap>状态</td> -->
<!-- 								<td nowrap>合议结论</td> -->
<!-- 								<td nowrap>合议回复日期</td> -->
<!-- 							</tr> -->
<!-- 						</thead> -->
<!-- 						<tbody id="approve_discussSubTBody"> -->
<!-- 						</tbody> -->
<!-- 					</table> -->
<!-- 				</div> -->
<!-- 				<div class="panelPageFormContent main_tabdiv"> -->
<!-- 					<dl id="approve_discussSubDL" style="height: auto;"> -->
<!-- 						<dt>合议回复</dt> -->
<!-- 						<dd> -->
<!-- 							<textarea readOnly id="approve_subDiscussReply" rows="3" -->
<!-- 								cols="61"></textarea> -->
<!-- 						</dd> -->
<!-- 					</dl> -->
<!-- 					<dl style="height: auto; width: 100%;"> -->
<!-- 						<dt>合议内容</dt> -->
<!-- 						<dd> -->
<!-- 							<textarea readOnly id="approve_discussContent" rows="3" cols="61"></textarea> -->
<!-- 						</dd> -->
<!-- 					</dl> -->
<!-- 				</div> -->
<!-- 							</fieldset> -->
<!-- 			</div> -->
			<!-- 查看合议 end -->
			
	</div>
	<div class="formBarButton">
	<button class="but_gray" type="button" id=""
				onclick="exit();" >退出</button>
				</div>
</body>
