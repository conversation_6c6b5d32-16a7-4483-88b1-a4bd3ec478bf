/**
 * 点击保存时校验必录项
 */
function approveDoSave(){
	if($("#approveRemark", navTab.getCurrentPanel()).val()==null || $("#approveRemark", navTab.getCurrentPanel()).val()==""){
		alertMsg.info("审批意见不能为空!");
		$("#approveRemark", navTab.getCurrentPanel()).focus();
	}else if($("#approveDecision", navTab.getCurrentPanel()).val()==null || $("#approveDecision", navTab.getCurrentPanel()).val()==""){
		alertMsg.info("请选择审批结论!");
	    $("#approveDecision", navTab.getCurrentPanel()).focus();
	}else if($("#approveCaseFlag", navTab.getCurrentPanel()).val()==null || $("#approveCaseFlag", navTab.getCurrentPanel()).val()==""){
		alertMsg.info("请选择案件标识!");
	    $("#approveCaseFlag", navTab.getCurrentPanel()).focus();
	}else if($("#approveDecision", navTab.getCurrentPanel()).val()==2 && $("#approveRejectReason", navTab.getCurrentPanel()).val()==""){
		alertMsg.info("请选择不通过原因!");
	    $("#approveRejectReason", navTab.getCurrentPanel()).focus();
	}else if($("#approveDecision", navTab.getCurrentPanel()).val() == 1 && $("#approveRejectReason", navTab.getCurrentPanel()).val()!=""){
		alertMsg.info("审批结论为通过，不应选择不通过原因!");
	    $("#approveRejectReason", navTab.getCurrentPanel()).focus();
	}else{
		$("#toAuditConclusionInitform", navTab.getCurrentPanel()).submit();
	}
}
/**
 * 点击审批确认时校验必录项
 */
function approveDoUpdate(){
	if($("#approveRemark", navTab.getCurrentPanel()).val()==null || $("#approveRemark", navTab.getCurrentPanel()).val()==""){
		alertMsg.error("审批意见不能为空!");
	    $("#approveRemark", navTab.getCurrentPanel()).foucs();
	}else if($("#approveDecision", navTab.getCurrentPanel()).val()==null || $("#approveDecision", navTab.getCurrentPanel()).val()==""){
		alertMsg.error("请选择审批结论!");
	    $("#approveDecision", navTab.getCurrentPanel()).foucs();
	}else if($("#approveCaseFlag", navTab.getCurrentPanel()).val()==null || $("#approveCaseFlag", navTab.getCurrentPanel()).val()==""){
		alertMsg.error("请选择案件标识!");
	    $("#approveCaseFlag", navTab.getCurrentPanel()).foucs();
	}else if($("#approveDecision", navTab.getCurrentPanel()).val()==2 && $("#approveRejectReason", navTab.getCurrentPanel()).val()==""){
		alertMsg.error("请选择不通过原因!");
	    $("#approveRejectReason", navTab.getCurrentPanel()).foucs();
	}else if($("#approveDecision", navTab.getCurrentPanel()).val() == 1 && $("#approveRejectReason", navTab.getCurrentPanel()).val()!=""){
		alertMsg.error("审批结论为通过，不应选择不通过原因!");
	    $("#approveRejectReason", navTab.getCurrentPanel()).foucs();
	}else{
		$("#toAuditConclusionInitform", navTab.getCurrentPanel()).attr("action","clm/audit/updateApproveConclusion_CLM_addApproveConclusionAction.action?menuId=${menuId}&taskId="+$("#approveTaskId").val());
		$("#toAuditConclusionInitform", navTab.getCurrentPanel()).attr("onsubmit","return validateCallback(this, approveConfirmAjaxDone)");
		$("#toAuditConclusionInitform", navTab.getCurrentPanel()).submit();
	}
}

//审批确认回调函数
function approveConfirmAjaxDone(json) {
	if (json.statusCode == DWZ.statusCode.ok) {
		alertMsg.correct(json.message);
		//若审批确认成功，则刷新访问理赔工作台
		//关闭审核页面
		navTab.closeCurrentTab();
		//先关闭访问理赔工作台页面
		navTab.closeTab("20345");
		//重新打开理赔工作台页面
		var url = "clm/clmWorkPlatform/clmWorkPlatformInitialize_CLM_clmWorkPlatformAction.action";
		navTab.openTab("20345", url, {title:'访问理赔工作台'});
	} else {
		alertMsg.error(json.message);
	}
}


/**
 * 退出
 */
/*function approveExit(){
	 alertMsg.confirm("当前页面录入的信息将不被保存，是否确定退出？",{
	 	okCall:function(){
			navTab.closeCurrentTab();
	 	}
	 });
}*/

/**
 * 上一步
 * @param caseId
 */
function approveUpStep(caseId){
	 alertMsg.confirm("当前页面录入的信息将不被保存，是否确认回到上一步？",{
	 	okCall:function(){
			prev_approve('8',caseId);
	 	}
	 });
}