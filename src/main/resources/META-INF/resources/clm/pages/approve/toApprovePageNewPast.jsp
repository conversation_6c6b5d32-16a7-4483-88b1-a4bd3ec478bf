
<%@ page language="java" contentType="text/html; charset=utf-8"
	pageEncoding="UTF-8"%><%@ include file="/clm/pages/common/commonJsCss.jsp" %>
<%@ taglib uri="/struts-tags" prefix="s"%>
<script type="text/javascript" src="clm/pages/audit/noMouseUse.js"></script> 
<script type="text/javascript">
var isApprove = "approve";
//上一步中的标识
var presave = 0;
//影像查询gaojun_wb 
function queryApproveClaimImages() {
	var caseId = $("#approve_caseId",navTab.getCurrentPanel()).val();
	$.post("clm/common/queryClaimImages_CLM_commonQueryAction.action",{caseId:caseId},function(data){
		if(data.message!=""){
			//不等于空点击返回的url
			var urlValue= data.message;//替换掉&符号
			window.open(urlValue);
		}else{
		   alertMsg.error("调用影像接口失败");
		}
	},'json');
}
//风险评估报告
function riskReportApprove(){
	var caseId=$("#approve_caseId",navTab.getCurrentPanel()).val();
	var url="clm/audit/claimRiskReportConfigInfo_CLM_claimRiskReportConfigAction.action?caseId="+caseId;
	$("#riskReportApprove",navTab.getCurrentPanel()).attr("href",url).click();
} 
//既往赔案查询
function alwaysClaim(){
	var caseId=$("#approve_caseId",navTab.getCurrentPanel()).val();
	var url="clm/common/queryAlwaysClaim_CLM_commonQueryAction.action?caseId="+caseId;
	$("#comAlwaysClaimApprove",navTab.getCurrentPanel()).attr("href",url).click();
} 
//保险条款查看
function queryPolicyClause() {
	var caseId = $("#claimCaseVOcASEID",navTab.getCurrentPanel()).val();
	/* var url="clm/common/queryPolicyListByCaseId_CLM_queryPolicyClauseAction.action?caseId="+185; //查询赔案关联的保单和险种*/
	var url = "clm/common/queryPolicyNoByCaseId_CLM_queryPolicyClauseAction.action?caseId="
			+ caseId;
	$("#comPolicyClause",navTab.getCurrentPanel()).attr("href", url).click();
}
//保单查询
function policQueryPage(){
	debugger;
	var caseId=$("#approve_caseId",navTab.getCurrentPanel()).val();
	var url="clm/common/initPolicQueryPage_CLM_commonQueryAction.action?caseId="+caseId;
	$("#policQueryId",navTab.getCurrentPanel()).attr("href",url).click();		
}
//备注信息查看
function getMemoApprove() {
	var caseId=$("#approve_caseId",navTab.getCurrentPanel()).val();
	var url = "clm/memo/memoView_CLM_memoAction.action?clmCaseVO.caseId="+caseId;
	$("#memoApprove",navTab.getCurrentPanel()).attr("href", url).click();
}
//查看风险保额  xuyz_wb add
function queryCustomerRiskApprove() {
	var caseId = $("#approve_caseId",navTab.getCurrentPanel()).val();
	var url = "clm/common/queryCustomerRisk_CLM_commonQueryAction.action?caseId="
			+ caseId;
	$("#queryCustomerRiskApprove",navTab.getCurrentPanel()).attr("href", url).click();
}
//查询任务轨迹 xuyz_wb add
function queryTaskTraceApprove() {
	var caseId = $("#approve_caseId",navTab.getCurrentPanel()).val();
	var url = "clm/taskmanage/queryTaskTraceInitByCaseNo_CLM_queryTaskTraceAction.action?caseId="
			+ caseId +"&taskTraceFlag=taskTraceFlag";
	$("#queryTaskTraceApprove",navTab.getCurrentPanel()).attr("href", url).click();
}
//查看再保意见
function queryApproveClaimRIS() {
	var caseId = $("#approve_caseId",navTab.getCurrentPanel()).val();
	var url = "clm/common/queryClaimRIS_CLM_commonQueryAction.action?caseId="
			+ caseId;
	$("#claimRISApprove",navTab.getCurrentPanel()).attr("href", url).click();
}
//分支流程查询  xuyz_wb add
function queryBranchFlowApprove(){
	var caseId = $("#approve_caseId",navTab.getCurrentPanel()).val();
	var url = "clm/common/queryBranchFlow_CLM_queryBranchFlowAction.action?caseId="
			+ caseId;
	$("#queryBranchFlowApprove",navTab.getCurrentPanel()).attr("href", url).click();
}
var check;
function getPar(id) {
	check = document.getElementById(id);
}
//签名比对gaojun_wb 
function comparisonToApprove() {
	var caseId = $("#approve_caseId",navTab.getCurrentPanel()).val();
	$.post("clm/common/signatureComparison_CLM_commonQueryAction.action",{caseId:caseId},function(data){
		if(data.message!=""){
			//不等于空点击返回的url
			window.open(data.message);
		}else{
			alertMsg.error("调用影像接口失败");
		}
	},'json');
}
//查看报案、立案、签批信息
function caseQueryPage(){
	var caseId=$("#approve_caseId", navTab.getCurrentPanel()).val();
	var url="clm/common/caseCommonQueryPage_CLM_commonQueryAction.action?caseId="+caseId;
	$("#rePortRegisSignId", navTab.getCurrentPanel()).attr("href",url).click();		
}

//影像库查询huangjh_wb 
function queryImageMsgFormECS() {
	$.post("clm/common/queryImageUrl_CLM_commonQueryAction.action",function(data){
		if(data.message!=""){
			//不等于空点击返回的url
			var urlValue= data.message.replace(/\&/g,"%26");//替换掉&符号
			navTab.openTab("title", "clm/common/queryImageMsgUrl_CLM_commonQueryAction.action?urlValue="+urlValue, {title:'影像库查询'});
		}else{
			alertMsg.error("获取影像url失败");
		}
	},'json');
}

//跳转到下一个选项卡方法，参数id为要跳转到的选项卡的Id
function next_approve(id,caseId){
	//将当前页面的参数传递给下一界 面
	if(id=='2'){
		document.getElementById(id).href="clm/audit/approvecontractDealInit_CLM_contractDealAction.action?caseId="+caseId;
	}
	if(id=='3'){
		document.getElementById(id).href="clm/approve/approveConclusionInit_CLM_approveConclusionAction.action?caseId="+caseId+'&pastFlag=1';
	}
	//将要跳转到的隐藏的选项卡显示
	$("#"+id,navTab.getCurrentPanel()).css("display","block");
	//调用选项卡的click方法，将页面跳转至下个选项卡中
	$("#"+id,navTab.getCurrentPanel()).click();
	
	//用于进度条显示
	$("#step"+id,navTab.getCurrentPanel()).find("div").attr("class","main_step"); 
	$("#step"+(id-1),navTab.getCurrentPanel()).find("div").attr("class","main_laterline");	
	$("#n"+id,navTab.getCurrentPanel()).find("div").attr("class","main_n1d");		 
	$("#n"+(id-1),navTab.getCurrentPanel()).find("div").attr("class","main_later");
	$("#step"+(id-1),navTab.getCurrentPanel()).find("div").attr("class","main_laterline");
}
//跳转上一步
function prev_approve(id,caseId){
	//将当前页面的参数传递给上一界面
	if(id=='1'){
		document.getElementById(id).href="clm/register/approveDataCollectInit_CLM_dataCollectAction.action?caseId="+caseId;
	}
	if(id=='2'){
		document.getElementById(id).href="clm/audit/approvecontractDealInit_CLM_contractDealAction.action?caseId="+caseId;
	}
	//调用选项卡的click方法，将页面跳转至下个选项卡中
	$("#"+id,navTab.getCurrentPanel()).click();
	
	//用于进度条显示
	$("#step"+id,navTab.getCurrentPanel()).find("div").attr("class","main_step"); 
	$("#step"+(eval(id)+1),navTab.getCurrentPanel()).find("div").attr("class","main_laterline");	
	$("#n"+id,navTab.getCurrentPanel()).find("div").attr("class","main_n1d");		 
	$("#n"+(eval(id)+1),navTab.getCurrentPanel()).find("div").attr("class","main_later");
	$("#step"+(eval(id)+1),navTab.getCurrentPanel()).find("div").attr("class","main_laterline");
}

$(function(){
	var viewNum = ${viewNum};
	
	if(viewNum == 0){
		$("#n1", navTab.getCurrentPanel()).find("div").attr("class","main_n1d");
		$("#n3", navTab.getCurrentPanel()).find("div").attr("class","main_n2");
		$("#1",navTab.getCurrentPanel()).each(function(){
			$(this).attr("style","");
		});
		$("#2",navTab.getCurrentPanel()).each(function(){
			$(this).attr("style","display:none");
		});
		$("#3",navTab.getCurrentPanel()).each(function(){
			$(this).attr("style","display:none");
		});
		
		$("#step1",navTab.getCurrentPanel()).find("div").attr("class","main_step");
		$("#step2",navTab.getCurrentPanel()).find("div").attr("class","main_stepOther");
		$("#step3",navTab.getCurrentPanel()).find("div").attr("class","main_stepOther");
	}
	if(viewNum == 1){//扫描/回销索赔单证
		$("#n1", navTab.getCurrentPanel()).find("div").attr("class","main_n2");
		$("#n2", navTab.getCurrentPanel()).find("div").attr("class","main_n1d");
		$("#1",navTab.getCurrentPanel()).each(function(){
			$(this).attr("style","");
		});
		$("#2",navTab.getCurrentPanel()).each(function(){
			$(this).attr("style","");
		});
		$("#3",navTab.getCurrentPanel()).each(function(){
			$(this).attr("style","display:none");
		});
		
		$("#step1",navTab.getCurrentPanel()).find("div").attr("class","main_stepOther");
		$("#step2",navTab.getCurrentPanel()).find("div").attr("class","main_step");
		$("#step3",navTab.getCurrentPanel()).find("div").attr("class","main_stepOther");
		
	}
	if(viewNum == 2){//保单挂起/解挂
		$("#n2", navTab.getCurrentPanel()).find("div").attr("class","main_n2");
		$("#n3", navTab.getCurrentPanel()).find("div").attr("class","main_n1d");
		$("#1",navTab.getCurrentPanel()).each(function(){
			$(this).attr("style","");
		});
		$("#2",navTab.getCurrentPanel()).each(function(){
			$(this).attr("style","");
		});
		$("#3",navTab.getCurrentPanel()).each(function(){
			$(this).attr("style","");
		});
		
		$("#step1",navTab.getCurrentPanel()).find("div").attr("class","main_stepOther");
		$("#step2",navTab.getCurrentPanel()).find("div").attr("class","main_stepOther");
		$("#step3",navTab.getCurrentPanel()).find("div").attr("class","main_step");
	}
});
</script>
<div layoutH="0">
	<div class="step_header">
		<input type="hidden" id ="approveTaskId" value="${approveTaskId}">
		<input type="hidden" id ="caseId" value="${caseId}">
		<input type="hidden" id ="approve_caseId" value="${caseId}">
		
		<table width="100%" border="0" cellspacing="0" cellpadding="0">
			<tr>
				<td width="2%" class="n1" id="n1"><div class="main_n1d">1</div></td>
			    <td id="step1"><div class="main_step">核对案件信息</div></td>
			    <td width="2%" class="n2" id="n2"><div class="main_n2">2</div></td>
			    <td id="step2"><div class="main_stepOther">合同处理</div></td>
			    <td width="2%" class="n3" id="n3"><div class="main_n2">3</div></td>
			    <td id="step3"><div class="main_stepOther">出具审批结论</div></td>
			</tr>
			<tr> 
				<td height="20"></td>
			</tr>
		</table>
	</div>
	<div class="pageContent">
		<div class="tabs" currentIndex="${viewNum}" eventType="click">
		<div id="cmdivApprove" style="display: none;"
					onmouseup="MM_changeProp('cmdivApprove','','display','none','DIV')">
					<dl class="colummenu" id="cm">
						<dt>
							<a href="javaScript:void(0)" onclick="caseQueryPage();">报案信息查看</a><a
								id="rePortRegisSignId" href="#" target="navTab"
								title="报案信息查看"></a>
						</dt>
						<dt>
							<a href="javaScript:void(0)" onclick="policQueryPage();">保单查询
							</a><a id="policQueryId" href="#" target="navTab"
								title="保单查询"></a>
						</dt>
						<dt>
							<a href="javaScript:void(0)" onclick="alwaysClaim();"  id="comAlwaysClaimFirst">既往赔案查询</a><a
								id="comAlwaysClaimApprove" href="#" target="navTab"
								title="既往赔案查询"></a>
						</dt>
						<dt>
							<a rel="memoApprove" width="1000" height="450" [mask=true
								]  href="#" onclick="getMemoApprove();" id="memoAuditFirst">备注信息查看</a> <a
								id="memoApprove" rel="memoApprove" href="#" target="dialog"
								[mask=true ] style="display: none;" width="1000" height="450">备注信息查看</a>
						</dt>
						<dt>
							<a href="javaScript:void(0)" onclick="queryApproveClaimImages()">影像查询</a>
						</dt>
						<dt>
							<a href="javaScript:void(0)" onclick="queryBranchFlowApprove();">各分支流程查询</a>
							<a id="queryBranchFlowApprove" rel="queryBranchFlowApprove"
								href="#" target="dialog" [mask=true ] style="display: none;"
								width="1000" height="450">各分支流程查询</a>
						</dt>
						<dt>
							<a href="javaScript:void(0)" onclick="queryTaskTraceApprove();">查询任务轨迹</a><a
								id="queryTaskTraceApprove" rel="queryTaskTraceApprove" href="#"
								target="dialog" [mask=true ] style="display: none;" width="1000"
								height="450">查询任务轨迹</a>
						</dt>
						<dt>
							<a href="javaScript:void(0);" id="queryImageMsgInfo"
								 onclick="queryImageMsgFormECS()">影像库查询</a>
								 <a id="queryImageMsg" href="" rel="queryImageMsg" target="navTab"
									 style="display: none;" [mask=true]  width="1000" height="450">影像库查询</a>
						</dt>
						<!-- <dt>
							<a href="javaScript:void(0)" onclick="queryPolicyClause()">保险条款查看</a><a
								id="comPolicyClause" href="#" target="navTab"
								style="display: none;" rel="comPolicyClause">保险条款查看</a>
						</dt> -->
						<dt>
							<a href="javaScript:void(0)" onclick="queryApproveClaimRIS();"  id="claimRISFirst">查看再保意见</a><a
								id="claimRISApprove" rel="claimRISApprove" href="#"
								target="dialog" [mask=true ] style="display: none;" width="1000"
								height="450">查看再保意见</a>
						</dt>
						<dt>
							<a href="javaScript:void(0)"
								onclick="queryCustomerRiskApprove();">查看风险保额</a><a
								id="queryCustomerRiskApprove" rel="queryCustomerRiskApprove"
								href="#" target="dialog" [mask=true ] style="display: none;"
								width="1000" height="450">查看风险保额</a>
						</dt>
						<dt>
							<a href="javaScript:void(0)" onclick="comparisonToApprove();">签名比对</a>
						</dt>
						<%-- <dt>
							<a href="clm/common/checkIdentityInfo_CLM_commonQueryAction.action?caseId=${caseId}&checkType=1"
								target="dialog" rel="page2" width="1000" height="550"
								[mask=true]>身份验真</a>
						</dt> --%>
						<dt>
							<a href="javaScript:void(0)" onclick="riskReportApprove()" id="riskReport">风险评估报告</a><a
								id="riskReportApprove" href="#" target="dialog" [mask=true
								] style="display: none;" rel="riskReportApprove" width="1000"
								height="450">风险评估报告</a>
						</dt>
					</dl>
				</div>
			<div class="tabsHeader">
				<div class="tabsHeaderContent">
					<a class="button" style="float: right;display: block;" id="changePropApprove" href="#" ><span>!</span></a>
					<ul>
 				        <li><a href="clm/register/approveDataCollectInit_CLM_dataCollectAction.action?caseId=${caseId}" class="j-ajax" id="1"><span>核对案件信息</span></a></li>
						<li><a href="clm/audit/approvecontractDealInit_CLM_contractDealAction.action?caseId=${caseId}" class="j-ajax" id="2" style="display:none"><span>合同处理</span></a></li>
					    <li><a href="clm/approve/approveConclusionInit_CLM_approveConclusionAction.action?caseId=${caseId}&pastFlag=1" class="j-ajax" id="3" style="display:none"><span>出具审批结论</span></a></li>
					</ul>
				</div>
			</div>
			<div class="tabsContent main_tabsContent">
				<div id="caseQueryTabs1"></div>
				<div id="caseQueryTabs2"></div>
				<div id="caseQueryTabs3"></div>
			</div>
			<div class="tabsFooter">
				<div class="tabsFooterContent"></div>
			</div>
		</div>
	</div>
</div>
<script type="text/javascript" >
var  firstClick=0;
$('#changePropApprove', navTab.getCurrentPanel()).toggle(
		  function () {
			  if(firstClick==0){
				  onFirstClick();
				//78067 ALM12222
				  //queryBlackNameInfo();
				  firstClick=1;
				}  
			//78067 ALM12222
			  queryBlackNameInfo();
			  queryMemo();
			  if($('#cmdivApprove', navTab.getCurrentPanel()).attr("style") == "display: none"){
				  MM_changeProp('cmdivApprove','','display','block','DIV');
				  } else {
					  MM_changeProp('cmdivApprove','','display','none','DIV');
				  }
		  },
		  function () {
			//78067 ALM12222
			  queryBlackNameInfo();
			  queryMemo();
			  if($('#cmdivApprove', navTab.getCurrentPanel()).attr("style") == "display: none"){
				  MM_changeProp('cmdivApprove','','display','block','DIV');
				  } else {
					  MM_changeProp('cmdivApprove','','display','none','DIV');
				  }
		  }
);
function MM_changeProp(objId,x,theProp,theValue) { //v9.0
	$("#"+objId).css("position",'relative'); 
	$("#"+objId).css("z-index",'1'); 
var obj = null; with (document){ if (getElementById)
obj = getElementById(objId); }
if (obj){
  if (theValue == true || theValue == false)
    eval("obj.style."+theProp+"="+theValue);
  else eval("obj.style."+theProp+"='"+theValue+"'");
}
}
//当第一次加载时 把没有内容的超链接变成灰色  ajax
function onFirstClick(){
	var caseId=$("#caseId", navTab.getCurrentPanel()).val();
	  $.ajax({
		 'url' :'clm/common/queryLinkValue_CLM_commonQueryAction.action?caseID='+caseId,
		 'type' :'post',
		 'datatype' : 'json' ,
		 'success':function(data){
		    var data = eval("("+data+")");
		    if(data.statusCode!=null&&data.statusCode=="301"){
		    	firstClick=0;
		    }
		    if(data.alwaysSize=='0'){//无数据置灰   既往赔案查询
		    	$("#comAlwaysClaimFirst",navTab.getCurrentPanel()).attr("disabled",
		    			"disabled");
		    }
		    if(data.riSuggestSize=='0'){//  再保意见
		    	$("#claimRISFirst",navTab.getCurrentPanel()).attr("disabled",
		    			"disabled");
		    }
		    
		  },
  		 'error':function(){
  				alert("出错了！");
  			} 
    	});	
}
function  queryMemo(){
	var caseId=$("#caseId", navTab.getCurrentPanel()).val();
	  $.ajax({
		 'url' :'clm/common/queryMemoValue_CLM_commonQueryAction.action?caseID='+caseId,
		 'type' :'post',
		 'datatype' : 'json' ,
		 'success':function(data){
		    var data = eval("("+data+")"); 
		    if(data.memoSize=='1'){//   备注信息 
		    	$("#memoAuditFirst",navTab.getCurrentPanel()).css("color",
		    			"red");
		    }if(data.memoSize!='1'){//   备注信息 
		    	$("#memoAuditFirst",navTab.getCurrentPanel()).css("color",
    			"black");
          }
		    
		  },
		 'error':function(){
				alert("出错了！");
			} 
  	});	
}
//风险评估报告中查询黑名单
function  queryBlackNameInfo(){
	var caseId=$("#caseId", navTab.getCurrentPanel()).val();
	$.ajax({
		 'url' :'clm/audit/queryBlackNameInfo_CLM_claimRiskReportConfigAction.action?caseId='+caseId,
		 'type' :'post',
		 'datatype' : 'json' ,
		 'success':function(data){
		    var data = eval("("+data+")"); 
		    if(data.blackNameSize=='1'){//   备注信息 
		    	$("#riskReport",navTab.getCurrentPanel()).css("color",
		    			"red");
		    	//alertMsg.info("风险评估报告信息查看!");
		    }
		    if(data.blackNameSize!='1'){//   备注信息 
		    	$("#riskReport",navTab.getCurrentPanel()).css("color",
   			"black");
         }
		    
		  },
		 'error':function(){
				alert("出错了！");
			} 
 	});	
}
</script>