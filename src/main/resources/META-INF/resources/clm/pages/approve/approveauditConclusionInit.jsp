<%@ page language="java" contentType="text/html; charset=utf-8"
	pageEncoding="UTF-8"%><%@ include file="/clm/pages/common/commonJsCss.jsp" %>
<%@ taglib uri="http://www.git.com.cn/taglib/udmp/field" prefix="Field"%>
<%@ taglib uri="/struts-tags" prefix="s"%>
 
<s:set var="ctx">${pageContext.request.contextPath}</s:set>
 
<script type="text/javascript" src="clm/pages/approve/approveauditConclusionInit.js"></script> 


<script type="text/javascript" charset="UTF-8">
   var auditRejectReason='${claimAuditApproveVO.auditRejectReason}'; //拒付/审核不通过原因
   var auditDecision='${claimAuditApproveVO.auditDecision}'; //赔付结论
   var caseIdNum='${caseId}';
   var caseFlagNum='${claimCaseVO.caseFlag}';
   var overCompFlagNum='${claimCaseVO.overCompFlag}';
   var listIdNum='${claimAuditApproveVO.listId}';
   if (isApprove == "approve") {
		//OneDIV
		var obj=$("div#approve_auditConclusionInitOne", navTab.getCurrentPanel());
		//按钮
		obj.find("button").each(function(){
			$(this).attr("disabled",true);
		});
		//输入框 复选框 单选按钮  控制
		obj.find("input").each(function(){
			$(this).attr("disabled",true);
		});
		//控制单选按钮可操作
		obj.find("input[type='radio']").each(function(){
			$(this).attr("disabled",false);
		});
		//下拉框
		obj.find("select").each(function(){
			$(this).attr("disabled",true);
		});
		//多行文本框控制
	    obj.find("textarea").each(function(){
			$(this).attr("disabled",true);
		}); 
	    //TwoDIV
		var objTwo=$("div#approve_auditConclusionInitTwo", navTab.getCurrentPanel());
		//如果不是  上一步 和 下一步按钮
		objTwo.find("button").each(function(){
			var buttonName=$(this).text();
			if(buttonName!="上一步" && buttonName!="下一步"){
				$(this).attr("disabled",true);
			}
			if(buttonName=="下一步"){
				$(this).parent().parent().css("display","block");
			}
		});
		//产生审核报告按钮禁用
	    objTwo.find("a").each(function(){
			$(this).attr("disabled",true);
		}); 
		
		$("input[name='r2']", navTab.getCurrentPanel()).attr("disabled",false);
	}
   
   //页面加载数据判断
    //是否常规给付 初始化控制
    var iscommon=$("#approve_isCommon", navTab.getCurrentPanel()).val();
	if(iscommon==""){
		$("select#approve_isCommon", navTab.getCurrentPanel()).find("option").text("无");
	}
	//产生审核报告按钮 初始化控制
	if(caseFlagNum==""){
		$("#approve_auditReportCreate", navTab.getCurrentPanel()).attr("disabled",true);
	}else{
        if(caseFlagNum!="4" && caseFlagNum!="5" && caseFlagNum!="6"){
        	$("#approve_auditReportCreate", navTab.getCurrentPanel()).attr("disabled",false);
    		var url = "clm/audit/produceAuditReport_CLM_auditConclusionAction.action?caseId="+caseIdNum+"&claimAuditApproveVO.listId="+listIdNum;
    	    +"&claimCaseVO.caseFlag="+caseFlagNum+"&claimCaseVO.overCompFlag="+overCompFlagNum;
    		$("#approve_auditReportCreate", navTab.getCurrentPanel()).attr("href",url);
		}
	}
	//拒付/审核不通过原因 和其他原因初始化控制
	$.auditRejectReasonJSP = $("#approve_auditRejectReason", navTab.getCurrentPanel());
	 if(auditRejectReason==""){
		$("#approve_auditRejectReason", navTab.getCurrentPanel()).attr("disabled",true);
		$("#approve_otherReason", navTab.getCurrentPanel()).attr("disabled",true);
		$("#approve_auditRejectReason", navTab.getCurrentPanel()).attr("value","");
		$("#approve_otherReason", navTab.getCurrentPanel()).attr("value","");
	}else if(auditRejectReason=="99"){
		$.auditRejectReasonJSP.empty();
		if(auditDecision=="3"){
			var option = $("#approve_reasonOneAudit", navTab.getCurrentPanel()).html();
			$(option).appendTo($.auditRejectReasonJSP);
			
		}else if(auditDecision=="6"){
			var option = $("#approve_reasonTwoAudit", navTab.getCurrentPanel()).html();
			$(option).appendTo($.auditRejectReasonJSP);
		}else{
			$("<option value=''>请选择</option>").appendTo($.auditRejectReason); 
		}
		$("#approve_auditRejectReason", navTab.getCurrentPanel()).attr("disabled",false);
		$("#approve_otherReason", navTab.getCurrentPanel()).attr("disabled",false);
	}else{
		$.auditRejectReasonJSP.empty();
		if(auditDecision=="3"){
			var option = $("#approve_reasonOneAudit", navTab.getCurrentPanel()).html();
			$(option).appendTo($.auditRejectReasonJSP);
		}else if(auditDecision=="6"){
			var option = $("#approve_reasonTwoAudit", navTab.getCurrentPanel()).html();
			$(option).appendTo($.auditRejectReasonJSP);
		}else{
			$("<option value=''>请选择</option>").appendTo($.auditRejectReason); 
		}
		$("#approve_auditRejectReason", navTab.getCurrentPanel()).attr("disabled",false);
		$("#approve_otherReason", navTab.getCurrentPanel()).attr("disabled",true);
		$("#approve_otherReason", navTab.getCurrentPanel()).attr("value","");
	}
	if(auditRejectReason!=""){
		$("select#approve_auditRejectReason", navTab.getCurrentPanel()).children().each(function(){
			 var val = $(this).attr("value");
			 if(auditRejectReason == val){
				 $(this).attr("selected",true);
			 }
		});
	}
	
	 
	/* if(queryAlwaysClaim!=""){
 	  $("#approve_auditReportCreate", navTab.getCurrentPanel()).attr("disabled",true);
	} */
</script>

<body>
	<div class="panelPageFormContent" layoutH="11">
		<form id="approve_auditConclusionInitForm" action="" method="post"
			class="pageForm required-validate" novalidate="novalidate"
			onsubmit="return validateCallback(this, navTabAjaxDone)" >
			<input type="hidden" name="claimAuditApproveVO.listId" id="approve_claimAuditApproveVOlistId" value="${claimAuditApproveVO.listId}"/>
			<div id="approve_auditConclusionInitOne">
			<dl>
				<dt>赔案号</dt>
				<dd>
					<input name="caseId" type="hidden" size="30" value="${caseId}" id="approve_caseId"/>
					<input name="claimCaseVO.caseId" type="hidden" size="30" value="${caseId}" id="claimCaseVO.caseId"/>
					<input name="claimCaseVO.caseNo" type="text" size="30" value="${claimCaseVO.caseNo}" readonly="readonly" />
				</dd>
			</dl>
			<dl>
				<dt>事 件 号</dt>
				<dd>
					<input name="claimAccidentVO.accidentNo" value="${claimAccidentVO.accidentNo}" type="text" size="30" readonly="readonly" />
				</dd>
			</dl>
			<div class="panelPageFormContent">
				<dl>
					<dt>
					      <input type="checkbox" size="5px"
							disabled="disabled" value="${claimCaseVO.advanceFlag}" <s:if test="claimCaseVO.advanceFlag==1">checked="checked"</s:if>/>
					</dt>
					<dd>预付标识</dd>
				</dl>

				<dl>
					<dt>
						<font>*</font>案件标识
					</dt>
					<dd>
					     <Field:codeTable cssClass="combox title"  id="approve_caseFlagAudit" name="claimCaseVO.caseFlag" value="${claimCaseVO.caseFlag}"  
                             tableName="APP___CLM__DBUSER.T_CASE_LEVEL" nullOption="true" orderBy="code" whereClause="code in(1,2,3)" ></Field:codeTable>
					</dd>
				</dl>
				<dl>
				<dt>超期</dt>
				<dd>
					<input
						style="border: 0px; background: 0px; width: auto; float: left;"
						type="checkbox" size="5px" id="isOverComp" disabled="true"
						<s:if test="claimCaseVO.isOverComp eq 1"> checked="checked"</s:if>
						name="claimCaseVO.isOverComp" value="${claimCaseVO.isOverComp}" />
				</dd>
				</dl>
				<dl>
					<dt>
					    <input type="checkbox" size="5px" id="approve_overCompFlag" <s:if test="claimCaseVO.overCompFlag eq 1"> checked="checked"</s:if>
							name="claimCaseVO.overCompFlag" value="${claimCaseVO.overCompFlag}" />
					</dt>
					<dd>超期补偿</dd>
				</dl>
			</div>
			
			<div class="divfclass">
				<h1>
					<img src="clm/images/tubiao.png">案件赔付结论
				</h1>
			</div>

					<dl>
						<dt>
							<font>*</font>赔付结论
						</dt>
						<dd>
						
						<Field:codeValue tableName="APP___CLM__DBUSER.T_CLAIM_AUDIT_DECISION" value="${claimAuditApproveVO.auditDecision}"/>
						 
						</dd>
					</dl>
					<dl>
						<dt>
							<font>*</font>是否常规给付
						</dt>
						<dd>
						    <Field:codeTable cssClass="combox title"  id="approve_isCommon"  name="claimAuditApproveVO.isCommon" orderBy="code" disabled="true" value="${claimAuditApproveVO.isCommon}" tableName="APP___CLM__DBUSER.T_CLAIM_COMMON" nullOption="true"></Field:codeTable>
						</dd>
					</dl>
					<dl>
						<dt>拒付/审核不通过原因</dt>
						<dd>
							<select class="combox title"  id="approve_auditRejectReason" name="claimAuditApproveVO.auditRejectReason" onChange="clickauditRejectReason(this.value)">
								<option value="">请选择</option> 
						    </select>
						    <div style="display:none;">
							<Field:codeTable cssClass="combox title"  id="approve_reasonOneAudit" name="" 
	                             tableName="APP___CLM__DBUSER.T_CLAIM_AUDIT_REJECT" orderBy="substr(code,0,1)" disabled="false" nullOption="true" defaultValue="" ></Field:codeTable>
							<Field:codeTable cssClass="combox title"  id="approve_reasonTwoAudit" name="" 
	                             tableName="APP___CLM__DBUSER.T_CLAIM_REJECT_REASON" orderBy="substr(code,0,1)" disabled="false" nullOption="true" defaultValue=""></Field:codeTable>
							</div>
						</dd>
					</dl>

					<dl>
						<dt>其他原因</dt>
						<dd>
							<input name="claimAuditApproveVO.otherReason" id="approve_otherReason" maxlength="100" type="text"
								value="${claimAuditApproveVO.otherReason}"/>
						</dd>
					</dl>

					<dl style="width:1000px;height:auto;">
						<dt><font>*</font>审核意见</dt>
						<dd>
							<textarea style="width: 800px;" rows="3" cols="6" id="approve_auditRemark" onpropertychange="if(value.length>1500) value=value.substring(0,1500)"
								name="claimAuditApproveVO.auditRemark">${claimAuditApproveVO.auditRemark}</textarea>
						</dd>
					</dl>
				<div>
				<div class="formBarButton">
					<ul>
						<li>
							<button type="button" class="but_blue" onclick="saveclaimAuditApprove()">保存</button>
						</li>
					</ul>
				</div>		
			</div>   
			
			<div class="divfclass">
				<h1>
					<img src="clm/images/tubiao.png">赔付通知书
				</h1>
			</div>
			<div class="panelPageFormContent" >
					<div class="panel" style="margin:10px 0px;">
				    <div class="divfclass">
						<h1>
							<img src="clm/images/tubiao.png">受益人和领款人信息
						</h1>
					</div>
					<input type="checkbox" size="5px" id="approve_tomessage" value="0"/>按受益人发送赔付通知书
					<div class="tabdivclassbr" >
						<table class="list" style="width: 100%;" id="approve_beneAndPayee">
							<thead>
								<tr align="center">
									<td nowrap>选择</td>
									<td nowrap>序号</td>
									<td nowrap>受益人姓名</td>
									<td nowrap>领款人姓名</td>
									<td nowrap>受益金额</td>
									<td nowrap>受益比例</td>
									<td nowrap>领款方式</td>
									<td nowrap>银行账号</td>
									<td nowrap>领款金额</td>
								</tr>
							</thead>
							<tbody>
								<s:iterator value="beneAndPayeeVOList" var="status" status="var">
									<tr align="center">
										<td><input type="radio" name="beneAndPayee" value="<s:property value="claimbene.beneId"></s:property>" id="approve_check"/></td>
										<td><s:property value="#var.index+1"></s:property></td>
										<td><s:property value="claimbene.beneName"></s:property></td>
										<td><s:property value="claimpayee.payeeName"></s:property></td>
										<td><s:property value="claimpay.payAmount"></s:property></td>
										<td><s:property value="claimpay.payMole"></s:property>/<s:property value="claimpay.payDeno"></s:property></td>
										<td><s:property value="claimpayee.payMode"></s:property></td>
										<td><span type="expandBankAccount"><s:property value="claimpayee.accountNo"></s:property></span></td>
										<td><s:property value="claimpay.payAmount"></s:property></td>
									</tr>
								</s:iterator>
							</tbody>
						</table>
					</div>
					
				   </div>
				   			   
				<div class="panelPageFormContent">
				<div class="divfclass">
					<h1>
						<img src="clm/images/tubiao.png">赔付明细表
					</h1>
				</div>
		            <table>
		            	<tr>
						<td><input onclick="show(this)" type="button" style="width:20px;height:20px;text-align:center;" hiddenDivId="medical" value="-"/></td>
						<td><h2 style="margin-left: 10px;">医疗类</h2></td></tr>
					</table>

					<div class="panel" id="approve_medical">
					<div class="pageFormContent medical tabdivclassbr">
					<table class="list" style="width: 100%;">
						<thead>
							<tr align="center">
								<td nowrap>选择</td>
								<td nowrap>序号</td>
								<td nowrap>保单号</td>
								<td nowrap>险种名称</td>
								<td nowrap>保险责任名称</td>
								<td nowrap>账单金额</td>
								<td nowrap>实际住院天数</td>
								<td nowrap>预付金额</td>
								<td nowrap>合同结算金额</td>
								<td nowrap>超期补偿金额</td>
								<td nowrap>实际赔付金额</td>
								<td nowrap>剩余有效保额</td>
								<td nowrap>合同状态</td>
							</tr>
						</thead>
						<tbody>
							<s:iterator value="payDetailListMedical" var="status" status="var">
								<tr align="center">
									<td><input type="radio" name="r1" id="approve_MedBody"/></td>
									<td><s:property value="#var.index+1"></s:property></td>
									<td><s:property value="policyCode"></s:property></td>
									<td><s:property value="productNameSys"></s:property></td>
									<td><s:property value="liabName"></s:property></td>
									<td><s:property value="sumAmount"></s:property><s:if test="sumAmount eq null">0</s:if></td>
									<td><s:property value="reallyDay"></s:property><s:if test="reallyDay eq null">0</s:if></td>
									<td><s:property value="advancePay"></s:property><s:if test="advancePay eq null">0</s:if></td>
									<td><s:property value="freeAmount"></s:property><s:if test="freeAmount eq null">0</s:if></td>
									<td><s:property value="overdueMoney"></s:property><s:if test="overdueMoney eq null">0</s:if></td>
									<td><s:property value="actualPay"></s:property><s:if test="actualPay eq null">0</s:if></td>
									<td><s:property value="remainEffPay"></s:property><s:if test="remainEffPay eq null">0</s:if></td>
									<td><Field:codeValue tableName="APP___CLM__DBUSER.T_LIABILITY_STATUS" value="${liabilityState}"/>
									<input id="approve_clmRemark" name="approve_clmRemark" value="${clmRemark}" type="hidden"/>
									</td>
								</tr>
							</s:iterator>
						</tbody>
					</table>
					<dl>
						<dt>备注信息</dt>
						<dd >
							<textarea maxlength="1000" id="approve_MedBody" name="payDetailVO.remark" style="width:900px;height:90px;" rows="3" cols="6" readonly></textarea>
						</dd>	
					</dl>
					</div>
					</div>
					
					<table><tr>
					<td><input onclick="show(this)" type="button" style="width:20px;height:20px;text-align:center;" hiddenDivId="nomedical" value="-"/></td>
					<td><h2 style="margin-left: 10px;">非医疗类</h2></td></tr></table>
					<div class="divider"></div>
					
					<div  id="approve_nomedical ">
					<div class="pageFormContent nomedical tabdivclass">
					
				    <input type="hidden" name="payDetailVO.listId" id="approve_unMedListId">
					<table class="list" style="width: 100%;">
						<thead>
							<tr align="center">
								<td nowrap>选择</td>
								<td nowrap>序号</td>
								<td nowrap>保单号</td>
								<td nowrap>险种名称</td>
								<td nowrap>保险责任名称</td>
								<td nowrap>保险金额</td>
								<td nowrap>年度红利</td>
								<td nowrap>终了红利</td>
								<td nowrap>合同结算金额</td>
                                <td nowrap>超期补偿金额</td>
								<td nowrap>实际赔付金额</td>
								<td nowrap>剩余有效保额</td>
								<td nowrap>合同状态</td>
							</tr>
						</thead>
						<tbody>
							<s:iterator value="payDetailListUnMedical" var="status" status="var" >
								<tr align="center">
									<td><input type="radio" name="r2" id="approve_UnMedBody"/><input type="hidden" value='<s:property value="listId"/>'/></td>
									<td><s:property value="#var.index+1"></s:property></td>
									<td><s:property value="policyCode"></s:property></td>
									<td><s:property value="productNameSys"></s:property></td>
									<td><s:property value="liabName"></s:property></td>
									<td><s:property value="amount"></s:property></td>
									<td><s:property value="bonusSa"></s:property><s:if test="bonusSa eq null">0</s:if></td>
									<td><s:property value="bonusSaEnd"></s:property><s:if test="bonusSaEnd eq null">0</s:if></td>
									<td><s:property value="freeAmount"></s:property><s:if test="freeAmount eq null">0</s:if></td>
									<td><s:property value="overdueMoney"></s:property><s:if test="overdueMoney eq null">0</s:if></td>
									<td><s:property value="actualPay"></s:property><s:if test="actualPay eq null">0</s:if></td>
							        <td><s:property value="remainEffPay"></s:property><s:if test="remainEffPay eq null">0</s:if></td>
							        <td><Field:codeValue tableName="APP___CLM__DBUSER.T_LIABILITY_STATUS" value="${liabilityState}"/>
									<input id="approve_clmRemark" name="approve_clmRemark" value="${clmRemark}" type="hidden"/>
									</td>
								</tr>
							</s:iterator>
						</tbody>
					</table>
					<dl style="width: 100%;">
						<dt>备注信息</dt>
						<dd >
							<textarea maxlength="1000" id="approve_UnMedBody" name="payDetailVO.remark" style="width: 900px;height:30px;" rows="3" cols="6" readonly>
							</textarea>
						</dd>	
					</dl>
					</div>
				    </div>
				
			</div>

			</div>
            </div>
			<div class="formBarButton" id="approve_auditConclusionInitTwo">
					 <ul>
					<!-- 	<li>
							 <button type="button" class="but_blue"  onclick="notNote()">拒付通知书</button>
						</li> -->
						<li id="approve_auditReportDiv">
							 	<a href="javascript:void(0);" class="but_blue main_buta"  id="approve_auditReportCreate" rel="page2" target="dialog"   max="true"  mask="true" type="button"><span>产生审核报告</span></a>
						</li> 
						<li>
							<button type="button" class="but_blue"  onclick="prev_approve('7','${caseId}')">上一步</button>
						</li>
						<li>
							<button type="button" class="but_blue"  onclick="saveInfoAuditConclusion()" >保存</button>
						</li>
						<li>
							<button type="button" class="but_blue"  id="affirmSuer" onclick="sureInfo()">审核确认</button>
						</li>
						<li>
							<button type="button" class="but_blue"  onclick="next_approve('9','${caseId}')">下一步</button>
						</li>
						<li>
							<button type="button" class="but_gray"  onclick="exit()">退出</button>
						</li>
					 </ul>
			</div>

		</form>
	</div>
	<div id="approve_printShow"></div>
</body>
