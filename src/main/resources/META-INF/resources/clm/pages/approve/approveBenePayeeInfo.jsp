<%@ page language="java" pageEncoding="UTF-8"%><%@ include file="/clm/pages/common/commonJsCss.jsp" %>
<%@ taglib uri="/struts-tags" prefix="s"%>
<%@ taglib uri="http://www.git.com.cn/taglib/udmp/field" prefix="Field"%>
<s:set var="ctx">${pageContext.request.contextPath}</s:set>
<script type="text/javascript" language="javascript"
	src="clm/js/commonMianBox.js">
</script>
<!-- 受益人信息 -->
<script type="text/javascript">
$(function(){
	var beneCertiType = '${beneVO.beneCertiType}';
	if(beneCertiType=='0'){
		var birth = '${beneVO.beneBirth}';
		var beneCertiStart = '<s:date name="beneVO.beneCertiStart" format="yyyy-MM-dd"/>';
		var beneCertiEnd = '<s:date name="beneVO.beneCertiEnd" format="yyyy-MM-dd"/>';
		//换算出客户的年龄（周岁,“客户出生日期”开始至“证件有效起期”经过的整年度）
		var beneCertiStartDate = new Date(beneCertiStart.replace(/-/g, '/'));
		var beneCertiEndDate = new Date(beneCertiEnd.replace(/-/g, '/'));
		var beneCertiStartYear = beneCertiStartDate.getFullYear();
		var beneCertiEndYear = beneCertiEndDate.getFullYear();
		var years = beneCertiEndYear-beneCertiStartYear;
		if(years>50){
			$("#beneCertiEnd", navTab.getCurrentPanel()).val("");
			$("#beneCertiEnd", navTab.getCurrentPanel()).attr("disabled",true);
			$("#beneCertiEndCheckBoxApproveId",navTab.getCurrentPanel()).attr("checked","checked");
		}else{
			$("#beneCertiEnd", navTab.getCurrentPanel()).attr("disabled",true);
		}
	} 
	if(beneCertiType=='4'){
		$("#beneCertiEnd", navTab.getCurrentPanel()).val("");
		$("#beneCertiEnd", navTab.getCurrentPanel()).attr("disabled",true);
		$("#beneCertiEndCheckBoxApproveId",navTab.getCurrentPanel()).attr("checked","checked");
	}
	var beneCertiEnd = $("#beneCertiEnd", navTab.getCurrentPanel()).val();
	if (beneCertiEnd == '9999-12-31'){
		$("#beneCertiEnd", navTab.getCurrentPanel()).val("");
		$("#beneCertiEnd", navTab.getCurrentPanel()).attr("disabled",true);
		$("#beneCertiEndCheckBoxApproveId",navTab.getCurrentPanel()).attr("checked","checked");
    }
	var payeeCertiType = '${payeeVO.payeeCertiType}';
	if(payeeCertiType=='0'){
		var birth = '${payeeVO.payeeBirth}';
		var payeeCertiStart = '<s:date name="payeeVO.payeeCertiStart" format="yyyy-MM-dd"/>';
		var payeeCertiEnd = '<s:date name="payeeVO.payeeCertiEnd" format="yyyy-MM-dd"/>';
		//换算出客户的年龄（周岁,“客户出生日期”开始至“证件有效起期”经过的整年度）
		var payeeCertiStartDate = new Date(payeeCertiStart.replace(/-/g, '/'));
		var payeeCertiEndDate = new Date(payeeCertiEnd.replace(/-/g, '/'));
		var payeeCertiStartYear = payeeCertiStartDate.getFullYear();
		var payeeCertiEndYear = payeeCertiEndDate.getFullYear();
		var years = payeeCertiEndYear-payeeCertiStartYear;
		if(years>50){
			$("#payeeCertiEnd", navTab.getCurrentPanel()).val("");
			$("#payeeCertiEnd", navTab.getCurrentPanel()).attr("disabled",true);
			$("#payeeCertiEndCheckBoxApproveId",navTab.getCurrentPanel()).attr("checked","checked");
		}else{
			$("#payeeCertiEnd", navTab.getCurrentPanel()).attr("disabled",true);
		}
	} 
	if(payeeCertiType=='4'){
		$("#payeeCertiEnd", navTab.getCurrentPanel()).val("");
		$("#payeeCertiEnd", navTab.getCurrentPanel()).attr("disabled",true);
		$("#payeeCertiEndCheckBoxApproveId",navTab.getCurrentPanel()).attr("checked","checked");
	}
	var payeeCertiEnd = $("#payeeCertiEnd", navTab.getCurrentPanel()).val();
	if (payeeCertiEnd == '9999-12-31'){
		$("#payeeCertiEnd", navTab.getCurrentPanel()).val("");
		$("#payeeCertiEnd", navTab.getCurrentPanel()).attr("disabled",true);
		$("#payeeCertiEndCheckBoxApproveId",navTab.getCurrentPanel()).attr("checked","checked");
    }
	var beneLegalPersonId = $("#beneLegalPersonId", navTab.getCurrentPanel()).val();
	if(beneLegalPersonId.length > 0){
		$("#legalPersonQueryBeneStyle", navTab.getCurrentPanel()).attr("disabled",false);
		$("#legalPersonQueryBeneStyleButton", navTab.getCurrentPanel()).attr("disabled",false);
	}else{
		$("#legalPersonQueryBeneStyle", navTab.getCurrentPanel()).attr("disabled",true);
		$("#legalPersonQueryBeneStyleButton", navTab.getCurrentPanel()).attr("disabled",true);
	}
	var contraryPayFlag = $("#contraryPayFlag", navTab.getCurrentPanel()).val();
	if(contraryPayFlag == 1){
		$("#contraryPayFlagCheckBoxId", navTab.getCurrentPanel()).attr("checked","checked");
		$("#legalPersonQueryPayeeStyle", navTab.getCurrentPanel()).attr("disabled",false);
		$("#legalPersonQueryPayeeStyleButton", navTab.getCurrentPanel()).attr("disabled",false);
	}else{
		$("#contraryPayFlagCheckBoxId", navTab.getCurrentPanel()).removeAttr("checked");
		$("#legalPersonQueryPayeeStyle", navTab.getCurrentPanel()).attr("disabled",true);
		$("#legalPersonQueryPayeeStyleButton", navTab.getCurrentPanel()).attr("disabled",true);
	}
})
</script>
<div class="main_box">
	<div class="main_heading"><h1><img src="clm/images/tubiao.png">受益人信息
	<b class="maim_lpask"></b></h1>
	</div>
	<input type="hidden" name="beneVO.legalPersonId" value="${beneVO.legalPersonId}" id="beneLegalPersonId">
	<div class="main_lptwo">
	<p><div class="panelPageFormContent">
	<dl style="width: 100%;">
	<dt style="width: 12%;">
		&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
		<a disabled="true" id ="legalPersonQueryBeneStyle" href="javaScript:void(0)" onclick="queryLegalPersonInfoBene();"  >
		<button type="button" class="but_blue " id="legalPersonQueryBeneStyleButton" disabled="disabled" >法人信息录入</button>
		</a>
		<a target="dialog" id ="legalPersonQueryBene" href="javaScript:void(0)" rel="legalPersonQueryBene" style="display:none;" width="1000" height="450"  > 法人信息录入</a>
	</dt>
	</dl>
		    <dl>
				<dt>受益人与被保人关系</dt>
				<dd>
				   <s:if test="beneVO.beneRelation!=null">
					  <Field:codeValue tableName="APP___CLM__DBUSER.T_LA_PH_RELA" value="${beneVO.beneRelation}"  />
				   </s:if>
				</dd>
			</dl>
			<dl>
				<dt>投保人与受益人关系</dt>
				<dd>
					<s:if test="claimPayVO.beneHolderRelation!=null">
					  <Field:codeValue tableName="APP___CLM__DBUSER.T_LA_PH_RELA" value="${claimPayVO.beneHolderRelation }"  />
				   </s:if>
				</dd>
			</dl>
			<dl>
				<dt>姓名</dt>
				<dd>
				   ${beneVO.beneName }
				</dd>
			</dl>
			<dl>
				<dt>性别</dt>
				<dd>
				   <s:if test="beneVO.beneSex!=null">
					  <Field:codeValue tableName="APP___CLM__DBUSER.T_GENDER" value="${beneVO.beneSex}"/>
				   </s:if>
				</dd>
			</dl>
			<dl>
				<dt>出生日期</dt>
				<dd>
				   <s:date name="beneVO.beneBirth" format="yyyy-MM-dd"/>
				</dd>
			</dl>
			<dl>
				<dt>证件类型</dt>
				<dd>
				   <s:if test="beneVO.beneCertiType!=null">
					  <Field:codeValue tableName="APP___CLM__DBUSER.T_CERTI_TYPE" value="${beneVO.beneCertiType}"/>
				   </s:if>
				</dd>
			</dl>
			<dl>
				<dt>证件号码</dt>
				<dd>
				   ${beneVO.beneCertiNo }
				</dd>
			</dl>
			<dl>
				<dt>受益人国籍</dt>
				<dd>
				   <s:if test="beneVO.beneCertiType!=null">
					  <Field:codeValue tableName="APP___CLM__DBUSER.T_COUNTRY" value="${beneVO.beneNation}"/>
				   </s:if>
				</dd>
			</dl>
			
			<!-- 51699增加受益相关信息录入 -->
			<dl>
				<dt><font>* </font>受益人职业代码</dt>
				<dd>
					<s:if test="beneVO.beneJobCode!=null">
						<Field:codeValue tableName="APP___CLM__DBUSER.T_JOB_CODE"	value="${beneVO.beneJobCode}"/>
					</s:if>
				</dd>
			</dl>
			<dl>
				<dt><font>* </font>受益人电话</dt>
				<dd>
				   ${beneVO.benePhone }
				</dd>
			</dl>
			<div class="mian_site">
				<dl>
					<dt><font>* </font>受益人地址</dt>
				</dl>
				<div class="main_detail">
					<dl style="width: 25%">
						<dd>
                           	<Field:codeValue  tableName="APP___CLM__DBUSER.T_DISTRICT" value="${beneVO.beneProvince }"
                           	whereClause="DISTRICT_LEVEL = 1"/>
							&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp省/直辖市<font style="color:#FF0000">* </font>
						</dd>
					</dl>
					<dl style="width: 25%">
						<dd>
							<Field:codeValue  tableName="APP___CLM__DBUSER.T_DISTRICT" value="${beneVO.beneCity }"
	                           	whereClause="DISTRICT_LEVEL = 2"/>
							&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp市<font style="color:#FF0000">* </font>
						</dd>
					</dl>
					<dl style="width: 25%">
						<dd>
							<Field:codeValue  tableName="APP___CLM__DBUSER.T_DISTRICT" value="${beneVO.beneDistrict }"
	                           	whereClause="DISTRICT_LEVEL = 3"/>
							&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp区/县<font style="color:#FF0000">* </font>
						</dd>
					</dl>
					<dl style="width: 25%">
						<dd>	
	                           ${beneVO.beneAddress}
							&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp乡镇/街道<font style="color:#FF0000">* </font>
						</dd>
					</dl>
				</div>
			</div>
			
			<dl>
				<dt>证件有效起期</dt>
				<dd>
				   <s:date name="beneVO.beneCertiStart" format="yyyy-MM-dd"/>
				</dd>
			</dl>
			<dl>
<!-- 				<dt>证件有效止期</dt> -->
<!-- 				<dd> -->
<%-- 				   <s:date name="beneVO.beneCertiEnd" format="yyyy-MM-dd"/> --%>
<!-- 				</dd> -->
				<dt>证件有效止期</dt>
				<dd>
				   <input type="expandDateYMD" name="beneVO.beneCertiEnd" id="beneCertiEnd" gt="beneCertiStart" value="<s:date name="beneVO.beneCertiEnd" format="yyyy-MM-dd"/>" disabled="disabled"  />
				   <input type="checkbox" id="beneCertiEndCheckBoxApproveId" disabled="disabled"  />长期
				</dd>
			</dl>
			<dl>
				<dt>电子邮箱</dt>
				<dd>
					<input type="text"  name="beneEmail" id="beneEmail" value="${beneVO.beneEmail }">
				</dd>
			</dl>
			<dl>
				<dt>受益金额</dt>
				<dd>
					${claimPayVO.payAmount }
				</dd>
			</dl>
			<dl>
				<dt>受益分子</dt>
				<dd>
				   ${claimPayVO.payMole }
				</dd>
			</dl>
			<dl>
				<dt>受益分母</dt>
				<dd>
				   ${claimPayVO.payDeno }
				</dd>
			</dl>
</div></p>				
	</div>
</div>

<!-- 领款人信息 -->

<div class="main_box">
	<div class="main_heading"><h1><img src="clm/images/tubiao.png">领款人信息
		<b class="maim_lpask"></b></h1>
		</div>
		<input type="hidden" name="claimPayVO.contraryPayFlag" value="${claimPayVO.contraryPayFlag}" id="contraryPayFlag">
		<input type="hidden" name="payeeVO.legalPersonId" value="${payeeVO.legalPersonId}" id="payeeLegalPersonId">
		<div class="main_lptwo">
		<p><div class="panelPageFormContent">
		<div>
		<dl style="width: 100%;">
		<dt style="width: 12%;">
			<a disabled="true" id ="legalPersonQueryPayeeStyle" href="javaScript:void(0)" onclick="queryLegalPersonInfoPayee();" >
			<button type="button" class="but_blue " id="legalPersonQueryPayeeStyleButton" disabled="disabled" >法人信息录入</button>
			</a>
		<a target="dialog" id ="legalPersonQueryPayee" href="javaScript:void(0)" style="display:none;"  width="1000" height="450"  > 法人信息录入</a>
		</dt>
		<dt style="width: 12%;">
		对公支付，请勾选<input type="checkbox" id="contraryPayFlagCheckBoxId" onclick="chooseMethod();"/>
		</dt>
		</dl>
		    <dl>
				<dt>领款人与受益人关系</dt>
				<dd>
				     <s:if test="payeeVO.payeeRelation!=null">
					  <Field:codeValue tableName="APP___CLM__DBUSER.T_LA_PH_RELA" value="${payeeVO.payeeRelation}"  />
				     </s:if>
				</dd>
			</dl>
			<dl>
				<dt>姓名</dt>
				<dd>
				  ${payeeVO.payeeName }
				</dd>
			</dl>
			<dl>
				<dt>性别</dt>
				<dd>
				     <s:if test="payeeVO.payeeSex!=null">
					  <Field:codeValue tableName="APP___CLM__DBUSER.T_GENDER" value="${payeeVO.payeeSex}"/>
				     </s:if>
				</dd>
			</dl>
			<dl>
				<dt>出生日期</dt>
				<dd>
				   <s:date name="payeeVO.payeeBirth" format="yyyy-MM-dd"/>
				</dd>
			</dl>
			<dl>
				<dt>证件类型</dt>
				<dd>
				   <s:if test="payeeVO.payeeCertiType!=null">
					  <Field:codeValue tableName="APP___CLM__DBUSER.T_CERTI_TYPE" value="${payeeVO.payeeCertiType}"/>
				   </s:if>
				</dd>
			</dl>
			<dl>
				<dt>证件号码</dt>
				<dd>
				   ${payeeVO.payeeCertiNo }
				</dd>
			</dl>
			<dl>
				<dt>领款人国籍</dt>
				<dd>
				   <s:if test="payeeVO.payeeNation!=null">
					  <Field:codeValue tableName="APP___CLM__DBUSER.T_COUNTRY" value="${payeeVO.payeeNation}"/>
				   </s:if>
				</dd>
			</dl>
			<dl>
				<dt>证件有效起期</dt>
				<dd>
				   <s:date name="payeeVO.payeeCertiStart" format="yyyy-MM-dd"/>
				</dd>
			</dl>
			<dl>
				<dt>证件有效止期</dt>
				<dd>
				   <input type="expandDateYMD" name="payeeVO.payeeCertiEnd" id="payeeCertiEnd" gt="payeeCertiStart" value="<s:date name="payeeVO.payeeCertiEnd" format="yyyy-MM-dd"/>" disabled="disabled"  />
				   <input type="checkbox" id="payeeCertiEndCheckBoxApproveId" disabled="disabled" onclick="payeeCertiEndCheckBoxAdvance(this);"/>长期
				</dd>
			</dl>
			<!-- 71408 反洗钱增加 -->
			<dl>
				<dt><font>* </font>领款人职业代码</dt>
				<dd>
					<s:if test="payeeVO.payeeJobCode!=null">
						<Field:codeValue tableName="APP___CLM__DBUSER.T_JOB_CODE"	value="${payeeVO.payeeJobCode}"/>
					</s:if>
				</dd>
			</dl>
			<dl>
				<dt><font>* </font>领款人电话</dt>
				<dd>
				   ${payeeVO.payeePhone }
				</dd>
			</dl>
			<div class="mian_site">
				<dl>
					<dt><font>* </font>领款人地址</dt>
				</dl>
				<div class="main_detail">
					<dl style="width: 25%">
						<dd>
                           	<Field:codeValue  tableName="APP___CLM__DBUSER.T_DISTRICT" value="${payeeVO.payeeState }"
                           	whereClause="DISTRICT_LEVEL = 1"/>
							&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp省/直辖市<font style="color:#FF0000">* </font>
						</dd>
					</dl>
					<dl style="width: 25%">
						<dd>
							<Field:codeValue  tableName="APP___CLM__DBUSER.T_DISTRICT" value="${payeeVO.payeeCity }"
	                           	whereClause="DISTRICT_LEVEL = 2"/>
							&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp市<font style="color:#FF0000">* </font>
						</dd>
					</dl>
					<dl style="width: 25%">
						<dd>
							<Field:codeValue  tableName="APP___CLM__DBUSER.T_DISTRICT" value="${payeeVO.payeeDistrict }"
	                           	whereClause="DISTRICT_LEVEL = 3"/>
							&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp区/县<font style="color:#FF0000">* </font>
						</dd>
					</dl>
					<dl style="width: 25%">
						<dd>	
	                           ${payeeVO.payeeAddress}
							&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp乡镇/街道<font style="color:#FF0000">* </font>
						</dd>
					</dl>
				</div>
			</div>
			<dl>
				<dt>电子邮箱</dt>
				<dd>
					<input type="text"  name="payeeEmail" id="payeeEmail" value="${payeeVO.payeeEmail }">
				</dd>
			</dl>
			<dl>
				<dt>支付方式</dt>
				<dd>
				   <s:if test="payeeVO.payMode!=null">
					  <Field:codeValue tableName="APP___CLM__DBUSER.T_PAY_MODE" value="${payeeVO.payMode}"/>
				   </s:if>
				</dd>
			</dl>
			<dl>
				<dt>银行账户名</dt>
				<dd>
				   ${payeeVO.accountName }
				</dd>
			</dl>
			<dl>
				<dt>银行账号</dt>
				<dd><span type="expandBankAccount">
				   ${payeeVO.accountNo }</span>
				</dd>
            </dl>
		</div></p>				
	</div>
</div>
   
</div>
