<%@ page language="java" contentType="text/html; charset=utf-8"
	pageEncoding="UTF-8"%><%@ include file="/clm/pages/common/commonJsCss.jsp" %>
<%@ taglib uri="/struts-tags" prefix="s"%>
<%@ taglib uri="http://www.git.com.cn/taglib/udmp/field" prefix="Field"%>

 
<script type="text/javascript"
	src="clm/pages/inspect/qualityInspectionResult.js">	
</script>
<script type="text/javascript">
//-----------审批页面禁止操作--------
	if (isApprove == "approve") {
		//申请人信息DIV
		var obj=$("div#apprpveRemitManageDivOne", navTab.getCurrentPanel());
		var objTwo=$("div#apprpveRemitManageDivTwo", navTab.getCurrentPanel());
		//如果不是  上一步 和 下一步按钮
		objTwo.find("button").each(function(){
			var buttonName=$(this).text();
			if(buttonName!="上一步" && buttonName!="下一步"){
				$(this).attr("disabled",true);
			}
		});
		//输入框 复选框 单选按钮  控制
		obj.find("input").each(function(){
			$(this).attr("disabled",true);
		});
		//下拉框
		obj.find("select").each(function(){
			$(this).attr("disabled",true);
		});
		//a标签
		obj.find("a").each(function(){
			$(this).attr("disabled",true);
		});
		//多行文本框控制
	    obj.find("textarea").each(function(){
			$(this).attr("disabled",true);
		});
		$(".waiverManage", navTab.getCurrentPanel()).each(function(){
			$(this).attr("disabled",false);
		});
	}

	//查询豁免详细信息
	function findApproveWaiverManage(obj){
		document.getElementById('approveRemitManageId', navTab.getCurrentPanel()).style.display="";
		var caseId = $("#caseId", navTab.getCurrentPanel()).val();
		var value = $(obj).val();
		var policyId = $(obj).parent().parent().find("td").eq(2).find("input").val();
		var busiItemId = $(obj).parent().parent().find("td").eq(3).find("input").val();
		var itemId = $(obj).parent().parent().find("td").eq(4).find("input").val(); 
		if("1" == value){
			$(obj).attr("checked",false);
			$(obj).attr("value","0");
			$("#approveRemitManageId", navTab.getCurrentPanel()).find("input").val(null);
			$("#approveRemitManageId", navTab.getCurrentPanel()).find("select").val(null);
			$("#approveRemitManageId", navTab.getCurrentPanel()).find("textarea").val(null);
		} else {
			$(obj).attr("value","1");
			$.ajax({
				'url':'clm/audit/findWaiverManage_CLM_remitManageAction.action?claimCaseVO.caseId='+caseId+'&claimCaseVO.policyId='+policyId+'&claimCaseVO.busiItemId='+busiItemId+'&claimCaseVO.itemId='+itemId+'&isApprove='+isApprove,
				'type':'post',
				'datatype':'json',
				'success':function(data){
				    var data=eval("("+data+")");
				    //起交日期
					$("#startPayDate",navTab.getCurrentPanel()).val(data.validdateDateString);
					//缴费止期
					$("#paidupdate",navTab.getCurrentPanel()).val(data.paidupString);
				    //缴费频率
					$("#initialTypeId",navTab.getCurrentPanel()).val(data.chargeName);
					//每期保费
					$("#nextPrem",navTab.getCurrentPanel()).val(data.nextPrem);
					//实际保费
					$("#waiverPremAf",navTab.getCurrentPanel()).val(data.nextPrem);
					//累计保费
					$("#totalPremAf",navTab.getCurrentPanel()).val(data.totalPremAf);
					//交至日期
					$("#dueDate",navTab.getCurrentPanel()).val(data.payDueDateString);
					//豁免标志
					$("#waiverFalg",navTab.getCurrentPanel()).val(data.isWaived);
					//豁免起期
					$("#waiverStart",navTab.getCurrentPanel()).val(data.waiveStartString);
					//豁免止期
					$("#waiverEnd",navTab.getCurrentPanel()).val(data.waiveEndString);
					//豁免原因
					$("#waiverReason",navTab.getCurrentPanel()).val(data.waiveReason);
					//豁免期数
					$("#waivePeriod",navTab.getCurrentPanel()).val(data.policyPeriod);
					//总豁免保费
					$("#waiveAmt",navTab.getCurrentPanel()).val(data.waiveAmt);
					//豁免描述
					$("#waiveDesc",navTab.getCurrentPanel()).val(data.waiveDesc);
				}
			});
		}
		if (isApprove == "approve") {
			//申请人信息DIV
			var obj=$("div#apprpveRemitManageDivOne", navTab.getCurrentPanel());
			var objTwo=$("div#apprpveRemitManageDivTwo", navTab.getCurrentPanel());
			//如果不是  上一步 和 下一步按钮
			objTwo.find("button").each(function(){
				var buttonName=$(this).text();
				if(buttonName!="上一步" && buttonName!="下一步"){
					$(this).attr("disabled",true);
				}
			});
			//输入框 复选框 单选按钮  控制
			obj.find("input").each(function(){
				$(this).attr("disabled",true);
			});
			//下拉框
			obj.find("select").each(function(){
				$(this).attr("disabled",true);
			});
			//a标签
			obj.find("a").each(function(){
				$(this).attr("disabled",true);
			});
			//多行文本框控制
		    obj.find("textarea").each(function(){
				$(this).attr("disabled",true);
			});
			$(".waiverManage", navTab.getCurrentPanel()).each(function(){
				$(this).attr("disabled",false);
			});
		}
	}
	//弹出是否
/* 	function exit(){
		 alertMsg.confirm("当前页面录入的信息将不被保存，是否确定退出？",{
		 	okCall:function(){
				navTab.closeCurrentTab();
		 	}
		 });
	} */
</script>
<div class="panelPageFormContent" id="apprpveRemitManageDiv">
    <!-- 分页切换页码提交使用 -->
	<%-- <form id="pagerForm" method="post" action="clm/audit/remitInit_CLM_remitManageAction.action">
		<input type="hidden" name="pageNum" value="${currentPage.pageNo }" />
		<input type="hidden" name="numPerPage" value="${currentPage.pageSize}" />
		<input type="hidden" name="caseId" value="${caseId}" />
	</form> --%>
	<form method="post">
		
		<dl>
			<dt>赔案号</dt>
			<dd>
				<input name="" type="text" size="17" value="${claimCaseVO.caseNo }" readonly="readonly"/> 
				<input id="caseId" type="hidden" name="claimCaseVO.caseId" value="${claimCaseVO.caseId}">
			</dd>
		</dl>
		<dl>
			<dt>事件号</dt>
			<dd>
				<input name="accident.accidentNo" type="text" size="17" value="${accident.accidentNo}" readonly="readonly"/> 
			</dd>
		</dl>
		
		<div class="tabdivclassbr" >
			<table class="list" style="width: 100%;">
				<thead>
					<tr>
						<th nowrap>选择</th>
						<th nowrap>序号</th>
						<th nowrap>保单号</th>
						<th nowrap>保单险种号</th>
						<th nowrap>险种代码</th>
						<th nowrap>险种名称</th>
						<th nowrap>险种状态</th>
						<th nowrap>宽限期</th>
						<th nowrap>交至日期</th>
						<th nowrap>缴费止期</th>
					</tr>
				</thead>
				<tbody>
					<s:iterator value="currentPage.pageItems" status="st" var="cp">
						<tr> 
							<td align="center"><input type="radio" name="radio" class="waiverManage" onclick="findApproveWaiverManage(this);"  value="0"/></td>
							<td align="center">${st.index+1}</td>
							<td align="center"><input type="hidden" value="${cp.policyId}"/>${cp.policyCode}</td>
							<td align="center"><input type="hidden" value="${cp.busiItemId}"/>${cp.busiItemId}</td>
							<td align="center"><input type="hidden" value="${cp.itemId}"/>${cp.productCodeStd}</td>
							<td align="center">${cp.productNameStd}</td>
							<td align="center">
								<Field:codeValue tableName="APP___CLM__DBUSER.T_LIABILITY_STATUS" value="${cp.liabilityState}"/> 
							</td>
							<td align="center">60</td>
							<td align="center"><s:date name='#cp.payDueDate' format='yyyy-MM-dd'/></td>
							<td align="center"><s:date name='#cp.paidupDate' format='yyyy-MM-dd'/></td>
						</tr>
					</s:iterator>
				</tbody>
			</table>
			<div class="panelBar">
				<div class="pages" >
					<span>显示</span>
					<s:select list="#{10:'10',20:'20',50:'50',100:'100',200:'200'}"
						name="select" onchange="navTabPageBreak({numPerPage:this.value},'apprpveRemitManageDiv')"
						value="currentPage.pageSize">
					</s:select>
					<span>条，共${currentPage.total}条</span>
				</div>
				<div class="pagination" targetType="navTab"
					totalCount="${currentPage.total}" rel="apprpveRemitManageDiv"
					numPerPage="${currentPage.pageSize}" pageNumShown="10"
					currentPage="${currentPage.pageNo}"></div>
	
			</div>
		</div>
		
		
	
	<div id="apprpveRemitManageDivOne">
		<div class="panelPageFormContent"  style="display:none"  id="approveRemitManageId">
			<div class="divfclass">
				<h1>
					<img src="clm/images/tubiao.png">豁免详细信息
				</h1>
			</div>
			<dl>
				<dt>起交日期</dt>
				<dd>
					<input id="startPayDate" value="" readonly="readonly"/>
				</dd>
			</dl>
			<dl>
				<dt>缴费止期</dt>
				<dd>
					<input name="" id="paidupdate" 
						 value="" readonly="readonly"/>
				</dd>
			</dl>
			<dl>
				<dt>缴费频率
				<dt>
				<dd>
					<input name="" id="initialTypeId"  readonly="readonly">
				</dd>
			</dl>
			<dl>
				<dt>每期保费</dt>
				<dd>
					<input id="nextPrem" value=""  readonly="readonly">
				</dd>
			</dl>
			<dl>
				<dt>实际保费</dt>
				<dd>
					<input name="" id="waiverPremAf" readonly="readonly">
				</dd>
			</dl>
			<dl>
				<dt>累计保费</dt>
				<dd>
					<input name="" id="totalPremAf" readonly="readonly">
				</dd>
			</dl>
			<dl>
				<dt>交至日期</dt>
				<dd>
					<input name="claimCaseVO.dueDate" value="" id="dueDate" readonly="readonly">
				</dd>
			</dl>
			<dl>
				<dt><font>*</font>免交标志</dt>
				<dd>
				    <Field:codeTable cssClass="combox title"  name="claimCaseVO.isWaived" tableName="APP___CLM__DBUSER.T_YES_NO" value="" nullOption="true" id="waiverFalg"/>
				</dd>
			</dl>
			<dl>
				<dt><font>*</font>豁免起期</dt>
				<dd>
					<input type="text" name="claimCaseVO.waiveStart"
						id="waiverStart" size="17" onPropertychange="updateWaiveStart(this)"/>
				</dd>
			</dl>
			<dl>
				<dt><font>*</font>豁免止期</dt>
				<dd>
					<input type="text" name="claimCaseVO.waiveEnd"
						id="waiverEnd" size="17" onPropertychange="updateWaiveEnd(this)"/>
				</dd>
			</dl>
			<dl>
				<dt><font>*</font>豁免原因</dt>
				<dd>
					<Field:codeTable cssClass="combox title"  name="claimCaseVO.waiveReason" tableName="APP___CLM__DBUSER.T_EXEMPT_REASON" value="" nullOption="true" id="waiverReason"/> 
				</dd>
			</dl>
			<dl>
				<dt>豁免期数</dt>
				<dd>
					<input id="waivePeriod"  value="" readonly="readonly" />
				</dd>
			</dl>
			<dl>
				<dt>总豁免保费</dt>
				<dd>
					<input type="expandNumber" id="waiveAmt" name="claimCaseVO.waiveAmt" value="" readonly="readonly"/>
				</dd>
			</dl>
			
			<dl style="width:1000px;height:auto;">
				<dt>豁免描述</dt>
				<dd><textarea id="waiveDesc" rows="2" cols="100"></textarea></dd>
			</dl>
		</div>
	</div>
	
	<div class="formBarButton" id="apprpveRemitManageDivTwo">
		<ul>
			<li>
				<button type="button" class="but_blue" onclick="prev_approve('4',${caseId})">上一步</button>
			</li>
			<li>
				<button class="but_blue" type="button">保存</button>
			</li>
			<li>
				<button type="button" class="but_blue" onclick="next_approve('6',${caseId})">下一步</button>
			</li>
			<li>
				<button type="button" class="but_blue" onclick="exit()" >退出</button>
			</li>
		</ul>
	</div>
	</form>
</div>