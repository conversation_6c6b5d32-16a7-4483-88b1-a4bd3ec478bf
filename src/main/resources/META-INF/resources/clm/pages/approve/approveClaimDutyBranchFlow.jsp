<%@ page language="java" contentType="text/html; charset=utf-8" pageEncoding="UTF-8"%><%@ include file="/clm/pages/common/commonJsCss.jsp" %>
<%@ taglib uri="/struts-tags" prefix="s"%>
<%@ taglib uri="http://www.git.com.cn/taglib/udmp/field" prefix="Field"%>
<!--add xuyz_wb 分支流程 查询医疗单证页面--> 
<script type="text/javascript">

var obj=$("div#approveInfoJspBranchFlow", navTab.getCurrentPanel());
//按钮
obj.find("button").each(function(){
	$(this).attr("disabled",true);
});
//输入框 复选框 单选按钮  控制
obj.find("input").each(function(){
	$(this).attr("disabled",true);
});
//下拉框
obj.find("select").each(function(){
	$(this).attr("disabled",true);
});
//a标签
obj.find("a").each(function(){
	$(this).attr("disabled",true);
});
//多行文本框控制
obj.find("textarea").each(function(){
	$(this).attr("disabled",true);
});

//隐藏dwz封装的添加中的input
function hidentRowNum(){
	$("[name='dwz_rowNum']").css("display","none");
}
setTimeout('hidentRowNum()',100);
</script>
		
<div class="pageContent" id="approveInfoJspBranchFlow">
	<div class="pageFormContent">
			<div  style="width:100%;height:100%;" class="pageContent">
			<div id="claimSubCase" align="left" style="float:left;width:100px;height:2300px;border-right:10px solid #CCC;" >
				 <input type="checkbox" name="checkbbox1" id="" value="1" checked hiddenDivId="honsp" ChangeTypecode='claimTypecode' onclick="smallChange(this)"> 医疗单证
			</div>
			<div id="2" align="left"  >
			<div class="pageFormContent" >
			<!-- 医疗单证录入   div  开始 -->
		<div id="honsp" style="display:block">
			<div >
				<input type="button" onclick="show(this)" ChangeTypecode='honspTypecode' hiddenDivId="honspInt" name="honspInt" value="-">医疗单证录入
			</div>
			<div id="honspInt" >
			<input name="honspTypecode" value="" type="hidden" readonly="readonly" id="honspTypecode" />
			 <input type="checkbox" name="" value="1" checked="checked" hiddenDivId="hospital" ChangeTypecode='honspTypecode' onclick="smallChange(this)">门诊/住院 
		 	 <input type="checkbox" name="" value="2" checked="checked" hiddenDivId="medical1News" ChangeTypecode='honspTypecode' onclick="smallChange(this)">全球高端医疗费用 
		 	 <input type="checkbox" name="" value="3" checked="checked" hiddenDivId="medical2News" ChangeTypecode='honspTypecode' onclick="smallChange(this)">防癌医疗费用
		 	 
		 	 <!-- 门诊/住院信息   div  start -->	
		 	 <div id="hospital"  class="panel">
		 	 
		 	 <h1>门诊/住院</h1>
		 	 <div class="pageFormContent" >
		 	 <input type="hidden" name="" id="hospitallistCkedIndex"/>
		 	 <input type='hidden' name="" id='claimDate' value='<s:date name='claimSubCaseVO.claimDate' format='yyyy-MM-dd' />' />
		 	 <input type='hidden' name="" id='accDate' value='<s:date name='accident.accDate' format='yyyy-MM-dd' />' />
		 	 	 <table  class="list" id ="addhas" style="border:2px solid #CCC;">
						<thead>
							<tr>
							<th type="enum" name=""
									enumUrl="clm/pages/html/hospitalbtnRadio.jsp">选择</th>
									
								<th type="text" name="items[#index#].itemInt" readonly defaultVal="#index#" size="12" fieldClass="digits" >序号</th>
								
								<th type="enum" name=""
									enumUrl="clm/pages/html/dutyAccountNumber.jsp" size="12">账单号<font class="point" color="red">*</font></th>	
								
								<th type="enum" name=""
									enumUrl="clm/pages/register/dutyIResultSelect.jsp" size="12" >医院名称<font class="point" color="red">*</font></th>
									
								<th type="text" name=""
									  style="width:1%">医院等级</th>
									
								<th type="enum" name=""
									enumUrl="clm/pages/html/hospitalBillTreatType.jsp" size="12">治疗类型</th>
								
								<th type="enum" name=""
									enumUrl="clm/pages/register/dutyIResultStartDate.jsp" size="6">开始时间<font class="point" color="red">*</font></th>
									
								<th type="enum" name=""
									enumUrl="clm/pages/register/dutyIResultEndDate.jsp" size="6">结束时间<font class="point" color="red">*</font></th>	
								
								<th type="enum" name=""
									 enumUrl="clm/pages/html/hospitalbtnStayDays.jsp" size="12">住院天数</th>
									
								<th type="enum" name=""
									enumUrl="clm/pages/html/hospitalbtnAccDays.jsp" size="12">距离意外事故发生日天数</th>
								
								<th type="enum" name=""
									enumUrl="clm/pages/html/hospitalbtnDays.jsp" size="12">距离出险日期天数</th>
								
								<th type="enum" name=""
									enumUrl="clm/pages/html/dutyAccResultSelect.jsp" size="12">出险结果</th>
								
								<th type="enum" name=""
									enumUrl="clm/pages/html/dutyDiseaseResultSelect.jsp" size="12">疾病编码</th>
								
								<th type="text" name="claimBillVOlist[#index#].sumAmount"
									 size="10" style="width:10%">总费用</th>
								
								<th type="text" name="claimBillVOlist[#index#].deductAmount"
									 size="10" style="width:10%">扣除费用</th>
								
								<th type="text" name="claimBillVOlist[#index#].calcAmount"
									 size="10" style="width:10%">理算金额</th>
								
								<!-- <th type="enum" name=""
									enumUrl="clm/pages/html/hospitalbtnEditDel.jsp" size="10" style="width:10%">操作</th> -->
								
							</tr>
						</thead>
						<tbody class="list" id="hospitalTBody">
								 <input type="hidden" value="1">
								 <s:iterator value="claimBillVOlist" status="st">
									<tr>
										<td>
											<input type='radio' value='0' onclick="radiockecd(this)" class="MedRadio"/> 
										</td>
										<td>
											<input class="digits textInput" type="text" size="5" value="${st.index+1}" />
										</td>
										<td>
											<input class="textInput" type="text"  value="${billNo}"/>
										</td>
										<td>
											<Field:codeTable cssClass="combox title"  name="claimBillVOlist[${st.index}].hospitalCode" tableName="APP___CLM__DBUSER.T_HOSPITAL" nullOption="true" value="${hospitalCode}" onChange="queryHospitalMsg(this);"/> 
										</td> 
										<td>
											<input class="textInput" type="text" value="${hospitalLevel}"/>
										</td>
										<td>
											<Field:codeTable cssClass="combox title"  name="claimBillVOlist[${st.index}].treatType" tableName="APP___CLM__DBUSER.T_CURE_TYPE" orderBy="substr(code,1,2)" nullOption="true" onChange="cureStatus();" value="${treatType}"/> 
										</td> 
										<td>
											<input type="expandDateYMD" name="claimBillVOlist[${st.index}].treatStart" class="date" value="<s:date name='treatStart' format='yyyy-MM-dd' />" onPropertychange="queryCalimDate(this);"/>
											<a class="inputDateButton" href="javascript:;" >选择</a>
										</td>
										<td>
											<input type="expandDateYMD" name="claimBillVOlist[${st.index}].treatEnd" class="date" value="<s:date name='treatEnd' format='yyyy-MM-dd' />" onPropertychange="countStayDays(this);"/>
											<a class="inputDateButton" href="javascript:;" >选择</a>
										</td>
										<td>
											 <input class="textInput" type="text" value="${stayDays}"/> 
										</td>
										<td>
											 <input class="textInput" type="text" value="${accidentDays}"/> 
										</td>
										<td>
											 <input class="textInput" type="text" value="${claimDays}"/> 
										</td>
										<td>
										    <Field:codeTable   name="claimBillVOlist[${st.index}].accDetail" tableName="APP___CLM__DBUSER.T_ACCIDENT1" nullOption="true" cssClass="notuseflagDelete combox title comboxDD accResult1"  showInput="true" showInputId="accResultOne"  value="${accDetail}"/>
											<input type="text" class="accResult1Name" value="${accResult1Name}"/>
										</td>
										<td>
					                        <select class="combox title accResult2"  name="claimBillVOlist[${st.index}].icdCode" class="" size="0" _cstyle_="1">
												<option value="<s:property value="icdCode"/>"><s:property value="icdCode"/></option>
											</select>
											<input type="text" value="${accResult2Name}"/> 
										</td>
										<td>
											<input class="textInput" type="text" name="claimBillVOlist[${st.index}].sumAmount" value="${sumAmount}"/>
										</td>
										<td>
											<input class="textInput" type="text" name="claimBillVOlist[${st.index}].deductAmount" value="${deductAmount}"/>
										</td>
										<td>
											<input class="textInput" type="text" name="claimBillVOlist[${st.index}].calcAmount" value="${calcAmount}"/>
										</td>
									</tr>
							</s:iterator>
								 
						</tbody>
					</table> 
					</div>
			<!-- 门诊/住院信息   div  end -->
		 	 </div>
		 	 
		 	 <!--医疗费用明细页面  div start -->
		 	 <%--  <div id ="hospitalcost" style="display:none">
		 	 	<%@ include file="dutyHospitalCostInit.jsp" %>
		 	 </div> --%>
		 	 <!--医疗费用明细页面  div end -->	
		 	 	
		 	 	<!-- 全球高端医疗费用信息   div  start -->	
		 	 <div id="medical1News"  class="panel">
		 	 	<%-- <%@ include file="dutymedical1NewsInit.jsp" %> --%>
		 	 	 <h1>全球高端医疗费用</h1>
				 <div class="pageFormContent" >
				 	<table  class="list" id ="addhas" style="border:2px solid #CCC;">
						<thead>
							<tr>
								<th type="enum" name=""
									enumUrl="clm/pages/html/hospitalbtnRadio.jsp">选择</th>
									
								<th type="text" name="items[#index#].itemInt" readonly defaultVal="#index#" size="12" fieldClass="digits" >序号</th>
								
								<th type="enum" name=""
									enumUrl="clm/pages/html/highDutyAccountNumber.jsp" size="12">账单号<font class="point" color="red">*</font></th>	
								
								<th type="enum" name=""
									enumUrl="clm/pages/html/highDutyIResultSelect.jsp" size="12" >医院名称<font class="point" color="red">*</font></th>
									
								<th type="text" name=""
									  style="width:1%">医院等级</th>
									  
								<th type="text" name=""
									  style="width:1%">昂贵标志</th>	
									  
								<th type="enum" name=""
									enumUrl="clm/pages/html/highHospitalBillTreatType.jsp" size="12">治疗类型</th>
								
								<th type="enum" name=""
									enumUrl="clm/pages/html/highDutyIResultStartDate.jsp" size="6">开始时间<font class="point" color="red">*</font></th>
									
								<th type="enum" name=""
									enumUrl="clm/pages/html/highDutyIResultEndDate.jsp" size="6">结束时间<font class="point" color="red">*</font></th>	
								
								<th type="enum" name=""
									 enumUrl="clm/pages/html/hospitalbtnStayDays.jsp" size="12">住院天数</th>
								
								<th type="enum" name=""
									enumUrl="clm/pages/html/highDutyOtherPay.jsp" size="6" style="width:10%">社保/公费/第三方支付</th>
								
								<th type="text" name="claimHighBillVOlist[#index#].sumAmount"
									 size="10" style="width:10%">总费用</th>
								
								<th type="text" name="claimHighBillVOlist[#index#].deductAmount"
									 size="10" style="width:10%">扣除费用</th>
								
								<th type="text" name="claimHighBillVOlist[#index#].calcAmount"
									 size="10" style="width:10%">理算金额</th>
								
								<!-- <th type="enum" name=""
									enumUrl="clm/pages/html/hospitalbtnEditDel.jsp" size="10" style="width:10%">操作</th> -->
							</tr>
						</thead>
						<tbody class="list" id="highEndTBody">
							 <input type="hidden" value="2">
							 <s:iterator value="claimHighBillVOlist" status="st">
									<tr>
										<td>
											<input type='radio' value='0' onclick="radiockecd(this)" class="MedRadio"/> 
										</td>
										<td>
											<input class="digits textInput" type="text" size="5" value="${st.index+1}" />
										</td>
										<td>
											<input class="textInput" type="text"  value="${billNo}"/>
										</td>
										<td>
											<Field:codeTable cssClass="combox title"  name="claimHighBillVOlist[${st.index}].hospitalCode" tableName="APP___CLM__DBUSER.T_HOSPITAL" nullOption="true" value="${hospitalCode}" onChange="queryHospitalMsg(this);"/> 
										</td> 
										<td>
											<input class="textInput" type="text" value="${hospitalLevel}"/>
										</td>
										<td>
											<s:if test="isCostly eq 1">
												<input class="textInput" type="text" value="是"/>
											</s:if>
											<s:else>
												<input class="textInput" type="text" value="否"/>
											</s:else>
										</td>
										<td>
											<Field:codeTable cssClass="combox title"  name="claimHighBillVOlist[${st.index}].treatType" tableName="APP___CLM__DBUSER.T_CURE_TYPE" orderBy="substr(code,1,2)" nullOption="true" onChange="cureStatus();" value="${treatType}"/> 
										</td> 
										<td>
											<input type="expandDateYMD" name="claimHighBillVOlist[${st.index}].treatStart" class="date" value="<s:date name='treatStart' format='yyyy-MM-dd' />" onPropertychange="queryCalimDate(this);"/>
											<a class="inputDateButton" href="javascript:;" >选择</a>
										</td>
										<td>
											<input type="expandDateYMD" name="claimHighBillVOlist[${st.index}].treatEnd" class="date" value="<s:date name='treatEnd' format='yyyy-MM-dd' />" onPropertychange="countStayDays(this);"/>
											<a class="inputDateButton" href="javascript:;" >选择</a>
										</td>
										<td>
											 <input class="textInput" type="text" value="${stayDays}"/> 
										</td>
										<td>
											 <Field:codeTable cssClass="combox title"  name="claimHighBillVOlist[#index#].otherPay" tableName="APP___CLM__DBUSER.T_YES_NO" nullOption="true" value="${otherPay}"/> 
										</td>
										<td>
											<input class="textInput" type="text" name="claimHighBillVOlist[${st.index}].sumAmount" value="${sumAmount}"/>
										</td>
										<td>
											<input class="textInput" type="text" name="claimHighBillVOlist[${st.index}].deductAmount" value="${deductAmount}"/>
										</td>
										<td>
											<input class="textInput" type="text" name="claimHighBillVOlist[${st.index}].calcAmount" value="${calcAmount}"/>
										</td>
									</tr>
							</s:iterator>
						</tbody>
					</table>
					</div>
<!-- 全球高端医疗费用信息   div  end -->
		 	 </div>
			<!-- 全球高端医疗费用信息   div  end -->
		 	 	
		 	 <!-- 防癌医疗费用信息   div  start -->	
		 	 <div id="medical2News"  class="panel">
		 	 
		 	 <h1>防癌医疗费用</h1>
		 	 <div class="pageFormContent" >
		 	 	<table  class="list" id ="addhas" style="border:2px solid #CCC;">
						<thead>
							<tr>
							<th type="enum" name=""
									enumUrl="clm/pages/html/hospitalbtnRadio.jsp">选择</th>
									
								<th type="text" name="items[#index#].itemInt" readonly defaultVal="#index#" size="12" fieldClass="digits" >序号</th>
								
								<th type="enum" name=""
									enumUrl="clm/pages/html/cancerDutyAccountNumber.jsp" size="12">账单号<font class="point" color="red">*</font></th>	
								
								<th type="enum" name=""
									enumUrl="clm/pages/html/cancerDutyIResultSelect.jsp" size="12" >医院名称<font class="point" color="red">*</font></th>
									
								<th type="text" name=""
									  style="width:1%">医院等级</th>
									  
								<th type="text" name=""
									  style="width:1%">防癌定点标志</th>	
									  
								<th type="enum" name=""
									enumUrl="clm/pages/html/cancerHospitalBillTreatType.jsp" size="12">治疗类型</th>
								
								<th type="enum" name=""
									enumUrl="clm/pages/html/cancerDutyIResultStartDate.jsp" size="6">开始时间<font class="point" color="red">*</font></th>
									
								<th type="enum" name=""
									enumUrl="clm/pages/html/cancerDutyIResultEndDate.jsp" size="6">结束时间<font class="point" color="red">*</font></th>	
								
								<th type="enum" name=""
									 enumUrl="clm/pages/html/hospitalbtnStayDays.jsp" size="12">住院天数</th>
								
								<th type="enum" name=""
									enumUrl="clm/pages/html/claimCancerDutyOtherPay.jsp" size="6" style="width:10%">社保/公费/第三方支付</th>
								
								<th type="text" name="claimCancerBillVOlist[#index#].sumAmount"
									 size="10" style="width:10%">总费用</th>
								
								<th type="text" name="claimCancerBillVOlist[#index#].deductAmount"
									 size="10" style="width:10%">扣除费用</th>
								
								<th type="text" name="claimCancerBillVOlist[#index#].calcAmount"
									 size="10" style="width:10%">理算金额</th>
								
								<!-- <th type="enum" name=""
									enumUrl="clm/pages/html/hospitalbtnEditDel.jsp" size="10" style="width:10%">操作</th> -->
							</tr>
						</thead>
						<tbody class="list" id="cancerPreTBody">
							<input type="hidden" value="3">
							<s:iterator value="claimCancerBillVOlist" status="st">
									<tr>
										<td>
											<input type='radio' value='0' onclick="radiockecd(this)" class="MedRadio"/> 
										</td>
										<td>
											<input class="digits textInput" type="text" size="5" value="${st.index+1}" />
										</td>
										<td>
											<input class="textInput" type="text" value="${billNo}"/>
										</td>
										<td>
											<Field:codeTable cssClass="combox title"  name="claimCancerBillVOlist[${st.index}].hospitalCode" tableName="APP___CLM__DBUSER.T_HOSPITAL" nullOption="true" value="${hospitalCode}" onChange="queryHospitalMsg(this);"/> 
										</td> 
										<td>
											<input class="textInput" type="text" value="${hospitalLevel}"/>
										</td>
										<td>
											<s:if test="isCancer eq 1">
												<input class="textInput" type="text" value="是"/>
											</s:if>
											<s:else>
												<input class="textInput" type="text" value="否"/>
											</s:else>
										</td>
										<td>
											<Field:codeTable cssClass="combox title"  name="claimCancerBillVOlist[${st.index}].treatType" tableName="APP___CLM__DBUSER.T_CURE_TYPE" orderBy="substr(code,1,2)" nullOption="true" onChange="cureStatus();" value="${treatType}"/> 
										</td> 
										<td>
											<input type="expandDateYMD" name="claimCancerBillVOlist[${st.index}].treatStart" class="date" value="<s:date name='treatStart' format='yyyy-MM-dd' />" onPropertychange="queryCalimDate(this);"/>
											<a class="inputDateButton" href="javascript:;" >选择</a>
										</td>
										<td>
											<input type="expandDateYMD" name="claimCancerBillVOlist[${st.index}].treatEnd" class="date" value="<s:date name='treatEnd' format='yyyy-MM-dd' />" onPropertychange="countStayDays(this);"/>
											<a class="inputDateButton" href="javascript:;" >选择</a>
										</td>
										<td>
											 <input class="textInput" type="text" value="${stayDays}"/> 
										</td>
										<td>
											 <Field:codeTable cssClass="combox title"  name="claimCancerBillVOlist[#index#].otherPay" tableName="APP___CLM__DBUSER.T_YES_NO" nullOption="true" value="${otherPay}"/> 
										</td>
										<td>
											<input class="textInput" type="text" name="claimCancerBillVOlist[${st.index}].sumAmount" value="${sumAmount}"/>
										</td>
										<td>
											<input class="textInput" type="text" name="claimCancerBillVOlist[${st.index}].deductAmount" value="${deductAmount}"/>
										</td>
										<td>
											<input class="textInput" type="text" name="claimCancerBillVOlist[${st.index}].calcAmount" value="${calcAmount}"/>
										</td>
									</tr>
							</s:iterator>
						</tbody>
					</table> 
					</div>
			<!-- 防癌医疗费用信息   div  end -->
		 	 </div>
	 	 	</div>
	 	 	<!--医疗单证录入  div 结束  -->
	 	 	</div>
			
			
				</div>
				</div>
				</div>
	</div>
</div>




