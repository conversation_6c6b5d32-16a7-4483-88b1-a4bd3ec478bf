<%@ page language="java" contentType="text/html; charset=utf-8"
	pageEncoding="UTF-8"%><%@ include file="/clm/pages/common/commonJsCss.jsp" %>
<%@ taglib uri="http://www.git.com.cn/taglib/udmp/field" prefix="Field"%>
<%@ taglib uri="/struts-tags" prefix="s"%>
<script type="text/javascript">
function generateBfCheckTask(){
	$("#bfTaskResult", navTab.getCurrentPanel()).attr("action","clm/handExtractSurvey/generateBfCheckTask_CLM_handExtractSurveyAction.action");
	var insuredIds = "";
	$("input#checkBfTask","table#extractBfResultId").each(function(){
		if ($(this).is(':checked')) {
			insuredIds = insuredIds + $(this).val() + ",";
		}
	});
	if(insuredIds == ""){
		alertMsg.info("请至少选中列表中的一条记录！");
		return;
	}
    $("#bfTaskResult", navTab.getCurrentPanel()).submit();
}
function deleteBfCheckTask() {
	$("#bfTaskResult", navTab.getCurrentPanel()).attr("action","clm/handExtractSurvey/deleteBfCheckTask_CLM_handExtractSurveyAction.action");
	$('input[flag="flag"]', navTab.getCurrentPanel()).each(function(){
		if($(this).prop('checked') == true){
			$(this).val(1);
		}
    });   
	var insuredIds = "";
	$("input#checkBfTask","table#extractBfResultId").each(function(){
		if ($(this).is(':checked')) {
			insuredIds = insuredIds + $(this).val() + ",";
		}
	});
	if(insuredIds == ""){
		alertMsg.info("请至少选中列表中的一条记录！");
		return;
	}
	$("#bfTaskResult", navTab.getCurrentPanel()).submit();
}
//弹出是否
/* function exit(){
	alertMsg.confirm("是否确定退出？",{
		okCall:function(){
			navTab.closeCurrentTab();
		}
	});
} */

$("#allBfMessage", navTab.getCurrentPanel()).change(function(){
	  var check=$(this).attr("checked");
	  if(check=="checked"){
		    $("input#checkBfTask","table#extractBfResultId").each(function(){
				if (!$(this).is(':checked')) {
					$(this).attr("checked",true);
				}
			});
	  }else{
		  $("input#checkBfTask","table#extractBfResultId").each(function(){
				if ($(this).is(':checked')) {
					$(this).attr("checked",false);
				}
			});
	  }
	});
$("input#checkBfTask", navTab.getCurrentPanel()).change(function(){
		var num=0,numone=0;
		$("input#checkBfTask","table#extractBfResultId").each(function(){
			num++;
			if ($(this).is(':checked')) {
				numone++;
			}
		});
		if(numone==num){
			$("#allBfMessage", navTab.getCurrentPanel()).attr("checked",true);
		}else{
			$("#allBfMessage", navTab.getCurrentPanel()).attr("checked",false);
		}
}); 
</script>
<!-- 分页切换页码提交使用 -->
<form id="pagerForm" method="post" action="clm/handExtractSurvey/surveyExtractResultInit_CLM_handExtractSurveyAction.action">
	<input type="hidden" name="pageNum" value="${currentPage.pageNo }" />
	<input type="hidden" name="numPerPage" value="${currentPage.pageSize}" />
	<input type="hidden" name="claimBfSurveyPlanVO.planName" value="${claimBfSurveyPlanVO.planName}"/>
</form>
<div class="pageContent" layoutH="36">
   <div class="panel"  style="margin:10px;" defH="auto">
	 <h1>保后调查抽取结果</h1>
	 <div class="tabsContent" >
	 <form id="bfTaskResult" method="post" action="" novalidate="novalidate" class="pageForm required-validate" onsubmit="return validateCallback(this, dialogAjaxDone);" rel="pagerForm">
	        <input type="hidden" name="claimSurveyBatchVO.batchId" value="${claimSurveyBatchVO.batchId}"/>
			<div class="pageFormContent">
				<div class="panel" >
						<div>
							<table class="list" width="100%" id="extractBfResultId">
								<thead>
									<tr>
										<th>选择 <input type="checkbox" size="5px" id="allBfMessage" value="0"/></th>
										<th>序号</th>
										<th>客户号</th>
				                        <th>客户姓名</th>
				                        <th>证件号码</th>
				                       	<th>投保次数</th>
				                        <th>累计风险保额</th>
				                       	<th>管理机构</th>
				                       	<th>距离上次保后调查的时间</th>
								    </tr>
								</thead>
								<tbody>
								     <s:iterator value="currentPage.pageItems" status="st">
										<tr>
											<td align="center"><input type="checkbox" id="checkBfTask" flag="flag" name="claimSurveyTaskVOs[${st.index}].flag"/></td>
											<td align="center">${st.index+1}</td>
											<td align="center">
												<input type="hidden" name="claimSurveyTaskVOs[${st.index}].bizNo" value="${policyCode}"/>
												<input type="hidden" name="claimSurveyTaskVOs[${st.index}].batchId" value="${claimSurveyBatchVO.batchId}"/>
												${insuredId}
											</td>
											<td align="center">
												<input type="hidden" name="claimSurveyTaskVOs[${st.index}].customerName" value="${insuredName}"/>
												${insuredName}
											</td>
											<td align="center">${insuredCode}</td>
											<td align="center">${payeeNum}</td>
											<td align="center">${totalPremAf}</td>
											<td align="center"><Field:codeValue tableName="APP___CLM__DBUSER.T_UDMP_ORG" value="${organCode}"/></td>
											<td align="center">${accDays}</td>
										</tr>
								   </s:iterator> 
								</tbody>
							</table>
							<div class="panelBar">
								<div class="pages" Width="60%">
									<span>显示</span>
									<s:select list="#{10:'10',20:'20',50:'50',100:'100',200:'200'}"
										name="select" onchange="navTabPageBreak({numPerPage:this.value})"
										value="currentPage.pageSize">
									</s:select>
									<span>条，共${currentPage.total}条</span>
								</div>
								<div class="pagination" targetType="navTab"
									totalCount="${currentPage.total}"
									numPerPage="${currentPage.pageSize}" pageNumShown="10"
									currentPage="${currentPage.pageNo}"></div>
					
								</div>
						  </div>
				     </div>
			    </div>
				<div class="formBar" >
					<ul style="margin-right:550px;"> 
						<li>
							<div class="button">
								<div class="buttonContent" >
									<button type="button" onclick="extractBfCheckTaskBack('1');">返回</button> 
								</div>
							</div>
						</li>
					    <li>
							<div class="button"><div class="buttonContent" ><button type="button" onclick="generateBfCheckTask();">发起保后调查</button></div></div>
						</li>
					    <li>
							<div class="button"><div class="buttonContent" ><button type="button" onclick="deleteBfCheckTask();">删除</button></div></div>
						</li>
					    <li>
							<div class="button"><div class="buttonContent" ><button type="button" class="close" onclick="exit();">退出</button></div></div>
						</li>
					</ul>
				</div>
			</form>
      	</div>  
   	</div>
</div>



