<%@ page language="java" contentType="text/html; charset=utf-8"
	pageEncoding="UTF-8"%><%@ include
	file="/clm/pages/common/commonJsCss.jsp"%>
<%@ taglib uri="/struts-tags" prefix="s"%>
<%@ taglib uri="http://www.git.com.cn/taglib/udmp/field" prefix="Field"%>


<script type="text/javascript">
	//点击修改将指定行的文本设置为可修改
	function editFixed(index) {
		$("[name='surveyChannelEvaluateUpdate[" + index + "].bizNo']").attr(
				"disabled", false);
		$("[name='surveyChannelEvaluateUpdate[" + index + "].surveyDate']")
				.attr("disabled", false);
		$("[name='surveyChannelEvaluateUpdate[" + index + "].cooperAchieve']")
				.attr("disabled", false);
		$("[name='surveyChannelEvaluateUpdate[" + index + "].evaluateLevel']")
				.attr("disabled", false);
		$("[name='surveyChannelEvaluateUpdate[" + index + "].reduceMoney']")
				.attr("disabled", false);
		$("[name='surveyChannelEvaluateUpdate[" + index + "].remark']").attr(
				"disabled", false);
		$("[name='surveyChannelEvaluateUpdate[" + index + "].listId']").attr(
				"disabled", false);
		$("[name='surveyChannelEvaluateUpdate[" + index + "].channelId']")
				.attr("disabled", false);
		$("[name='surveyChannelEvaluateUpdate[" + index + "].channelType']")
				.attr("disabled", false);
		$("[name='surveyChannelEvaluateUpdate[" + index + "].channelName']")
				.attr("disabled", false);
		$("[name='surveyChannelEvaluateUpdate[" + index + "].bizType']").attr(
				"disabled", false);
		$("[name='surveyChannelEvaluateUpdate[" + index + "].evaluatePer']")
				.attr("disabled", false);
		$("[name='surveyChannelEvaluateUpdate[" + index + "].evaluateDate']")
				.attr("disabled", false);
	}
	//提交from表单
	function submitAddFixed() {
		$("#surveyFixedChannel", navTab.getCurrentPanel()).find("input").each(
				function(index, obj) {
					if ($(obj).attr("checked") == "checked") {
						//获取name为channelIndex将索引值赋值到value上
						$("[name='fixedSelectIndex']", navTab.getCurrentPanel()).val(index);
					}
				});
		$("#updateOrAddFixed", navTab.getCurrentPanel()).submit();
	}

	//退出
/* 	function fixedExit() {
		 alertMsg.confirm("当前页面录入的信息将不被保存，是否确定退出？", {
		 	okCall : function() {
				navTab.closeCurrentTab();
		 	}
		 });
	} */

	function abc() {
		alert("13212312312312");
	}
	//获取查询条件中的渠道名称
	$(function() {
		var channelName = $("[name='surveyChannelVO.channelName']", navTab.getCurrentPanel()).val();
		$("[name='channelName']", navTab.getCurrentPanel()).val(channelName);
		var channelType = $("[name='surveyChannelVO.channelType']", navTab.getCurrentPanel()).val();
		$("[name='channelType']", navTab.getCurrentPanel()).val(channelType);
		var filialeOrg = $("[name='surveyChannelVO.filialeOrg']", navTab.getCurrentPanel()).val();
		$("[name='filialeOrg']", navTab.getCurrentPanel()).val(filialeOrg);
	});
</script>
<!-- 分页查询访问路径 -->
<div class="pageContent">
	<!-- 分页查询访问路径 -->
	<form id="pagerForm" method="post"
		action="clm/channel/detailedFixedChannel_CLM_fixedChannelAction.action">
		<input type="hidden" name="pageNum"
			value="${evaluateCurrentPage.pageNo}" /> <input type="hidden"
			name="numPerPage" value="${evaluateCurrentPage.pageSize}" /> <input
			type="hidden" name="channelId"
			value="${evaluateCurrentPage.paramObject.channelId}" />
	</form>
	<div class="divfclass">
		<h1>
			<img src="clm/images/tubiao.png">渠道详细信息
		</h1>
	</div>
	<div class="panelPageFormContent">
		<dl>
			<dt>渠道名称</dt>
			<dd>
				<input type="text"  readonly="readonly"
					value="${surveyChannelVO.channelName }" />
			</dd>
		</dl>
		<dl>
			<dt>渠道类型</dt>
			<dd>
				<input type="text" readonly="readonly"
					value="${surveyChannelVO.channelType }" />
			</dd>
		</dl>
		<dl>
			<dt>渠道地址</dt>
			<dd>
				<input type="text" readonly="readonly"
					value="${surveyChannelVO.channelAddress }" />
			</dd>
		</dl>
		<dl>
			<dt>二级机构名称</dt>
			<dd>
				<input type="text" readonly="readonly"
					value="${surveyChannelVO.filialeOrg }" />
			</dd>
		</dl>
		<dl>
			<dt>三级机构名称</dt>
			<dd>
				<input type="text" readonly="readonly"
					value="${surveyChannelVO.mediueOrg }" />
			</dd>
		</dl>
		<dl>
			<dt>有效标志</dt>
			<dd>
				<span><input type="checkbox" disabled="disabled"
					style="border: 0px; background: 0px; width: auto;"
					<s:if test="surveyChannelVO.validFlag eq 1">checked="checked"</s:if> />有效</span>
			</dd>
		</dl>
		<dl>
			<dt>渠道等级</dt>
			<dd>
				<input type="text" readonly="readonly"
					value="${surveyChannelVO.channelLevel }" />
			</dd>
		</dl>
		<dl>
			<dt>联络部门/科室</dt>
			<dd>
				<input type="text" readonly="readonly"
					value="${surveyChannelVO.contactDept }" />
			</dd>
		</dl>
		<dl>
			<dt>联系人</dt>
			<dd>
				<input type="text" readonly="readonly"
					value="${surveyChannelVO.contactPer }" />
			</dd>
		</dl>
		<dl>
			<dt>联系人职务</dt>
			<dd>
				<input type="text" readonly="readonly"
					value="${surveyChannelVO.contactPerJob }" />
			</dd>
		</dl>
		<dl>
			<dt>联系方法（电话）</dt>
			<dd>
				<input type="text" readonly="readonly"
					value="${surveyChannelVO.contactPhone }" />
			</dd>
		</dl>
		<dl>
			<dt>联系方式（手机）</dt>
			<dd>
				<input type="expandMobile" readonly="readonly"
					value="${surveyChannelVO.contactMobile }" />
			</dd>
		</dl>
		<dl>
			<dt>是否签订合作协议</dt>
			<dd>
				<span><input type="checkbox" readonly="readonly"
					style="border: 0px; background: 0px; width: auto;"
					<s:if test="surveyChannelVO.isCooperation eq 1">checked="checked"</s:if> />是</span>
			</dd>
		</dl>
		<dl>
			<dt>与该渠道建立合作管理的同业名称</dt>
			<dd>
				<input type="text" readonly="readonly"
					value="${surveyChannelVO.sameBusiNamae }" />
			</dd>
		</dl>
		<dl>
			<dt>渠道维护人</dt>
			<dd>
				<input type="text" readonly="readonly"
					value="${surveyChannelVO.safeguardBy }" />
			</dd>
		</dl>
		<dl>
			<dt>维护人联系方式</dt>
			<dd>
				<input type="text" readonly="readonly"
					value="${surveyChannelVO.safeguardPhone }" />
			</dd>
		</dl>
		<dl style="width: 100%; height: auto">
			<dt>备注</dt>
			<dd>
				<textarea id="surveyCourse" rows="3" cols="100" readonly="readonly">${surveyChannelVO.remark }</textarea>
			</dd>
		</dl>
	</div>

	<div class="divfclass">
		<h1>
			<img src="clm/images/tubiao.png">非通用部分_医疗
		</h1>
	</div>
	<div class="tabdivclassbr">
		<table class="list" style="width: 100%;">
			<thead align="center">
				<tr>
					<th nowrap>开放查询科室</th>
					<th nowrap>查询方式</th>
					<th nowrap>查询年限范围</th>
				</tr>
			</thead>
			<tbody align="center">
				<s:iterator value="surveyChannelUncomList" status="var">
					<s:if test="uncommonType eq 1">
						<tr>
							<td>${queryDept}</td>
							<td>${queryMode}</td>
							<td>${queryYear}</td>
						</tr>
					</s:if>
				</s:iterator>
			</tbody>
		</table>
	</div>
	<div class="divfclass">
		<h1>
			<img src="clm/images/tubiao.png">非通用部分_社保/农合
		</h1>
	</div>
	<div class="tabdivclassbr">
		<table class="list" style="width: 100%;">
			<thead align="center">
				<tr>
					<th nowrap>开放查询科室</th>
					<th nowrap>查询方式</th>
					<th nowrap>查询年限范围</th>
					<th nowrap>开发查询地域范围</th>
				</tr>
			</thead>
			<tbody align="center">
				<s:iterator value="surveyChannelUncomList" status="var">
					<s:if test="uncommonType eq 2">
						<tr>
							<td>${queryDept}</td>
							<td>${queryMode}</td>
							<td>${queryYear}</td>
							<td>${queryDistrict}</td>
						</tr>
					</s:if>
				</s:iterator>
			</tbody>
		</table>
	</div>
	<div class="divfclass">
		<h1>
			<img src="clm/images/tubiao.png">非通用部分_社保/农合
		</h1>
	</div>
	<div class="panelPageFormContent">
		<dl>
			<dt>是否具备司法鉴定资质</dt>
			<dd>
				<span><input type="checkbox" disabled="disabled"
					style="border: 0px; background: 0px; width: auto;"
					<s:if test="surveyChannelVO.isJudExpertise eq 1">checked="checked"</s:if> />是</span>
			</dd>
		</dl>
	</div>
	<form id="updateOrAddFixed"
		action="clm/channel/updateOrAddFixedChannel_CLM_fixedChannelAction.action"
		method="post" onsubmit="return navTabSearch(this)"
		class="pagerForm required-validate" rel="pagerForm2">
		<input name="surveyChannelVO.channelId"
			value="${surveyChannelVO.channelId}" type="hidden"> <input
			name="channelName" value="" type="hidden"> <input
			name="filialeOrg" value="" type="hidden"> <input
			name="channelType" value="" type="hidden"> <input
			name="fixedSelectIndex" value="0" type="hidden">
		<div>
			<div class="divfclass">
				<h1>
					<img src="clm/images/tubiao.png">渠道评价信息
				</h1>
			</div>
			<div>

				<div class="pageFormdiv">
					<a class="but_blue main_buta"
						href="clm/pages/channel/queryChannelLevel.jsp" lookupGroup="">渠道等级查询</a>
				</div>
				<div class="tabdivclassbr">
<!-- 				<table id="tableId" id="flag" -->
<!-- 							class="list nowrap itemDetail" addButton="添加理赔类型" width="100%"> -->
							
					<table class="list nowrap itemDetail"  addButton="添加" width="100%">
						<thead>
							<tr>
								<th type="text" size="3" readonly fieldClass="digits"
									defaultVal="#index#" disabled="disabled">序号</th>
								<th type="text" name="surveyChannelEvaluateAdd[#index#].bizNo"
									fieldClass="digits" size="20">赔案号</th>
								<th type="date"
									name="surveyChannelEvaluateAdd[#index#].surveyDate"
									format="yyyy-MM-dd HH:mm:ss" name="items.itemDataTime"
									size="20">调查时间</th>
								<th type="text"
									name="surveyChannelEvaluateAdd[#index#].cooperAchieve"
									size="20">合作成果</th>
								<th type="enum" enumUrl="clm/pages/html/channelLevel.jsp">评价等级</th>
								<th type="text"
									name="surveyChannelEvaluateAdd[#index#].reduceMoney"
									fieldClass="number" size="20">减损金额</th>
								<th type="text" name="surveyChannelEvaluateAdd[#index#].remark"
									fieldClass="number" size="40">备注</th>
								<th type="del" fieldClass="number">操作</th>
							</tr>
						</thead>
						<tbody>
							<s:iterator value="evaluateCurrentPage.pageItems"
								status="fixedVar">
								<tr>
									<td><input type="text"
										value="<s:property value="#fixedVar.index+1"/>" size="3"
										disabled="disabled" /></td>
									<td><input type="text"
										name="surveyChannelEvaluateUpdate[<s:property value="#fixedVar.index"/>].bizNo"
										value="${bizNo}" disabled="disabled" /></td>
									<td><input type="expandDateYMD" flag="flag" class="date"
										name="surveyChannelEvaluateUpdate[<s:property value="#fixedVar.index"/>].surveyDate"
										disabled="disabled"
										value="<s:date name='surveyDate' format='yyyy-MM-dd'/>"
										size="20" /> <a class="inputDateButton" href="javascript:;"
										disabled="disabled">选择</a>
										<td><input size="20" type="text"
										name="surveyChannelEvaluateUpdate[<s:property value="#fixedVar.index"/>].cooperAchieve"
										value="${cooperAchieve}" disabled="disabled" /></td>
									<td><select class="combox title"
										name="surveyChannelEvaluateUpdate[<s:property value="#fixedVar.index"/>].evaluateLevel"
										disabled="disabled">
											<option value="01"
												<s:if test="evaluateLevel eq 01"> selected="selected"</s:if>>高级

											<td><input type="text"
										name="surveyChannelEvaluateUpdate[<s:property value="#fixedVar.index"/>].reduceMoney"
										value="${reduceMoney}" disabled="disabled" size="20" /></td>
									<td><input type="text"
										name="surveyChannelEvaluateUpdate[<s
									:property value="#fixedVar.index"/>].remark"
										value="${remark}" disabled="disabled" size="50" /> <!-- 将未显示的数据进行隐藏用作修改使用-->
										<input type="hidden"
										name="surveyChannelEvaluateUpdate[<s:property value="#fixedVar.index"/>].listId"
										value="${listId}" disabled="disabled" /> <input type="hidden"
										name="surveyChannelEvaluateUpdate[<s:property value="#fixedVar.index"/>].channelId"
										value="${channelId}" disabled="disabled" /> <input
										type="hidden"
										name="surveyChannelEvaluateUpdate[<s:property value="#fixedVar.index"/>].channelType"
										value="${channelType}" disabled="disabled" /> <input
										type="hidden"
										name="surveyChannelEvaluateUpdate[<s:property value="#fixedVar.index"/>].channelName"
										value="${channelName}" disabled="disabled" /> <input
										type="hidden"
										name="surveyChannelEvaluateUpdate[<s:property value="#fixedVar.index"/>].bizType"
										value="${bizType}" disabled="disabled" /> <input
										type="hidden"
										name="surveyChannelEvaluateUpdate[<s:property value="#fixedVar.index"/>].evaluatePer"
										value="${evaluatePer}" disabled="disabled" /> <input
										type="hidden"
										name="surveyChannelEvaluateUpdate[<s:property value="#fixedVar.index"/>].evaluateDate"
										value="<s:date name='evaluateDate' format='yyyy-MM-dd'/>"
										disabled="disabled" /></td>
									<td><a title="编辑" class="btnEdit"
										href="javascript:void(0);"
										onclick="editFixed('<s:property value="#fixedVar.index"/>')">编辑</a>
									</td>
								</tr>
							</s:iterator>
						</tbody>
					</table>
					<!-- 分页查询区域 -->
					<div class="panelBar">
						<div class="pages">
							<span>显示</span>
							<s:select list="#{1:'1',5:'5',10:'10',20:'20'}" name="select"
								onchange="navTabPageBreak({numPerPage:this.value},'addChannelPage')"
								value="evaluateCurrentPage.pageSize">
							</s:select>
							<span>条，共${evaluateCurrentPage.total}条</span>
						</div>
						<div class="pagination" targetType="navTab" rel="addChannelPage"
							totalCount="${evaluateCurrentPage.total}"
							numPerPage="${evaluateCurrentPage.pageSize}" pageNumShown="10"
							currentPage="${evaluateCurrentPage.pageNo}"></div>
					</div>
					</div>
					
					<div class="panelPageFormContent">
					<dl>
						<dt>操作员</dt>
						<dd>
							<input type="text" disabled="disabled" value="${operationPerson}" />
						</dd>
					</dl>
					<dl>
						<dt>操作日期</dt>
						<dd>
							<input type="text" disabled="disabled"
								value="<s:date name="time" format="yyyy-MM-dd"/>" />
						</dd>
					</dl>
					</div>
			</div>
		</div>
	</form>
	<div class="formBarButton">
		<ul>
			<li><button type="button" class="but_blue"
					onclick="submitAddFixed()">保存</button></li>
			<li><button type="button" class="but_gray" onclick="exit()">退出</button></li>
		</ul>
	</div>
</div>
