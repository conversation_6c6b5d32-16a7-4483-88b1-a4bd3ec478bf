<%@ page language="java" contentType="text/html; charset=utf-8"
	pageEncoding="UTF-8"%><%@ include file="/clm/pages/common/commonJsCss.jsp" %>
<%@ taglib uri="/struts-tags" prefix="s"%>
<%@ taglib uri="http://www.git.com.cn/taglib/udmp/field" prefix="Field"%>

<script type="text/javascript">
var url2 = "<%=request.getAttribute("host")%>";
//var url2 = json.url;
//var url2=ip_code_sys + "/nologin/nologin.jsp?param=" + json.uuid;
 console.log(url2);
//关闭当前页面
/*navTab.closeCurrentTab();
navTab.openTab("23412324", url2, {title:'固定渠道管理'}); 
 */
$(function(){
	var channelIndex ="${param.fixedSelectIndex}";
	if(channelIndex!=""){
		$("#surveyFixedChannel", navTab.getCurrentPanel()).find("tr").eq(channelIndex).find("input").click();
		alertMsg.correct('保存成功!');
	}
	
});
//清空查询条件
function emptyFixed(){
	$("[name='surveyChannelVO.channelType']").val("");
	$("[name='surveyChannelVO.filialeOrg']").val("");
	$("[name='surveyChannelVO.channelName']").val("");
}
//查询指定渠道结果的详情信息
function detailedFixedChannel(channelId){
	
	$("#addChannelPage", navTab.getCurrentPanel()).loadUrl("clm/channel/detailedFixedChannel_CLM_fixedChannelAction.action?channelId="+channelId);
}
</script>
<!-- 分页查询访问路径 -->

	<div id="jbsxBox2" class="unitBox"  layoutH="36" >
	 	<iframe id="otherIframe" name="otherIframe"  width="100%" height="99%" src="${host}" noresize></iframe>
	</div>


<%-- 
<div layoutH="10"  id="aaaa">
<!-- 分页查询访问路径 -->
	<form id="pagerForm" method="post"
		action="clm/channel/queryFixedChannel_CLM_fixedChannelAction.action">
		<input type="hidden" name="pageNum" value="${fixedCurrentPage.pageNo}" />
		<input type="hidden" name="numPerPage" value="${fixedCurrentPage.pageSize}" />
	</form>
	<!-- 查询事件访问路径 -->
	<form id="taskManage"
		action="clm/channel/queryFixedChannel_CLM_fixedChannelAction.action"
		method="post" onsubmit="return navTabSearch(this)"
		class="pagerForm required-validate" rel="pagerForm">
		<div class="panelPageFormContent">
			<div class="divfclass">
				<h1>
					<img src="clm/images/tubiao.png">固定渠道条件
				</h1>
			</div>
			<div class="pageFormInfoContent">
				<dl>
					<dt>渠道类型</dt>
					<dd>
						<Field:codeTable cssClass="combox title" name="surveyChannelVO.channelType"
							value="${surveyChannelVO.channelType}"
							tableName="APP___CLM__DBUSER.T_FIXED_CHANNEL_TYPE"
							nullOption="true" />
					</dd>
				</dl>
				<dl>
					<dt>二级机构名称</dt>
					<dd>
						<select class="combox title" name="surveyChannelVO.filialeOrg">
							<option value="">全部</option>
							<s:iterator value="orgs" status="var">
								<option value="${organCode}"
									<s:if test="fixedCurrentPage.paramObject.filialeOrg eq organCode"> selected="selected"</s:if>>${organName }</option>
							</s:iterator>
						</select>
					</dd>
				</dl>
				<dl>
					<dt>渠道名称</dt>
					<dd>
						<input name="surveyChannelVO.channelName" type="text"
							value="${fixedCurrentPage.paramObject.channelName }"
							onkeyup="this.value=this.value.replace(/\s/g,'')" />
					</dd>
				</dl>
				<div class="pageFormdiv">
					<button type="submit" class="but_blue">查询</button>
					<button type="button" class="but_blue" onclick="emptyFixed()">清空</button>
				</div>
			</div>
		</div>
	</form>
		<div>
		<div class="divfclass">
		<h1>
			<img src="clm/images/tubiao.png">查询渠道结果
		</h1>
		</div>
		<div>
		<div class="tabdivclassbr">
		<table class="list" style="width:100%;">
						<thead align="center">
							<tr>
								<th nowrap>序号</th>
								<th nowrap>选择</th>
								<th nowrap>渠道名称</th>
								<th nowrap>复核标志</th>
								<th nowrap>渠道等级</th>
								<th nowrap>渠道类型</th>
								<th nowrap>二级机构名称</th>
								<th nowrap>三级机构名称</th>
							</tr>
						</thead>
						<tbody id="surveyFixedChannel" align="center">
							<s:if test="imageFlag != null">
								<tr>
									<td colspan="8">
										<div class="noRueryResult">请选择条件查询数据！</div>
									</td>
								</tr>
							</s:if>
							<s:elseif test="fixedCurrentPage.pageItems == null || fixedCurrentPage.pageItems.size()==0">
								<tr>
									<td colspan="8">
										<div class="noRueryResult">没有符合条件的查询结果！</div>
									</td>
								</tr>
							</s:elseif>
							<s:iterator value="fixedCurrentPage.pageItems" status="var" var="st">
							<tr>
								<td><s:property value="#var.index+1"/></td>
								<td><input name="channel" type="radio"  onclick="detailedFixedChannel(${channelId})"></td>
								<td>${channelName }</td>
								<td>${validFlag }</td>
								<td>${channelLevel }</td>
								<td>
									<Field:codeValue value="${channelType}" tableName="APP___CLM__DBUSER.T_FIXED_CHANNEL_TYPE"/>
								</td>
								<td>${filialeOrg }</td>
								<td>${mediueOrg }</td>
							</tr>
						</s:iterator>
					</tbody>
					</table>
					<!-- 分页查询区域 -->
					<div class="panelBar">
						<div class="pages">
							<span>显示</span>
						<s:select list="#{5:'5',10:'10',20:'20'}"
							name="select" onchange="navTabPageBreak({numPerPage:this.value})"
							value="fixedCurrentPage.pageSize">
						</s:select>
						<span>条，共${fixedCurrentPage.total}条</span>
					</div>
					<div class="pagination" targetType="navTab"
						totalCount="${fixedCurrentPage.total}"
						numPerPage="${fixedCurrentPage.pageSize}" pageNumShown="10"
						currentPage="${fixedCurrentPage.pageNo}"></div>
					</div>
					</div>
				</div>
			</div>
			<!-- 添加详情页面 -->
			<div id="addChannelPage">
			</div>
</div> --%>
