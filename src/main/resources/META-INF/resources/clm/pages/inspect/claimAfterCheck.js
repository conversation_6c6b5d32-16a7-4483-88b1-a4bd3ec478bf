//声明一个全局常量
var shu = 1;
//查询质检项目和质检要点
function queryClaimAfterCheckProject() {
	var checkType = $("#checkType", navTab.getCurrentPanel()).val();
	if(checkType == "" || checkType == null){
		alertMsg.error("必须有一项查询条件!");
	} else {
		$("#findClaimAfterCheck", navTab.getCurrentPanel()).submit();
	}
}
// 点击按钮查询信息
function findClaimAfterCheckProject(itemId) {
	var item = $(itemId).val();
	$("#saveButton", navTab.getCurrentPanel()).removeAttr("disabled");
	$("#claimAfterCheckItemIdId", navTab.getCurrentPanel()).val(item);
	$.ajax({
				'url' : 'clm/inspect/findClaimAfterCheckProject_CLM_claimAfterCheckProjectAction.action?itemId='
						+ item,
				'type' : 'post',
				'datatype' : 'json',
				'success' : function(data) {
					var data = eval("(" + data + ")");
					$("#findCheckType", navTab.getCurrentPanel()).val(
							data.checkType);
					$("#itemIdId", navTab.getCurrentPanel()).val(data.itemId);
					$("#findItemNameId", navTab.getCurrentPanel()).val(
							data.itemName);
					$("#findItemName", navTab.getCurrentPanel()).val(
							data.itemName);
					$("#creatDateId", navTab.getCurrentPanel()).val(
							data.creatDateStr);
					$("#userIdId", navTab.getCurrentPanel()).val(data.userId);
					$("#userNameId", navTab.getCurrentPanel()).val(
							data.userName);
					$("#findCheckTypeId", navTab.getCurrentPanel()).val(
							data.checkTypeStr);
					$("#clmAfterItemIdId", navTab.getCurrentPanel()).val(
							data.itemId);
					if (data.validFlag == 1) {
						$("#clmAfterCheckClickId", navTab.getCurrentPanel()).attr("checked", "checked");
						$("#clmAfterCheckClickId", navTab.getCurrentPanel()).val("1");
					} else {
						$("#clmAfterCheckClickId", navTab.getCurrentPanel()).val("0");
						$("#clmAfterCheckClickId", navTab.getCurrentPanel()).attr("checked", false);
					}
					$("#claimAfcChcekIdId", navTab.getCurrentPanel()).empty();
					shu = 1;
					if(data.claimAfcGistVOList.length == 0){
						insertHtml= "<tr>"
							+ "<td>"+1+"<a title='添加' type='hidden' class='btnAdd' id='addButton' href='javascript:void(0);' onclick='addClaimAfter(this);'></a></td>"
							+ "<td><input id='gistDescCheckId' size='150' name='claimAfcGistList[0].gistDesc'></td>"
							/*+ "<td><input id='checkboxValidFlag' type='checkbox' name='claimAfcGistList[0].validFlag' value='0' onclick='checkboxValidFlag(this);'/>有效 "*/
							+"<input type='hidden' id='isUpdate' name='claimAfcGistList[0].isUpdate' value='1'></td>"
							+ "<td><a title='编辑' class='btnEdit' id='editButton' href='javascript:void(0);' onclick='editds(this);'>编辑</a>"
							/*+ "<a title='添加' class='btnAdd' id='addButton' href='javascript:void(0);' onclick='addClaimAfter(this);'>添加</a>"*/
							+ "</td>" + "</tr>";
						$("#claimAfcChcekIdId", navTab.getCurrentPanel())
						.append(insertHtml);
					}else{
						for ( var i = 0; i < data.claimAfcGistVOList.length; i++) {
							var a = "";
							var b = 0;
							if (data.claimAfcGistVOList[i].validFlag == 1) {
								a = "checked='checked'";
								b = 1;
							}
							if(i == data.claimAfcGistVOList.length - 1){
								insertHtml = "<tr id='claimAfcGistListTR"+i+"'>"
									+ "<td>"
									+ (i + 1) +"<a title='添加' type='hidden' class='btnAdd' id='addButton' href='javascript:void(0);' onclick='addClaimAfter(this);'></a>"
									+ "<input name='claimAfcGistList["+i+"].gistId' value="
									+ data.claimAfcGistVOList[i].gistId
									+ " type='hidden' id='claimAfcGistId"+i+"'></td>"
									+ "<td>"
									/*+ data.claimAfcGistVOList[i].gistDesc*/
									+"<input type='text' size='150' id='gistDescafc"+i+"' value="+data.claimAfcGistVOList[i].gistDesc+" onChange='chgClaimAfter(this,"+i+");' readonly='readonly' />"
									+ "<input type='hidden' id='gistDescafcname"+i+"'  name='claimAfcGistList["+i+"].gistDesc' value="
									+ data.claimAfcGistVOList[i].gistDesc
									+ " ></td>"
									/*+ "<td><input type='checkbox' disabled='disabled' style='border:0px;background:0px;width:auto;' "
									+ a
									+ " value="
									+ b
									+ " name='claimAfcGistList["+i+"].validFlag' onclick='checkboxValidFlag(this);'/>"
									+"有效*/+"<input type='hidden' id='isUpdate' name='claimAfcGistList["+i+"].isUpdate' value='0'></td>"
									+ "<td><a title='编辑' class='btnEdit' id='editButton' href='javascript:void(0);' onclick='editds(this,"+i+");'>编辑</a>"
									/*+ "<a title='添加' class='btnAdd' id='addButton' href='javascript:void(0);' onclick='addClaimAfter(this);'>添加</a>"*/
									+ "<a title='删除' class='btnDel' id='delButton' href='javascript:void(0);' onclick='editds("+i+","+'99'+");'>删除</a>"
									+ "</td>" + "</tr>";
							}else{
								insertHtml = "<tr id='claimAfcGistListTR"+i+"'>"
									+ "<td>"
									+ (i + 1)+"<a title='添加' type='hidden' class='btnAdd' id='addButton' href='javascript:void(0);' onclick='addClaimAfter(this);'></a>"
									+ "<input name='claimAfcGistList["+i+"].gistId' value="
									+ data.claimAfcGistVOList[i].gistId
									+ " type='hidden' id='claimAfcGistId"+i+"'></td>"
									+ "<td>"
									/*+ data.claimAfcGistVOList[i].gistDesc*/
									+"<input type='text' size='150' id='gistDescafc"+i+"' value="+data.claimAfcGistVOList[i].gistDesc+" onChange='chgClaimAfter(this,"+i+");' readonly='readonly' />"
									+ "<input type='hidden' id='gistDescafcname"+i+"' name='claimAfcGistList["+i+"].gistDesc' value="
									+ data.claimAfcGistVOList[i].gistDesc
									+ " ></td>"
									/*+ "<td><input disabled='disabled' type='checkbox' style='border:0px;background:0px;width:auto;' "
									+ a
									+ " value="
									+ b
									+ " name='claimAfcGistList["+i+"].validFlag' onclick='checkboxValidFlag(this);'/>"
									+"有效*/+"<input type='hidden' id='isUpdate' name='claimAfcGistList["+i+"].isUpdate' value='0'></td>"
									+ "<td><a title='编辑' class='btnEdit' id='editButton' href='javascript:void(0);' onclick='editds(this,"+i+");'>编辑</a>"
									+ "<a title='删除' class='btnDel' id='delButton' href='javascript:void(0);' onclick='editds("+i+","+'99'+");'>删除</a>"
									+ "</td>" + "</tr>";
							}
							$("#claimAfcChcekIdId", navTab.getCurrentPanel())
									.append(insertHtml);
							 shu = i+2;
						}
					}
					}
			});
}
function chgClaimAfter(obj,i){
	var zhi=$(obj).val();
	$("#gistDescafcname"+i, navTab.getCurrentPanel()).val(zhi);
	
}
// 保存
function saveClaimAfterCheckProject() {
	//判断是否有相同的质检要点名称
	var sameFlag = false;
	var length = $("#claimAfcChcekIdId", navTab.getCurrentPanel()).find("tr").length;
	for(var i = 0; i < length; i++){//add by  guosj  2018-05-31 校验录入名称中不能有空值
		var nameValue = $("#claimAfcChcekIdId", navTab.getCurrentPanel()).find("tr:eq("+i+")").find("td:eq(1)").find("input").val().trim();
		if(nameValue==""){
			alertMsg.error("您有未录入的质检要点列表下的<质检要点说明>，请重新录入");
			return false;
		}
	}
	for(var i = 0; i < length; i++){
		var obj1 = $("#claimAfcChcekIdId", navTab.getCurrentPanel()).find("tr:eq("+i+")").find("td:eq(1)").find("input").val().trim();
		for(var j = i+1; j < length; j++){
			var obj2 = $("#claimAfcChcekIdId", navTab.getCurrentPanel()).find("tr:eq("+j+")").find("td:eq(1)").find("input").val().trim();
			if(obj1 == obj2){
				sameFlag = true;
				break;
			}
		}
		if(sameFlag){
			break;
		}
	}
	if(sameFlag){
		alertMsg.error("您录入的质检要点<要点名称>有重复数据，请重新录入");
		return false;
	}
	if($("#findCheckType", navTab.getCurrentPanel()).val() == null || $("#findCheckType", navTab.getCurrentPanel()).val() == ""){
		alertMsg.error("质检类型必选!");
	}
	if ($("#findItemNameId", navTab.getCurrentPanel()).val() == null || $("#findItemNameId", navTab.getCurrentPanel()).val() == "") {
		alertMsg.error("质检项目名称为空!");
	} else {
		$("#saveClaimAfterCheckProject", navTab.getCurrentPanel()).submit();
	}
}

// 保存的回调函数
function saveAjaxDone(json) {
	if (json.statusCode == DWZ.statusCode.ok) {
		queryClaimAfterCheckProject();
		alertMsg.correct(json.message);
	} else {
		alertMsg.error(json.message);
	} 
}

// 选中复选框时赋值
function clmAfterCheckClick() {
	if ($("#clmAfterCheckClickId", navTab.getCurrentPanel()).attr("checked") == "checked") {
		$("#clmAfterCheckClickId", navTab.getCurrentPanel()).val("1");
	} else {
		$("#clmAfterCheckClickId", navTab.getCurrentPanel()).val("0");
	}
}
// 触发质检要点复选框时赋值
function checkboxValidFlag(k) {
	$(k, navTab.getCurrentPanel()).val("0");
	if($(k, navTab.getCurrentPanel()).attr("checked") == "checked"){
		$(k, navTab.getCurrentPanel()).val("1");
	}
}

function next_del(k,obj) {
	var gistid=$("#claimAfcGistId"+k, navTab.getCurrentPanel()).val();
	$.ajax({
		'url' : 'clm/inspect/deleteClaimAfterCheckProject_CLM_claimAfterCheckProjectAction.action?gistid='+ gistid,
		'type' : 'post',
		'datatype' : 'json',
		'success' : function(data) {
			var data1 = eval("(" + data + ")");
			if(DWZ.statusCode.ok==data1.statusCode){
				//alertMsg.info("删除成功！!");
				saveClaimAfterCheckProject();
			}
		}
	});
	
	var tr=$("#claimAfcGistListTR"+k, navTab.getCurrentPanel());
	tr.remove();
}
// 编辑图标
function editds(k,obj) {
	/*$(k).parent().parent().find("td:eq(2)").find("input[type='checkbox']").removeAttr("disabled");
	$(k).parent().parent().find("input#isUpdate").val(1);*/
	//$("#gistDescafc"+obj, navTab.getCurrentPanel()).attr("disabled",false);
	
	if(obj!='99'){
		$("#gistDescafc"+obj, navTab.getCurrentPanel()).removeAttr("readonly");
	}else{
			 alertMsg.confirm("您确定删除该选项!", {
				 okCall : function() {
				     next_del(k,obj);
				 }
			 });
			 return false;
	}
}
// 重置
function cliamAfterCheckRest() {
	$("#cliamAfterCheckRest", navTab.getCurrentPanel()).submit();
}
//退出
/*function cliamAfterCheckExit() {
	 alertMsg.confirm("当前页面录入的信息将不被保存，是否确定退出？", {
	 	okCall : function() {
			navTab.closeCurrentTab();
	 	}
	 });
}*/

// 新增
function add() {
	$("#addClaimAfterCheck", navTab.getCurrentPanel()).submit();
}

//添加
function addClaimAfter(k){
	var index =$(k).parent().parent().parent().find("tr:last").find("td:eq(0)").text();
	var indexk = 1+(index*1);
	//$(k).parent().find("a:eq(1)").remove();
	insertHtml = "<tr id='claimAfcGistListTR"+indexk+"'>"
		+ "<td>"+ indexk +"" + "<a title='添加' type='hidden' class='btnAdd' id='addButton' href='javascript:void(0);' onclick='addClaimAfter(this);'></a>"+"</td>"
		+ "<td><input id='gistDescCheckId' size='150' name='claimAfcGistList["+index+"].gistDesc'></td>"
		/*+ "<td><input type='checkbox' style='border:0px;background:0px;width:auto;' name='claimAfcGistList["+index+"].validFlag' value='0' onclick='checkboxValidFlag(this)'/>有效</td>"*/
		
		+ "<td><a title='编辑' class='btnEdit' id='editButton' href='javascript:void(0);' onclick='editds(this,"+indexk+");'>编辑</a> <input type='hidden' id='isUpdate' name='claimAfcGistList["+(indexk-1)+"].isUpdate' value='1'>"
		/*+ "<a title='添加' type='hidden' class='btnAdd' id='addButton' href='javascript:void(0);' onclick='addClaimAfter(this);'>添加</a>"*/
		+ "<a title='删除' class='btnDel' id='delButton' href='javascript:void(0);' onclick='editds("+indexk+","+'99'+");'>删除</a>"
		+ "</td>" + "</tr>";
	$("#claimAfcChcekIdId", navTab.getCurrentPanel()).append(insertHtml);
}