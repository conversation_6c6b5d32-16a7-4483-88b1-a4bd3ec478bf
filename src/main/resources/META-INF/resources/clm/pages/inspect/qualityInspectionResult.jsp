<%@ page language="java" contentType="text/html; charset=utf-8"
	pageEncoding="UTF-8"%><%@ include file="/clm/pages/common/commonJsCss.jsp" %>
<%@ taglib uri="/struts-tags" prefix="s"%>
<%@ taglib uri="http://www.git.com.cn/taglib/udmp/field" prefix="Field"%>

<script type="text/javascript"
	src="clm/pages/inspect/qualityInspectionResult.js">	
</script>
<div class="pageContent pageHeader">
	<form id="form1"
		action="clm/inspect/saveInspectResult_qualityInspectionResultAction.action"
		method="post" onsubmit="return navTabSearch(this,'caseQueryTabs1');">
		<div class="pageFormContent">
			<p>
				<label style="width: 20%">赔案号</label> <input
					name="claimCaseVO.caseNo" value="${claimCaseVO.caseNo}" type="text"
					size="20" readonly="readonly" />

			</p>
			<p>
				<label style="width: 20%">事件号</label> <input
					name="claimAccidentVO.accidentNo"
					value="${claimAccidentVO.accidentNo}" class="readonly" type="text"
					size="20" readonly="readonly" />
			</p>
		</div>
		<div class="panel">
			<h1>质检结论</h1>
			<div>
				<div class="pageFormContent">
					<dl>
						<dt>质检结论：</dt>
						<dd>
							<select class="combox title"  id="inspectResult"
								name="inspectResultParamVo.inspectResult"
								value="${inspectResultParamVo.inspectResult}"
								onchange="changeAvalable(this.id,'unPassReason')">
								<option value="">----请选择 ----</option>
								<s:iterator value="inspectResultParamVo.inspectResultList"
									var="inspectResult" status="count">
									<option value="<s:property value="#count.index" />">
										<s:property value="#inspectResult" /></option>
								</s:iterator>
							</select>
						</dd>
					</dl>
					<dl>
						<dt>不通过原因：</dt>
						<dd>
							<select class="combox title"  id="unPassReason"
								name="inspectResultParamVo.unPassReason"
								value="${inspectResultParamVo.unPassReason}" disabled="disabled">
								<option value="">-------------------------------请选择
									------------------------------</option>
								<s:iterator value="inspectResultParamVo.unPassReasonList"
									var="unPassReason" status="count">
									<option value="<s:property value="#count.index" />">
										<s:property value="#unPassReason" /></option>
								</s:iterator>
							</select>
						</dd>
					</dl>
					<dl
						style="float: float; display: block; width: 100%; margin: 0 0 30px 0">
						<dt style="width: 11%;">备注信息：</dt>
						<dd style="width: 60%;">
							<textarea name="inspectResultParamVo.memo" id="memo" rows="3"
								cols="70">${inspectResultParamVo.memo}</textarea>
						</dd>

					</dl>
					<dl
						style="float: float; display: block; width: 33%; margin: 0 0 30px 0">
						<dt>质检日期：</dt>
						<dd>
							<input type="text" id="inspectDate" readonly="readonly"
								class="textInput readonly"
								name="inspectResultParamVo.inspectDate"
								value="${inspectResultParamVo.inspectDate}" size="17" />
						</dd>
					</dl>
					<dl
						style="float: float; display: block; width: 33%; margin: 0 0 30px 0">
						<dt>质检人员机构号：</dt>
						<dd>
							<input type="text" name="inspectResultParamVo.personTermCode"
								value="${inspectResultParamVo.personTermCode}"
								readonly="readonly" class="textInput readonly"
								id="personTermCode" />
						</dd>
					</dl>
					<dl
						style="float: float; display: block; width: 33%; margin: 0 0 30px 0">
						<dt>质检人员：</dt>
						<dd>
							<input type="text" name="inspectResultParamVo.personCode"
								value="${inspectResultParamVo.personCode}" readonly="readonly"
								class="textInput readonly" id="personCode" style="width: 30px" />
							<input type="text" name="inspectResultParamVo.personName"
								value="${inspectResultParamVo.personName}" readonly="readonly"
								class="textInput readonly" id="personName" />
						</dd>
					</dl>
				</div>
				<div>
					<dl>
						<dd>
							<div class="button">
								<div class="buttonContent">
									<button type="submit" onclick="return notNull()" style="float: center">保存</button>
								</div>
							</div>
						</dd>
					</dl>
				</div>
			</div>

		</div>
		<div class="formBar">
			<ul>
				<li>
					<div class="button">
						<div class="buttonContent">
							<button type="button" class="close">退出</button>
						</div>
					</div>
				</li>
			</ul>
		</div>
	</form>
</div>
