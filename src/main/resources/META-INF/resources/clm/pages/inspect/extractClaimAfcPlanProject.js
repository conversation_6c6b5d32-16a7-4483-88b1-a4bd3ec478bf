//进入维护页面时质检名如果不为空则为只读
$(function() {
	 
	//自动简易案件 11
	var automatedCase = "";
	//非自动简易案件 12
	var notAutomatedCase = "";
	//普通案件 13
	var ordinaryCase = "";
	//诉讼案件 14
	var litigationCase = "";
	//疑难案件 15
	var difficultCase = "";
	//调查案件 16
	var investigationCase = "";
	//自动审批案件 17
	var automaticApproval = "";
	//直赔案件 18
	var autoDirectCase = "";
	var strListId = $("#strListId",navTab.getCurrentPanel()).val();
	var strListIds = strListId.substring(1,strListId.length-1);
	var strListIdss = strListIds.split(",");
	for(var i = 0; i < strListIdss.length; i++){
		if(strListIdss[i].trim() == "11"){
			automatedCase = "checked=checked";
		} else if (strListIdss[i].trim() == "12") {
			notAutomatedCase = "checked=checked";
		} else if (strListIdss[i].trim() == "13") {
			ordinaryCase = "checked=checked";
		} else if (strListIdss[i].trim() == "14") {
			litigationCase = "checked=checked";
		} else if (strListIdss[i].trim() == "15") {
			difficultCase = "checked=checked";
		} else if (strListIdss[i].trim() == "16") {
			investigationCase = "checked=checked";
		} else if (strListIdss[i].trim() == "17") {
			automaticApproval = "checked=checked";
		} else if (strListIdss[i].trim() == "18") {
			autoDirectCase = "checked=checked";
		}
	}
	var inhtml = "<span><input style='margin-left: 30px' type='checkbox' "+automatedCase+" name='middleCheckConditionVO.caseFlag' disabled='false' value='11' id='automatedId'/>自动简易案件</span>"
	+ "<span><input style='margin-left: 30px' type='checkbox' "+notAutomatedCase+" name='middleCheckConditionVO.caseFlag' disabled='false' value='12' id='notAutomatedId'/>非自动简易案件</span>"
	+ "<span><input style='margin-left: 30px' type='checkbox' "+ordinaryCase+" name='middleCheckConditionVO.caseFlag' disabled='false' value='13' id='ordinaryId'/>普通案件</span>"
	+ "<span><input style='margin-left: 30px' type='checkbox' "+litigationCase+" name='middleCheckConditionVO.caseFlag' disabled='false' value='14' id='litigationId'/>诉讼案件</span>"
	+ "<span><input style='margin-left: 30px' type='checkbox' "+difficultCase+" name='middleCheckConditionVO.caseFlag' disabled='false' value='15' id='difficultId'/>疑难案件</span>"
	+ "<span><input style='margin-left: 30px' type='checkbox' "+investigationCase+" name='middleCheckConditionVO.caseFlag' disabled='false' value='16' id='investigationId'/>调查案件</span>"
	+ "<span><input style='margin-left: 30px' type='checkbox' "+automaticApproval+" name='middleCheckConditionVO.caseFlag' disabled='false' value='17' id='automaticApprovalId'/>自动审批案件</span>"
	+ "<span><input style='margin-left: 30px' type='checkbox' "+autoDirectCase+" name='middleCheckConditionVO.caseFlag' disabled='false' value='18' id='automaticApprovalId'/>直赔案件</span>";
	
	$("#caseFlagId",navTab.getCurrentPanel()).children().children("td:eq(0)").append(inhtml);

	// 抽取层级extractLevelId
	if ($("#claimextractLevelId", navTab.getCurrentPanel()).val() == "1") {
		$("#clmExtractCaseId", navTab.getCurrentPanel()).css("display", "block");
		$("#clmExtractCustomerId", navTab.getCurrentPanel()).css("display", "none");
	}
	if ($("#claimextractLevelId", navTab.getCurrentPanel()).val() == "2") {
		$("#clmExtractCaseId", navTab.getCurrentPanel()).css("display", "none");
		$("#clmExtractCustomerId", navTab.getCurrentPanel()).css("display", "block");
	}

});

//根据质检类型查询质检计划名称
function matchPlanName(k){
	var planType = $(k).val();
	var planName = $("#selectExtract", navTab.getCurrentPanel()).val();
	
	$.ajax({   
            'url' : 'clm/inspect/findMatchPlanName_CLM_extractClaimAfcPlanProjectAction.action?claimAfcPlanVO.planType='+planType,
	        'type' :'post',
	        'datatype' : 'json',
	        'success' : function (data){
	        	var claimAfcPlanVOList = eval("(" + data + ")");
	        	var options="";
	        	options="<option value=''>请选择</option>";
	        	for(var i = 0; i < claimAfcPlanVOList.length;i++){
					 
					options+="<option value='"+claimAfcPlanVOList[i].planName+"' ";
					
					if(claimAfcPlanVOList[i].planName==planName){
						options+="selected";
					}
					options+=">"+claimAfcPlanVOList[i].planName+"</option>";
				}
	        	$("#selectExtract", navTab.getCurrentPanel()).loadMyComboxOptions(options,' ');
	       } 
	  });
}

//根据质检计划名称查询质检类型
function matchPlanType(k){
	var planName = $(k).val();
	if(planName == ""){
		var options="";
        options="<option value='1'>常规</option>";
        options+="<option value='2'>专项</option>";
        $("#claimAfcPlanProjecPlanTypeId").loadMyComboxOptions(options,'1');
	}else{
		$.ajax({   
			'url' : 'clm/inspect/findMatchPlanName_CLM_extractClaimAfcPlanProjectAction.action?claimAfcPlanVO.planName='+encodeURI(encodeURI(planName)),
	        'type' :'post',                                                        
	        'datatype' : 'json',
	        'success' : function (data){
	        	  var claimAfcPlanVOList = eval("(" + data + ")");
	        	  var options="";
	        		  if(claimAfcPlanVOList.length == 1){
	        			  if(claimAfcPlanVOList[i].planType == 1){
		        			   options+="<option value='1'>常规</option>";
		        			   $("#claimAfcPlanProjecPlanTypeId").loadMyComboxOptions(options,'1');
		        		   }else{
		        			   options+="<option value='2'>专项</option>";
		        			   $("#claimAfcPlanProjecPlanTypeId").loadMyComboxOptions(options,'2');
		        		   }
	        		  }
	        		  if(claimAfcPlanVOList.length == 2){
	        			  options+="<option value='1'>常规</option>";
	        			  options+="<option value='2'>专项</option>";
	        			  $("#claimAfcPlanProjecPlanTypeId").loadMyComboxOptions(options,'1');
	        		  }
	        		   
					 
				}
		  });
	}
	 

}

// 查询
function queryExtractRule() {
	if ($("#selectExtract", navTab.getCurrentPanel()).val().trim() == "") {
		alertMsg.error("请录入必录项!");
		return false;
	}
	$("#queryExtractClaimAfcId", navTab.getCurrentPanel()).submit();
}
// 赔案1，客户2，如果是客户层级，则把赔案为不可修改。如果是赔案层级，则把客户为不可修改。
function getExtractLevel(k) {
	if ($(k).val() == "1") {
		$("#clmExtractCaseId", navTab.getCurrentPanel()).css("display", "block");
		$("#clmExtractCustomerId", navTab.getCurrentPanel()).css("display", "none");
	}
	if ($(k).val() == "2") {
		$("#clmExtractCaseId", navTab.getCurrentPanel()).css("display", "none");
		$("#clmExtractCustomerId", navTab.getCurrentPanel()).css("display", "block");
	}
}
// 抽取
function extractClaimCase() {
	// 抽检方式
	$("#extractRateType", navTab.getCurrentPanel()).removeAttr("disabled");
	// 检查水平
	$("#inspectionStandard", navTab.getCurrentPanel()).removeAttr("disabled");
	// 检查级别
	$("#inspectionLevelnumber", navTab.getCurrentPanel()).removeAttr("disabled");
	
	$("#claimextractLevelId", navTab.getCurrentPanel()).removeAttr("disabled");
	$("#simpleId", navTab.getCurrentPanel()).removeAttr("disabled");
	$("#auditorPeopleId", navTab.getCurrentPanel()).removeAttr("disabled");
	$("#commonId", navTab.getCurrentPanel()).removeAttr("disabled");
	$("#dieId", navTab.getCurrentPanel()).removeAttr("disabled");
	$("#hightCanId", navTab.getCurrentPanel()).removeAttr("disabled");
	$("#importDiseaseId", navTab.getCurrentPanel()).removeAttr("disabled");
	$("#mayhemId", navTab.getCurrentPanel()).removeAttr("disabled");
	$("#medicalTreatmentId", navTab.getCurrentPanel()).removeAttr("disabled");
	$("#finishId", navTab.getCurrentPanel()).removeAttr("disabled");
	$("#specialDiseaseId", navTab.getCurrentPanel()).removeAttr("disabled");
	$("#commonAbilityId", navTab.getCurrentPanel()).removeAttr("disabled");
	$("#importantAbilityId", navTab.getCurrentPanel()).removeAttr("disabled");
	$("#accidentId", navTab.getCurrentPanel()).removeAttr("disabled");
	$("#pathemaId", navTab.getCurrentPanel()).removeAttr("disabled");
	// 案件标识
	$("#automatedId", navTab.getCurrentPanel()).removeAttr("disabled");
	$("#notAutomatedId", navTab.getCurrentPanel()).removeAttr("disabled");
	$("#ordinaryId", navTab.getCurrentPanel()).removeAttr("disabled");
	$("#litigationId", navTab.getCurrentPanel()).removeAttr("disabled");
	$("#difficultId", navTab.getCurrentPanel()).removeAttr("disabled");
	$("#investigationId", navTab.getCurrentPanel()).removeAttr("disabled");
	$("#automaticApprovalId", navTab.getCurrentPanel()).removeAttr("disabled");
	
	if ($("#dateMinId", navTab.getCurrentPanel()).val() == ''
			|| $("#dateMinId", navTab.getCurrentPanel()).val() == null) {
		alertMsg.error("审批起止日期不能为空!");
	} else if ($("#dateMaxId", navTab.getCurrentPanel()).val() == ''
			|| $("#dateMaxId", navTab.getCurrentPanel()).val() == null) {
		alertMsg.error("审批起止日期不能为空!");
	} else if (AfcplanId==""||AfcplanId==null){
		alertMsg.error("请先查询计划在进行抽取！");
	}else {
		$("#selectedProductEXT option", navTab.getCurrentPanel()).each(function() {
			this.selected = true;
		});
		$("#orgListEXT option", navTab.getCurrentPanel()).each(function() {
			this.selected = true;
		});
		$("#areaListEXT option", navTab.getCurrentPanel()).each(function() {
			this.selected = true;
		});
		$("#extractClaimCaseId", navTab.getCurrentPanel()).submit();
	}
}
//删除
function deleteCurrentPage(){
	var taskId = "";
	$("[type='checkbox']", navTab.getCurrentPanel()).each(function(){
		if($(this).attr("checked") == "checked"){
			taskId+=$(this).val()+",";
		}
	});
	var planId = $("#currentPageVarPlanId", navTab.getCurrentPanel()).val();
	alertMsg.confirm("请确认是否删除？",{
		okCall : function() {
			$.ajax({
				url : "clm/inspect/deleteCurrentPageVar_CLM_extractClaimAfcPlanProjectAction.action?claimAfcTaskVO.taskIdStr="+taskId+"&claimAfcTaskVO.planId="+planId,
				global : false,
				type : "POST",
				dataType : "json",
				success : function(s) {
					if (s.statusCode == DWZ.statusCode.ok) {
						$("#queryClaimTaskId", navTab.getCurrentPanel()).attr("action","clm/inspect/queryClaimTask_CLM_extractClaimAfcPlanProjectAction.action?claimAfcTaskVO.planId="+planId);
						$("#queryClaimTaskId", navTab.getCurrentPanel()).submit();
					}
				}
			
			});
		}
	});
}

//查询
function queryClaimTask(){
	var planId = $("#currentPageVarPlanId", navTab.getCurrentPanel()).val();
	$("#queryClaimTaskId", navTab.getCurrentPanel()).attr("action","clm/inspect/queryClaimTask_CLM_extractClaimAfcPlanProjectAction.action?claimAfcTaskVO.planId="+planId);
	$("#queryClaimTaskId", navTab.getCurrentPanel()).submit();
}
//确认
function affirmInformation(){
	var planId = $("#extractClaimAfcPlanId" , navTab.getCurrentPanel()).val();
	var taskId = "";
	$("[type='checkbox']", navTab.getCurrentPanel()).each(function(){
		if($(this).attr("checked") == "checked"){
			taskId+=$(this).val()+",";
		}
	});
	$.ajax({
		url : "clm/inspect/affirmInformation_CLM_extractClaimAfcPlanProjectAction.action?claimAfcTaskVO.taskIdStr="+taskId+"&claimAfcTaskVO.planId="+planId,
		global : false,
		type : "POST",
		dataType : "json",
		success : function(s) {
			if (s.statusCode == DWZ.statusCode.ok) {
				alertMsg.correct("质检任务已分配给质检人");
				//查询分配之后的的数据
				$("#queryClaimTaskId", navTab.getCurrentPanel()).submit();
			} else {
				alertMsg.error(s.message);
			}
			}
	});
}
//撤销
function backoutExtract(){
	$("#backoutExtractId", navTab.getCurrentPanel()).submit();
}
//退出
/*function exitExtract(){
	alertMsg.confirm("您的信息将不会被抽取，是否确定退出？", {
		okCall : function() {
			navTab.closeCurrentTab();
		}
	});
}*/


/*
 * 从左侧列表转移到右侧列表的方法
 */
function moveselect(objId, targetId,flag,all) {
	
	var obj = document.getElementById(objId);
	var target = document.getElementById(targetId);
	if (flag) {
		//判断all是否为1,唯一则调用转移所有的方法
		if(all=="1"){
			for (var i = 0; i < obj.length; i++) {
				mot = obj.options[i].text;
				mov = obj.options[i].value;
				var newoption = document.createElement("OPTION");
				newoption.text = mot;
				newoption.value = mov;
				target.add(newoption);
			}
			obj.options.length = 0;
		}else{
			while (obj.selectedIndex > -1) {
				mot = obj.options[obj.selectedIndex].text;
				mov = obj.options[obj.selectedIndex].value;
				obj.remove(obj.selectedIndex);
				var newoption = document.createElement("OPTION");
				newoption.text = mot;
				newoption.value = mov;
				target.add(newoption);
			}
		}
	}else{
		if(all=="1"){
			for (var i = 0; i < obj.length; i++) {
				mot = obj.options[i].text;
				mov = obj.options[i].value;
				var newoption = document.createElement("OPTION");
				newoption.text = mot;
				newoption.value = mov;
				target.add(newoption);
			}
			obj.options.length = 0;
			//为报案机构的时候查询人员
			getInstitutions();
		}else{
			
			while (obj.selectedIndex > -1) {
				mot = obj.options[obj.selectedIndex].text;
				mov = obj.options[obj.selectedIndex].value;
				obj.remove(obj.selectedIndex);
				var newoption = document.createElement("OPTION");
				newoption.text = mot;
				newoption.value = mov;
				target.add(newoption);
				//为报案机构的时候查询人员
				getInstitutions();
		}
	  }
	}
}