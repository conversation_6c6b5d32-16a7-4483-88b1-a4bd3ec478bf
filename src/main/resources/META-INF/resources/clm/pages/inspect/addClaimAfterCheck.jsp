<%@ page language="java" contentType="text/html; charset=utf-8"
	pageEncoding="UTF-8"%><%@ include file="/clm/pages/common/commonJsCss.jsp" %>
<%@ taglib uri="/struts-tags" prefix="s"%>


<%@ taglib uri="http://www.git.com.cn/taglib/udmp/field" prefix="Field"%>
<script type="text/javascript"
	src="clm/pages/inspect/addClaimAfterCheck.js"></script>

<div layouth="10">
	<div class="divfclass">
		<h1><img src="clm/images/tubiao.png">维护质检项目</h1>
	</div>
		<form id="cliamAfterCheckRest" method="post"
							onsubmit="return navTabSearch(this)"
							action="clm/inspect/queryClaimAfterCheck_CLM_claimAfterCheckProjectAction.action">
		</form>	
		<form id="findClaimAfterCheck" method="post"
			onsubmit="return navTabSearch(this)"
			action="clm/inspect/findClaimAfterCheck_CLM_claimAfterCheckProjectAction.action">
			<input name="claimAfcItemVO.checkType" id="checkTypeId" type="hidden" />
		</form>
		<form id="addClaimAfterCheck" method="post"
			onsubmit="return navTabSearch(this)"
			action="clm/inspect/addClaimAfterCheck_CLM_claimAfterCheckProjectAction.action">
		</form>
		<div class="pageFormInfoContent">
			<div class="divfclass">
				<h1><img src="clm/images/tubiao.png">查询条件</h1>
			</div>
			<dl>
				<dt><label><font class="point" color="red">* </font>质 检 类 型 </label></dt>
				<dd>
					<Field:codeTable cssClass="combox title"  id="checkType" name="claimAfcItemVO.checkType"
						value="${claimAfcItemVO.checkType}" tableName="APP___CLM__DBUSER.T_QC_TYPE" whereClause="1=1" orderBy="decode(code,'0','2','1','1',code)"></Field:codeTable>
				</dd>
			</dl>
			<div class="pageFormdiv"><button type="button"class="but_blue" onclick="queryClaimAfterCheckProject();">查询</button>
			<button type="button"class="but_blue" id="claimAfterAddId" onclick="addCheck();">新增</button>
			</div>
			<input id="claimAfterCheckItemIdId" value="" type="hidden" />
		</div>
	
	<div class="panelPageFormContent ">
		<div class="divfclass">
			<h1><img src="clm/images/tubiao.png">查询质检项目结果</h1>
		</div>
			<div class="tabdivclassbr">
				<table class="list" width="100%">
					<thead>
						<tr>
							<th nowrap>序号</th>
							<th nowrap>质检类型</th>
							<th nowrap>质检项目名称</th>
						</tr>
					</thead>
					<tbody>
					</tbody>
				</table>
			</div>
	</div>
	
	<div id="findClaimAfterCheckProjects">
			<form id="saveClaimAfcItem" method="post"
				action="clm/inspect/saveclaimAfcItem_CLM_claimAfterCheckProjectAction.action"
				class="pageForm required-validate"
				onsubmit="return validateCallback(this, saveAjaxDone);">
		<div class="divfclass">
			<h1><img src="clm/images/tubiao.png">质检项目</h1>
		</div>
		<input id="clmAfterItemIdId" type="hidden" name="claimAfcItemVO.itemId" value="" />
				<div class="panelPageFormContent ">
					<dl>
						<dt><font class="point" color="red">* </font>质检类型 </dt>
						<dd>
							<Field:codeTable cssClass="combox title"  id="findCheckType"
								name="claimAfcItemVO.checkType"
								value="${claimAfcItemVO.checkType}" tableName="APP___CLM__DBUSER.T_QC_TYPE" whereClause="1=1" orderBy="decode(code,'0','2','1','1',code)"></Field:codeTable>
						</dd>
					</dl>
					<dl>
						<dt><font class="point" color="red">* </font>有效期标志</dt>
						<dd>
							<input id="clmAfterCheckClickId" name="claimAfcItemVO.validFlag" style="border:0px;background:0px;width:auto; float: left;"
								type="checkbox" value="${claimAfcItemVO.validFlag}"
								onclick="clmAfterCheckClick();" />有效
						</dd>
					</dl>
					<dl>
						<dt><font class="point" color="red">* </font>质检项目名称</dt>
						<dd>
							<input id="findItemNameId" name="claimAfcItemVO.itemName" type="text"
								value="${claimAfcItemVO.itemName}" onkeydown="this.onkeyup();" onkeyup="this.size=(this.value.length>4?this.value.length:4);" size="4" />
						</dd>
					</dl>
					<dl>
						<dt>制定日期</dt>
						<dd>
							<input id="creatDateId" name="claimAfcItemVO.creatDate"
								value="<s:date name="claimAfcItemVO.creatDate" format="yyyy-MM-dd"/>"
								disabled="disabled" /><a class="inputDateButton"
								href="javascript:;" disabled="disabled">选择</a> <input
								name="claimAfcItemVO.creatDate"
								value="${claimAfcItemVO.creatDateStr}" type="hidden" />
						</dd>
					</dl>
					<dl>
						<dt>制定人</dt>
						<dd>
							<input id="userIdId" disabled="disabled" style="width: 30px;border-right:0px" value="${claimAfcItemVO.userId}">
							<input id="userNameId" disabled="disabled" style="width:110px;" value="${claimAfcItemVO.userName}">
						</dd>
					</dl>
				</div>
		
		<div class="divfclass">
			<h1><img src="clm/images/tubiao.png">质检要点管理</h1>
		</div>
			<div class="panelPageFormContent">
				<dl>
					<dt><font class="point" color="red">* </font>质检项目名称</dt>
					<dd>
						<input name="claimAfcItemVO.itemId" value="" id="itemIdId" style="width: 30px;border-right:0px"
							disabled="disabled" />
						<input id="findItemName" value="" disabled="disabled" style="width:110px;" />
					</dd>
				</dl>
				<dl>
					<dt>质检类型</dt>
					<dd>
						<input id="findCheckTypeId" value="" size="3" disabled="disabled"/>
					</dd>
				</dl>
			</div>
		
		<div class="divfclass">
			<h1><img src="clm/images/tubiao.png">质检要点列表</h1>
		</div>
				<div class="tabdivclassbr">
				<table class="list" width="100%">
					<thead>
						<tr>
							<th type="text" name="items[#index#].itemInt" readonly
								defaultVal="#index#" onkeydown="this.onkeyup();" onkeyup="this.size=(this.value.length>4?this.value.length:4);" size="4" fieldClass="digits">序号</th>
							<th>质检要点说明</th>
							<!-- <th>有效标志</th> -->
							<th>操作</th>
						</tr>
					</thead>
					<tbody id="claimAfcChcekIdId">
						<s:if test="claimAfcGistList.size() eq 0">
								<tr>
									<td>1</td>
									<td><input id='gistDescCheckId' name="claimAfcGistList[0].gistDesc" type="text" ></td>
									<!-- <td><input id='checkboxValidFlag' type='checkbox' name='claimAfcGistList[0].validFlag' value='0' onclick="checkboxValidFlags(this);" style="border:0px;background:0px;width:auto;" />有效
									</td> -->
									<input type='hidden' id='isUpdate' name='claimAfcGistList[0].isUpdate' value='1'>
									<td><a title='编辑' class='btnEdit' id='editButton' href='javascript:void(0);' onclick='editds(this);'>编辑</a>
									<a title='添加' class='btnAdd' id='addButton' href='javascript:void(0);' onclick='addClaimAfter(this);'>添加</a></td>
								</tr>
						</s:if>	
					</tbody>
				</table>
				</div>

		<div class="formBarButton">
			<ul>
				<li><button type="reset"class="but_gray" onclick="cliamAfterCheckRest();">重置</button></li>
				<li><button type="button"class="but_blue" onclick="saveClaimAfcItem();">保存</button></li>
				<li><button type="reset"class="but_gray" onclick="exit();">退出</button></li>
			</ul>
		</div>
		</form>
	</div>
</div>
