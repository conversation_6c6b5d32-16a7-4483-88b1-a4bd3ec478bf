<%@page import="java.math.BigDecimal"%>
<%@ page language="java" pageEncoding="UTF-8"%><%@ include file="/clm/pages/common/commonJsCss.jsp" %>
<%@ taglib uri="/struts-tags" prefix="s"%>
<script type="text/javascript">
$('#claimRegistChangeProp', navTab.getCurrentPanel()).toggle(
	  function () {
		  if($('#claimRegisterCmdiv', navTab.getCurrentPanel()).css("display") == "none"){
			  MM_changeProp('claimRegisterCmdiv','','display','block','DIV');
			  } else {
				  MM_changeProp('claimRegisterCmdiv','','display','none','DIV');
			  }
		  },
	  function () {
		  if($('#claimRegisterCmdiv', navTab.getCurrentPanel()).css("display") == "none"){
			  MM_changeProp('claimRegisterCmdiv','','display','block','DIV');
			  } else {
				  MM_changeProp('claimRegisterCmdiv','','display','none','DIV');
			  }
	  }
);
function MM_changeProp(objId,x,theProp,theValue) { //v9.0
	$("#"+objId).css("position",'relative'); 
	$("#"+objId).css("z-index",'1'); 
var obj = null; with (document){ if (getElementById)
obj = getElementById(objId); }
if (obj){
  if (theValue == true || theValue == false)
    eval("obj.style."+theProp+"="+theValue);
  else eval("obj.style."+theProp+"='"+theValue+"'");
}
}
function hideAlertInfo(obj) { //v9.0
	if($("div",navTab.getCurrentPanel()).attr("id")!=obj){
		$(obj).hide();
	}
}
var isApprove = "approve";
//上一步中的标识
var presave = 0;
//影像查询
function queryApproveClaimImages() {
	var caseId = $("#caseId",navTab.getCurrentPanel()).val();
	$.post("clm/common/queryClaimImages_CLM_commonQueryAction.action",{caseId:caseId},function(data){
		if(data.message!=""){
			//不等于空点击返回的url
			var urlValue= data.message;//替换掉&符号
			window.open(urlValue);
		}else{
		   alertMsg.error("调用影像接口失败");
		}
	},'json');
}

//预付查询  xuyz_wb add
function queryBranchFlowApprove(){
	var caseId = $("#caseId",navTab.getCurrentPanel()).val();
	var url = "clm/common/queryBranchAdvance_CLM_queryBranchFlowAction.action?caseId="
			+ caseId;
	$("#queryBranchFlowApprove",navTab.getCurrentPanel()).attr("href", url).click();
}

//查看报案、立案、签批信息
function caseQueryPage(){
	var caseId=$("#caseId", navTab.getCurrentPanel()).val();
	var url="clm/common/caseCommonQueryPage_CLM_commonQueryAction.action?caseId="+caseId;
	$("#rePortRegisSignId", navTab.getCurrentPanel()).attr("href",url).click();		
}

//任务时效查看 huangjh_wb
function queryTaskTraceApprove() {
	var caseId = $("#caseId",navTab.getCurrentPanel()).val();
	var url = "clm/taskmanage/queryTaskTraceInitByCaseNo_CLM_queryTaskTraceAction.action?caseId="
			+ caseId +"&taskTraceFlag=taskTraceFlag";
	$("#queryTaskTraceApprove",navTab.getCurrentPanel()).attr("href", url).click();
}
//既往赔案查询
function alwaysClaim(){
	var caseId=$("#caseId", navTab.getCurrentPanel()).val();
	var url="clm/common/queryAlwaysClaim_CLM_commonQueryAction.action?caseId="+caseId;
	$("#comAlwaysClaim", navTab.getCurrentPanel()).attr("href",url).click();
} 

//备注信息查看
function getMemoInspectAfter() {
	var caseId=$("#caseId", navTab.getCurrentPanel()).val();
	var url = "clm/memo/memoView_CLM_memoAction.action?clmCaseVO.caseId="+caseId;
	$("#memoInspectAfter", navTab.getCurrentPanel()).attr("href", url).click();
}

//保单查询
function policQueryPage(){
	var caseId=$("#caseId",navTab.getCurrentPanel()).val();
	var url="clm/common/initPolicQueryPage_CLM_commonQueryAction.action?caseId="+caseId;
	$("#policQueryId",navTab.getCurrentPanel()).attr("href",url).click();		
}

/* function MM_changeProp(objId,x,theProp,theValue) { //v9.0
	  var obj = null; with (document){ if (getElementById)
	  obj = getElementById(objId); }
	  if (obj){alert(obj);  alert(theProp); alert(theValue);
	    if (theValue == true || theValue == false)
	      eval("obj.style."+theProp+"="+theValue);
	    else eval("obj.style."+theProp+"='"+theValue+"'");
	  }
	} */
	
//跳转到下一个选项卡方法，参数id为要跳转到的选项卡的Id
function next_approve(id,caseId){
	//将当前页面的参数传递给下一界 面
	if(id=='2'){
		document.getElementById(id).href="clm/audit/approvecontractDealInit_CLM_contractDealAction.action?caseId="+caseId+"&afterFlag=1";
	}
	if(id=='3'){
		document.getElementById(id).href="clm/approve/approveConclusionInit_CLM_approveConclusionAction.action?caseId="+caseId+"&afterFlag=1";
	}
	//将要跳转到的隐藏的选项卡显示
	$("#"+id,navTab.getCurrentPanel()).css("display","block");
	//调用选项卡的click方法，将页面跳转至下个选项卡中
	$("#"+id,navTab.getCurrentPanel()).click();
	
	//用于进度条显示
	$("#step"+id,navTab.getCurrentPanel()).attr("class","step"); 
	$("#step"+(id-1),navTab.getCurrentPanel()).attr("class","step_prev");	
	$("#n"+id,navTab.getCurrentPanel()).attr("class","n"+id+"d");		 
	$("#n"+(id-1),navTab.getCurrentPanel()).attr("class","n"+(id-1)+"e");
	$("#step"+(id-1),navTab.getCurrentPanel()).attr("class","step_prev");
}
//跳转上一步
function prev_approve(id,caseId){
	//将当前页面的参数传递给上一界面
	if(id=='1'){
		document.getElementById(id).href="clm/register/approveDataCollectInit_CLM_dataCollectAction.action?caseId="+caseId+"&afterFlag=1";
	}
	if(id=='2'){
		document.getElementById(id).href="clm/audit/approvecontractDealInit_CLM_contractDealAction.action?caseId="+caseId+"&afterFlag=1";
	}
	//调用选项卡的click方法，将页面跳转至下个选项卡中
	$("#"+id,navTab.getCurrentPanel()).click();
	
	//用于进度条显示
	$("#step"+id,navTab.getCurrentPanel()).attr("class","step"); 
	$("#step"+(id-1),navTab.getCurrentPanel()).attr("class","step_prev");	
	$("#n"+id,navTab.getCurrentPanel()).attr("class","n"+id+"d");		 
	$("#n"+(id-1),navTab.getCurrentPanel()).attr("class","n"+(id-1)+"e");
	$("#step"+(id-1),navTab.getCurrentPanel()).attr("class","step_prev");
}
</script>
<!--获取url中的CaseNo -start -->
<!--获取url中的CaseNo -end -->
<!--定义字符集  -->
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<div id="signRegister" layoutH="20">
<div class="pageContent">
	<div class="tabs" currentIndex="0" eventType="click">
<!-- 		<div class="tabsHeader">
			<div class="tabsHeaderContent"> -->
				<!-- <div id="signRegisterCmdiv" style="display:none;" onmouseup="MM_changeProp('signRegisterCmdiv','','display','none','DIV')"> -->
				<div id="claimRegisterCmdiv" style="display:none;" onmouseleave="hideAlertInfo(claimRegisterCmdiv);" onmouseup="MM_changeProp('claimRegisterCmdiv','','display','none','DIV')">
             		 <dl class="colummenu" id="signRegisterCm">
						<dt>
							<a href="javaScript:void(0)" onclick="caseQueryPage();">查看报案信息</a>
							<a id="rePortRegisSignId" href="#" target="navTab" title="查看报案信息"></a>
						</dt>
	                    <dt>
	                    	<a href="javaScript:void(0)" onclick="policQueryPage();">保单查询</a>
	                    	<a id="policQueryId" href="#" target="navTab" title="保单查询"></a>
	                    </dt> 
	                    <dt>
	                    	<a href="javaScript:void(0)" onclick="getMemoInspectAfter();">备注信息查看</a>
	                    	<a id="memoInspectAfter" href="#" lookupGroup=""></a>
	                    </dt>
	                    <dt>
	                    	<a href="javaScript:void(0)"  onclick="alwaysClaim()">既往赔案查询</a>
	                    	<a id="comAlwaysClaim" href="#" lookupGroup=""></a>
	                    </dt>
	                    <dt>
	                    	<a href="javaScript:void(0)" onclick="queryApproveClaimImages()">影像查询</a>
	                    </dt>
	                    <dt>
							<a href="javaScript:void(0)" onclick="queryBranchFlowApprove();">预付查询</a>
							<a id="queryBranchFlowApprove" rel="queryBranchFlowApprove" href="#" target="dialog" style="display: none;" width="1000" height="450">预付查询</a>
						</dt>
	                    <dt>
	                    	<a href="javaScript:void(0)" onclick="queryTaskTraceApprove();">时效查看</a>
	                    	<a id="queryTaskTraceApprove" rel="queryTaskTraceApprove" href="#" target="dialog" [mask=true ] style="display: none;" width="1000" height="450">时效查看</a>
						</dt>
			  		</dl>
            	</div>
				<div class="tabsHeader">
				<div class="tabsHeaderContent">
					<%-- <a class="button" style="float: right;" id="" href="#" onclick="MM_changeProp('signRegisterCmdiv','','display','block','DIV')"><span>!</span></a> --%>
					<a class="button" style="float: right;" id="claimRegistChangeProp" href="#" ><span>!</span></a>
					<ul>
					   <li><a href="clm/audit/claimAfterCheckIni_CLM_claimAfterCheckIntegrationAction.action?caseId=${caseId}" class="j-ajax" id="4"><span>事后质检处理与反馈</span></a></li>
					<li><a href="clm/register/approveDataCollectInit_CLM_dataCollectAction.action?caseId=${caseId}&afterFlag=1" class="j-ajax" id="1"><span>核对案件信息</span></a></li>
					<li><a href="clm/audit/approvecontractDealInit_CLM_contractDealAction.action?caseId=${caseId}&afterFlag=1" class="j-ajax" id="2"><span>合同处理</span></a></li>
				   	<li><a href="clm/approve/approveConclusionInit_CLM_approveConclusionAction.action?caseId=${caseId}&afterFlag=1" class="j-ajax" id="3"><span>出具审批结论</span></a></li>
					</ul>
				</div>
			   </div>
			<!-- </div>
		</div> -->
		<div class="tabsContent">
			<div id="caseQueryTabs1"></div>
			<div id="caseQueryTabs2"></div>
			<div id="caseQueryTabs3"></div>
			<div id="caseQueryTabs4"></div>
		</div>
	</div>
	<div class="tabsFooter">
		<div class="tabsFooterContent"></div>
	</div>
</div>
</div>