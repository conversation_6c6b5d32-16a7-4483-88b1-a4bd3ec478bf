//声明一个全局常量
var shu = 1;
//查询质检项目和质检要点
function queryClaimAfterCheckProject() {
	var checkType = $("#checkType", navTab.getCurrentPanel()).val();
	if(checkType == "" || checkType == null){
		alertMsg.error("必须有一项查询条件!");
	} else {
		$("#checkTypeId", navTab.getCurrentPanel()).val(checkType);
		$("#findClaimAfterCheck", navTab.getCurrentPanel()).submit();
	}
}
// 点击按钮查询信息
function findClaimAfterCheckProject(itemId) {
	var item = $(itemId).val();
	$("#claimAfterCheckItemIdId", navTab.getCurrentPanel()).val(item);
	$.ajax({
				'url' : 'clm/inspect/findClaimAfterCheckProject_CLM_claimAfterCheckProjectAction.action?itemId='
						+ item,
				'type' : 'post',
				'datatype' : 'json',
				'success' : function(data) {
					var data = eval("(" + data + ")");
					$("#findCheckType", navTab.getCurrentPanel()).val(
							data.checkType);
					$("#itemIdId", navTab.getCurrentPanel()).val(data.itemId);
					$("#findItemNameId", navTab.getCurrentPanel()).val(
							data.itemName);
					$("#findItemName", navTab.getCurrentPanel()).val(
							data.itemName);
					$("#userIdId", navTab.getCurrentPanel()).val(data.userId);
					$("#userNameId", navTab.getCurrentPanel()).val(
							data.userName);
					$("#findCheckTypeId", navTab.getCurrentPanel()).val(
							data.checkTypeStr);
					$("#clmAfterItemIdId", navTab.getCurrentPanel()).val(
							data.itemId);
					if (data.validFlag == 1) {
						$("#clmAfterCheckClickId", navTab.getCurrentPanel()).attr("checked", "checked");
						$("#clmAfterCheckClickId", navTab.getCurrentPanel()).val("1");
					} else {
						$("#clmAfterCheckClickId", navTab.getCurrentPanel()).val("0");
						$("#clmAfterCheckClickId", navTab.getCurrentPanel()).attr("checked", false);
					}
					$("#claimAfcChcekIdId", navTab.getCurrentPanel()).empty();
					shu = 1;
					for ( var i = 0; i < data.claimAfcGistVOList.length; i++) {
						var a = "";
						var b = 0;
						if (data.claimAfcGistVOList[i].validFlag == 1) {
							a = "checked='checked'";
							b = 1;
						}
						insertHtml = "<tr>"
								+ "<td>"
								+ (i + 1)
								+ "<input name='claimAfcGistVO.gistId' value="
								+ data.claimAfcGistVOList[i].gistId
								+ " type='hidden'></td>"
								+ "<th>"
								+ data.claimAfcGistVOList[i].gistDesc
								+ "<input name='claimAfcGistVO.gistDesc' value="
								+ data.claimAfcGistVOList[i].gistDesc
								+ " type='hidden'></td>"
								/*+ "<td><input id='checkboxValidFlag' type='checkbox' "
								+ a
								+ " disabled='disabled' value="
								+ b
								+ " name='claimAfcGistVO.validFlag' onclick='checkboxValidFlags();'/>有效</td>"*/
								+ "<td><a title='编辑' class='btnEdit' id='editButton' href='javascript:void(0);' onclick='editds(this);'>编辑</a>"
								+ "<a title='添加' class='btnAdd' id='addButton' href='javascript:void(0);' onclick='addClaimAfter(this);'>添加</a>"
								+ "</td>" + "</tr>";
						$("#claimAfcChcekIdId", navTab.getCurrentPanel())
								.append(insertHtml);
						 shu = i+2;
					}
				}
			});
}
// 保存
function saveClaimAfcItem() {
	//至少录入一条质检要点
	var isOK = false;
	$("#claimAfcChcekIdId", navTab.getCurrentPanel()).find("tr").each(function (){
		var AfcGist = $(this).find("td:eq(1)").find("input").val().trim();
		if(AfcGist != ""){
			isOK = true;
		}
	});
	if(!isOK){
		alertMsg.error("请录入质检要点");
		return false;
	}
	//判断是否有相同的质检要点名称
	var sameFlag = false;
	var length = $("#claimAfcChcekIdId", navTab.getCurrentPanel()).find("tr").length;
	
	for(var i = 0; i < length; i++){
		var obj1 = $("#claimAfcChcekIdId", navTab.getCurrentPanel()).find("tr:eq("+i+")").find("td:eq(1)").find("input").val().trim();
		for(var j = i+1; j < length; j++){
			var obj2 = $("#claimAfcChcekIdId", navTab.getCurrentPanel()).find("tr:eq("+j+")").find("td:eq(1)").find("input").val().trim();
			if(obj1 == obj2){
				sameFlag = true;
				break;
			}
		}
		if(sameFlag){
			break;
		}
	}
	if(sameFlag){
		alertMsg.error("您录入的质检要点<要点名称>有重复数据，请重新录入");
		return false;
	}
	if($("#findCheckType", navTab.getCurrentPanel()).val() == null || $("#findCheckType", navTab.getCurrentPanel()).val() == ""){
		alertMsg.error("质检类型必选!");
		return false;
	}
	if ($("#findItemNameId", navTab.getCurrentPanel()).val() == null || $("#findItemNameId", navTab.getCurrentPanel()).val() == "") {
		alertMsg.error("质检项目名称必填!");
	} else {
		//alert($("#findItemNameId", navTab.getCurrentPanel()).val());
		$("#saveClaimAfcItem", navTab.getCurrentPanel()).submit();
	}
}

// 保存的回调函数
function saveAjaxDone(json) {
	if (json.statusCode == DWZ.statusCode.ok) {
		addCheck();
		alertMsg.correct(json.message);
	} else {
		alertMsg.error(json.message);
	}
}

// 选中复选框时赋值
function clmAfterCheckClick() {
	if ($("#clmAfterCheckClickId", navTab.getCurrentPanel()).attr("checked") == "checked") {
		$("#clmAfterCheckClickId", navTab.getCurrentPanel()).val("1");
	} else {
		$("#clmAfterCheckClickId", navTab.getCurrentPanel()).val("0");
	}
}
// 触发质检要点复选框时赋值
function checkboxValidFlags(k) {
	$(k, navTab.getCurrentPanel()).val("0");
	if($(k, navTab.getCurrentPanel()).attr("checked") == "checked"){
		$(k, navTab.getCurrentPanel()).val("1");
	}
}

// 编辑图标
function editds(k) {
	$(k).parent().parent().find("td:eq(2)").removeAttr("disabled");
	$(k).parent().parent().find("input#isUpdate").val(1);
}
// 重置
function cliamAfterCheckRest() {
	$("#cliamAfterCheckRest", navTab.getCurrentPanel()).submit();
}
//退出
/*function cliamAfterCheckExit() {
	 alertMsg.confirm("当前页面录入的信息将不被保存，是否确定退出？", {
	 	okCall : function() {
			navTab.closeCurrentTab();
	 	}
	 });
}*/

//添加
function addClaimAfter(k){
	var index =$(k).parent().parent().parent().find("tr:last").find("td:eq(0)").text();
	var indexk = 1+(index*1);
	$(k).parent().find("a:eq(1)").remove();
	insertHtml = "<tr>"
		+ "<td>"+ indexk + "</td>"
		+ "<td><input type='text' id='gistDescCheckId' class='textInput' name='claimAfcGistList["+index+"].gistDesc'><input type='hidden' id='isUpdate' name='claimAfcGistList["+(indexk-1)+"].isUpdate' value='1'></td>"
		/*+ "<td><input type='checkbox' name='claimAfcGistList["+index+"].validFlag' value='0' onclick='checkboxValidFlags(this)' style='border:0px;background:0px;width:auto;'/>有效</th><input type='hidden' id='isUpdate' name='claimAfcGistList["+(indexk-1)+"].isUpdate' value='1'></td>"*/
		+ "<td><a title='编辑' class='btnEdit' id='editButton' href='javascript:void(0);' onclick='editds(this);'>编辑</a>"
		+ "<a title='添加' class='btnAdd' id='addButton' href='javascript:void(0);' onclick='addClaimAfter(this);'>添加</a>"
		+ "</td>" + "</tr>";
	$("#claimAfcChcekIdId", navTab.getCurrentPanel()).append(insertHtml);
}

// 新增
function addCheck() {
	$("#addClaimAfterCheck", navTab.getCurrentPanel()).submit();
}