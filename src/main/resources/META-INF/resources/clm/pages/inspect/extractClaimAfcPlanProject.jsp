<%@ page language="java" contentType="text/html; charset=utf-8"
	pageEncoding="UTF-8"%><%@ include file="/clm/pages/common/commonJsCss.jsp" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/fmt" prefix="fmt"%>
<%@ taglib uri="/struts-tags" prefix="s"%>

<%@ taglib uri="http://www.git.com.cn/taglib/udmp/field" prefix="Field"%>
<script type="text/javascript" src="clm/pages/inspect/extractClaimAfcPlanProject.js"></script>
<script type="text/javascript" src="clm/scripts/common/selectValueSearch.js"></script>

<script type="text/javascript">
var AfcplanId = <%=request.getAttribute("claimAfcPlanVO.planId")%>
var Afcplantype = <%=request.getAttribute("claimAfcPlanVO.planType")%>

//险种快速查询
/* var val_flag = "";  //定义一个标识，防止多次重复验证
$("#businessSearchId", navTab.getCurrentPanel()).bind("input propertychange",function(){
	var value = $("#businessSearchId", navTab.getCurrentPanel()).val();
	if(value != val_flag){
		val_flag = value;
		var optionStr = "";
		for(var i=0;i<_businessArray.length;i++){
			var obj = _businessArray[i];
			var text = obj.productCodeSys + "-" + obj.productNameSys;
			if(text.indexOf(value) != "-1"){
				optionStr = optionStr + "<option value='"+ obj.productCodeSys +"'>"+ obj.productCodeSys + "-" + obj.productNameSys + "</option>";
			}
		}
		$("#allProductEXT", navTab.getCurrentPanel()).html("");
		$("#allProductEXT", navTab.getCurrentPanel()).append(optionStr);
	}
});

//片区快速查询
var appendItem_flag = "";  //定义一个标识，防止多次重复验证
$("#appendItemSearchId", navTab.getCurrentPanel()).bind("input propertychange",function(){
	var value = $("#appendItemSearchId", navTab.getCurrentPanel()).val();
	if(value != appendItem_flag){
		appendItem_flag = value;
		var optionStr = "";
		for(var i=0;i<_apendItemArray.length;i++){
			var obj = _apendItemArray[i];
			var text = obj.areaCode + "-" +  obj.areaName;
			if(text.indexOf(value) != "-1"){
				optionStr = optionStr + "<option value='" + obj.areaCode + "'>" + obj.areaCode + "-" + obj.areaName + "</option>";
			};
		};
		$("#selectedAreaEXT", navTab.getCurrentPanel()).html("");
		$("#selectedAreaEXT", navTab.getCurrentPanel()).append(optionStr);
	}
});


//机构快速查询
var organ_flag = "";  //定义一个标识，防止多次重复验证
$("#organSearchId", navTab.getCurrentPanel()).bind("input propertychange", function(){
	var value = $("#organSearchId", navTab.getCurrentPanel()).val();
	if(value != organ_flag){
		organ_flag = value;
		var optionStr = "";
		for(var i=0;i<_organArray.length;i++){
			var obj = _organArray[i];
			var text = obj.organCode + "-" +  obj.organName;
			if(text.indexOf(value) != "-1"){
				optionStr = optionStr + "<option value='" + obj.organCode + "'>" + obj.organCode + "-" + obj.organName + "</option>";
			};
		};
		$("#selectedOrganizationEXT", navTab.getCurrentPanel()).html("");
		$("#selectedOrganizationEXT", navTab.getCurrentPanel()).append(optionStr);
	};
});


//定义险种集合
var _businessArray = new Array();

//定义片区集合
var _apendItemArray = new Array();

//定义机构集合
var _organArray = new Array(); */

</script>
<%-- 
<s:iterator value="businessProductVOs" var="bus">
	<script>
		var obj = new Object();
		obj.productCodeSys = '<s:property value="#bus.productCodeSys"/>';
		obj.productNameSys = '<s:property value="#bus.productNameSys"/>';
		_businessArray.push(obj);
	</script>
</s:iterator>

<s:iterator value="areaVOList" var="item">
	<script>
		var obj = new Object();
		obj.areaCode = '<s:property value="#item.areaCode"/>';
		obj.areaName = '<s:property value="#item.areaName"/>';
		_apendItemArray.push(obj);
	</script>
</s:iterator>

<s:iterator value="orgVOList" var="org">
	<script>
		var obj = new Object();
		obj.organCode = '<s:property value="#org.organCode"/>';
		obj.organName = '<s:property value="#org.organName"/>';
		_organArray.push(obj);
	</script>
</s:iterator> --%>

<div class="pageContent pageHeader" layouth="10">
	<div class="divfclass">
		<h1><img src="clm/images/tubiao.png">事后质检任务抽取</h1>
	</div>
		<div class="divfclass">
			<h1><img src="clm/images/tubiao.png">查询条件</h1>
		</div>
	<div class="pageFormInfoContent">
		<form
			action="clm/inspect/queryExtractClaimAfcPlanProject_CLM_extractClaimAfcPlanProjectAction.action"
			method="post" class="pageForm required-validate" id="queryExtractClaimAfcId"
			onsubmit="return  navTabSearch(this)">
			
			
			
			<dl>
				<dt><label><font class="point" color="red">* </font>质 检 计 划 名 称</label></dt>
				<dd>
					<select class="combox"  id="selectExtract" name="claimAfcPlanVO.planName" onChange="matchPlanType(this);">
					    <option value="" >请选择</option>
						<s:iterator value="claimAfcPlanVOList" var="claimAfcPlan">
							<option value="${planName}" <s:if test="claimAfcPlanVO.planName == planName">selected</s:if> >${planName}</option>
						</s:iterator>
					</select>
				</dd>
			</dl>
			<dl>
				<dt><label><font class="point" color="red">* </font>质 检类 型 </label></dt>
				<dd>
					<Field:codeTable cssClass="combox title" 
						name="claimAfcPlanVO.planType" value="${claimAfcPlanVO.planType}"
						tableName="APP___CLM__DBUSER.T_QC_TYPE" defaultValue="1" id="claimAfcPlanProjecPlanTypeId"
						onChange="matchPlanName(this);"></Field:codeTable>
 						<input value="${strList}" id="strListId" type="hidden"/>
				</dd>
			</dl>
			<div class="pageFormdiv"><button type="button" class="but_blue" onclick="queryExtractRule()" value="查询">查询</button></div>
		</form>
	</div>
	<form action="clm/inspect/extractClaimCase_CLM_extractClaimAfcPlanProjectAction.action"
			method="post" class="pageForm required-validate" id="extractClaimCaseId"
			onsubmit="return  navTabSearch(this)">
	<div class="divfclass">
		<h1><img src="clm/images/tubiao.png">事后质检抽取条件</h1>
	</div>
		<div >
			<table width="100%" style="margin-left: 85px" >
				<tbody id="caseFlagId">
				<tr>
					<td></td>
				</tr>
				</tbody>
			</table>
		</div>
		<div class="panelPageFormContent main_tabdiv">
			<dl>
				<dt><font >* </font>审批起止日期<input type="hidden" name="claimAfcPlanVO.planType" value="${claimAfcPlanVO.planType}"/><input type="hidden" value="${claimAfcPlanVO.planId}" name="claimAfcPlanVO.planId"/></dt>
				<dd>
					<input class="date" id="dateMinId" name="middleCheckConditionVO.approveStartDate"  type="expandDateYMD"/> <a class="inputDateButton"
						href="javascript:;">选择</a>
					<span style="padding-left:5px;">至</span>
				</dd>
				<dd>
					<input class="date" id="dateMaxId" name="middleCheckConditionVO.approveEndDate"  type="expandDateYMD"/> <a class="inputDateButton"
						href="javascript:;">选择</a>
				</dd>
			</dl>
		</div>
		<div class="panelPageFormContent">
				<dl>
					<dt>抽检方式</dt>
					<dd>	
						<Field:codeTable cssClass="combox title"  id="extractRateType"
							name="claimAfcPlanVO.extractRateType" value="${claimAfcPlanVO.extractRateType}"
							tableName="APP___CLM__DBUSER.T_QC_EXTRACTRATETYPE" disabled="true"/>
					</dd>
				</dl>
				<s:if test="claimAfcPlanVO.extractRateType == 1">
					<dl id="extractRateDiv">
						<dt>抽检率%</dt>
						<dd>
							<input name="claimAfcPlanVO.extractRate" id="extractRate"
								type="text" class="organ" value="${claimAfcPlanVO.extractRate}"
								onchange="changeeExtractRate(this.value)" readonly="readonly"/>
						</dd>
					</dl>
				</s:if>
				<s:if test="claimAfcPlanVO.extractRateType == 2">
					<dl id="inspectionStandardDiv">
						<dt>检查水平</dt>
						<dd>
							<Field:codeTable cssClass="combox title"  id="inspectionStandard"
								name="claimAfcPlanVO.inspectionStandard" value="${claimAfcPlanVO.inspectionStandard}"
								tableName="APP___CLM__DBUSER.T_QC_INSPECTIONSTANDARD" disabled="true"/>
						</dd>
					</dl>
					<dl id="inspectionLevelDiv">
						<dt>检查级别</dt>
						<dd id="inspectionLevelDd">
							<Field:codeTable cssClass="combox title"  id="inspectionLevelnumber"
								name="claimAfcPlanVO.inspectionLevelnumber" value="${claimAfcPlanVO.inspectionLevelnumber}"
								tableName="APP___CLM__DBUSER.T_INSPECTION_LEVELNUMBER" disabled="true"/>
						</dd>
					</dl>
				</s:if>
			</div>
	<div class="divfclass">
		<h1><img src="clm/images/tubiao.png">操作人权限区间</h1>
	</div>
		<div class="tabdivclassbr">
			<table class="list" width="100%">
				<thead>
					<tr>
						<th nowrap>序号</th>
						<th nowrap>权限类型</th>
						<th nowrap>权限区间</th>
					</tr>
				</thead>
				<tbody>
					<s:iterator value="claimAfcAuthVOList" var="claimAfcAuth"
						status="st">
						<tr>
							<td><div align="center">${st.index+1}</div></td>
							<td>
							    <s:if test="permissionTypeCode eq 1">审批权限（疑难）</s:if>
							    <s:if test="permissionTypeCode eq 2">审批权限（普通）</s:if>
							    <s:if test="permissionTypeCode eq 3">审核权限</s:if>
							    <input type="hidden" value="${permissionTypeCode }"  name="claimAfcAuthVOList[<s:property value="#st.index"/>].permissionTypeCode" />
							</td>
							<td><div align="center">
								<input type="hidden" name="claimAfcAuthVOList[<s:property value="#st.index"/>].authMin" value="${authMin}"/>
									${authMin}至
								<input type="hidden" name="claimAfcAuthVOList[<s:property value="#st.index"/>].authMax" value="${authMax}"/>
									${authMax}</div>
							</td>
						</tr>
					</s:iterator>
				</tbody>
			</table>
		</div>
			<!-- <div class="pageFormdiv"><button type="button" class="but_blue" disabled="disabled">添加</button></div> -->
	
	<div class="divfclass">
		<h1><img src="clm/images/tubiao.png">操作机构</h1>
	</div>
		<div class="tabdivclassbr">
			<table class="list" width="100%">
				<thead>
					<tr>
						<th nowrap>序号</th>
						<th nowrap>操作机构代码</th>
						<th nowrap>操作机构名称</th>
					</tr>
				</thead>
				<tbody>
					<s:iterator value="claimAfcOrgVOList" var="claimAfcOrg" status="st">
						<tr>
							<td><div align="center">
									${st.index+1}
								</div></td>
							<td><div align="center">
									${organCode}<input type="hidden" name="claimAfcOrgVOList[<s:property value="#st.index"/>].organCode" value="${organCode}"/>
								</div></td>
							<td><div align="center">
									<Field:codeValue tableName="APP___CLM__DBUSER.T_UDMP_ORG" value="${organCode}" />
								</div></td>
						</tr>
					</s:iterator>
				</tbody>
			</table>
		</div>
	
	<div class="divfclass">
		<h1><img src="clm/images/tubiao.png">操作员</h1>
	</div>
		<div class="tabdivclassbr">
			<table class="list" width="100%">
				<thead>
					<tr>
						<th nowrap>序号</th>
						<th nowrap>操作员代码</th>
						<th nowrap>操作员姓名</th>
					</tr>
				</thead>
				<tbody>
					<s:iterator value="claimAfcOprVOList" var="claimAfcOpr" status="st">
						<tr>
							<td><div align="center">
									${st.index+1}
								</div></td>
							<td><div align="center">${userName}<input type="hidden" name="claimAfcOprVOList[<s:property value="#st.index"/>].checkPer" value="${checkPer}"/></div></td>
							<td><div align="center">
									${realName}
								</div></td>
								</div></td>
						</tr>
					</s:iterator>
				</tbody>
			</table>
		</div>
<!-- 			<div class="pageFormdiv"><button type="button" class="but_blue" disabled="disabled">添加</button></div> -->
	
	<div class="panelPageFormContent">
		<dl>
			<dt>抽取层级</dt>
			<dd>
				<Field:codeTable cssClass="combox title"  id="claimextractLevelId"
			name="claimAfcPlanVO.extractLevel"
			value="${claimAfcPlanVO.extractLevel}" tableName="APP___CLM__DBUSER.T_QC_LEVEL"  disabled="true"/>
			</dd>
		</dl>
	</div>

	<div id="clmExtractCaseId">
		<div class="divfclass">
			<h1><img src="clm/images/tubiao.png">按赔案层级</h1>
		</div>
		<dl>
			<dt></dt>
			<dd>
			
			</dd>
		</dl>
		<div class="panelPageFormContent">
		<dl style="width: 100%;">
			<dt>理赔类型</dt>
			<dd style="width: 60%;">
				<span>
				<input style="border:0px;background:0px;width:auto; float: left;" type="checkbox"
								<s:if test="1 in strTypeList">checked="checked"</s:if>
								name="middleCheckConditionVO.claimType" value="01" disabled="false" id="dieId"/>身故
								</span>
								<span>
								<input
								style="margin-left: 10px" type="checkbox"
								<s:if test="4 in strTypeList">checked="checked"</s:if>
								name="middleCheckConditionVO.claimType" value="04" disabled="false" id="hightCanId"/>高残
								</span>
								<span>
								<input
								style="margin-left: 10px" type="checkbox"
								<s:if test="3 in strTypeList">checked="checked"</s:if>
								name="middleCheckConditionVO.claimType" value="03" disabled="false" id="importDiseaseId"/>重大疾病
								</span>
								<span>
								<input
								style="margin-left: 10px" type="checkbox"
								<s:if test="2 in strTypeList">checked="checked"</s:if>
								name="middleCheckConditionVO.claimType"
								value="02" disabled="false" id="mayhemId"/>伤残
								</span>
								<span>
								<input style="margin-left: 10px" type="checkbox"
								<s:if test="8 in strTypeList">checked="checked"</s:if>
								name="middleCheckConditionVO.claimType" value="08" disabled="false" id="medicalTreatmentId"/>医疗
								</span>
								<span>
								<input
								style="margin-left: 10px" type="checkbox"
								<s:if test="10 in strTypeList">checked="checked"</s:if>
								name="middleCheckConditionVO.claimType" value="10"disabled="false" id="specialDiseaseId"/>特种疾病
								</span>
								<span>
								<input
								style="margin-left: 10px" type="checkbox"
								<s:if test="6 in strTypeList">checked="checked"</s:if>
								name="middleCheckConditionVO.claimType" value="06" disabled="false" id="commonAbilityId"/>一般失能
								</span>
								<span>
								<input
								style="margin-left: 10px" type="checkbox"
								<s:if test="7 in strTypeList">checked="checked"</s:if>
								name="middleCheckConditionVO.claimType"
								value="07" disabled="false" id="importantAbilityId" />重大失能
								</span>
			</dd>
		</dl>
		<dl>
			<dt>出险原因</dt>
			<dd>
								<span>
				<input style="border:0px;background:0px;width:auto; float: left;" type="checkbox"
								<s:if test="2 in reasonStrList">checked="checked"</s:if>
								name="middleCheckConditionVO.accReason" value="2" disabled="false" id="accidentId"/>意外
								</span>
								<span>
								<input
								style="margin-left: 20px" type="checkbox"
								<s:if test="1 in reasonStrList">checked="checked"</s:if>
								name="middleCheckConditionVO.accReason" value="1" disabled="false" id="pathemaId"/>疾病
								</span>
			</dd>
		</dl>
		</div>
		<div >
			<div class="divfclass">
				<h1><img src="clm/images/tubiao.png">险种</h1>
			</div>
			<%-- <div class="panelPageFormContent main_query">
				<span style="line-height:30px;margin-left: 38px;margin-right:6px;">险种快速查询</span>
				<input type="text" id="businessSearchId" />
			</div> --%>
				<div style="margin-top:35px;margin-left: 40px;">
					<table >
						<tr >
							<td>
									<dl class='nowrap'>
										<dt>险种</dt>
									</dl>
									<td>
									</td>
										<td>
										<dl class='nowrap'>
											<dt>参与质检的险种</dt>
										</dl>
									</td>
						</tr>		
						<tr>
						<td width="40%">
							<div>
								<select size="10" name="allProductEXT" id="allProductEXT"
											multiple style="width: 96%;">
												<s:iterator value="businessProductVOs" var="product">
													<s:if test="productNameSys != null">
														<option value="${productCodeSys}">
															${productCodeSys}-${productNameSys}</option>
													</s:if>
												</s:iterator>
										</select>
							</div>
						</td>
						<td width="8%">
							<div style="margin: 0 30px 0 30px; float: left;">
									<div class="buttonContent">
										<input type="button" value="&gt;" name="addOne"
											 disabled="disabled" class="but_gray" style="padding-left: 14px; padding-right: 14px;">
									</div>
									
								<div  style="clear: left; margin: 5px 0px 0px;">
									<div class="buttonContent">
										<input type="button" class="but_gray" value="&gt;&gt;" name="addAll"
											 disabled="disabled">
									</div>
								</div>
								<div  style="clear: left; margin: 5px 0px 0px;">
									<div class="buttonContent">
										<input type="button" class="but_gray" value="&lt;" name="releaseOne"
											 disabled="disabled" style="padding-left: 14px; padding-right: 14px;">
									</div>
								</div>
								<div  style="clear: left; margin: 5px 0px 0px;">
									<div class="buttonContent">
										<input type="button" class="but_gray" value="&lt;&lt;"
											name="releaseAll"
											 disabled="disabled">
									</div>
								</div>
							</div>
						</td>
						<td width="40%">
							<div>
								<select size="10" id="selectedProductEXT"
											name="middleCheckConditionVO.productCode"
											multiple style="width: 96%;">
												<s:iterator value="businessStrList" var="businessStr">
													<option value="${productCodeSys}">
														${productCodeSys}-${productNameSys}</option>
												</s:iterator>
										</select>
							</div>
						</td>
					</tr>
							
					</table>
				</div>
			
		</div>
	</div>
	<div id="clmExtractCustomerId">
		<div class="divfclass">
				<h1><img src="clm/images/tubiao.png">按客户层级</h1>
		</div>
		<div class="panelPageFormContent">
			<dl>
				<dt><label>保单生效日期</label></dt>
				<dd>
					<input name="claimAfcPlanVO.validDate" readonly="readonly"
					value="<fmt:formatDate value="${claimAfcPlanVO.validDate}"
										pattern="yyyy-MM-dd" ></fmt:formatDate>"
					id="validDateId"/>
				</dd>
			</dl>
			<dl>
				<dt><label>客户号</label></dt>
				<dd>
					<input name="claimAfcPlanVO.customerId"
					value="${claimAfcPlanVO.customerId}" id="customerIdId" readonly="readonly"/>
				</dd>
			</dl>
			<dl>
				<dt><label>证件号码</label></dt>
				<dd>
					<input name="claimAfcPlanVO.certiCode"
					value="${claimAfcPlanVO.certiCode}" id="certiCodeId" readonly="readonly"/>
				</dd>
			</dl>
		</div>
		<div>
		<div class="divfclass">
			<h1><img src="clm/images/tubiao.png">片区</h1>
		</div>
			<%-- <div class="panelPageFormContent main_query">
				<span style="float:left;line-height:1.6;">片区快速查询</span><input type="text" id="appendItemSearchId"  style="float:left;line-height:1.6;" /> 
			</div>--%>
				<div style="margin-top:35px;margin-left: 40px;">
					<table >
						<tr >
							<td>
									<dl class='nowrap'>
										<dt>片区</dt>
									</dl>
									<td>
									</td>
										<td>
										<dl class='nowrap'>
											<dt>参与质检的片区</dt>
										</dl>
									</td>
						</tr>		
						<tr>
						<td width="40%">
							<div>
								<select id="selectedAreaEXT" name="selectedAreaEXT"
									multiple="multiple" style="width: 96%;" size=10>
									<s:iterator value="areaVOList" var="area">
										<s:if test="areaName != null">
											<option value="${areaCode}">
												${areaCode}-${areaName}</option>
										</s:if>
									</s:iterator>
								</select>
							</div>
						</td>
						<td width="8%">
							<div style="margin: 0 30px 0 30px; float: left;">
									<div class="buttonContent">
										<input id="toRightO" type="button" value="&gt;"  onclick="moveselect('selectedAreaEXT', 'areaVOList', true);"
											 disabled="disabled" class="but_gray" style="padding-left: 14px; padding-right: 14px;">
									</div>
									
								<div  style="clear: left; margin: 5px 0px 0px;">
									<div class="buttonContent">
										<input type="button" class="but_gray" value="&gt;&gt;" id="allToRightO"
											 onclick="moveselect('selectedAreaEXT','areaVOList', true,'1');" disabled="disabled">
									</div>
								</div>
								<div  style="clear: left; margin: 5px 0px 0px;">
									<div class="buttonContent">
										<input type="button" class="but_gray" value="&lt;" id="toleftO" onclick="moveselect('areaVOList', 'selectedAreaEXT', true);"
											 disabled="disabled" style="padding-left: 14px; padding-right: 14px;">
									</div>
								</div>
								<div  style="clear: left; margin: 5px 0px 0px;">
									<div class="buttonContent">
										<input type="button" class="but_gray" value="&lt;&lt;" onclick="moveselect('areaVOList','selectedAreaEXT', true,'1');"
											id="allToLeftO" disabled="disabled">
									</div>
								</div>
							</div>
						</td>
						<td width="40%">
							<div>
								<select size="10" id="areaListEXT" multiple="multiple"
											name="middleCheckConditionVO.area"
											multiple style="width: 96%;">
												<s:iterator value="areaStrList" var="areaStr">
												<option value="${areaCode}">
													${areaCode}-${areaName}</option>
											</s:iterator>
										</select>
							</div>
						</td>
					</tr>
							
					</table>
				</div>
		</div>
		<div>
		<div class="divfclass">
			<h1><img src="clm/images/tubiao.png">管理机构</h1>
		</div>
		<%-- <div class="panelPageFormContent main_query">
				<span style="float:left;line-height:1.6;">管理机构快速查询</span><input type="text" id="organSearchId"  style="float:left;line-height:1.6;" />
			</div> --%>
				<div style="margin-top:35px;margin-left: 40px;">
					<table >
						<tr >
							<td>
									<dl class='nowrap'>
										<dt>管理机构</dt>
									</dl>
									<td>
									</td>
										<td>
										<dl class='nowrap'>
											<dt>参与质检的管理机构</dt>
										</dl>
									</td>
						</tr>		
						<tr>
						<td width="40%">
							<div>
								
								<select id="selectedOrganizationEXT" name="name"
									multiple="multiple" style="width: 96%;" size=10 >
									<s:iterator value="orgVOList" var="org">
										<s:if test="organName != null">
											<option value="${organCode}">
												${organCode}-${organName}</option>
										</s:if>
									</s:iterator>
								</select>
							</div>
						</td>
						<td width="8%">
							<div style="margin: 0 30px 0 30px; float: left;">
									<div class="buttonContent">
										<input id="toRightO"  type="button" value="&gt;"  onclick="moveselect('selectedOrganizationEXT','orgListEXT',true)"
											 disabled="disabled" class="but_gray" style="padding-left: 14px; padding-right: 14px;">
									</div>
									
								<div  style="clear: left; margin: 5px 0px 0px;">
									<div class="buttonContent">
										<input type="button" class="but_gray" value="&gt;&gt;" id="allToRightO"
											 onclick="moveselect('selectedOrganizationEXT','orgListEXT',true,'1')" disabled="disabled">
									</div>
								</div>
								<div  style="clear: left; margin: 5px 0px 0px;">
									<div class="buttonContent">
										<input type="button" class="but_gray" value="&lt;" id="toleftO" onclick="moveselect('orgListEXT','selectedOrganizationEXT',true)"
											 disabled="disabled" style="padding-left: 14px; padding-right: 14px;">
									</div>
								</div>
								<div  style="clear: left; margin: 5px 0px 0px;">
									<div class="buttonContent">
										<input type="button" class="but_gray" value="&lt;&lt;" onclick="moveselect('orgListEXT','selectedOrganizationEXT',true,'1')"
											id="allToLeftO" disabled="disabled">
									</div>
								</div>
							</div>
						</td>
						<td width="40%">
							<div>
								<select size="10" id="orgListEXT" multiple="multiple"
											name="middleCheckConditionVO.organizationCode"
											multiple style="width: 96%;">
												<s:iterator value="orgStrList" var="orgStr">
												<option value="${organCode}">
													${organCode}-${organName}</option>
											</s:iterator>
										</select>
							</div>
						</td>
					</tr>
							
					</table>
				</div>
		</div>
	</div>
		<div class="panelPageFormContent">
			<dl style="height: auto">
				<dt style="width: 65px;">备注</dt>
				<dd>
					<textarea rows="3" cols="80" style="margin-left: 15px;" readonly="readonly">${claimAfcPlanVO.remark}</textarea>
				</dd>
			</dl>
		</div>
	</form>
	<div class="formBarButton">
		<ul>
			<li><button type="button"  class="but_blue" onclick="extractClaimCase();">抽取</button></li>
			<li><button type="reset"  class="but_gray" onclick="exit();">退出</button></li>
		</ul>
	</div>
</div>
