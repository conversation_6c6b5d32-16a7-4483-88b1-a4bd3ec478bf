<%@ page language="java" contentType="text/html; charset=utf-8" pageEncoding="UTF-8"%><%@ include file="/clm/pages/common/commonJsCss.jsp" %>
<%@taglib uri="/struts-tags" prefix="s"%>
<!DOCTYPE html PUBLIC  "_//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<%@ page import="java.util.*"%>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<script type="text/javascript">
/* $(function(){
	var flag = '${flag}';
	if (flag == 'true') {
		alertMsg.error('您要查询的操作员不存在，请重新输入查询条件！');
	}
}); */
function queryOpr(){
	$("#pagerForms",$.pdialog.getCurrent()).submit();
}
</script>
<!-- 分页切换页码提交使用 -->
<form id="pagerForm" method="post" action="clm/inspect/queryOpr_CLM_claimAfcPlanProjectAction.action">
	<input type="hidden" name="pageNum" value="${currentPage.pageNo }" />
	<input type="hidden" name="numPerPage" value="${currentPage.pageSize}" />
</form>
<div class="pageHeader">
<form id="pagerForms" method="post" action="clm/inspect/queryOpr_CLM_claimAfcPlanProjectAction.action" onsubmit="return dialogSearch(this,'caseQueryTabs2');"
			class="pagerForm required-validate" novalidate="novalidate" rel="pagerForm">
	<div class="tabdivclassbr">
		<table class="list" width="100%" border="0" cellspacing="0" cellpadding="0">
					<tr>
						<td nowrap>业务员代码</td>
						<td nowrap>
							<input name="userVO.userName" type="text" id="userId" value="${userVO.userName}">
						</td>
						<td nowrap>姓名</td>
						<td nowrap>
							<input type="text" name="userVO.realName" id="realName" value="${userVO.realName}" onkeydown="this.onkeyup();" onkeyup="this.size=(this.value.length>4?this.value.length:4);" size="4" />
						</td>
						<td nowrap colspan="8">
						<div class="pageFormdiv"><button type="button" class="but_blue" onclick="queryOpr();" >查询</button></div>
						</td>
					</tr>
				</table>
		</div>
	</form>
</div>
<div class=""   layoutH="32">
<div class="tabdivclassbr">
	<table id="" class="list main_dbottom" style="width: 100%;">
		<thead>
			<tr>
					<th nowrap>序号</th>
					<th nowrap>业务员代码</th>
					<th nowrap>姓名</th>
					<th nowrap>机构号码</th>
					<th nowrap>审核权限</th>
					<th nowrap>审批权限(普通)</th>
					<th nowrap>审批权限(疑难)</th>
					<th nowrap>有效标识</th>
					<th nowrap>查找带回</th>
			</tr>
		</thead>
		<tbody>
			
			<s:if test="imageFlag != null">
							<tr>
								<td colspan="10">
									<div class="noRueryResult">请选择条件查询数据！</div>
								</td>
							</tr>
						</s:if>
						<s:elseif test="currentPage.PageItems == null || currentPage.PageItems.size()==0">
							<tr>
								<td colspan="10">
									<div class="noRueryResult">没有符合条件的查询结果！</div>
								</td>
							</tr>
						</s:elseif>
		
			<s:iterator value="currentPage.pageItems" status="st" var="cps">
				<tr align="center">
					<td>${st.index+1}</td>
					<td>${cps.userName}</td>
					<td>${cps.realName}</td>
					<td>${cps.organCode}</td>
					<td>${cps.auditLevelStr}</td>
					<td>${cps.approveLevelStr}</td>
					<td>${cps.approveDifficultLevelStr}</td>
					<td><s:if test="userDisable == 'N'.toString()">有效</s:if><s:else>无效</s:else></td>
					<td>
					   <a class="btnSelect" href="javascript:$.bringBack({checkPer:'${cps.userId}', userName:'${cps.userName}', realName:'${cps.realName}'})" title="查找带回"></a> 
					</td>
				</tr>
			</s:iterator>
		</tbody>
	</table>

	<div class="panelBar">
				<div class="pages">
				<span>显示</span>
				<s:select list="#{5:'5',10:'10',20:'20',50:'50',100:'100'}"
					name="select" onchange="dialogPageBreak({numPerPage:this.value})"
					value="currentPage.pageSize">
				</s:select>
				<span>条，共${currentPage.total}条</span>
			</div>
				 <div class="pagination" targetType="dialog" totalCount="${currentPage.total}" numPerPage="${currentPage.pageSize}" pageNumShown="10" 
				 currentPage="${currentPage.pageNo}"
				 ></div>
	</div>
</div>

<!-- 关闭按钮 -->
<div class="formBarButton" >
		<ul> 
		    <li>
		    	<button type="button"  class="close but_gray" >关闭</button>
			</li>
		</ul>
</div>
</div>
