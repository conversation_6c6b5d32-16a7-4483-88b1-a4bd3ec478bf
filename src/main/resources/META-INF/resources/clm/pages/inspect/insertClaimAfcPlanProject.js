// 质检要点的下拉列表
function getGist(k,index){
	var itemId = $(k).val();
	$.ajax({
		url : "clm/inspect/edittClainAfcPlanDel_CLM_claimAfcPlanProjectAction.action?claimAfcGistVO.itemId="+itemId,
		global : false,
		type : "POST",
		dataType : "json",
		async : false,
		success : function(beneVOList) {
			var optionStr = "";
			$.each(beneVOList, function(key, val) {
				var beneId = beneVOList[key].gistId;
				var beneName = beneVOList[key].gistDesc;
				optionStr += "<option value='" + beneId + "'>"
						+ beneName + "</option>";
			});
			$("#tbodyClaimAfcPlanId",navTab.getCurrentPanel()).children("tr:eq("+index+")").children("td:eq(2)").html("<select class='selectToInput' name='claimAfcPlanRelaVOList["+index+"].gistId'>"+ optionStr + "</select>");
		}
	});
	$("tbodyClaimAfcPlanId",navTab.getCurrentPanel()).initUI();
	setTimeout(function(){
			onloadChangeSelet();},10);
}
// 操作人权限的添加
function addClaimAfcAuthBO(k,index){
	var indexI = (index+1);
	$.ajax({
		url : "clm/inspect/findPermissionRegion_CLM_claimAfcPlanProjectAction.action",
		global : false,
		type : "POST",
		dataType : "json",
		success : function (json){
			var optionStr = "<option>请选择</option>";
			for(var i=0;i<json.length;i++){
				optionStr += "<option value='" + json[i] + "'>"+ json[i] + "</option>";
			}
			
			var inHtml = "<select class='combox' name='claimAfcAuthVOList["+index+"].authMin'>"+optionStr+"</select>"+'至'+"<select class='combox' name='claimAfcAuthVOList["+index+"].authMax'>"+optionStr+"</select>";
			var insertHtml = "<tr>"
							+"<td><div align='center'>"+indexI+"</div></td>"
							+"<td><div align='center'>"+inHtml+"</div></td>"
							+"<td><div align='center'><input size='7' name='claimAfcAuthVOList["+index+"].extractRate' /></div></td>"
							+"<td><a title='添加' class='btnAdd' id='addButton' href='javascript:void(0);' onclick='addClaimAfcAuthBO(this,"+indexI+");'>添加</a></td>";
			$("#claimAfcAuthVOTbodyIdId", navTab.getCurrentPanel()).append(insertHtml);
			$(k).parent().parent().find("td:eq(3)").find("a").css("display",'none');
		}
	});
}
// 操作机构的保存
function insertClaimAfcOrg(k,index){
	var indexI = index+1;
	var  insertHtml = "<tr>"
		+"<td><div align='center'>"+indexI+"</div></td>"
		+"<td><div align='center'><input name='claimAfcOrgVOList["+index+"].organCode'/></div></td>"
		+"<td><div align='center'><input/></div></td>"
		+"<td><div align='center'><input name='claimAfcOrgVOList["+index+"].extractRate'/></div></td>"
		+"<td><a title='添加' class='btnAdd' id='insertButton' href='javascript:void(0);' onclick='insertClaimAfcOrg(this,"+indexI+")'>添加</a></td>";
		+"</tr>";
	$("#claimAfcOrgTbId", navTab.getCurrentPanel()).append(insertHtml);
	$(k).parent().parent().find("td:eq(4)").find("a:eq(0)").css("display",'none');
}

// 操作员添加
function insertClaimAfcOpr(k,index){
	var indexI = index+1;
	var  insertHtml = "<tr>"
		+"<td><div align='center'>"+indexI+"</div></td>"
		+"<td><div align='center'><input name='claimAfcOprVOList["+index+"].checkPer'/></div></td>"
		+"<td><div align='center'><input/></div></td>"
		+"<td><div align='center'><input name='claimAfcOprVOList["+index+"].extractRate'/></div></td>"
		+"<td><a title='添加' class='btnAdd' id='insertButton' href='javascript:void(0);' onclick='insertClaimAfcOpr(this,"+indexI+")'>添加</a></td>";
		+"</tr>";
	$("#TbodyClaimAfcOprId", navTab.getCurrentPanel()).append(insertHtml);
	$(k).parent().parent().find("td:eq(4)").find("a:eq(0)").css("display",'none');
}
// 层级
function getExtractLevel(k) {
	if ($(k).val() == "1") {
		$("#clmCaseId", navTab.getCurrentPanel()).css("display", "block");
		$("#clmCustomerId", navTab.getCurrentPanel()).css("display", "none");
	}
	if ($(k).val() == "2") {
		$("#clmCaseId", navTab.getCurrentPanel()).css("display", "none");
		$("#clmCustomerId", navTab.getCurrentPanel()).css("display", "block");
	}
}
/**
 * 退出
 */
/*function exitClaimAfcPlanProject() {
	 alertMsg.confirm("当前页面录入的信息将不被保存，是否确定退出？", {
	 	okCall : function() {
			navTab.closeCurrentTab();
	 	}
	 });
}*/
//改变类型
function changeType(){
	$("#caseFlagId",navTab.getCurrentPanel()).children().children("td:eq(0)").find("h1").remove();
	if($("#claimAfcPlanProjecPlanTypeId",navTab.getCurrentPanel()).val() == "2"){
		var inhtml = "<h1>" 
			+ "案件标识<input style='margin-left: 30px' type='checkbox' name='middleCheckConditionVO.caseFlag' value='8'/>调查"
			+ "<input style='margin-left: 30px' type='checkbox' name='middleCheckConditionVO.caseFlag' value='10'/>部分给付"
			+ "<input style='margin-left: 30px' type='checkbox' name='middleCheckConditionVO.caseFlag' value='3'/>疑难/争议"
			+ "<input style='margin-left: 30px' type='checkbox' name='middleCheckConditionVO.caseFlag' value='9'/>回退"
			+"</h1>";
		$("#caseFlagId",navTab.getCurrentPanel()).children().children("td:eq(0)").append(inhtml);
	} 
	if($("#claimAfcPlanProjecPlanTypeId",navTab.getCurrentPanel()).val() == "1") {
		var inhtml = "<h1>" 
			+ "案件标识<input style='margin-left: 30px' type='checkbox'name='middleCheckConditionVO.caseFlag' value='4'/>简易"
			+ "<input style='margin-left: 30px' type='checkbox' name='middleCheckConditionVO.caseFlag' value='6'/>单人审核"
			+ "<input type='checkbox' style='margin-left: 30px' name='middleCheckConditionVO.caseFlag' value='1'/>普通"
			+ "<input type='checkbox' style='margin-left: 30px' name='middleCheckConditionVO.caseFlag' value='8'/>调查 "
			+ "<input type='checkbox' style='margin-left: 30px' name='middleCheckConditionVO.caseFlag' value='7'/>自核"
			+"</h1>";
		$("#caseFlagId",navTab.getCurrentPanel()).children().children("td:eq(0)").append(inhtml);
	}
	var trs = $("#tbodyClaimAfcPlanId",navTab.getCurrentPanel()).children();
	var tr = trs.length -1;
	var index = $("#tbodyClaimAfcPlanId",navTab.getCurrentPanel()).children("tr:eq("+tr+")").children("td:eq(0)").text();
	var indexI = index - 1;
	var PlanType = $("#claimAfcPlanProjecPlanTypeId" , navTab.getCurrentPanel()).val();
	$.ajax({
		url : "clm/inspect/findItemName_CLM_claimAfcPlanProjectAction.action?claimAfcItemVO.checkType="
				+ PlanType,
		global : false,
		type : "POST",
		dataType : "json",
		success : function(beneVOList) {
			var optionStr = "<option>请选择</option>";
			$.each(beneVOList, function(key, val) {
				var beneId = beneVOList[key].itemId;
				var beneName = beneVOList[key].itemName;
				optionStr += "<option value='" + beneId + "'>"
						+ beneName + "</option>";
			});
			for(var i = 0; i < trs.length; i++){
				var inHtml = "<select class='combox' onchange='getGist(this,"+i+");' name='claimAfcPlanRelaVOList["+indexI+"].itemId'>"+ optionStr + "</select>";
				$("#tbodyClaimAfcPlanId",navTab.getCurrentPanel()).children("tr:eq("+i+")").children("td:eq(1)").html(inHtml);
			}
		}
	});
}

//初始化
$(function() {
	$("#clmCaseId", navTab.getCurrentPanel()).css("display", "none");
	$("#clmCustomerId", navTab.getCurrentPanel()).css("display", "block");
});
//清空
function emptyAllMesage123() {
	alertMsg.confirm("请确认是否清空？",{
		okCall : function() {
			$("#addClaimAfcPlanProjectInitId",navTab.getCurrentPanel()).submit();
		}
	});
}
//扣分校验
function integerCheck(k){
	var deductVal = $(k).val();
	if(deductVal < 0 || deductVal > 100){
		alertMsg.error("扣分标准在0至100分");
	}
}

/**
 * 改变类型
 */
function changePlanType(){
	var trs = $("#tbodyClaimAfcPlanId",navTab.getCurrentPanel()).children();
	var tr = trs.length -1;
	var index = $("#tbodyClaimAfcPlanId",navTab.getCurrentPanel()).children("tr:eq("+tr+")").children("td:eq(0)").text();
	var indexI = index - 1;
	var PlanType = $("#claimAfcPlanProjecPlanTypeId" , navTab.getCurrentPanel()).val();
	$.ajax({
		url : "clm/inspect/findItemName_CLM_claimAfcPlanProjectAction.action?claimAfcItemVO.checkType="
				+ PlanType,
		global : false,
		type : "POST",
		dataType : "json",
		success : function(beneVOList) {
			var optionStr = "<option>请选择</option>";
			$.each(beneVOList, function(key, val) {
				var beneId = beneVOList[key].itemId;
				var beneName = beneVOList[key].itemName;
				optionStr += "<option value='" + beneId + "'>"
						+ beneName + "</option>";
			});
			for(var i = 0; i < trs.length; i++){
				var inHtml = "<select class='selectToInput' onchange='getQuaPoint(this,"+i+");' name='claimAfcPlanRelaVOList["+indexI+"].itemId'>"+ optionStr + "</select>";
				$("#tbodyClaimAfcPlanId",navTab.getCurrentPanel()).children("tr:eq("+i+")").children("td:eq(1)").html(inHtml);
				$("#tbodyClaimAfcPlanId",navTab.getCurrentPanel()).children("tr:eq("+i+")").children("td:eq(2)").html("<div align='center'></div>");
			}
		}
	});
	$("tbodyClaimAfcPlanId",navTab.getCurrentPanel()).initUI();
	setTimeout(function(){
			onloadChangeSelet();},280);
}

/**
 * 删除操作人权限区间表格中相应的行（点击行内删除按钮）
 */
function deleteRow_auth(obj) {
	$(obj).parent().parent().remove();
	resetIndex("tab_auth");
}
/**
 * 删除操作机构表格中相应的行（点击行内删除按钮）
 */
function deleteRow_org(obj) {
	$(obj).parent().parent().remove();
	resetIndex("tab_org");
}
/**
 * 删除操作员表格中相应的行（点击行内删除按钮）
 */
function deleteRow_oper(obj) {
	$(obj).parent().parent().remove();
	resetIndex("tab_oper");
}
/**
 * 重置表格首列
 */
function resetIndex(tabName) {
	var i = 0;
	$("#"+tabName+" tr:gt(0)", navTab.getCurrentPanel()).each(function() {
		$(this).find("td:eq(0)").text(++i);
	});
}


//操作人权限的添加
function addClaimAfcAuth(){
	var $table = $('#tab_auth', navTab.getCurrentPanel());
	var num = $("#tab_auth tr:last td:nth-child(1)", navTab.getCurrentPanel()).html();
	num = num==null?0:num;
	$.ajax({
		url : "clm/inspect/findPermissionRegion_CLM_claimAfcPlanProjectAction.action",
		global : false,
		type : "POST",
		dataType : "json",
		success : function (json){
			var optionStr = "<option>请选择</option>";
			for(var i=0;i<json.length;i++){
				optionStr += "<option value='" + json[i] + "'>"+ json[i] + "</option>";
			}
			var inHtml = "<dl class='nowrap'><dt></dt><dd style='width: 150px;'><select class='combox' name='claimAfcAuthVOList["+parseInt(num)+"].authMin'>"+optionStr+"</select></dd>"+'<span style="padding-left:10px">至</span>'+"<dd><select class='combox' name='claimAfcAuthVOList["+parseInt(num)+"].authMax'>"+optionStr+"</select></dd></dl>";
			var insertHtml = "<tr class='thisTR'>"
							+"<td>"+(parseInt(num) + 1)+"</td>"
							+"<td> "+"<select onchange='queryApprovePermissionType(this);' name='claimAfcAuthVOList["+parseInt(num)+"].permissionTypeCode' class='combox title'><option value=''>请选择</option><option value='1'>审批权限（疑难）</option><option value='2'>审批权限（普通）</option><option value='3'>审核权限</option></select>"+" </td>"
							+"<td> "+inHtml+" </td>"
							+"<td><a href='javascript:;' class='btnDel' onclick='deleteRow_auth(this)'>删除</a></td>";
							+"</tr>";
			$table.append(insertHtml);
			$("#claimAfcAuthVOTbodyIdId", navTab.getCurrentPanel()).initUI();
		}
	});
}
// 操作机构的保存
function addClaimAfcOrg(){
	var $table = $('#tab_org', navTab.getCurrentPanel());
	var num = $("#tab_org tr:last td:nth-child(1)", navTab.getCurrentPanel()).html();
	num = num==null?0:num;
	var  insertHtml = "<tr>"
		+"<td>"+(parseInt(num) + 1)
		+"<input type='checkbox' flag='uwflag' style='border: 0px; background: 0px; width: auto; float: none;'"
		+"name='claimAfcOrgVOList["+parseInt(num)+"].isChecked' id='uwcheck"+(parseInt(num) + 1)+"'>"
		+"</td>"
		+"<td><input name='claimAfcOrgVOList["+parseInt(num)+"].organCode' id='branchCode"+(parseInt(num) + 1)+"'"
		+"type='text' class='organ' clickId='menuBtn"+(parseInt(num) + 1)+"' showOrgName='branchname"+(parseInt(num) + 1)+"' readonly='readonly' needAll='true'/>"
		+"<a id='menuBtn"+(parseInt(num) + 1)+"' class='btnLook' href='#'></a></td>"
		+"<td><input id='branchname"+(parseInt(num) + 1)+"' type='text' size='30' readOnly/></td>"
		+"</tr>";
	$table.append(insertHtml);
	$("#claimAfcOrgTbId", navTab.getCurrentPanel()).initUI();
}

// 操作员添加
function addClaimAfcOper(){
	var $table = $('#tab_oper', navTab.getCurrentPanel());
	var num = $("#tab_oper tr:last td:nth-child(1)", navTab.getCurrentPanel()).html();
	num = num==null?0:num;
	var  insertHtml = "<tr>"
		+"<td>"+(parseInt(num) + 1)+"</td>"
		+"<td><div align='center'><input id='claimAfcOprId' class='data' name='claimAfcOprVOList["+parseInt(num)+"].checkPer' type='hidden'/> <input name='claimAfcOprVOList["+parseInt(num)+"].userName' readonly='readonly' type='text'  size='20'/>" 
		+"<a class='btnLook' href='clm/inspect/queryOpr_CLM_claimAfcPlanProjectAction.action' lookupGroup='claimAfcOprVOList["+parseInt(num)+"]'>业务人员查询 </a></div></td>"
		+"<td><div align='center'><input name='claimAfcOprVOList["+parseInt(num)+"].realName' readonly='readonly'/></div></td>"
		+"<td><a href='javascript:;' class='btnDel' onclick='deleteRow_oper(this)'>删除</a></td>"
		+"</tr>";
	$table.append(insertHtml);
	$("#TbodyClaimAfcOprId", navTab.getCurrentPanel()).initUI();
}

//根据权限类型查询审批权限
function queryApprovePermissionType(k) {
	var url = "";
	var approvePermissionType = $(k).val();
	
	if (approvePermissionType == '2') {
		url = "clm/handworkAssignTask/queryPermissionRegion_CLM_handworkAssignTaskAction.action?permissionTypeVO.taskType=4&permissionTypeVO.difficultFlag=0";
	} else if (approvePermissionType == '1'){
		url = "clm/handworkAssignTask/queryPermissionRegion_CLM_handworkAssignTaskAction.action?permissionTypeVO.taskType=4&permissionTypeVO.difficultFlag=1";
	} else if (approvePermissionType == '3'){
		url = "clm/handworkAssignTask/queryPermissionRegion_CLM_handworkAssignTaskAction.action?permissionTypeVO.taskType=3";
	}
	
	$.ajax({
		url : url,
		global : false,
		type : "POST",
		dataType : "json",
		success : function(json) {
			var data = eval(json);
			var interHTML = "<option value=''>请选择</option>";
			for(var i = 0; i < data.length; i++){
				interHTML += "<option value='" + data[i].permissionName + "'>" + data[i].permissionName + "</option>";
			}
			var num = $(k).closest(".thisTR").find("td:eq(0)").html();
			var selectHtml = "<select class='combox' name='claimAfcAuthVOList["+parseInt(num-1)+"].authMin'>"+interHTML+"</select>"
			var selectHtml1 = "<select class='combox' name='claimAfcAuthVOList["+parseInt(num-1)+"].authMax'>"+interHTML+"</select>"
			$(k).closest(".thisTR").find("td:eq(2)").find("dd:first").empty().append(selectHtml).initUI();
			$(k).closest(".thisTR").find("td:eq(2)").find("dd:last").empty().append(selectHtml1).initUI();
		}
	});
}

//质检要点相关js
/**
 * 质检要点列表的添加
 */
function addClainAfcPlan(){
	var $table = $('#tab_plan', navTab.getCurrentPanel());
	var num = $("#tab_plan tr:last td:nth-child(1)", navTab.getCurrentPanel()).html();
	num = num==null?0:num;
	
	var claimAfcPlanProjecPlanTypeId = $("#claimAfcPlanProjecPlanTypeId",navTab.getCurrentPanel()).val();
	$.ajax({
		url : "clm/inspect/edClainAfcPlanDel_CLM_claimAfcPlanProjectAction.action?claimAfcItemVO.checkType="+claimAfcPlanProjecPlanTypeId,
		global : false,
		type : "POST",
		dataType : "json",
		async : false,
		success : function(beneVOList) {
			var optionStr = "<option>请选择</option>";
			$.each(beneVOList, function(key, val) {
				var beneId = beneVOList[key].itemId;
				var beneName = beneVOList[key].itemName;
				optionStr += "<option value='" + beneId + "'>"
						+ beneName + "</option>";
			});
			var inHtml = "<select class='selectToInput' onchange='getGist(this,"+parseInt(num)+");' name='claimAfcPlanRelaVOList["+parseInt(num)+"].itemId'>"+ optionStr + "</select>";
			var insertHtml = "<tr>"
				+"<td>"+(parseInt(num) + 1)+"</td>"
				+"<td>"+inHtml+"</td>"
				+"<td></td>"
				+"<td>"
				+"<input id='insertSignRoleId' style='border:0px;background:0px;width:auto;' type='checkbox' name='claimAfcPlanRelaVOList["+parseInt(num)+"].signRole' value='签收人/移动签收人'/>签收人/移动签收人<input size='1' name='claimAfcPlanRelaVOList["+parseInt(num)+"].signRoleDeduct' value='' onkeyup='changeThis(this)' onblur='integerCheck(this)'/> " 
				+"<input id='insertAuditOrganId' style='border:0px;background:0px;width:auto;' type='checkbox' name='claimAfcPlanRelaVOList["+parseInt(num)+"].auditOrgan' value='外包商/医疗复核岗'/>外包商/医疗复核岗<input size='1' name='claimAfcPlanRelaVOList["+parseInt(num)+"].auditOrganDeduct' value='' onkeyup='changeThis(this)' onblur='integerCheck(this)'/> " 
				+"<input id='insertResignRoleId' style='border:0px;background:0px;width:auto;' type='checkbox' name='claimAfcPlanRelaVOList["+parseInt(num)+"].resignRole' value='立案人/外包商'/>立案人/外包商<input size='1' name='claimAfcPlanRelaVOList["+parseInt(num)+"].resignRoleDeduct' value='' onkeyup='changeThis(this)' onblur='integerCheck(this)'/> " 
				+"<input id='insertSelfRuleId' style='border:0px;background:0px;width:auto;' type='checkbox' name='claimAfcPlanRelaVOList["+parseInt(num)+"].selfRule' value='自核规则'/>自核规则<input size='1' name='claimAfcPlanRelaVOList["+parseInt(num)+"].selfRuleDeduct' value='' onkeyup='changeThis(this)' onblur='integerCheck(this)'/> " 
				+"<input id='insertExamineRoleId' style='border:0px;background:0px;width:auto;' type='checkbox' name='claimAfcPlanRelaVOList["+parseInt(num)+"].examineRole' value='审核人'/>审核人<input size='1' name='claimAfcPlanRelaVOList["+parseInt(num)+"].examineRoleDeduct' value='' onkeyup='changeThis(this)' onblur='integerCheck(this)'/> "
				+"<input id='insertApproveRoleId' style='border:0px;background:0px;width:auto;' type='checkbox' name='claimAfcPlanRelaVOList["+parseInt(num)+"].approveRole' value='审批人'/>审批人<input size='1' name='claimAfcPlanRelaVOList["+parseInt(num)+"].approveRoleDeduct' value='' onkeyup='changeThis(this)' onblur='integerCheck(this)'/> " 
				+"<input id='insertSurveyRoleId' style='border:0px;background:0px;width:auto;' type='checkbox' name='claimAfcPlanRelaVOList["+parseInt(num)+"].surveyRole' value='调查人'/>调查人<input size='1' name='claimAfcPlanRelaVOList["+parseInt(num)+"].surveyRoleDeduct' value='' onkeyup='changeThis(this)' onblur='integerCheck(this)'/>"
				+"</td>"
				+"<td><a href='javascript:;' class='btnDel' onclick='deleteRow_plan(this)'>删除</a></td>"
				+"</tr>";
			$table.append(insertHtml);
			$("#tbodyClaimAfcPlanId", navTab.getCurrentPanel()).initUI();
		}
	});
	onloadChangeSelet();
}
function changeThis(obj){
	obj.value=obj.value.replace(/\D/g,'');
}
/**
 * 修改质检项目名称
 */
function getQuaPoint(k,index){
	var itemId = $(k).val();
	$.ajax({
		url : "clm/inspect/edittClainAfcPlanDel_CLM_claimAfcPlanProjectAction.action?claimAfcGistVO.itemId="+itemId,
		global : false,
		type : "POST",
		dataType : "json",
		async : false,
		success : function(beneVOList) {
			var optionStr = "";
			$.each(beneVOList, function(key, val) {
				var beneId = beneVOList[key].gistId;
				var beneName = beneVOList[key].gistDesc;
				optionStr += "<option value='" + beneId + "'>"
						+ beneName + "</option>";
			});
			$("#tbodyClaimAfcPlanId",navTab.getCurrentPanel()).find("tr:eq("+index+")").find("td:eq(2)").html("<select class='selectToInput' name='claimAfcPlanRelaVOList["+index+"].gistId'>"+ optionStr + "</select>");
		}
	});
	$("tbodyClaimAfcPlanId",navTab.getCurrentPanel()).initUI();
	setTimeout(function(){
			onloadChangeSelet();},10);
}
/**
 * 删除质检要点（点击行内删除按钮）
 */
function deleteRow_plan(obj) {
	$(obj).parent().parent().remove();
	resetIndex("tab_plan");
}

//保存
function save(){
	// 抽检方式校验
	if($("#extractRateType").val() == "1"){ // 按比例抽检
		if($("#extractRate").val() == null || $("#extractRate").val() == ""){
			alertMsg.error("按比例抽检时，抽检率不能为空！");
			return false;
		}
	}
	//质检名称校验
	if($("#claimAfcPlanProjecPlanNameId",navTab.getCurrentPanel()).val() == ""){
		alertMsg.error("质检计划名称不能为空");
		return false;
	}
	//质检要点校验
	var trs = $("#tbodyClaimAfcPlanId",navTab.getCurrentPanel()).children();
	if(trs.length == 0){
		alertMsg.error("质检要点不能为空");
		return false;
	}
	for(var i = 0; i < trs.length; i++){
		//质检要点
		var itemlist = $("#tbodyClaimAfcPlanId", navTab.getCurrentPanel()).children("tr:eq("+i+")").children("td:eq(1)").find("select").val();
		var gistlist = $("#tbodyClaimAfcPlanId", navTab.getCurrentPanel()).children("tr:eq("+i+")").children("td:eq(2)").find("select").val();
		if(itemlist == "请选择" || gistlist == undefined){
			alertMsg.error("质检要点或质检项目必填");
			return false;
		}
		var isCheckNum = 0;
		$("#tbodyClaimAfcPlanId", navTab.getCurrentPanel()).children("tr:eq("+i+")").children("td:eq(3)").find("input[type='checkbox']").each(function(){
			if ($(this).is(':checked')) {
				isCheckNum++;
			}
		});
		if(isCheckNum == 0){
			alertMsg.error("受检岗位至少勾选一个");
			return false;
		}
		if($("#tbodyClaimAfcPlanId", navTab.getCurrentPanel()).children("tr:eq("+i+")").find("input#insertSignRoleId").is(':checked')){
			if($("#tbodyClaimAfcPlanId", navTab.getCurrentPanel()).children("tr:eq("+i+")").find("#insertSignRoleId").next().val().trim() == ""){
				alertMsg.error("勾选受检岗位，请录入扣分标准");
				return false;
			}
		}
		if($("#tbodyClaimAfcPlanId", navTab.getCurrentPanel()).children("tr:eq("+i+")").find("input#insertAuditOrganId").is(':checked')){
			if($("#tbodyClaimAfcPlanId", navTab.getCurrentPanel()).children("tr:eq("+i+")").find("#insertAuditOrganId").next().val().trim() == ""){
				alertMsg.error("勾选受检岗位，请录入扣分标准");
				return false;
			}
		}
		if($("#tbodyClaimAfcPlanId", navTab.getCurrentPanel()).children("tr:eq("+i+")").find("input#insertResignRoleId").is(':checked')){
			if($("#tbodyClaimAfcPlanId", navTab.getCurrentPanel()).children("tr:eq("+i+")").find("#insertResignRoleId").next().val().trim() == ""){
				alertMsg.error("勾选受检岗位，请录入扣分标准");
				return false;
			}
		}
		if($("#tbodyClaimAfcPlanId", navTab.getCurrentPanel()).children("tr:eq("+i+")").find("input#insertSelfRuleId").is(':checked')){
			if($("#tbodyClaimAfcPlanId", navTab.getCurrentPanel()).children("tr:eq("+i+")").find("#insertSelfRuleId").next().val().trim() == ""){
				alertMsg.error("勾选受检岗位，请录入扣分标准");
				return false;
			}
		}
		if($("#tbodyClaimAfcPlanId", navTab.getCurrentPanel()).children("tr:eq("+i+")").find("input#insertExamineRoleId").is(':checked')){
			if($("#tbodyClaimAfcPlanId", navTab.getCurrentPanel()).children("tr:eq("+i+")").find("#insertExamineRoleId").next().val().trim() == ""){
				alertMsg.error("勾选受检岗位，请录入扣分标准");
				return false;
			}
		}
		if($("#tbodyClaimAfcPlanId", navTab.getCurrentPanel()).children("tr:eq("+i+")").find("input#insertApproveRoleId").is(':checked')){
			if($("#tbodyClaimAfcPlanId", navTab.getCurrentPanel()).children("tr:eq("+i+")").find("#insertApproveRoleId").next().val().trim() == ""){
				alertMsg.error("勾选受检岗位，请录入扣分标准");
				return false;
			}
		}
		if($("#tbodyClaimAfcPlanId", navTab.getCurrentPanel()).children("tr:eq("+i+")").find("input#insertSurveyRoleId").is(':checked')){
			if($("#tbodyClaimAfcPlanId", navTab.getCurrentPanel()).children("tr:eq("+i+")").find("#insertSurveyRoleId").next().val().trim() == ""){
				alertMsg.error("勾选受检岗位，请录入扣分标准");
				return false;
			}
		}
	}
	// 操作人权限区间
	var trList = $("#claimAfcAuthVOTbodyIdId",navTab.getCurrentPanel()).children();
	// 操作员
	var trIds = $("#TbodyClaimAfcOprId",navTab.getCurrentPanel()).children();
	// 操作机构
	var orgs = $("#claimAfcOrgTbId",navTab.getCurrentPanel()).children();
	// 案件标识
	var caseFlagList = $("#caseFlagId", navTab.getCurrentPanel()).find("input[type='checkbox']:checked");
	var surveyItemNum = caseFlagList.length;
	
	if(surveyItemNum == 0) {
		if(trList.length == 0){
			alertMsg.error("不勾选案件标识，操作人权限信息必填");
			return false;
		}else{
			for(var i = 0; i < trList.length; i++){
				//操作人权限抽检率
				var typeCode = $("#claimAfcAuthVOTbodyIdId", navTab.getCurrentPanel()).children("tr:eq("+i+")").children("td:eq(1)").find("select").val();
				var authMin = $("#claimAfcAuthVOTbodyIdId", navTab.getCurrentPanel()).children("tr:eq("+i+")").children("td:eq(2)").find("select:eq(0)").val();
				var authMax = $("#claimAfcAuthVOTbodyIdId", navTab.getCurrentPanel()).children("tr:eq("+i+")").children("td:eq(2)").find("select:eq(1)").val();
				if(typeCode == "" || authMin == "" || authMin== undefined  || authMin == "请选择" || authMax == "" 
					|| authMax == "请选择" || authMax == undefined ){
					alertMsg.error("不勾选案件标识，存在未填写完成的“操作人权限区间”信息");
					return false;
				}
				//抽检率已校验（抽取比例在0至100之间）
			}
		}
	} else {
		for(var j=0;j<caseFlagList.length;j++) {
			if($(caseFlagList[j]).val() == '12' || $(caseFlagList[j]).val() == '16') {
				if(trIds.length == 0){
					alertMsg.error("案件标识为非自动简易案件或调查案件，操作员信息必填");
					return false;
				}else{
					for(var i = 0; i < trIds.length; i++){
						var checkPer = $("#TbodyClaimAfcOprId", navTab.getCurrentPanel()).children("tr:eq("+i+")").children("td:eq(1)").find("input").val();
						if(checkPer == ""){
							alertMsg.error("案件标识为非自动简易案件或调查案件，存在未填写完成的“操作员”信息");
							return false;
						}
					}
				}
				
			} else if($(caseFlagList[j]).val() == '13' || $(caseFlagList[j]).val() == '17') {
				if(trList.length == 0){
					alertMsg.error("案件标识为普通案件或自动审批案件，操作人权限信息必填");
					return false;
				}else{
					for(var i = 0; i < trList.length; i++){
						//操作人权限抽检率
						var typeCode = $("#claimAfcAuthVOTbodyIdId", navTab.getCurrentPanel()).children("tr:eq("+i+")").children("td:eq(1)").find("select").val();
						var authMin = $("#claimAfcAuthVOTbodyIdId", navTab.getCurrentPanel()).children("tr:eq("+i+")").children("td:eq(2)").find("select:eq(0)").val();
						var authMax = $("#claimAfcAuthVOTbodyIdId", navTab.getCurrentPanel()).children("tr:eq("+i+")").children("td:eq(2)").find("select:eq(1)").val();
						if(typeCode == "" || authMin == "" || authMin== undefined || authMin == "请选择" || authMax == "" 
								|| authMax == "请选择" || authMax == undefined ){
							alertMsg.error("案件标识为普通案件或自动审批案件，存在未填写完成的“操作人权限区间”信息");
							return false;
						}
					}
				}
			}
		}
	}
	// 操作人权限区间
	if(trList.length > 0){
		for(var i = 0; i < trList.length; i++){
			var typeCode = $("#claimAfcAuthVOTbodyIdId", navTab.getCurrentPanel()).children("tr:eq("+i+")").children("td:eq(1)").find("select").val();
			var authMin = $("#claimAfcAuthVOTbodyIdId", navTab.getCurrentPanel()).children("tr:eq("+i+")").children("td:eq(2)").find("select:eq(0)").val();
			var authMax = $("#claimAfcAuthVOTbodyIdId", navTab.getCurrentPanel()).children("tr:eq("+i+")").children("td:eq(2)").find("select:eq(1)").val();
			
			if(typeCode == "" || authMin == "" || authMin== undefined || authMin == "请选择" || authMax == "" 
				|| authMax == "请选择" || authMax == undefined ){
				alertMsg.error("存在未填写完成的“操作人权限区间”信息");
				return false;
			}
		}
	}
	// 操作员
	if(trIds.length > 0){
		for(var i = 0; i < trIds.length; i++){

			var checkPer = $("#TbodyClaimAfcOprId", navTab.getCurrentPanel()).children("tr:eq("+i+")").children("td:eq(1)").find("input").val();
			if(checkPer != ""){
				if(checkPer == ""){
					alertMsg.error("存在未填写完成的“操作员”信息");
					return false;
				}
			}
		}
	}
	$("#selectedProduct option",navTab.getCurrentPanel()).each(function(){
		this.selected=true;
	});
	$("#orgList option",navTab.getCurrentPanel()).each(function(){
		this.selected=true;
	});
	var orgLength = $("#orgList", navTab.getCurrentPanel()).children().length;
	var productLength = $("#selectedProduct", navTab.getCurrentPanel()).children().length;
	if(orgLength > 300){
		alertMsg.error("请选择适量的机构信息");
		return false;
	}
	if(productLength > 300){
		alertMsg.error("请选择适量的险种信息");
		return false;
	}
	$("#areaList option",navTab.getCurrentPanel()).each(function(){
		this.selected=true;
	});
	$("#insertId",navTab.getCurrentPanel()).submit();
}
// 根据抽检方式控制“抽检率”和“检查水平”，“检查级别”显示隐藏
function changeExtractRateType(value){
	if(value == "1"){// 抽检方式为1，按比例抽检：
		// 抽检率显示
		$("#extractRateDiv").css("display",'block');
		// 检查水平隐藏
		$("#inspectionStandardDiv").css("display",'none');
		// 检查级别隐藏
		$("#inspectionLevelDiv").css("display",'none');
	}else if(value == "2"){// 抽检方式为1，按国标抽检：
		// 抽检率隐藏
		$("#extractRateDiv").css("display",'none');
		// 抽检率置为空
		$("#extractRate").val("");
		// 检查水平显示
		$("#inspectionStandardDiv").css("display",'block');
		// 检查级别显示
		$("#inspectionLevelDiv").css("display",'block');
	}
}
//根据检查水平控制检查级别的值
function changeInspectionStandard(value){
	if(value != null && value != "" ){
		$.ajax({
  			'type':'post',
  			'url':'clm/inspect/queryInspectionLevel_CLM_claimAfcPlanProjectAction.action?inspectionLevelnumberVO.inspectionStandard='+value,
  			'datatype':'json',
  			'success':function(data){
  				var data = eval("(" + data + ")");
  				var interHTML = "";
  				for(var i = 0; i<data.length; i++){
  					interHTML += "<option value="+data[i].levelnumberCode+">"+data[i].levelnumberName+"</option>";
  				}
  				var selectHtml = "<select class='combox' name='claimAfcPlanVO.inspectionLevelnumber' id='inspectionLevelnumber'>"+interHTML+"</select>";
  				$("#inspectionLevelDd").empty().append(selectHtml).initUI();
  			},
  			'error':function(){
  				alert("出错了！");
  			}
  	}); 
	}
}

//操作机构列表信息展示
function tabOrgInit(){
	var $table = $('#tab_org', navTab.getCurrentPanel());
	$.ajax({
		url : "queryAllOrganTree_organTreeAction.action?1=1&uporganCode=86",
		global : false,
		type : "POST",
		dataType : "json",
		success : function(data) {
			for(var num=0;num<data.length;num++){
				var  insertHtml = 
					"<tr>"
						+"<td width='20%'>"+(parseInt(num) + 1)
							+"<input type='checkbox' flag='uwflag' style='border: 0px; background: 0px; width: auto; float: none;'"
							+"name='claimAfcOrgVOList["+parseInt(num)+"].isChecked' id='uwcheck"+(parseInt(num) + 1)+"'>"
						+"</td>"
						+"<td width='40%'>" 
							+"<input name='claimAfcOrgVOList["+parseInt(num)+"].organCode' id='branchCode"+(parseInt(num) + 1)+"'"
							+"type='text' class='organ' clickId='menuBtn"+(parseInt(num) + 1)+"' showOrgName='branchname"+(parseInt(num) + 1)+"' readonly='readonly' needAll='true' value='"+ data[num].organCode+ "' />"
						+"</td>"
						+"<td width='40%'>" 
							+"<input id='branchname"+(parseInt(num) + 1)+"' type='text' size='30' readOnly value='"+ data[num].organName + "' />"
						+"</td>"
					+"</tr>";
				$table.append(insertHtml);
				$("#claimAfcOrgTbId", navTab.getCurrentPanel()).initUI();
			}
		}
	});
	
}
// 抽检率输入范围在0-100
function changeeExtractRate(value){
    var limitNum = value.replace(/[^0-9.]+/g, "");
    if(limitNum == null || limitNum == ""){
    	alertMsg.error("请输入0-100之间的数值");
		$("#extractRate").val("");
		return false;
    }
    if(limitNum <0 || limitNum > 100){
		alertMsg.error("请输入0-100之间的数值");
		$("#extractRate").val("");
		return false;
	}
}
