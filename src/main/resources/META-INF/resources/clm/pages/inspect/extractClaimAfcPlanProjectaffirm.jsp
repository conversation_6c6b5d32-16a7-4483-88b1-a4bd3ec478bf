<%@ page language="java" contentType="text/html; charset=utf-8"
	pageEncoding="UTF-8"%><%@ include file="/clm/pages/common/commonJsCss.jsp" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/fmt" prefix="fmt"%>
<%@ taglib uri="/struts-tags" prefix="s"%>

<%@ taglib uri="http://www.git.com.cn/taglib/udmp/field" prefix="Field"%>
<script type="text/javascript"
	src="clm/pages/inspect/extractClaimAfcPlanProjectaffirm.js">
</script>

<div class="pageContent pageHeader" layouth="10">
<!-- 	<div style="margin-top: 20px; margin-left: 15px"> -->
		<div class="divfclass">
				<h1><img src="clm/images/tubiao.png">事后质检任务抽取结果确认</h1>
			</div>
<!-- 	</div> -->
	<div>
		<form action="clm/inspect/extractClaimAfcPlanProjectInit_CLM_extractClaimAfcPlanProjectAction.action"
			method="post" class="pageForm required-validate" id="backoutExtractId"
			onsubmit="return  navTabSearch(this)">
		</form>
		<form id="pagerForm" method="post" action="clm/inspect/queryPagingTotal_CLM_extractClaimAfcPlanProjectAction.action">
			<input type="hidden" name="pageNum" value="${claimAfcTaskVOcurrentPage.pageNo} " /> 
			<input type="hidden" name="numPerPage" value="${claimAfcTaskVOcurrentPage.pageSize}" /> 
			<input type="hidden" name="paramObject" value="${claimAfcTaskVOcurrentPage.paramObject}" />
			<input type="hidden" name="claimAfcPlanVO.planId" value="${claimAfcTaskVO.planId}" />
			<input type="hidden" name="claimAfcTaskVO.caseNo" value="${claimAfcTaskVO.caseNo}" />
		</form>
		<form id="affirmInformation" action="clm/inspect/affirmInformation_CLM_extractClaimAfcPlanProjectAction.action"
			method="post" class="pageForm required-validate" onsubmit="return  navTabSearch(this)">
		</form>
		<form id="queryClaimTaskId" action="clm/inspect/queryClaimTask_CLM_extractClaimAfcPlanProjectAction.action"
			method="post" class="pageForm required-validate" onsubmit="return  navTabSearch(this)">
<!-- 		<div style="margin-top: 20px; margin-left: 20px"> -->
<!-- 			<h1>事后质检抽取结果</h1> -->
<!-- 		</div> -->
		<div class="divfclass">
				<h1><img src="clm/images/tubiao.png">事后质检抽取结果</h1>
			</div>
		<div class="pageFormInfoContent">
			<dl>
				<dt>在抽取结果中快速定位</dt>
			</dl>
			<dl>
				<dt>赔案号</dt>
				<dd>
					<input name="claimAfcTaskVO.caseNo" maxlength="11" onkeyup="changeCaseNO(this)" value="${claimAfcTaskVO.caseNo}" id="caseNo"/>
					<input type="hidden" value="${claimAfcTaskVO.planId}" id="currentPageVarPlanId"/>
					<input type="hidden" value="${nullFlag}" id="nullFlagId">
				</dd>
			</dl>
			<div class="pageFormdiv"><button type="button" class="but_blue" onclick="queryClaimTask()">查询</button></div>
		</div>
		</form>
	</div>
	<div class="tabdivclassbr main_tabdiv ">
		<table class="list main_dbottom" width="100%">
			<thead>
				<tr>
					<th nowrap>序号<input type="checkbox" group="radioName" class="checkboxCtrl"></th>
					<th nowrap>赔案号</th>
					<th nowrap>管理机构</th>
					<th nowrap>出险人</th>
					<th nowrap>结案日期</th>
					<th nowrap>审核人</th>
					<th nowrap>审批人</th>
				</tr>
			</thead>
			<tbody id="currentPageVarTbodyId">
				<s:iterator value="claimAfcTaskVOcurrentPage.pageItems" var="currentPageVar"
					status="st">
					<tr>
						<td>${st.index+1}<input class="radioIndex" name="radioName" value="${taskId}" type="checkbox"/><input type="hidden" value="${caseNo}"/></div></td>
						<td>${caseNo}</td>
						<td>${organCodeName}</td>
						<td>${customerName}</td>
						<td>${endCaseTime}</td>
						<td>${auditorName}</td>
						<td>${approverName}</td>
					</tr>
				</s:iterator>
			</tbody>
		</table>
		<div class="panelBar" >
			<div class="pages">
					<span>共${claimAfcTaskVOcurrentPage.total}条</span>		
			</div>
			<div class="pagination" targetType="navTab"
				totalCount="${claimAfcTaskVOcurrentPage.total}"
				numPerPage="${claimAfcTaskVOcurrentPage.pageSize}" pageNumShown="10"
				currentPage="${claimAfcTaskVOcurrentPage.pageNo}">
			</div>
		</div>
	</div>
		<div style="margin-left: 20px">
			<input class="but_gray" type="button" value="删除" onclick="deleteCurrentPage()"/>
		</div>

	<div class="panelPageFormContent">

		<div class="panelPageFormContent">
			<dl>
				<dt>抽取日期</dt>
				<dd>
				<input readonly="readonly" value="<fmt:formatDate value="${claimAfcTaskVO.extractDate}" pattern="yyyy-MM-dd" />"/>
				</dd>
			</dl>
			<dl>
				<dt>操作员</dt>
				<dd>
				<input style="width: 60px;border-right:0px" size="2" readonly="readonly" value="${claimAfcTaskVO.userNameId}"/><input
					readonly="readonly" value="${claimAfcTaskVO.userName}" style="width:90px;"/><input type="hidden" value="${claimAfcTaskVO.planId}" id="extractClaimAfcPlanId">
				</dd>	
			</dl>
		</div>
	</div>
	
	<div class="formBarButton">
		<ul>
			<li>
				<button class="but_blue" type="button" onclick="affirmInformation()">确认</button>
			</li>
			<li>
				<button class="but_gray" type="button" onclick="backoutExtract()">撤销</button>
			</li>
		</ul>
	</div>
</div>