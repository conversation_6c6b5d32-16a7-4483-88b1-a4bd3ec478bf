	var gTaskId;
	var gPlanId;
	var gCaseId;
	function tabDisablebleInit(){
		//禁用 核对案件信息、合同处理、出具审批结论和备注（！）
		 $("#1",navTab.getCurrentPanel()).parent("li").find("a").attr('disabled',"true");
		 $("#2",navTab.getCurrentPanel()).parent("li").find("a").attr('disabled',"true");
		 $("#3",navTab.getCurrentPanel()).parent("li").find("a").attr('disabled',"true");
		 $("#1",navTab.getCurrentPanel()).parent("li").attr('disabled',"true");
		 $("#2",navTab.getCurrentPanel()).parent("li").attr('disabled',"true");
		 $("#3",navTab.getCurrentPanel()).parent("li").attr('disabled',"true");
		 $("#claimRegistChangeProp",navTab.getCurrentPanel()).attr('disabled',"true");
	}
	tabDisablebleInit();
	
	//查询
	function qCheckPoolSubmit(){
		//禁用 核对案件信息、合同处理、出具审批结论和备注（！）
		tabDisablebleInit();
		 
		var qCheckPoolCaseNO = $("#qCheckPoolCaseNO", navTab.getCurrentPanel()).attr("value");//赔案号
		var qCheckPoolCustomerName = $("#qCheckPoolCustomerName", navTab.getCurrentPanel()).attr("value");//出险人
		var qCheckPoolInspector = $("#qCheckPoolInspector", navTab.getCurrentPanel()).attr("value");//质检人
		var qCheckPoolBeginDate = $("#qCheckPoolBeginDate", navTab.getCurrentPanel()).attr("value");//审批始期
		var qCheckPoolEndDate= $("#qCheckPoolEndDate", navTab.getCurrentPanel()).attr("value");//审批止期
		var qCheckPoolClaimType= $("#qCheckPoolClaimType", navTab.getCurrentPanel()).attr("value");//理赔类型
		
		var reg = /^(\d{4})-(\d{2})-(\d{2})$/;
		if (qCheckPoolBeginDate.length!=0 && (!reg.test(qCheckPoolBeginDate)&&RegExp.$2<=12&&RegExp.$3<=31)){
			alertMsg.error("请输入正确日期");
		}else if(qCheckPoolEndDate.length!=0 && (!reg.test(qCheckPoolEndDate)&&RegExp.$2<=12&&RegExp.$3<=31)){
			alertMsg.error("请输入正确日期");
		}else if(qCheckPoolCaseNO.length!=0 && qCheckPoolCaseNO.length!=11){
			alertMsg.error("请输入正确赔案号");
		}else if(qCheckPoolCaseNO.length!=0 && qCheckPoolCaseNO.substr(0,1)!=9){
			alertMsg.error("请输入正确赔案号");
		}else{
			if(qCheckPoolCaseNO==''
				&&qCheckPoolCustomerName==''
				&&qCheckPoolInspector==''
				&&qCheckPoolClaimType==''){
					alertMsg.error("请至少输入以下字段之一：赔案号、出险人、质检人、理赔类型。");
			}else{
				$("#qCheckCommPoolJspForm",navTab.getCurrentPanel()).submit();
			}
		}
	}
	//校验赔案号
	function changeCaseNO(obj){
		obj.value=obj.value.replace(/\D/g,'');
		if(obj.value[0]!=9){
			obj.value='';
		}
	}

	//质检要点确认完成
	function qCheckClaimAfcTask(){
		if($("#afterCheckIntegrationTwoTBody tr", navTab.getCurrentPanel()).length==0){
			alertMsg.error("请选择一个赔案");
		}else{
		var num=0;
		$("#confirmFormAll", navTab.getCurrentPanel()).find("#tablecheck tbody").find("tr").each(function(){
			var checkResult;
			$(this).find(".reviseComConclusion").each(function(){
				if($(this).prop('checked') != false){
					checkResult = $(this).val();
				}
			});
			var nameval = $(this).find("td:eq(5)").find("input").val();	
			if(checkResult==1) 
			{
				if(nameval=="" || nameval==null){
					num++;
				}			
		    }
		});
		if(num>0){
			alertMsg.error("质检结果为差错的备注必填！");
			return;
		}
		url="clm/inspect/calCommentConclusion_CLM_claimAfcCheckProjectAction.action?taskId="+gTaskId+"&planId="+gPlanId,
		$('#confirmFormAll', navTab.getCurrentPanel()).attr("action",url);
		$('#confirmFormAll', navTab.getCurrentPanel()).submit();
		}
	}

	//保存质检结论
	function saveClaimAfcTask(){
		if($("#qCheckComment",navTab.getCurrentPanel()).val()==""||$("#qCheckComment",navTab.getCurrentPanel()).val()==null){
			alertMsg.error("未得出质检修正结论");
			return;
		}else if($("#qCheckReviseRemark",navTab.getCurrentPanel()).val()==""||$("#qCheckReviseRemark",navTab.getCurrentPanel()).val()==null){
			alertMsg.error("请填写修正备注");
			return;
		}else{
		$("#qCheckComment", navTab.getCurrentPanel()).removeAttr("disabled");
		da =  $('#saveReviseConclusion', navTab.getCurrentPanel()).serialize();
		$.ajax({
			 dataType:"json",    
			 type: "post",        
			 url: "clm/inspect/updateClaimAfcConclusion_CLM_claimAfcCheckProjectAction.action",
			 data: da,
			 success : function(data) {
				 DWZ.ajaxDone(data);
				    if (data.statusCode == DWZ.statusCode.ok) {
				    	alertMsg.correct(data.message);
			   		}else{
			   			alertMsg.error(data.message);
			   		}
			  }
		}); 
		}
	}

	//退出
	/*function exit(){
		alertMsg.confirm("是否确定退出？",{
			okCall:function(){
				navTab.closeCurrentTab();
			}
		});
	}*/


	//点击单选钮
	function claimAfcPlanProjectRadio(taskId,planId,caseId) {
		gTaskId = taskId;
		gPlanId = planId;
	    gCaseId = caseId;
	    $("#caseId",navTab.getCurrentPanel()).val(caseId);
	    $("#4",navTab.getCurrentPanel()).removeAttr("href");
	    $("#1",navTab.getCurrentPanel()).attr("href","clm/register/approveDataCollectInit_CLM_dataCollectAction.action?caseId="+gCaseId+"&afterFlag=1");
	    $("#2",navTab.getCurrentPanel()).attr("href","clm/audit/approvecontractDealInit_CLM_contractDealAction.action?caseId="+gCaseId+"&afterFlag=1");
	    $("#3",navTab.getCurrentPanel()).attr("href","clm/approve/approveConclusionInit_CLM_approveConclusionAction.action?caseId="+gCaseId+"&afterFlag=1");
	    $("#qualityCheckConclusionVOTaskId",navTab.getCurrentPanel()).val(taskId);
		$("#claimAfcTaskVOTaskId",navTab.getCurrentPanel()).val(taskId);
		//启用 核对案件信息、合同处理、出具审批结论和备注（！）
		 $("#1",navTab.getCurrentPanel()).parent("li").removeAttr("disabled"); 
		 $("#2",navTab.getCurrentPanel()).parent("li").removeAttr("disabled"); 
		 $("#3",navTab.getCurrentPanel()).parent("li").removeAttr("disabled"); 
		 $("#1",navTab.getCurrentPanel()).parent("li").find("a").removeAttr("disabled"); 
		 $("#2",navTab.getCurrentPanel()).parent("li").find("a").removeAttr("disabled"); 
		 $("#3",navTab.getCurrentPanel()).parent("li").find("a").removeAttr("disabled"); 
		 $("#claimRegistChangeProp",navTab.getCurrentPanel()).removeAttr("disabled"); 
		//查质检要点
	     $.ajax({
					 dataType:"json",    
					 type: "post",       
					 url: "clm/inspect/queryQualityCheckProject_CLM_claimAfcCheckProjectAction.action?taskId="+taskId+"&planId="+planId+"&caseId="+gCaseId,
					 data: {},
					 success : function(result) {
						 queryCheckConclusion();
						 var data=result.qualityCheckProjectList;
						 var str="";
						 for(var i=0;i<data.length;i++){
							 str+="<tr>";
							 str+='<td><input type="hidden" name="qualityCheckProjectList['+i+'].gistId" value="'+data[i].gistId+'">'+(i+1)+'</td>';
							 str+='<td><input type="hidden" value='+data[i].gistDesc+' name="qualityCheckProjectList['+i+'].gistDesc">'+data[i].gistDesc+'</td>';
							 if(data[i].checkResult=='0'){
								 str+='<td><input type="hidden" value='+data[i].checkResult+' name="qualityCheckProjectList['+i+'].checkResult">正常</td>';
							}else {
								 str+='<td><input type="hidden" value='+data[i].checkResult+' name="qualityCheckProjectList['+i+'].checkResult">差错</td>';
							}
							 str+='<td><input type="hidden" value='+data[i].remark+' name="qualityCheckProjectList['+i+'].remark">'+data[i].remark+'</td>';
							 if(data[i].reviseCheckResult=='0'){
								 str+='<td><input type="radio" name="qualityCheckProjectList['+i+'].reviseCheckResult" value="0" checked>正常&nbsp;&nbsp;<input type="radio" class="reviseComConclusion" name="qualityCheckProjectList['+i+'].reviseCheckResult" value="1">差错</td>';
								 str+='<td><input name="qualityCheckProjectList['+i+'].reviseRemark" value='+data[i].reviseRemark+'></td>';
							 }else if(data[i].reviseCheckResult=='1'){
								 str+='<td><input type="radio" name="qualityCheckProjectList['+i+'].reviseCheckResult" value="0">正常&nbsp;&nbsp;<input type="radio" class="reviseComConclusion" name="qualityCheckProjectList['+i+'].reviseCheckResult" value="1" checked>差错</td>';	
								 str+='<td><input name="qualityCheckProjectList['+i+'].reviseRemark" value='+data[i].reviseRemark+'></td>';
							 }else{
								 str+='<td><input type="radio" name="qualityCheckProjectList['+i+'].reviseCheckResult" value="0">正常&nbsp;&nbsp;<input type="radio" class="reviseComConclusion" name="qualityCheckProjectList['+i+'].reviseCheckResult" value="1">差错</td>';	
								 str+='<td><input name="qualityCheckProjectList['+i+'].reviseRemark"></td>';
							 }
							 str+="</tr>";
						 }
						 $("#afterCheckIntegrationTwoTBody",navTab.getCurrentPanel()).html(str);
						 $("#afterCheckIntegrationTwo",navTab.getCurrentPanel()).initUI();
						 
					  }
			}); 
	}
	//查结论 
	function queryCheckConclusion(){
		 $.ajax({
			 dataType:"json",    
			 type: "post",        
			 url: "clm/inspect/queryQualityCheckConclusion_CLM_claimAfcCheckProjectAction.action?taskId="+gTaskId+"&planId="+gPlanId,
			 data: {},
			 success : function(data) {
			   	var con=checkConSelect(data.checkConclusion);
			   	$("#qCheckConclusion",navTab.getCurrentPanel()).loadMyComboxOptions(con);
			   	
			   	$("#qCheckRemark",navTab.getCurrentPanel()).text(data.remark);		
			   	var reviseCon=checkConSelect(data.reviseCommentCode);
				$("#qCheckComment",navTab.getCurrentPanel()).loadMyComboxOptions(reviseCon);
				$("#qCheckReviseRemark",navTab.getCurrentPanel()).val(data.reviseRemark);
				
			   	$("#qCheckDate",navTab.getCurrentPanel()).val(data.checkDateStr);
				$("#qCheckOrganCode",navTab.getCurrentPanel()).val(data.organCode);
				$("#qCheckBy",navTab.getCurrentPanel()).val(data.checkBy);
				$("#qCheckName",navTab.getCurrentPanel()).val(data.checkName);
			  }
	}); 
	}
	//初始质检结论和修正结论
	function checkConSelect(param){
		var str='';
		var selected1 = "";
		var selected2 = "";
		var selected3 = "";
	   	if(param=='1'){
	   		selected1 = "selected";
	   	}else if(param=='2'){
	   		selected2 = "selected";
	   	}else if(param=='3'){
	   		selected3 = "selected";
	   	}
	   	str +='<option value="1"'+ selected1 +'>01-严重差错</option>';
	   	str+='<option value="2"'+ selected2 +'>02-一般差错</option>';
	   	str+='<option value="3"'+ selected3 +'>03-无差错</option>';
	   	return str;
	}
	//得出修正总评
	function confirmFormAllAjaxDone(json) {
		DWZ.ajaxDone(json);
		var str='';
	    if (json.statusCode == DWZ.statusCode.ok) {
	    	str = checkConSelect(json.conclusion);
	    	$("#qCheckComment",navTab.getCurrentPanel()).loadMyComboxOptions(str);
	    }
		
	}
