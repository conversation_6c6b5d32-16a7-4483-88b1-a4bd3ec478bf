//进入维护页面时质检名如果不为空则为只读
$(function() {
	var planNameId = $("#claimAfcPlanProjecPlanNameId",
			navTab.getCurrentPanel()).val();
	if (!isNulOrEmpty(planNameId)) {
		$("#claimAfcPlanProjecPlanNameId", navTab.getCurrentPanel()).attr(
				"disabled", "disabled");
		$("select#claimAfcPlanProjecPlanTypeId", navTab.getCurrentPanel())
				.attr("disabled", "disabled");
	}
	//自动简易案件 11
	var automatedCase = "";
	//非自动简易案件 12
	var notAutomatedCase = "";
	//普通案件 13
	var ordinaryCase = "";
	//诉讼案件 14
	var litigationCase = "";
	//疑难案件 15
	var difficultCase = "";
	//调查案件 16
	var investigationCase = "";
	//自动审批案件 17
	var automaticApproval = "";
	//自动审批案件 17
	var autoDirectCase = "";
	var strListId = $("#strListId",navTab.getCurrentPanel()).val();
	var strListIds = strListId.substring(1,strListId.length-1);
	var strListIdss = strListIds.split(",");
	for(var i = 0; i < strListIdss.length; i++){
		if(strListIdss[i].trim() == "11"){
			automatedCase = "checked=checked";
		} else if (strListIdss[i].trim() == "12") {
			notAutomatedCase = "checked=checked";
		} else if (strListIdss[i].trim() == "13") {
			ordinaryCase = "checked=checked";
		} else if (strListIdss[i].trim() == "14") {
			litigationCase = "checked=checked";
		} else if (strListIdss[i].trim() == "15") {
			difficultCase = "checked=checked";
		} else if (strListIdss[i].trim() == "16") {
			investigationCase = "checked=checked";
		} else if (strListIdss[i].trim() == "17") {
			automaticApproval = "checked=checked";
		} else if (strListIdss[i].trim() == "18") {
			autoDirectCase = "checked=checked";
		}
	}
	var inhtml = "<span><input style='margin-left: 30px' type='checkbox' "+automatedCase+" name='middleCheckConditionVO.caseFlag' value='11'/>自动简易案件</span>"
		+ "<span><input style='margin-left: 30px' type='checkbox' "+notAutomatedCase+" name='middleCheckConditionVO.caseFlag' value='12'/>非自动简易案件</span>"
		+ "<span><input style='margin-left: 30px' type='checkbox' "+ordinaryCase+" name='middleCheckConditionVO.caseFlag' value='13'/>普通案件</span>"
		+ "<span><input style='margin-left: 30px' type='checkbox' "+litigationCase+" name='middleCheckConditionVO.caseFlag' value='14'/>诉讼案件</span>"
		+ "<span><input style='margin-left: 30px' type='checkbox' "+difficultCase+" name='middleCheckConditionVO.caseFlag' value='15'/>疑难案件</span>"
		+ "<span><input style='margin-left: 30px' type='checkbox' "+investigationCase+" name='middleCheckConditionVO.caseFlag' value='16'/>调查案件</span>"
		+ "<span><input style='margin-left: 30px' type='checkbox' "+automaticApproval+" name='middleCheckConditionVO.caseFlag' value='17'/>自动审批案件</span>"
		+ "<span><input style='margin-left: 30px' type='checkbox' "+autoDirectCase+" name='middleCheckConditionVO.caseFlag' value='18'/>直赔案件</span>"
		$("#caseFlagId",navTab.getCurrentPanel()).find("dl:eq(0)").find("dd").append(inhtml);
		
	// 抽取层级extractLevelId
	if ($("#extractLevelId", navTab.getCurrentPanel()).val() == "1") {
		$("#clmCaseId", navTab.getCurrentPanel()).css("display", "block");
		$("#clmCustomerId", navTab.getCurrentPanel()).css("display", "none");
	}
	if ($("#extractLevelId", navTab.getCurrentPanel()).val() == "2") {
		$("#clmCaseId", navTab.getCurrentPanel()).css("display", "none");
		$("#clmCustomerId", navTab.getCurrentPanel()).css("display", "block");
	}

});
//清空
function emptyAllMesage123() {
	alertMsg.confirm("请确认是否清空？",{
		okCall : function() {
			$("#addClaimAfcPlanProjectInitId",navTab.getCurrentPanel()).submit();
		}
	});
}

/**
 * 质检要点列表的添加
 */
function addClainAfcPlan(){
	var $table = $('#tab_plan', navTab.getCurrentPanel());
	var num = $("#tab_plan tr:last td:nth-child(1)", navTab.getCurrentPanel()).html();
	num = num==null?0:num;
	var claimAfcPlanProjecPlanTypeId = $("#claimAfcPlanProjecPlanTypeId",navTab.getCurrentPanel()).val();
	$.ajax({
		url : "clm/inspect/edClainAfcPlanDel_CLM_claimAfcPlanProjectAction.action?claimAfcItemVO.checkType="+claimAfcPlanProjecPlanTypeId,
		global : false,
		type : "POST",
		dataType : "json",
		async : false,
		success : function(beneVOList) {
			var optionStr = "<option>请选择</option>";
			$.each(beneVOList, function(key, val) {
				var beneId = beneVOList[key].itemId;
				var beneName = beneVOList[key].itemName;
				optionStr += "<option value='" + beneId + "'>"
						+ beneName + "</option>";
			});
			var tabName = "tab_plan";
			var inHtml = "<select class='selectToInput' onchange='getGist(this,"+parseInt(num)+");' name='claimAfcPlanRelaVOList["+parseInt(num)+"].itemId'>"+ optionStr + "</select>";
			var insertHtml = "<tr>"
				+"<td>"+(parseInt(num) + 1)+"</td>"
				+"<td>"+inHtml+"</td>"
				+"<td></td>"
				+"<td width='76%'>"
				+"<span><input id='insertSignRoleId' type='checkbox' name='claimAfcPlanRelaVOList["+parseInt(num)+"].signRole' value='签收人/移动签收人'/>签收人/移动签收人<input size='1' name='claimAfcPlanRelaVOList["+parseInt(num)+"].signRoleDeduct' value='' onkeyup='changeThis(this)' onblur='integerCheck(this)'/></span> " 
				+"<span><input id='insertAuditOrganId' type='checkbox' name='claimAfcPlanRelaVOList["+parseInt(num)+"].auditOrgan' value='外包商/医疗复核岗'/>外包商/医疗复核岗<input size='1' name='claimAfcPlanRelaVOList["+parseInt(num)+"].auditOrganDeduct' value='' onkeyup='changeThis(this)' onblur='integerCheck(this)' /></span> " 
				+"<span><input id='insertResignRoleId' type='checkbox' name='claimAfcPlanRelaVOList["+parseInt(num)+"].resignRole' value='立案人/外包商'/>立案人/外包商<input size='1' name='claimAfcPlanRelaVOList["+parseInt(num)+"].resignRoleDeduct' value='' onkeyup='changeThis(this)' onblur='integerCheck(this)'/></span> " 
				+"<span><input id='insertSelfRuleId' type='checkbox' name='claimAfcPlanRelaVOList["+parseInt(num)+"].selfRule' value='自核规则'/>自核规则<input size='1' name='claimAfcPlanRelaVOList["+parseInt(num)+"].selfRuleDeduct' value='' onkeyup='changeThis(this)' onblur='integerCheck(this)'/></span> " 
				+"<span><input id='insertExamineRoleId' type='checkbox' name='claimAfcPlanRelaVOList["+parseInt(num)+"].examineRole' value='审核人'/>审核人<input size='1' name='claimAfcPlanRelaVOList["+parseInt(num)+"].examineRoleDeduct' value='' onkeyup='changeThis(this)' onblur='integerCheck(this)'/></span> "
				+"<span><input id='insertApproveRoleId' type='checkbox' name='claimAfcPlanRelaVOList["+parseInt(num)+"].approveRole' value='审批人'/>审批人<input size='1' name='claimAfcPlanRelaVOList["+parseInt(num)+"].approveRoleDeduct' value='' onkeyup='changeThis(this)' onblur='integerCheck(this)'/></span> " 
				+"<span><input id='insertSurveyRoleId' type='checkbox' name='claimAfcPlanRelaVOList["+parseInt(num)+"].surveyRole' value='调查人'/>调查人<input size='1' name='claimAfcPlanRelaVOList["+parseInt(num)+"].surveyRoleDeduct' value='' onkeyup='changeThis(this)' onblur='integerCheck(this)'/></span>"
				+"</td>"
				+"<td><a href='javascript:void(0);' class='btnDel' onclick='deleteRow_plan(this)'>删除</a></td>"
				+"</tr>";
			$table.append(insertHtml);
			$("#tbodyClaimAfcPlanId", navTab.getCurrentPanel()).initUI();
		}
	});
	onloadChangeSelet();
}
function changeThis(obj){
	obj.value=obj.value.replace(/\D/g,'');
}

//质检要点的下拉列表
function getGist(k,index){
	var itemId = $(k).val();
	$.ajax({
		url : "clm/inspect/edittClainAfcPlanDel_CLM_claimAfcPlanProjectAction.action?claimAfcGistVO.itemId="+itemId,
		global : false,
		type : "POST",
		dataType : "json",
		async : false,
		success : function(beneVOList) {
			var optionStr = "";
			$.each(beneVOList, function(key, val) {
				var beneId = beneVOList[key].gistId;
				var beneName = beneVOList[key].gistDesc;
				optionStr += "<option value='" + beneId + "'>"
						+ beneName + "</option>";
			});
			$("#tbodyClaimAfcPlanId",navTab.getCurrentPanel()).children("tr:eq("+index+")").children("td:eq(2)").html("<select class='selectToInput' name='claimAfcPlanRelaVOList["+index+"].gistId'>"+ optionStr + "</select>");
		}
	});
	$("tbodyClaimAfcPlanId",navTab.getCurrentPanel()).initUI();
	setTimeout(function(){
			onloadChangeSelet();},100);
}

//质检要点列表的编辑
function edClainAfcPlanDel(k,index) {
	var indexI = index-1;
	var item = $(k).parent().parent().find("td:eq(1)").find("div").find("input").val();
	var item1 = $(k).parent().parent().find("td:eq(1)").find("div").find("select").val();
	var gist = $(k).parent().parent().find("td:eq(2)").find("input").val();
	var gist1 = $(k).parent().parent().find("td:eq(2)").find("select").val();
	//多次点击编辑的处理
	if(item1 != undefined){
		item = item1;
	}
	if(gist1 != undefined){
		gist = gist1;
	}
	var claimAfcPlanProjecPlanTypeId = $("#claimAfcPlanProjecPlanTypeId",navTab.getCurrentPanel()).val();
	$.ajax({
		url : "clm/inspect/edClainAfcPlanDel_CLM_claimAfcPlanProjectAction.action?claimAfcItemVO.checkType="+claimAfcPlanProjecPlanTypeId,
		global : false,
		type : "POST",
		dataType : "json",
		async : false,
		success : function(beneVOList) {
			var optionStr = "<option>请选择</option>";
			$.each(beneVOList, function(key, val) {
				var beneId = beneVOList[key].itemId;
				var beneName = beneVOList[key].itemName;
				if(item == beneId){
					optionStr += "<option selected value='" + beneId + "'>"
					+ beneName + "</option>";
				}else{
					optionStr += "<option value='" + beneId + "'>"
					+ beneName + "</option>";
				}
			});
			$(k).parent().parent().find("td:eq(1)").find("div").html("<select class='selectToInput' value='"+item+"' onchange='getGist(this,"+indexI+");' name='claimAfcPlanRelaVOList["+indexI+"].itemId'>"+ optionStr + "</select>");
		}
	});
	$.ajax({
		url : "clm/inspect/edittClainAfcPlanDel_CLM_claimAfcPlanProjectAction.action?claimAfcGistVO.itemId="+item,
		global : false,
		type : "POST",
		dataType : "json",
		async : false,
		success : function(beneVOList) {
			var optionStr = "";
			$.each(beneVOList, function(key, val) {
				var beneId = beneVOList[key].gistId;
				var beneName = beneVOList[key].gistDesc;
				if(gist == beneId){
					optionStr += "<option selected value='" + beneId + "'>"
					+ beneName + "</option>";
				}else{
					optionStr += "<option value='" + beneId + "'>"
					+ beneName + "</option>";
				}
			});
			$(k).parent().parent().find("td:eq(2)").html("<select class='selectToInput' value='"+gist+"' name='claimAfcPlanRelaVOList["+index+"].gistId'>"+ optionStr + "</select>");
		}
	});	
	$("tbodyClaimAfcPlanId",navTab.getCurrentPanel()).initUI();
	setTimeout(function(){
			onloadChangeSelet();},10);
}
//质检要点列表删除
function deleteClaimAfcPlanRela(k){
	var str = "";
	if($(k).parent().parent().find("td:eq(3)").find("input:eq(0)").attr("checked") == "checked"){
		str+=$(k).parent().parent().find("td:eq(3)").find("input:eq(0)").val()+",";
	}
	if($(k).parent().parent().find("td:eq(3)").find("input:eq(2)").attr("checked") == "checked"){
		str+=$(k).parent().parent().find("td:eq(3)").find("input:eq(2)").val()+",";
	}
	if ($(k).parent().parent().find("td:eq(3)").find("input:eq(4)").attr("checked") == "checked") {
		str+=$(k).parent().parent().find("td:eq(3)").find("input:eq(4)").val()+",";
	} 
	if ($(k).parent().parent().find("td:eq(3)").find("input:eq(6)").attr("checked") == "checked") {
		str+=$(k).parent().parent().find("td:eq(3)").find("input:eq(6)").val()+",";
	} 
	if ($(k).parent().parent().find("td:eq(3)").find("input:eq(8)").attr("checked") == "checked") {
		str+=$(k).parent().parent().find("td:eq(3)").find("input:eq(8)").val()+",";
	} 
	if ($(k).parent().parent().find("td:eq(3)").find("input:eq(10)").attr("checked") == "checked") {
		str+=$(k).parent().parent().find("td:eq(3)").find("input:eq(10)").val()+",";
	} 
	if ($(k).parent().parent().find("td:eq(3)").find("input:eq(12)").attr("checked") == "checked") {
		str+=$(k).parent().parent().find("td:eq(3)").find("input:eq(12)").val()+",";
	}
	var gistId = $(k).parent().parent().find("td:eq(2)").find("div").find("input").val();
	
	if(gistId == undefined || str == null && str == ""){
		alertMsg.confirm("请确认是否删除？",{
			okCall : function() {
				$(k).parent().parent().remove();
			}	
		});
	} else {
		alertMsg.confirm("请确认是否删除？",{
			okCall : function() {
				$.ajax({
					url : "clm/inspect/deleteClaimAfcPlanRela_CLM_claimAfcPlanProjectAction.action?claimAfcPlanRelaVO.roleName="+str+"&claimAfcPlanRelaVO.gistId="+gistId,
					global : false,
					type : "POST",
					dataType : "json",
					success : function(s) {
						$(k).parent().parent().remove();
						if (s.statusCode == DWZ.statusCode.ok) {
							alertMsg.correct(s.message);
						} else {
							alertMsg.error(s.message);
						}
					}
				});
			}
		});
	}
}
//权限区间的图标删除
function deleteClaimAfcAuth(k) {
	//var listId = $(k).parent().parent().find("td:eq(4) input").val();
	alertMsg.confirm("请确认是否删除？",{
		okCall : function() {
			$(k).parent().parent().remove();
			/*$.ajax({
				url : "clm/inspect/delClaimAfcAuth_CLM_claimAfcPlanProjectAction.action?claimAfcAuthVO.listId="
						+ listId,
				global : false,
				type : "POST",
				dataType : "json",
				success : function(s) {
					$(k).parent().parent().remove();
					if (s.statusCode == DWZ.statusCode.ok) {
						alertMsg.correct(s.message);
					} else {
						alertMsg.error(s.message);
					}
				}
			});*/
		}
	});
}
//权限区间的编辑图标
function editClaimAfcProject(k){
	var authMinHid = $(k).parent().parent().find("input#authMinHid").val();
	var authMaxHid = $(k).parent().parent().find("input#authMaxHid").val();
	var index = $(k).parent().parent().find("td:eq(0)").text();
	var approvePermissionType = $(k).parent().parent().find("td:eq(1)").find("input#addPermissionTypeCode").val();
	var indexI = index - 1;
	$.ajax({
		url : "clm/inspect/findPermissionRegion_CLM_claimAfcPlanProjectAction.action",
		global : false,
		type : "POST",
		dataType : "json",
		success : function (json){
			var optionStr1 = "<option>请选择</option>";
			var optionStr2 = "<option>请选择</option>";
			for(var i=0;i<json.length;i++){
				var selected1 = "";
				var selected2 = "";
				if(authMinHid == json[i]){
					selected1 = "selected";
				}
				if(authMaxHid == json[i]){
					selected2 = "selected";
				}
				optionStr1 += "<option "+selected1+" value='" + json[i] + "'>"+ json[i] + "</option>";
				optionStr2 += "<option "+selected2+" value='" + json[i] + "'>"+ json[i] + "</option>";
			}
			var selected3 = "";
			var selected4 = "";
			var selected5 = "";
			if(approvePermissionType == "1"){
				selected3 = "selected";
			}
			if(approvePermissionType == "2"){
				selected4 = "selected";
			}
			if(approvePermissionType == "3"){
				selected5 = "selected";
			}
			var optionStr3 = "<option>请选择</option><option "+selected3+" value='1'>审批权限（疑难）</option><option "+selected4+" value='2'>审批权限（普通）</option><option "+selected5+" value='3'>审核权限</option>";
			
			$(k).parent().parent().find("td:eq(1)").html("<dl class='nowrap'><dt></dt><dd style='width: 150px;'><select onchange='queryAddApprovePermissionType(this);' name='claimAfcAuthVOList["+indexI+"].permissionTypeCode' class='combox title'>"+optionStr3+"</select></dd></dl>");
			$(k).parent().parent().find("td:eq(2)").html("<dl class='nowrap'><dt></dt><dd style='width: 150px;'><select class='combox' name='claimAfcAuthVOList["+indexI+"].authMin'>"+optionStr1+"</select></dd>"+'<span style="padding-left:10px">至</span>'+"<dd><select class='combox' name='claimAfcAuthVOList["+indexI+"].authMax'>"+optionStr2+"</select></dd></dl>");
			$(k).parent().parent().initUI();
		} 
	});
}
//操作员编辑图标
function editClaimAfcOpr(k){
	var index = $(k).parent().parent().find("td:eq(0)").text();
	var indexI = index - 1;
	var	extractRate = $(k).parent().parent().find("td:eq(3)").find("input").val();
	$(k).parent().parent().find("td:eq(3)").html("<div align='center'><input value="+extractRate+" size='9' onfocusout = 'onfocusoutTwo(this)' name='claimAfcOprVOList["+indexI+"].extractRate'*100/></div>");

}
//复选框事件
function upExtractRateOpr(k) {
	var extractRate = $(k).parent().parent().parent().find("td:eq(3)").find("div").find("input:eq(0)").val();
	var index = $(k).parent().parent().parent().find("td:eq(0)").find("div").text();
	var indexI = index - 1;
	if(extractRate == ""){
		extractRate= 0;
	}
	if($(k).attr("checked") != null){
		$(k).parent().parent().parent().find("td:eq(3)").find("div").html("<input size='9' value="+extractRate+" id='extractRateIdId' name='claimAfcOprVOList["+indexI+"].extractRate'/>");
	} else if ($(k).attr("checked") == null){
		$(k).parent().parent().parent().find("td:eq(3)").find("div").find("input").attr("disabled",true);
	}
}
//操作机构编辑按钮
function editClaimAfcOrg(k){
	var extractRate = $(k).parent().parent().find("td:eq(3)").find("input:eq(0)").val();
	var index = $(k).parent().parent().find("td:eq(0)").html();
	var indexI = index - 1;
	if(extractRate == ""){
		extractRate= 0;
	}
	$(k).parent().parent().find("td:eq(3)").html("<input size='9' value="+extractRate+" id='extractRateIdId' onfocusout = 'onfocusoutTwo(this)' name='claimAfcOrgVOList["+indexI+"].extractRate'/>");
}
//权限的区间的添加
function addClaimAfcAuthVO(){
	var num = $("#tab_auth tr:last td:nth-child(1)", navTab.getCurrentPanel()).html();
	num = num==null?0:num;
	$.ajax({
		url : "clm/inspect/findPermissionRegion_CLM_claimAfcPlanProjectAction.action",
		global : false,
		type : "POST",
		dataType : "json",
		success : function (json){
			var optionStr = "<option>请选择</option>";
			for(var i=0;i<json.length;i++){
				optionStr += "<option value='" + json[i] + "'>"+ json[i] + "</option>";
			}
			
			var inHtml = "<dl class='nowrap'><dt></dt><dd style='width: 150px;'><select class='combox' name='claimAfcAuthVOList["+parseInt(num)+"].authMin'>"+optionStr+"</select></dd>"+'<span style="padding-left:10px">至</span>'+"<dd><select class='combox' name='claimAfcAuthVOList["+parseInt(num)+"].authMax'>"+optionStr+"</select></dd></dl>";
			var insertHtml = "<tr class='thisTR'>"
							+"<td>"+(parseInt(num)+1)+"</td>"
							+"<td> "+"<select onchange='queryAddApprovePermissionType(this);' name='claimAfcAuthVOList["+parseInt(num)+"].permissionTypeCode' class='combox title'><option value=''>请选择</option><option value='1'>审批权限（疑难）</option><option value='2'>审批权限（普通）</option><option value='3'>审核权限</option></select>"+" </td>"
							+"<td>"+inHtml+"</td>"
							+"<td><input  type='hidden' value='' /><a title='删除' class='btnDel' id='delButton' href='javascript:void(0);'onclick='deleteRow_auth(this);'>删除</a></td>";
			$("#claimAfcAuthVOTbodyId", navTab.getCurrentPanel()).append(insertHtml);
			$("#claimAfcAuthVOTbodyId", navTab.getCurrentPanel()).initUI();
		}
	});
}

//根据权限类型查询审批权限
function queryAddApprovePermissionType(k) {
	
	var url = "";
	var approvePermissionType = $(k).val();
	if (approvePermissionType == '2') {
		url = "clm/handworkAssignTask/queryPermissionRegion_CLM_handworkAssignTaskAction.action?permissionTypeVO.taskType=4&permissionTypeVO.difficultFlag=0";
	} else if (approvePermissionType == '1'){
		url = "clm/handworkAssignTask/queryPermissionRegion_CLM_handworkAssignTaskAction.action?permissionTypeVO.taskType=4&permissionTypeVO.difficultFlag=1";
	} else if (approvePermissionType == '3'){
		url = "clm/handworkAssignTask/queryPermissionRegion_CLM_handworkAssignTaskAction.action?permissionTypeVO.taskType=3";
	}
	
	$.ajax({
		url : url,
		global : false,
		type : "POST",
		dataType : "json",
		success : function(json) {
			var data = eval(json);
			var interHTML = "<option value=''>请选择</option>";
			for(var i = 0; i < data.length; i++){
				interHTML += "<option value='" + data[i].permissionName + "'>" + data[i].permissionName + "</option>";
			}
			var num = $(k).closest(".thisTR").find("td:eq(0)").html();
			var selectHtml = "<select class='combox' name='claimAfcAuthVOList["+parseInt(num-1)+"].authMin'>"+interHTML+"</select>"
			var selectHtml1 = "<select class='combox' name='claimAfcAuthVOList["+parseInt(num-1)+"].authMax'>"+interHTML+"</select>"
			$(k).closest(".thisTR").find("td:eq(2)").find("dd:first").empty().append(selectHtml).initUI();
			$(k).closest(".thisTR").find("td:eq(2)").find("dd:last").empty().append(selectHtml1).initUI();
		}
	});
}

/**
 * 操作人权限抽检率失去焦点方法
 * @param obj
 */
function onfocusoutTwo(obj){
	//单独处理0跟100
	if($(obj).val() == "0" || $(obj).val() == "100"){
		
	}else{
		var reg=new RegExp("^(([0-9]+\.[0-9]*[1-9][0-9]*)|([0-9]*[1-9][0-9]*\.[0-9]+)|([0-9]*[1-9][0-9]*))$");
		if(!reg.test($(obj).val())){
			$(obj).val("");
			alertMsg.error('抽取比例在0至100之间');
		}else{
			if($(obj).val()>=100){
				$(obj).val("");
				alertMsg.error('抽取比例在0至100之间');
			}
		}
	}
}

//操作机构的添加
function addClaimAfcOrg(){
	var $table = $('#tab_org', navTab.getCurrentPanel());
	var num = $("#tab_org tr:last td:nth-child(1)").find("div").html();
	num = num==null?0:num;
	var  insertHtml = "<tr>"
		+"<td style='width: 10% '>"+(parseInt(num) + 1)+"</td>"
		+"<td><div align='center'><input name='claimAfcOrgVOList["+parseInt(num)+"].organCode'/></div></td>"
		+"<td><div align='center'><input/></div></td>"
		+"<td><div align='center'><input name='claimAfcOrgVOList["+parseInt(num)+"].extractRate'/></div></td>"
		+"<td><a href='javascript:;' class='btnDel' onclick='deleteRow_org(this)'>删除</a></td>"
		+"</tr>";
	$table.append(insertHtml);
	$("#claimAfcOrgTbId", navTab.getCurrentPanel()).initUI();
}
//操作机构的复选框事件
function upExtractRate(k) {
	var extractRate = $(k).parent().parent().parent().find("td:eq(3)").find("div").find("input:eq(0)").val();
	var listId = $(k).parent().parent().parent().find("td:eq(3)").find("div").find("input:eq(1)").val();
	var index = $(k).parent().parent().parent().find("td:eq(0)").find("div").text();
	var indexI = index - 1;
	if(extractRate == ""){
		extractRate= 0;
	}
	if($(k).attr("checked") != null){
		$(k).parent().parent().parent().find("td:eq(3)").find("div").html("<input size='9' value="+extractRate+" id='extractRateId' name='claimAfcOrgVOList["+indexI+"].extractRate'/><input type='hidden' value="+listId+" name='claimAfcOrgVOList["+indexI+"].listId'/>");
	} else if ($(k).attr("checked") == null){
		$(k).parent().parent().parent().find("td:eq(3)").find("div").find("input").attr("disabled",true);
	}
}
//操作员的添加
function insertClaimAfcOprO () {
	var $table = $('#tab_oper', navTab.getCurrentPanel());
	var num = $("#tab_oper tr:last td:nth-child(1)", navTab.getCurrentPanel()).html();
	num = num==null?0:num;
	var  insertHtml = "<tr>"
		+"<td>"+(parseInt(num)+1)+"</td>"
		+"<td><input id='claimAfcOprId' class='data' name='claimAfcOprVOList["+parseInt(num)+"].checkPer' type='hidden'/> <input name='claimAfcOprVOList["+parseInt(num)+"].userName' readonly='readonly'/>"
		+"<a class='btnLook' href='clm/inspect/queryOpr_CLM_claimAfcPlanProjectAction.action' lookupGroup='claimAfcOprVOList["+parseInt(num)+"]'>业务人员查询 </a></td>"
		+"<td><input name='claimAfcOprVOList["+parseInt(num)+"].realName' readonly='readonly'/></td>"
		+"<td><a title='删除' class='btnDel' id='delButton' href='javascript:void(0);' onclick='deleteRow_oper(this);'>删除</a></td>";
		+"</tr>";
	$table.append(insertHtml);
	$("#claimAfcOprVId", navTab.getCurrentPanel()).initUI();
}

/**
 * 删除质检要点列表表格中相应的行（点击行内删除按钮）
 */
function deleteRow_plan(obj) {
	$(obj).parent().parent().remove();
	var i = 0;
	$("#tab_plan tr:gt(0)", navTab.getCurrentPanel()).each(function() {
		$(this).find("td:eq(0)").html((++i));
	});
}
function deleteRow_auth(obj) {
	$(obj).parent().parent().remove();
	var i = 0;
	$("#tab_auth tr:gt(0)", navTab.getCurrentPanel()).each(function() {
		$(this).find("td:eq(0)").html((++i));
	});
}
function deleteRow_org(obj) {
	$(obj).parent().parent().remove();
	var i = 0;
	$("#tab_org tr:gt(0)", navTab.getCurrentPanel()).each(function() {
		$(this).find("td:eq(0)").html((++i));
	});
}
function deleteRow_oper(obj) {
	$(obj).parent().parent().remove();
	var i = 0;
	$("#tab_oper tr:gt(0)", navTab.getCurrentPanel()).each(function() {
		$(this).find("td:eq(0)").html((++i));
	});
}
//保存
function inserClaimAfc(){
	// 质检要点列表
	var trs = $("#tbodyClaimAfcPlanId",navTab.getCurrentPanel()).children();
	if(trs.length == 0){
		alertMsg.error("质检要点列表不能为空");
		return false;
	}
	for(var i = 0; i < trs.length; i++){
		//质检要点
		var itemlist1 = $("#tbodyClaimAfcPlanId", navTab.getCurrentPanel()).children("tr:eq("+i+")").children("td:eq(1)").find("select").val();
		var itemlist2 = $("#tbodyClaimAfcPlanId", navTab.getCurrentPanel()).children("tr:eq("+i+")").children("td:eq(1)").find("input").val();
		var gistlist1 = $("#tbodyClaimAfcPlanId", navTab.getCurrentPanel()).children("tr:eq("+i+")").children("td:eq(2)").find("select").val();
		var gistlist2 = $("#tbodyClaimAfcPlanId", navTab.getCurrentPanel()).children("tr:eq("+i+")").children("td:eq(2)").find("input").val();
		//代表页面带入的数据，没有编辑
		if(gistlist1 == undefined){
			if(itemlist2 == "" || gistlist2 == ""){
				alertMsg.error("质检要点或质检项目必填");
				return false;
			}
		}else{
			if(itemlist1 == "请选择" || gistlist1 == ""){
				alertMsg.error("质检要点或质检项目必填");
				return false;
			}
		}
		//受检岗位
		var isCheckNum = 0;
		$("#tbodyClaimAfcPlanId", navTab.getCurrentPanel()).children("tr:eq("+i+")").children("td:eq(3)").find("input[type='checkbox']").each(function(){
			if ($(this).is(':checked')) {
				isCheckNum++;
			}
		});
		if(isCheckNum == 0){
			alertMsg.error("受检岗位至少勾选一个");
			return false;
		}
		
		if($("#tbodyClaimAfcPlanId", navTab.getCurrentPanel()).children("tr:eq("+i+")").find("input#insertSignRoleId").is(':checked')){
			if($("#tbodyClaimAfcPlanId", navTab.getCurrentPanel()).children("tr:eq("+i+")").find("#insertSignRoleId").next().val().trim() == ""){
				alertMsg.error("勾选受检岗位，请录入扣分标准");
				return false;
			}
		}
		if($("#tbodyClaimAfcPlanId", navTab.getCurrentPanel()).children("tr:eq("+i+")").find("input#insertAuditOrganId").is(':checked')){
			if($("#tbodyClaimAfcPlanId", navTab.getCurrentPanel()).children("tr:eq("+i+")").find("#insertAuditOrganId").next().val().trim() == ""){
				alertMsg.error("勾选受检岗位，请录入扣分标准");
				return false;
			}
		}
		if($("#tbodyClaimAfcPlanId", navTab.getCurrentPanel()).children("tr:eq("+i+")").find("input#insertResignRoleId").is(':checked')){
			if($("#tbodyClaimAfcPlanId", navTab.getCurrentPanel()).children("tr:eq("+i+")").find("#insertResignRoleId").next().val().trim() == ""){
				alertMsg.error("勾选受检岗位，请录入扣分标准");
				return false;
			}
		}
		if($("#tbodyClaimAfcPlanId", navTab.getCurrentPanel()).children("tr:eq("+i+")").find("input#insertSelfRuleId").is(':checked')){
			if($("#tbodyClaimAfcPlanId", navTab.getCurrentPanel()).children("tr:eq("+i+")").find("#insertSelfRuleId").next().val().trim() == ""){
				alertMsg.error("勾选受检岗位，请录入扣分标准");
				return false;
			}
		}
		if($("#tbodyClaimAfcPlanId", navTab.getCurrentPanel()).children("tr:eq("+i+")").find("input#insertExamineRoleId").is(':checked')){
			if($("#tbodyClaimAfcPlanId", navTab.getCurrentPanel()).children("tr:eq("+i+")").find("#insertExamineRoleId").next().val().trim() == ""){
				alertMsg.error("勾选受检岗位，请录入扣分标准");
				return false;
			}
		}
		if($("#tbodyClaimAfcPlanId", navTab.getCurrentPanel()).children("tr:eq("+i+")").find("input#insertApproveRoleId").is(':checked')){
			if($("#tbodyClaimAfcPlanId", navTab.getCurrentPanel()).children("tr:eq("+i+")").find("#insertApproveRoleId").next().val().trim() == ""){
				alertMsg.error("勾选受检岗位，请录入扣分标准");
				return false;
			}
		}
		if($("#tbodyClaimAfcPlanId", navTab.getCurrentPanel()).children("tr:eq("+i+")").find("input#insertSurveyRoleId").is(':checked')){
			if($("#tbodyClaimAfcPlanId", navTab.getCurrentPanel()).children("tr:eq("+i+")").find("#insertSurveyRoleId").next().val().trim() == ""){
				alertMsg.error("勾选受检岗位，请录入扣分标准");
				return false;
			}
		}
	}
	// 操作人权限区间
	var trList = $("#claimAfcAuthVOTbodyId",navTab.getCurrentPanel()).children();
	// 操作员
	var trIds = $("#claimAfcOprVId",navTab.getCurrentPanel()).children();
	// 操作机构
	var orgs = $("#claimAfcOrgTbId",navTab.getCurrentPanel()).children();
	// 抽检方式校验
	if($("#extractRateType").val() == "1"){ // 按比例抽检
		if($("#extractRate").val() == null || $("#extractRate").val() == ""){
			alertMsg.error("按比例抽检时，抽检率不能为空！");
			return false;
		}
	}
	// 案件标识
	var caseFlagList = $("#caseFlagId1", navTab.getCurrentPanel()).find("input[type='checkbox']:checked");
	var surveyItemNum = caseFlagList.length;
	if(surveyItemNum == 0) {
		if(trList.length == 0){
			alertMsg.error("不勾选案件标识，操作人权限信息必填");
			return false;
		}else{
			for(var i = 0; i < trList.length; i++){
				//操作人权限抽检率
				var typeCode1 = $("#claimAfcAuthVOTbodyId", navTab.getCurrentPanel()).children("tr:eq("+i+")").children("td:eq(1)").find("input").val();
				var typeCode2 = $("#claimAfcAuthVOTbodyId", navTab.getCurrentPanel()).children("tr:eq("+i+")").children("td:eq(1)").find("select").val();
				var authMin1 = $("#claimAfcAuthVOTbodyId", navTab.getCurrentPanel()).children("tr:eq("+i+")").children("td:eq(2)").find("input:eq(0)").val();
				var authMin2 = $("#claimAfcAuthVOTbodyId", navTab.getCurrentPanel()).children("tr:eq("+i+")").children("td:eq(2)").find("select:eq(0)").val();
				var authMax1 = $("#claimAfcAuthVOTbodyId", navTab.getCurrentPanel()).children("tr:eq("+i+")").children("td:eq(2)").find("input:eq(1)").val();
				var authMax2 = $("#claimAfcAuthVOTbodyId", navTab.getCurrentPanel()).children("tr:eq("+i+")").children("td:eq(2)").find("select:eq(1)").val();
				if(typeCode2 != undefined){//代表是编辑或新增
					if(typeCode2 == "请选择" || authMin2 == "请选择" || authMax2 == "请选择"){
						alertMsg.error("不勾选案件标识，存在未填写完成的“操作人权限区间”信息");
						return false;
					}
				}
				if(typeCode1 != undefined){//代表页面初始化，为了阻断错误数据
					if(typeCode1 == "" || authMin1 == "" || authMax1 == ""){
						alertMsg.error("不勾选案件标识，存在未填写完成的“操作人权限区间”信息");
						return false;
					}
				}
				//抽检率已校验（抽取比例在0至100之间）
			}
		}
	} else {
		for(var j=0;j<caseFlagList.length;j++) {
			 if($(caseFlagList[j]).val() == '12' || $(caseFlagList[j]).val() == '16') {
				if(trIds.length == 0){
					alertMsg.error("案件标识为非自动简易案件或调查案件，操作员信息必填");
					return false;
				}else{
					for(var i = 0; i < trIds.length; i++){
						var checkPer = $("#claimAfcOprVId", navTab.getCurrentPanel()).children("tr:eq("+i+")").children("td:eq(1)").find("input").val();
						if(checkPer == ""){
							alertMsg.error("案件标识为非自动简易案件或调查案件，存在未填写完成的“操作员”信息");
							return false;
						}
					}
				}
			} else if($(caseFlagList[j]).val() == '13' || $(caseFlagList[j]).val() == '17') {
				if(trList.length == 0){
					alertMsg.error("案件标识为普通案件或自动审批案件，操作人权限信息必填");
					return false;
				}else{
					for(var i = 0; i < trList.length; i++){
						//操作人权限抽检率
						var typeCode1 = $("#claimAfcAuthVOTbodyId", navTab.getCurrentPanel()).children("tr:eq("+i+")").children("td:eq(1)").find("input").val();
						var typeCode2 = $("#claimAfcAuthVOTbodyId", navTab.getCurrentPanel()).children("tr:eq("+i+")").children("td:eq(1)").find("select").val();
						var authMin1 = $("#claimAfcAuthVOTbodyId", navTab.getCurrentPanel()).children("tr:eq("+i+")").children("td:eq(2)").find("input:eq(0)").val();
						var authMin2 = $("#claimAfcAuthVOTbodyId", navTab.getCurrentPanel()).children("tr:eq("+i+")").children("td:eq(2)").find("select:eq(0)").val();
						var authMax1 = $("#claimAfcAuthVOTbodyId", navTab.getCurrentPanel()).children("tr:eq("+i+")").children("td:eq(2)").find("input:eq(1)").val();
						var authMax2 = $("#claimAfcAuthVOTbodyId", navTab.getCurrentPanel()).children("tr:eq("+i+")").children("td:eq(2)").find("select:eq(1)").val();
						if(typeCode2 != undefined){//代表是编辑或新增
							if(typeCode2 == "请选择" || authMin2 == "请选择" || authMax2 == "请选择"){
								alertMsg.error("案件标识为普通案件或自动审批案件，存在未填写完成的“操作人权限区间”信息");
								return false;
							}
						}
						if(typeCode1 != undefined){//代表页面初始化，为了阻断错误数据
							if(typeCode1 == "" || authMin1 == "" || authMax1 == ""){
								alertMsg.error("案件标识为普通案件或自动审批案件，存在未填写完成的“操作人权限区间”信息");
								return false;
							}
						}
					}
				}
			}
		}
	}
	// 操作人权限区间
	if(trList.length > 0){
		for(var i = 0; i < trList.length; i++){
			//操作人权限抽检率
			var typeCode1 = $("#claimAfcAuthVOTbodyId", navTab.getCurrentPanel()).children("tr:eq("+i+")").children("td:eq(1)").find("input").val();
			var typeCode2 = $("#claimAfcAuthVOTbodyId", navTab.getCurrentPanel()).children("tr:eq("+i+")").children("td:eq(1)").find("select").val();
			var authMin1 = $("#claimAfcAuthVOTbodyId", navTab.getCurrentPanel()).children("tr:eq("+i+")").children("td:eq(2)").find("input:eq(0)").val();
			var authMin2 = $("#claimAfcAuthVOTbodyId", navTab.getCurrentPanel()).children("tr:eq("+i+")").children("td:eq(2)").find("select:eq(0)").val();
			var authMax1 = $("#claimAfcAuthVOTbodyId", navTab.getCurrentPanel()).children("tr:eq("+i+")").children("td:eq(2)").find("input:eq(1)").val();
			var authMax2 = $("#claimAfcAuthVOTbodyId", navTab.getCurrentPanel()).children("tr:eq("+i+")").children("td:eq(2)").find("select:eq(1)").val();
			if(typeCode2 != undefined){//代表是编辑或新增
				if(typeCode2 != "请选择" || authMin2 != "请选择" || authMax2 != "请选择"){
					if(typeCode2 == "请选择" || authMin2 == "请选择" || authMax2 == "请选择"){
						alertMsg.error("存在未填写完成的“操作人权限区间”信息");
						return false;
					}
				}
			}
			if(typeCode1 != undefined){//代表页面初始化，为了阻断错误数据
				if(typeCode1 != "" || authMin1 != "" || authMax1 != ""){
					if(typeCode1 == "" || authMin1 == "" || authMax1 == ""){
						alertMsg.error("存在未填写完成的“操作人权限区间”信息");
						return false;
					}
				}
			}
		}
	}
	// 操作机构
	if(orgs.length > 0){
		for(var i = 0; i < orgs.length; i++){
			var organCode = $("#claimAfcOrgTbId", navTab.getCurrentPanel()).children("tr:eq("+i+")").children("td:eq(1)").find("input").val();
			var organName = $("#claimAfcOrgTbId", navTab.getCurrentPanel()).children("tr:eq("+i+")").children("td:eq(2)").find("input").val();
			var extractRate = $("#claimAfcOrgTbId", navTab.getCurrentPanel()).children("tr:eq("+i+")").children("td:eq(3)").find("input").val();
			if(organCode != "" || organName != "" || extractRate != ""){
				if(organCode == "" || organName == ""){
					alertMsg.error("存在未填写完成的“操作机构”信息");
					return false;
				}
			}
		}
	}
	// 操作员
	if(trIds.length > 0){
		for(var i = 0; i < trIds.length; i++){
			var checkPer = $("#claimAfcOprVId", navTab.getCurrentPanel()).children("tr:eq("+i+")").children("td:eq(1)").find("input").val();
			var extractRate = $("#claimAfcOprVId", navTab.getCurrentPanel()).children("tr:eq("+i+")").children("td:eq(3)").find("input").val();
			if(checkPer != "" || extractRate != ""){
				if(checkPer == "" || extractRate == ""){
					alertMsg.error("存在未填写完成的“操作员”信息");
					return false;
				}
			}
		}
	}
	$("#selectedProduct option", navTab.getCurrentPanel()).each(function(){
		this.selected=true;
	});
	$("#orgList option", navTab.getCurrentPanel()).each(function(){
		this.selected=true;
	});
	var orgLength = $("#orgList", navTab.getCurrentPanel()).children().length;
	var productLength = $("#selectedProduct", navTab.getCurrentPanel()).children().length;
	if(orgLength > 300){
		alertMsg.error("请选择适量的机构信息");
		return false;
	}
	if(productLength > 300){
		alertMsg.error("请选择适量的险种信息");
		return false;
	}
	$("#areaList option", navTab.getCurrentPanel()).each(function(){
		this.selected=true;
	});
	$("#insertClaimAfcPlanProjectId",navTab.getCurrentPanel()).submit();
}
//根据抽检方式控制“抽检率”和“检查水平”，“检查级别”显示隐藏
function changeExtractRateType(value){
	if(value == "1"){// 抽检方式为1，按比例抽检：
		// 抽检率显示
		$("#extractRateDiv").css("display",'block');
		// 检查水平隐藏
		$("#inspectionStandardDiv").css("display",'none');
		// 检查级别隐藏
		$("#inspectionLevelDiv").css("display",'none');
	}else if(value == "2"){// 抽检方式为1，按国标抽检：
		// 抽检率隐藏
		$("#extractRateDiv").css("display",'none');
		// 抽检率置为空
		$("#extractRate").val("");
		// 检查水平显示
		$("#inspectionStandardDiv").css("display",'block');
		// 检查级别显示
		$("#inspectionLevelDiv").css("display",'block');
	}
}
//抽检率输入范围在0-100
function changeeExtractRate(value){
    var limitNum = value.replace(/[^0-9.]+/g, "");
    if(limitNum == null || limitNum == ""){
    	alertMsg.error("请输入0-100之间的数值");
		$("#extractRate").val("");
		return false;
    }
    if(limitNum <0 || limitNum > 100){
		alertMsg.error("请输入0-100之间的数值");
		$("#extractRate").val("");
		return false;
	}
}
//根据检查水平控制检查级别的值
function changeInspectionStandard(value){
	if(value != null && value != "" ){
		$.ajax({
  			'type':'post',
  			'url':'clm/inspect/queryInspectionLevel_CLM_claimAfcPlanProjectAction.action?inspectionLevelnumberVO.inspectionStandard='+value,
  			'datatype':'json',
  			'success':function(data){
  				var data = eval("(" + data + ")");
  				var interHTML = "";
  				var selected1 = "";
  				if($("#inspectionLevelnumber").val() != null && $("#inspectionLevelnumber").val() != ""){
  					var inspectionLevelnumber = $("#inspectionLevelnumber").val();
  				}
  				for(var i = 0; i<data.length; i++){
  					if(data[i].levelnumberCode == inspectionLevelnumber){
  						selected1 = "selected";
  						interHTML += "<option value="+data[i].levelnumberCode+" "+selected1+">"+data[i].levelnumberName+"</option>";
  					}else{
  						interHTML += "<option value="+data[i].levelnumberCode+">"+data[i].levelnumberName+"</option>";
  					}
  					//interHTML += "<option value="+data[i].levelnumberCode+" if test='"+inspectionLevelnumber+"=="+data[i].levelnumberCode+"'>selected</s:if> >"+data[i].levelnumberName+"</option>";
  					
  				}
  				var selectHtml = "<select class='combox' name='claimAfcPlanVO.inspectionLevelnumber' id='inspectionLevelnumber'>"+interHTML+"</select>";
  				$("#inspectionLevelDd").empty().append(selectHtml).initUI();
  			},
  			'error':function(){
  				alert("出错了！");
  			}
  	}); 
	}
}
//操作机构列表信息展示
function tabOrgInit(){
	var $table = $('#tab_org', navTab.getCurrentPanel());
	$.ajax({
		url : "queryAllOrganTree_organTreeAction.action?1=1&uporganCode=86",
		global : false,
		type : "POST",
		dataType : "json",
		success : function(data) {
			for(var num=0;num<data.length;num++){
				var  insertHtml = 
					"<tr>"
						+"<td width='20%'>"+(parseInt(num) + 1)
							+"<input type='checkbox' flag='uwflag' style='border: 0px; background: 0px; width: auto; float: none;'"
							+"name='claimAfcOrgVOList["+parseInt(num)+"].isChecked' id='uwcheck"+(parseInt(num) + 1)+"'>"
						+"</td>"
						+"<td width='40%'>" 
							+"<input name='claimAfcOrgVOList["+parseInt(num)+"].organCode' id='branchCode"+(parseInt(num) + 1)+"'"
							+"type='text' class='organ' clickId='menuBtn"+(parseInt(num) + 1)+"' showOrgName='branchname"+(parseInt(num) + 1)+"' readonly='readonly' needAll='true' value='"+ data[num].organCode+ "' />"
						+"</td>"
						+"<td width='40%'>" 
							+"<input id='branchname"+(parseInt(num) + 1)+"' type='text' size='30' readOnly value='"+ data[num].organName + "' />"
						+"</td>"
					+"</tr>";
				$table.append(insertHtml);
				$("#claimAfcOrgTbId", navTab.getCurrentPanel()).initUI();
			}
		}
	});
	
}