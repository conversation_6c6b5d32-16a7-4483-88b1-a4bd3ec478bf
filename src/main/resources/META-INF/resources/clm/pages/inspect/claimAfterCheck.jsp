<%@ page language="java" contentType="text/html; charset=utf-8"
	pageEncoding="UTF-8"%><%@ include file="/clm/pages/common/commonJsCss.jsp" %>
<%@ taglib uri="/struts-tags" prefix="s"%>

<%@ taglib uri="http://java.sun.com/jsp/jstl/fmt" prefix="fmt"%>
<%@ taglib uri="http://www.git.com.cn/taglib/udmp/field" prefix="Field"%>
<script type="text/javascript"
	src="clm/pages/inspect/claimAfterCheck.js"></script>

<div layouth="10">
	<div class="divfclass">
		<h1><img src="clm/images/tubiao.png">维护质检项目</h1>
	</div>
	<form id="cliamAfterCheckRest" method="post"
						onsubmit="return navTabSearch(this)"
						action="clm/inspect/queryClaimAfterCheck_CLM_claimAfterCheckProjectAction.action">
	</form>					
	<form id="addClaimAfterCheck" method="post"
		onsubmit="return navTabSearch(this)"
		action="clm/inspect/addClaimAfterCheck_CLM_claimAfterCheckProjectAction.action">
	</form>
	<form id="pagerForm" method="post"
			action="clm/inspect/findClaimAfterCheck_CLM_claimAfterCheckProjectAction.action">
			<input type="hidden" name="pageNum" value="${pageInfo.currentPage}" />
			<input type="hidden" name="numPerPage" value="${pageInfo.numPerPage}" />
	</form>
	
	<form id="findClaimAfterCheck" method="post"
		onsubmit="return navTabSearch(this)"
		action="clm/inspect/findClaimAfterCheck_CLM_claimAfterCheckProjectAction.action" rel="pagerForm">
		<div class="pageFormInfoContent">
			<div class="divfclass">
				<h1><img src="clm/images/tubiao.png">查询条件</h1>
			</div>
			<dl>
				<dt> <font class="point" color="red">* </font>质 检 类 型  </dt>
				<dd>
					<Field:codeTable cssClass="combox title"  id="checkType" nullOption="true" name="claimAfcItemVO.checkType" value="${claimAfcItemVO.checkType}" tableName="APP___CLM__DBUSER.T_QC_TYPE" whereClause="1=1" orderBy="decode(code,'0','2','1','1',code)"></Field:codeTable>
				</dd>
			</dl>
			<div class="pageFormdiv">
				<button type="button" class="but_blue" onclick="queryClaimAfterCheckProject();">查询</button>
				<button type="button" class="but_blue" id="claimAfterAddId" onclick="add();">新增</button>
				<input id="claimAfterCheckItemIdId" value="" type="hidden" />
			</div>
		</div>
	</form>
		<div class="divfclass">
			<h1><img src="clm/images/tubiao.png">查询质检项目结果</h1>
		</div>
			<div class="tabdivclassbr">
				<table class="list" width="100%">
					<thead>
						<tr>
							<th nowrap>序号</th>
							<th nowrap>质检类型</th>
							<th nowrap>质检项目名称</th>
						</tr>
					</thead>
					<tbody>
						<s:if test="imageFlag != null">
							<tr>
								<td colspan="10">
									<div class="noRueryResult">请选择条件查询数据！</div>
								</td>
							</tr>
						</s:if>
						<s:elseif test="currentPage.pageItems == null || currentPage.pageItems.size()==0">
							<tr>
								<td colspan="10">
									<div class="noRueryResult">没有符合条件的查询结果！</div>
								</td>
							</tr>
						</s:elseif>
						<s:iterator value="currentPage.pageItems" var="claimAfcItem"
							status="st">
							<tr>
								<td><div align="center"><fmt:formatNumber value='${(currentPage.pageNo-1)*currentPage.pageSize + st.index+1}' pattern="000"/>
									<input type="radio" name="radio" id="ClaimAfteritemIdradio" style="border:0px;background:0px;width:auto; float: left;" onclick="findClaimAfterCheckProject(this);" value="${itemId}">
									</div></td>
								<td>
									<div align="center">
										<Field:codeValue tableName="APP___CLM__DBUSER.T_QC_TYPE" value="${checkType}"/>
									</div>
								</td>
								<td><div align="center">${itemName}</div></td>
							</tr>
						</s:iterator>
					</tbody>
				</table>
				<div class="panelBar" >
					<div class="pages">
							<span>显示</span>
							<s:select   list="#{5:'5',10:'10',20:'20',50:'50'}"  name="select" onchange="navTabPageBreak({numPerPage:this.value})" value="currentPage.pageSize">
				  				</s:select>
							<span>条，共${currentPage.total}条</span>		
					</div>
					<div class="pagination" targetType="navTab" 
						totalCount="${currentPage.total}"
						numPerPage="${currentPage.pageSize}" pageNumShown="10"
						currentPage="${currentPage.pageNo}">
					</div>
				</div>
			</div>
	<div id="findClaimAfterCheckProjects">
			<form id="saveClaimAfterCheckProject" method="post"
				action="clm/inspect/saveClaimAfterCheckProject_CLM_claimAfterCheckProjectAction.action"
				class="pageForm required-validate"
				onsubmit="return validateCallback(this, saveAjaxDone);">
		<div class="divfclass">
			<h1><img src="clm/images/tubiao.png">质检项目</h1>
		</div>
			<input id="clmAfterItemIdId" type="hidden" name="claimAfcItemVO.itemId" value="" />
				<div class="panelPageFormContent">
					<dl>
						<dt><font class="point" color="red">* </font>质检类型 </dt>
						<dd>
							<Field:codeTable cssClass="combox title"  id="findCheckType"
								name="claimAfcItemVO.checkType"
								value="${claimAfcItemVO.checkType}" tableName="APP___CLM__DBUSER.T_QC_TYPE" whereClause="1=1" orderBy="decode(code,'0','2','1','1',code)"></Field:codeTable>
						</dd>
					</dl>
					<dl>
						<dt><font class="point" color="red">* </font>有效期标志</dt>
						<dd>
							<input id="clmAfterCheckClickId" name="claimAfcItemVO.validFlag" style="border:0px;background:0px;width:auto; float: left;"
								type="checkbox" value="${claimAfcItemVO.validFlag}"
								onclick="clmAfterCheckClick();" />有效
						</dd>
					</dl>
					<dl>
						<dt><font class="point" color="red">* </font>质检项目名称</dt>
						<dd>
							<input id="findItemNameId" name="claimAfcItemVO.itemName"
								value="${claimAfcItemVO.itemName}" size="100" style="width: 180px;" />
						</dd>
					</dl>
					<dl>
						<dt>制定日期</dt>
						<dd>
							<input id="creatDateId" name="claimAfcItemVO.creatDate"
								value="<s:date name="claimAfcItemVO.creatDate" format="yyyy-MM-dd"/>"
								disabled="disabled" /><a class="inputDateButton"
								href="javascript:;" disabled="disabled">选择</a> <input
								name="claimAfcItemVO.creatDate"
								value="${claimAfcItemVO.creatDateStr}" type="hidden" />
						</dd>
					</dl>
					<dl>
						<dt>制定人</dt>
						<dd>
							<input id="userIdId" disabled="disabled" style="width: 55px;border-right:0px" value="${claimAfcItemVO.userId}">
							<input id="userNameId" disabled="disabled" style="width: 110px;" value="${claimAfcItemVO.userName}">
						</dd>
					</dl>
				</div>
		
		<div class="divfclass">
			<h1><img src="clm/images/tubiao.png">质检要点管理</h1>
		</div>	
			<div class="panelPageFormContent">
				<dl>
					<dt><font class="point" color="red">* </font>质检项目名称</dt>
					<dd>
						<input name="claimAfcItemVO.itemId" style="width: 30px;border-right:0px" value="" id="itemIdId"
							disabled="disabled" size="2"><input style="width: 150px;" id="findItemName"value="" disabled="disabled" />
					</dd>
				</dl>
				<dl>
					<dt>质检类型</dt>
					<dd>
						<input id="findCheckTypeId" value="" size="3" disabled="disabled"/>
					</dd>
				</dl>
			</div>
		<div class="divfclass">
			<h1><img src="clm/images/tubiao.png">质检要点列表</h1>
		</div>
			<div class="tabdivclassbr">
				<table class="list" width="100%">
					<thead>
						<tr>
							<td type="text" name="items[#index#].itemInt" readonly
								defaultVal="#index#" fieldClass="digits" nowrap>序号</td>
							<td nowrap>质检要点说明</td>
							<!-- <td nowrap>有效标志</td> -->
							<td nowrap>操作</td>
						</tr>
					</thead>
					<tbody id="claimAfcChcekIdId">
					</tbody>
				</table>
			</div>
		
			<div class="formBarButton">
				<ul>
					<li><button type="reset" class="but_gray" onclick="cliamAfterCheckRest();">重置</button></li>
					<li><button type="button"class="but_blue" id="saveButton" type="button" onclick="saveClaimAfterCheckProject();" disabled="disabled">保存</button></li>
					<li><button type="reset" class="but_gray" onclick="exit();">退出</button></li>
				</ul>
			</div>
		</form>
	</div>
</div>
