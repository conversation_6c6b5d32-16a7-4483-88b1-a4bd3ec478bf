$(function(){
	//如果没有数据返回上一页面
	var nullFlag = $("#nullFlagId", navTab.getCurrentPanel()).val();
	if(nullFlag == "true"){
		alertMsg.info("未抽取符合条件的任务，请重新抽取");
		$("#backoutExtractId", navTab.getCurrentPanel()).submit();
	}
});
//删除
function deleteCurrentPage(){
	var taskId = "";
	$("#currentPageVarTbodyId", navTab.getCurrentPanel()).find("[type='checkbox']").each(function(){
		if($(this).attr("checked") == "checked"){
			taskId+=$(this).val()+",";
		}
	});
	var planId = $("#currentPageVarPlanId", navTab.getCurrentPanel()).val();
	alertMsg.confirm("请确认是否删除？",{
		okCall : function() {
			$.ajax({
				url : "clm/inspect/deleteCurrentPageVar_CLM_extractClaimAfcPlanProjectAction.action?claimAfcTaskVO.taskIdStr="+taskId+"&claimAfcTaskVO.planId="+planId,
				global : false,
				type : "POST",
				dataType : "json",
				success : function(s) {
					if (s.statusCode == DWZ.statusCode.ok) {
						$("#queryClaimTaskId", navTab.getCurrentPanel()).attr("action","clm/inspect/queryClaimTask_CLM_extractClaimAfcPlanProjectAction.action?claimAfcTaskVO.planId="+planId);
						$("#queryClaimTaskId", navTab.getCurrentPanel()).submit();
					}
				}
			
			});
		}
	});
}

function changeCaseNO(obj){
	obj.value=obj.value.replace(/\D/g,'');
	if(obj.value[0]!=9){
		obj.value='';
	}
}

//查询
function queryClaimTask(){
	var caseno = $("#caseNo", navTab.getCurrentPanel()).val();
	if(caseno.length != 0 && caseno.length != 11){
		alertMsg.warn("录入的赔案号不正确，请重新输入！");
		return false;
	}
	var planId = $("#currentPageVarPlanId", navTab.getCurrentPanel()).val();
	$("#queryClaimTaskId", navTab.getCurrentPanel()).attr("action","clm/inspect/queryClaimTask_CLM_extractClaimAfcPlanProjectAction.action?claimAfcTaskVO.planId="+planId);
	$("#queryClaimTaskId", navTab.getCurrentPanel()).submit();
}
//确认
function affirmInformation(){
	var planId = $("#extractClaimAfcPlanId" , navTab.getCurrentPanel()).val();
	//判断是否选中
	var taskIdStr="";
	$("#currentPageVarTbodyId", navTab.getCurrentPanel()).find("[type='checkbox']").each(function(){
		if($(this).attr("checked") == "checked"){
			taskIdStr+=$(this).val()+",";
		}
	}); 
	
	if(taskIdStr==""){
		alertMsg.error("至少选择一条记录");
		return false;
	}
	 //end
	 $.ajax({
		url : "clm/inspect/affirmInformation_CLM_extractClaimAfcPlanProjectAction.action?claimAfcTaskVO.planId="+planId+"&claimAfcTaskVO.taskIdStr="+taskIdStr,
		type : "POST",
		dataType : "json",
		success : function(s) {
			if (s.statusCode == DWZ.statusCode.ok) {
//				alertMsg.correct("质检任务已分配给质检人"+s.message);
				alertMsg.correct("已经生成质检任务。");
				queryClaimTask();
				//$("#pagerForm", navTab.getCurrentPanel()).submit();
			} else {
				alertMsg.error(s.message);
			}
			}
	}); 
}
//撤销
function backoutExtract(){
	var planId = $("#extractClaimAfcPlanId" , navTab.getCurrentPanel()).val();
	$.ajax({
		url : "clm/inspect/backoutExtract_CLM_extractClaimAfcPlanProjectAction.action?claimAfcTaskVO.planId="+planId,
		type : "POST",
		dataType : "json",
		success : function(s) {
			if (s.statusCode == DWZ.statusCode.ok) {
				$("#backoutExtractId", navTab.getCurrentPanel()).submit();
			} else {
				alertMsg.error(s.message);
			}
			}
	});
}