
// 查询
function queryClaimAfcPlanProject() {
	$("#queryClaimAfcPlanProject", navTab.getCurrentPanel()).submit();
}
// 新增
function addClaimAfcPlanProject() {
	$("#addClaimAfcPlanProject", navTab.getCurrentPanel()).submit();
}
// 点击radio
function claimAfcPlanProjectRadio(radioValue) {
	var radioValues = $(radioValue).val();
	var valueSplit = radioValues.split(":");
	$("#claimAfcPlanProjecPlanName", navTab.getCurrentPanel()).val(valueSplit[0]);
	$("#claimAfcPlanProjecPlanType", navTab.getCurrentPanel()).val(valueSplit[1]);
	$("#claimAfcPlanProjecPlanId", navTab.getCurrentPanel()).val(valueSplit[2]);
	$("#claimAfcPlanProjectRadio", navTab.getCurrentPanel()).submit();
}



function saveClaimAfcPlanRela(k){
	var str = "";
	var strDeduct = "";
	var listId = $(k).parent().parent().find("td:eq(4)").find("input").val();
	var itemId = $(k).parent().parent().find("td:eq(1)").find("select").val();
	var gistId = $(k).parent().parent().find("td:eq(2)").find("select").val();
	var planId = $("#claimAfcProjectPlanId",navTab.getCurrentPanel()).val();

	if($(k).parent().parent().find("td:eq(3)").find("input:eq(0)").attr("checked") == "checked"){
		str+=$(k).parent().parent().find("td:eq(3)").find("input:eq(0)").val()+",";
	}
	if($(k).parent().parent().find("td:eq(3)").find("input:eq(2)").attr("checked") == "checked"){
		str+=$(k).parent().parent().find("td:eq(3)").find("input:eq(2)").val()+",";
	}
	if ($(k).parent().parent().find("td:eq(3)").find("input:eq(4)").attr("checked") == "checked") {
		str+=$(k).parent().parent().find("td:eq(3)").find("input:eq(4)").val()+",";
	} 
	if ($(k).parent().parent().find("td:eq(3)").find("input:eq(6)").attr("checked") == "checked") {
		str+=$(k).parent().parent().find("td:eq(3)").find("input:eq(6)").val()+",";
	} 
	if ($(k).parent().parent().find("td:eq(3)").find("input:eq(8)").attr("checked") == "checked") {
		str+=$(k).parent().parent().find("td:eq(3)").find("input:eq(8)").val()+",";
	} 
	if ($(k).parent().parent().find("td:eq(3)").find("input:eq(10)").attr("checked") == "checked") {
		str+=$(k).parent().parent().find("td:eq(3)").find("input:eq(10)").val()+",";
	} 
	if ($(k).parent().parent().find("td:eq(3)").find("input:eq(12)").attr("checked") == "checked") {
		str+=$(k).parent().parent().find("td:eq(3)").find("input:eq(12)").val()+",";
	}
	$.ajax({
		url : "clm/inspect/saveClaimAfcPlanRela_CLM_claimAfcPlanProjectAction.action?claimAfcPlanRelaVO.listId="+listId+"&claimAfcPlanRelaVO.itemId="+itemId+"&claimAfcPlanRelaVO.gistId="+gistId+"&claimAfcPlanRelaVO.planId="+planId+"&claimAfcPlanRelaVO.roleName="+str+"&claimAfcPlanRelaVO.deductPoint="+strDeduct,
		type : "POST",
		dataType : "json",
		success : function (json){
			$(k).parent().parent().find("td:eq(3)").find("div").text(json.extractRate*10);
		}
	});
	
}
// 操作员的图标删除
function deleteClaimAfcOpr(k) {
	//var listId = $(k).prev().val();
	alertMsg.confirm("请确认是否删除？",{
		okCall : function() {
			$(k).parent().parent().remove();
			/*$.ajax({
				url : "clm/inspect/delClaimAfcOpr_CLM_claimAfcPlanProjectAction.action?claimAfcOprVO.listId="
						+ listId,
				global : false,
				type : "POST",
				dataType : "json",
				success : function(s) {
					$(k).parent().parent().remove();
					if (s.statusCode == DWZ.statusCode.ok) {
						alertMsg.correct(s.message);
					} else {
						alertMsg.error(s.message);
					}
				}
			});*/
		}
	});
}
// 从左侧移动到右侧
function moveselect(objId, targetId, flag, all) {

	var obj = document.getElementById(objId);
	var target = document.getElementById(targetId);
	if (flag) {
		// 判断all是否为1,唯一则调用转移所有的方法
		if (all == "1") {
			for ( var i = 0; i < obj.length; i++) {
				mot = obj.options[i].text;
				mov = obj.options[i].value;
				var newoption = document.createElement("OPTION");
				newoption.text = mot;
				newoption.value = mov;
				target.add(newoption);
			}
			obj.options.length = 0;
		} else {
			while (obj.selectedIndex > -1) {
				mot = obj.options[obj.selectedIndex].text;
				mov = obj.options[obj.selectedIndex].value;
				obj.remove(obj.selectedIndex);
				var newoption = document.createElement("OPTION");
				newoption.text = mot;
				newoption.value = mov;
				target.add(newoption);
			}
		}
	} else {
		while (obj.selectedIndex > -1) {
			mot = obj.options[obj.selectedIndex].text;
			mov = obj.options[obj.selectedIndex].value;
			obj.remove(obj.selectedIndex);
			var newoption = document.createElement("OPTION");
			newoption.text = mot;
			newoption.value = mov;
			target.add(newoption);
			// 为报案机构的时候查询人员
			getInstitutions();
		}
	}
}

// 赔案1，客户2，如果是客户层级，则把赔案为不可修改。如果是赔案层级，则把客户为不可修改。
function getExtractLevel(k) {
	if ($(k).val() == "1") {
		$("#clmCaseId", navTab.getCurrentPanel()).css("display", "block");
		$("#clmCustomerId", navTab.getCurrentPanel()).css("display", "none");
	}
	if ($(k).val() == "2") {
		$("#clmCaseId", navTab.getCurrentPanel()).css("display", "none");
		$("#clmCustomerId", navTab.getCurrentPanel()).css("display", "block");
	}
}


//质检要点的下拉列表
function getGist(k,index){
	var itemId = $(k).val();
	$.ajax({
		url : "clm/inspect/edittClainAfcPlanDel_CLM_claimAfcPlanProjectAction.action?claimAfcGistVO.itemId="+itemId,
		global : false,
		type : "POST",
		dataType : "json",
		async : false,
		success : function(beneVOList) {
			var optionStr = "";
			$.each(beneVOList, function(key, val) {
				var beneId = beneVOList[key].gistId;
				var beneName = beneVOList[key].gistDesc;
				optionStr += "<option value='" + beneId + "'>"
						+ beneName + "</option>";
			});
			$(k).parent().parent().parent("tr:eq(index)").find("td:eq(2)").find("div").html("<select class='combox' name='claimAfcPlanRelaVOList["+index+"].gistId' id='beneNameId'>"+ optionStr + "</select>");
		}
	});
}
//质检要点列表的添加
function addClainAfcPlanDel(k,index){
	$(k).parent().find("a:eq(2)").remove();
	var indexI = index+1;
	var claimAfcPlanProjecPlanTypeId = $("#claimAfcPlanProjecPlanTypeId",navTab.getCurrentPanel()).val();
	$.ajax({
		url : "clm/inspect/edClainAfcPlanDel_CLM_claimAfcPlanProjectAction.action?claimAfcItemVO.checkType="+claimAfcPlanProjecPlanTypeId,
		global : false,
		type : "POST",
		dataType : "json",
		async : false,
		success : function(beneVOList) {
			var optionStr = "<option>请选择</option>";
			$.each(beneVOList, function(key, val) {
				var beneId = beneVOList[key].itemId;
				var beneName = beneVOList[key].itemName;
				optionStr += "<option value='" + beneId + "'>"
						+ beneName + "</option>";
			});
			var inHtml = "<select class='combox' onchange='getGist(this,"+index+");' name='claimAfcPlanRelaVOList["+index+"].itemId'>"+ optionStr + "</select>";
			var insertHtml = "<tr>"
				+"<td style='width: 10% '><div align='center'>"+indexI+"</div>" 
				+"</td>"
				+"<td style='width: 10%'><div align='center'>"+inHtml+"</div></td>"
				+"<td style='width: 10%'><div align='center'>"
				+"</div></td>"
				+"<td>"
				+"<input type='checkbox' name='claimAfcPlanRelaVOList["+index+"].signRole' value='签收人/移动签收人'/>签收人/移动签收人<input size='1' name='claimAfcPlanRelaVOList["+index+"].signRoleDeduct' value='' onblur='integerCheck(this)'/> " 
				+"<input type='checkbox' name='claimAfcPlanRelaVOList["+index+"].auditOrgan' value='外包商/医疗复核岗'/>外包商/医疗复核岗<input size='1' name='claimAfcPlanRelaVOList["+index+"].auditOrganDeduct' value='' onblur='integerCheck(this)'/> " 
				+"<input type='checkbox' name='claimAfcPlanRelaVOList["+index+"].resignRole' value='立案人/外包商'/>立案人/外包商<input size='1' name='claimAfcPlanRelaVOList["+index+"].resignRoleDeduct' value='' onblur='integerCheck(this)'/> " 
				+"<input type='checkbox' name='claimAfcPlanRelaVOList["+index+"].selfRule' value='自核规则'/>自核规则<input size='1' name='claimAfcPlanRelaVOList["+index+"].selfRuleDeduct' value='' onblur='integerCheck(this)'/> " 
				+"<input type='checkbox' name='claimAfcPlanRelaVOList["+index+"].examineRole' value='审核人'/>审核人<input size='1' name='claimAfcPlanRelaVOList["+index+"].examineRoleDeduct' value='' onblur='integerCheck(this)'/> "
				+"<input type='checkbox' name='claimAfcPlanRelaVOList["+index+"].approveRole' value='审批人'/>审批人<input size='1' name='claimAfcPlanRelaVOList["+index+"].approveRoleDeduct' value='' onblur='integerCheck(this)'/> " 
				+"<input type='checkbox' name='claimAfcPlanRelaVOList["+index+"].surveyRole' value='调查人'/>调查人<input size='1' name='claimAfcPlanRelaVOList["+index+"].surveyRoleDeduct' value='' onblur='integerCheck(this)'/>"
				+"</td>"
				+"<td style='width: 10%>" 
				+"<input type='hidden' value='${listId}'' /><a title='编辑' class='btnEdit' id='editButtonId' href='javascript:void(0);' onclick='edClainAfcPlanDel(this,"+index+");'>编辑</a>"
				+"<a title='添加' class='btnAdd' id='addButton' href='javascript:void(0);' onclick='addClainAfcPlanDel(this,"+indexI+");'>添加</a>"
				+"<a title='删除'class='btnDel' id='delButton' href='javascript:void(0);'onclick='deleteClaimAfcPlanRela(this);'>删除</a></td>"
				+"</td>"
				+"</tr>";
			$("#tbodyClaimAfcPlanId", navTab.getCurrentPanel()).append(
					insertHtml);
			$("#tbodyClaimAfcPlanId", navTab.getCurrentPanel()).initUI();
			$(k).parent().parent().find("td:eq(4)").find("a:eq(1)").css("display",'none');
		}
	});
}
//清空
function emptyMessage(){
	var palnId = $("#claimAfcProjectPlanId",navTab.getCurrentPanel()).val();
	$("#claimAfcPlanVOPlanId",navTab.getCurrentPanel()).val(palnId);
	alertMsg.confirm("请确认是否清空？",{
		okCall : function() {
			$("#claimAfcPlanProjectRadioId", navTab.getCurrentPanel()).submit();
		}
	});
}
//扣分校验
function integerCheck(k){
	var deductVal = $(k).val().trim();
	var prev = $(k).prev();
	if(prev.attr('checked') && deductVal==''){
		alertMsg.error("扣分标准在0至100分");
	}
	if(deductVal < 0 || deductVal > 100 ){
		alertMsg.error("扣分标准在0至100分");
	}
}

//质检计划结果删除    （质检要点列表删除）
function deleteClaimAfcPlanResult(k){
	alertMsg.confirm("请确认是否删除？",{
		okCall : function() {
			var radioValues = $(k).attr("value");
			var valueSplit = radioValues.split(":");
			$.ajax({
				url : "clm/inspect/delClaimAfcPlanProjectRadio_CLM_claimAfcPlanProjectAction.action?claimAfcPlanVO.planId="+valueSplit[2],
				global : false,
				type : "POST",
				dataType : "json",
				success : function(s) {
					$(k).parent().parent().parent().remove();
					if (s.statusCode == DWZ.statusCode.ok) {
						alertMsg.correct(s.message);
					} else {
						alertMsg.error(s.message);
					}
				}
			});
			
		}
	});
}
