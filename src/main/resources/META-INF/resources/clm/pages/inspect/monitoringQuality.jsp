<%@ page language="java" contentType="text/html; charset=utf-8"
	pageEncoding="UTF-8"%><%@ include file="/clm/pages/common/commonJsCss.jsp" %>
<%@ taglib uri="/struts-tags" prefix="s"%>

<%@ taglib uri="http://www.git.com.cn/taglib/udmp/field" prefix="Field"%>

<script type="text/javascript">	
   function queryNumber(){
	   var caseno = $("#caseNo", navTab.getCurrentPanel()).val();
		if(caseno.length != 0 && caseno.length != 11){
			alertMsg.warn("录入的赔案号不正确，请重新输入！");
			return false;
		}
	   var makeDate = $("#makeDate", navTab.getCurrentPanel()).val();
	   var checkDate = $("#checkDate", navTab.getCurrentPanel()).val();
	   var caseNo = $("#caseNo", navTab.getCurrentPanel()).val();
	   if(makeDate == "" && checkDate == "" && caseNo == ""){
		   alertMsg.warn("请至少录入质检生成起止日期、赔案号中的一个查询条件！");
		   return false;
	   }
	   $("#quality", navTab.getCurrentPanel()).submit();
   }
   function changeCaseNO(obj){
		obj.value=obj.value.replace(/\D/g,'');
		if(obj.value[0]!=9){
			obj.value='';
		}
	}
   function queryData(){
	   var action =$("#quality", navTab.getCurrentPanel()).attr("action");
	   $("#quality", navTab.getCurrentPanel()).attr("action","clm/inspect/queryData_CLM_monitoringQualityAction.action");
	   $("#quality", navTab.getCurrentPanel()).submit();
	   $("#quality", navTab.getCurrentPanel()).attr("action",action);
   }
   //退出
  /*  function monitExit(){
	   alertMsg.confirm("当前页面录入的信息将不被保存，是否确定退出？",{
		 	okCall:function(){
				navTab.closeCurrentTab();
		 	}
		  });
   } */
   $(function(){
	   var flag="${flag}";
	   //如果flag不为0说明是初始化进入将质检数设置为空
	   if(flag!="0"){
		  $("[name='claimAfcTaskVO.checkTotal']", navTab.getCurrentPanel()).val(""); 
		  $("[name='claimAfcTaskVO.checkNumber']", navTab.getCurrentPanel()).val(""); 
		  $("[name='claimAfcTaskVO.checkNumberNo']", navTab.getCurrentPanel()).val(""); 
	   }
   });
</script>
<div class="pageContent" layoutH="50">
	<form id="pagerForm" method="post"
		action="clm/inspect/queryData_CLM_monitoringQualityAction.action">
		<input type="hidden" name="pageNum" value="${claimAfcPlanPage.pageNo} " />
		<input type="hidden" name="numPerPage" value="${claimAfcPlanPage.pageSize}" />
		<input type="hidden" name="paramObject" value="${claimAfcPlanPage.paramObject}" />
	</form>
	<form id="quality"
		action="clm/inspect/queryNumber_CLM_monitoringQualityAction.action"
		method="post" onsubmit="return navTabSearch(this);"  rel="pagerForm">
		<div class="pageContent">
		<div class="divfclass">
			<h1><img src="clm/images/tubiao.png">查询条件</h1>
		</div>
			<div class="pageFormInfoContent">
				<dl>
						<dt>计划名称</dt>
						<dd>
						  <select class="combox title" name="claimAfcTaskVO.planId" value="claimAfcTaskVO.planId">
						     <option value="" >请选择</option>
							 <s:iterator value="claimAfcPlanVOs" var="claimAfcPlan">
								 <option value="${planId}" <s:if test="claimAfcTaskVO.planId == planId">selected</s:if> >${planName}</option>
							 </s:iterator>
						  </select>
						</dd>
				</dl>
				<dl>
					<dt>质检生成起止日期</dt>
					<dd>
						<input  type="expandDateYMD" class="data"  name="claimAfcTaskVO.makeDate" id="makeDate"
							value="<s:date name='claimAfcTaskVO.makeDate' format='yyyy-MM-dd'/>"
							/> <a class="inputDateButton" href="javascript:;">选择</a>
						<span  style="padding-left: 15px">至</span>
					</dd>	
					<dd>	
						<input type="expandDateYMD" class="data"  
							name="claimAfcTaskVO.checkDate" id="checkDate"
							value="<s:date name='claimAfcTaskVO.checkDate' format='yyyy-MM-dd'/>"
							/> <a class="inputDateButton" href="javascript:;">选择</a>
					</dd>
				</dl>
				<dl>
						<dt>赔案号</dt>
						<dd>
							<input type="text" id="caseNo" maxlength="11" onkeyup="changeCaseNO(this)" name="claimAfcTaskVO.caseNo"
								value="${claimAfcTaskVO.caseNo }" />
						</dd>
				</dl>
				<dl>
						<dt>管理机构</dt>
						<dd>
							<select class="selectToInput" name="claimAfcTaskVO.organCode" value="claimAfcTaskVO.organCode">
						     <option value="" >请选择</option>
							 <s:iterator value="organVOs" var="claimAfcPlan">
								 <option value="${organCode}" <s:if test="claimAfcTaskVO.organCode == organCode">selected</s:if> >${organName}</option>
							 </s:iterator>
						  </select> 
						</dd>
				</dl>
				<dl>
						<dt>质检人</dt>
						<dd>
						<Field:codeTable  cssClass="combox title" name="claimAfcTaskVO.checkBy"  tableName="APP___CLM__DBUSER.t_udmp_user" value="${claimAfcTaskVO.checkBy}"  
						whereClause="exists(select  check_by  from    APP___CLM__DBUSER.T_CLAIM_MIDC_TASK   where  check_by=user_id  union select check_by from    APP___CLM__DBUSER.T_CLAIM_AFC_TASK where  check_by=user_id )" nullOption="true"   />
							 
						</dd>
				</dl>
				<div class="pageFormdiv"><button type="button" class="but_blue" onclick="queryNumber()" >查询</button></div>
			</div>
		
		<div class="divfclass">
			<h1><img src="clm/images/tubiao.png">查询结果汇总</h1>
		</div>
				<div class="panelPageFormContent">
				 <dl>
						<dt>质检汇总</dt>
						<dd>
							<input type="text"
								name="claimAfcTaskVO.checkTotal" value="${claimAfcTaskVO.checkTotal }" onkeydown="this.onkeyup();" onkeyup="this.size=(this.value.length>4?this.value.length:4);" size="4" readonly="readonly" />
						</dd>
				</dl>
				<dl>
						<dt>已质检数</dt>
						<dd>
							<input  type="text"
								name="claimAfcTaskVO.checkNumber" value="${claimAfcTaskVO.checkNumber }" onkeydown="this.onkeyup();" onkeyup="this.size=(this.value.length>4?this.value.length:4);" size="4" readonly="readonly" />
						</dd>
				</dl>
				<dl>
						<dt>未质检数</dt>
						<dd>
							<input  type="text" 
								name="claimAfcTaskVO.checkNumberNo" value="${claimAfcTaskVO.checkNumberNo }" onkeydown="this.onkeyup();" onkeyup="this.size=(this.value.length>4?this.value.length:4);" size="4" readonly="readonly" />
						</dd>
				</dl>
				</div>
		
		<div class="divfclass">
			<h1><img src="clm/images/tubiao.png">查询结果列表</h1>
		</div>
			<div class="pageFormInfoContent">
			<dl>
				<dt>质检状态</dt>
						<dd>
							<select class="combox title"  name="claimAfcTaskVO.qcStatus">
								<option value=""  <s:if test="claimAfcPlanPage.paramObject.qcStatus eq null">selected="selected"</s:if>>全部</option>
								<option value="0" <s:if test="claimAfcPlanPage.paramObject.qcStatus eq 0">selected="selected"</s:if>>待质检</option>
								<option value="1" <s:if test="claimAfcPlanPage.paramObject.qcStatus eq 1">selected="selected"</s:if>>质检中</option>
								<option value="2" <s:if test="claimAfcPlanPage.paramObject.qcStatus eq 2">selected="selected"</s:if>>质检完成</option>
								
							</select>
						</dd>
				</dl>
				<div class="pageFormdiv"><button type="button" class="but_blue" onclick="queryData()">查询</button></div>
			</div>
				<div  class="tabdivclassbr main_tabdiv" >
				<a id="newSign" href="#" target="ajaxTodo" rel="starttask1111"></a>
				<table class="list main_dbottom" width="100%">
					<thead align="center">
						<tr>
							<th nowrap>选择</th>
							<th nowrap>赔案号</th>
							<th nowrap>质检状态</th>
							<th nowrap>管理机构</th>
							<th nowrap>审核人</th>
							<th nowrap>审批人</th>
							<th nowrap>质检人</th>
						</tr>
					</thead>
					<tbody align="center" id="documents">
						<!-- 循环显示数据 -->
						<s:if test="imageFlag != null">
							<tr>
								<td colspan="10">
									<div class="noRueryResult">请选择条件查询数据！</div>
								</td>
							</tr>
						</s:if>
						<s:elseif test="claimAfcPlanPage.pageItems == null || claimAfcPlanPage.pageItems.size()==0">
							<tr>
								<td colspan="10">
									<div class="noRueryResult">没有符合条件的查询结果！</div>
								</td>
							</tr>
						</s:elseif>
						<s:iterator  value="claimAfcPlanPage.pageItems" status="var">
							<tr>
								<td><input name="templateCode" type="radio" style="border:0px;background:0px;width:auto; float: left;" 
								  <s:if test="#var.index eq 0">checked="checked"</s:if>	></td>
								<td>${caseNo }</td>
								<td><Field:codeValue tableName="APP___CLM__DBUSER.T_QC_STATUS" value="${qcStatus}"/></td>
								<td>${organCode }</td>
								<td>${auditorName}</td>
								<td>${approverName}</td>
								<td>${checkName}</td>
							</tr>
						</s:iterator>
				  </tbody>
				</table>
			<div class="panelBar">
		<div class="pages">
			<span>显示</span>
			<s:select list="#{1:'1',5:'5',10:'10',20:'20'}"
				name="select" onchange="navTabPageBreak({numPerPage:this.value})"
				value="claimAfcPlanPage.pageSize">
			</s:select>
			<span>条，共${claimAfcPlanPage.total}条</span>
		</div>
		<div class="pagination" targetType="navTab"
			totalCount="${claimAfcPlanPage.total}"
			numPerPage="${claimAfcPlanPage.pageSize}" pageNumShown="10"
			currentPage="${claimAfcPlanPage.pageNo}"></div>
		</div>
			</div>
		
					<div class="formBarButton">
						<button type="button"  class="but_gray" onclick="exit()" >退出</button>
					</div>
		</div>
	</form>
</div>
