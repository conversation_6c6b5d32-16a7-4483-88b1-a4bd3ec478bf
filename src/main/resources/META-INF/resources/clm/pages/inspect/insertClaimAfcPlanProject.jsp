<%@ page language="java" contentType="text/html; charset=utf-8" pageEncoding="UTF-8"%><%@ include file="/clm/pages/common/commonJsCss.jsp" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/fmt" prefix="fmt"%>
<%@ taglib uri="/struts-tags" prefix="s"%>

<%@ taglib uri="http://www.git.com.cn/taglib/udmp/field" prefix="Field"%>

<script type="text/javascript" src="clm/pages/inspect/insertClaimAfcPlanProject.js"></script>
<script type="text/javascript">
function aaa(){
	$("#seriousDiseasePageId" , navTab.getCurrentPanel()).attr("href","clm/inspect/queryOpr_CLM_claimAfcPlanProjectAction.action");//从报案走的需要加上一个标识
	$("#seriousDiseasePageId" , navTab.getCurrentPanel()).click();
}
//险种快速查询
var val_flag = "";  //定义一个标识，防止多次重复验证
$("#businessSearchId", navTab.getCurrentPanel()).bind("input propertychange",function(){
	var value = $("#businessSearchId", navTab.getCurrentPanel()).val();
	if(value != val_flag){
		val_flag = value;
		var optionStr = "";
		for(var i=0;i<_businessArray.length;i++){
			var obj = _businessArray[i];
			var text = obj.productCodeSys + "-" + obj.productNameSys;
			if(text.indexOf(value) != "-1"){
				optionStr = optionStr + "<option value='"+ obj.productCodeSys +"'>"+ obj.productCodeSys + "-" + obj.productNameSys + "</option>";
			}
		}
		$("#allProduct", navTab.getCurrentPanel()).html("");
		$("#allProduct", navTab.getCurrentPanel()).append(optionStr);
	}
});
//片区快速查询
var appendItem_flag = "";  //定义一个标识，防止多次重复验证
$("#appendItemSearchId", navTab.getCurrentPanel()).bind("input propertychange",function(){
	var value = $("#appendItemSearchId", navTab.getCurrentPanel()).val();
	if(value != appendItem_flag){
		appendItem_flag = value;
		var optionStr = "";
		for(var i=0;i<_apendItemArray.length;i++){
			var obj = _apendItemArray[i];
			var text = obj.areaCode + "-" +  obj.areaName;
			if(text.indexOf(value) != "-1"){
				optionStr = optionStr + "<option value='" + obj.areaCode + "'>" + obj.areaCode + "-" + obj.areaName + "</option>";
			};
		};
		$("#selectedArea", navTab.getCurrentPanel()).html("");
		$("#selectedArea", navTab.getCurrentPanel()).append(optionStr);
	}
});
//机构快速查询
var organ_flag = "";  //定义一个标识，防止多次重复验证
$("#organSearchId", navTab.getCurrentPanel()).bind("input propertychange", function(){
	var value = $("#organSearchId", navTab.getCurrentPanel()).val();
	if(value != organ_flag){
		organ_flag = value;
		var optionStr = "";
		for(var i=0;i<_organArray.length;i++){
			var obj = _organArray[i];
			var text = obj.organCode + "-" +  obj.organName;
			if(text.indexOf(value) != "-1"){
				optionStr = optionStr + "<option value='" + obj.organCode + "'>" + obj.organCode + "-" + obj.organName + "</option>";
			};
		};
		$("#selectedOrganization", navTab.getCurrentPanel()).html("");
		$("#selectedOrganization", navTab.getCurrentPanel()).append(optionStr);
	};
});

//定义险种集合
var _businessArray = new Array();

//定义片区集合
var _apendItemArray = new Array();

//定义机构集合
var _organArray = new Array();


//操作机构全选按钮实现
$("#uwtomessage", navTab.getCurrentPanel()).change(function(){
	  var check=$(this).attr("checked");
	  if(check=="checked"){
		  $('input[flag="uwflag"]', navTab.getCurrentPanel()).each(function(){
				if($(this).prop('checked') != true){
					$(this).attr("checked",true);
					$(this).val(1);
				}
		  });
	  }else{
		  $('input[flag="uwflag"]', navTab.getCurrentPanel()).each(function(){
				if($(this).prop('checked') != false){
					$(this).attr("checked",false);
					$(this).val(0);
				}
		  });
	  }
});
// 调用操作机构列表信息初始化方法
tabOrgInit();

// 初始化页面时调用检查水平change事件
claimFireEvent($("#inspectionStandard"));
</script>

<s:iterator value="businessProductVOs" var="bus">
	<script>
		var obj = new Object();
		obj.productCodeSys = '<s:property value="#bus.productCodeSys"/>';
		obj.productNameSys = '<s:property value="#bus.productNameSys"/>';
		_businessArray.push(obj);
	</script>
</s:iterator>	
<s:iterator value="areaList" var="item">
	<script>
	var obj = new Object();
	obj.areaCode = '<s:property value="#item.areaCode"/>';
	obj.areaName = '<s:property value="#item.areaName"/>';
	_apendItemArray.push(obj);
	</script>
</s:iterator>	
<s:iterator value="orgVOList" var="org">
	<script>
		var obj = new Object();
		obj.organCode = '<s:property value="#org.organCode"/>';
		obj.organName = '<s:property value="#org.organName"/>';
		_organArray.push(obj);
	</script>
</s:iterator>
<a lookupGroup="claimAfcOprVOList[<s:property value="0"/>]"" id="seriousDiseasePageId" width="1000" height="600"  href="" class="btnLook" style="visibility: hidden;">业务人员查询</a>
<div layoutH="0">
	<form id="addClaimAfcPlanProjectInitId" method="post"
		onsubmit="return navTabSearch(this)"
		action="clm/inspect/addClaimAfcPlanProjectInit_CLM_claimAfcPlanProjectAction.action">
	</form>
	<form action="clm/inspect/insertAddClaimAfcPlanProject_CLM_claimAfcPlanProjectAction.action"
		method="post" class="pageForm required-validate" id="insertId"
		onsubmit="return validateCallback(this)">
		<div class="divfclass">
			<h1><img src="clm/images/tubiao.png">维护质检计划</h1>
		</div>
		<div class="panelPageFormContent">
			<dl>
				<dt><label><font class="point" color="red">* </font>质 检 计 划 名 称</label></dt>
				<dd>
					<input name="claimAfcPlanVO.planName" id="claimAfcPlanProjecPlanNameId" value="${claimAfcPlanVO.planName}" type="text" size="4" />
					<input type="hidden" name="claimAfcPlanVO.planId" value="${claimAfcPlanVO.planId}" id="claimAfcProjectPlanId"/>
				</dd>
			</dl>
			<dl>
				<dt><label><font class="point" color="red">* </font></label>质 检 类 型 </dt>
				<dd>
					<Field:codeTable cssClass="combox title"  id="claimAfcPlanProjecPlanTypeId"
					name="claimAfcPlanVO.planType" value="${claimAfcPlanVO.planType}"
					tableName="APP___CLM__DBUSER.T_QC_TYPE" onChange="changePlanType();" defaultValue="1" />
				</dd>
			</dl>
		</div>
		<div class="divfclass">
			<h1><img src="clm/images/tubiao.png">质检要点列表</h1>
		</div>
		<div class="tabdivclassbr">
			<table id="tab_plan" class="list" width="100%">
				<thead>
					<tr>
						<th nowrap>序号</th>
						<th nowrap>质检项目名称</th>
						<th nowrap>质检要点</th>
						<th nowrap>受检岗位-扣分标准</th>
						<th nowrap>操作</th>
					</tr>
				</thead>
				<tbody id="tbodyClaimAfcPlanId">
					<tr>
						<td>1</td>
						<td>
							<select class="selectToInput" onchange="getQuaPoint(this,0)" name="claimAfcPlanRelaVOList[<s:property value="0"/>].itemId">
								<option>请选择</option>
								<s:iterator value="claimAfcItemVOList" var="claimAfcItemStr">
									<option value="${itemId}">${itemName}</option>
								</s:iterator>
							</select>
						</td>
						<td></td>
						<td>
							<input type='checkbox' style="border:0px;background:0px;width:auto;" name="claimAfcPlanRelaVOList[<s:property value="0"/>].signRole"    id="insertSignRoleId" value="签收人/移动签收人"/>签收人/移动签收人<input size='1' name="claimAfcPlanRelaVOList[<s:property value="0"/>].signRoleDeduct" value="" onkeyup="changeThis(this)" onblur="integerCheck(this)"/>  
							<input type='checkbox' style="border:0px;background:0px;width:auto;" name="claimAfcPlanRelaVOList[<s:property value="0"/>].auditOrgan"  id="insertAuditOrganId" value="外包商/医疗复核岗"/>外包商/医疗复核岗<input size='1' name="claimAfcPlanRelaVOList[<s:property value="0"/>].auditOrganDeduct" value="" onkeyup="changeThis(this)" onblur="integerCheck(this)"/>  
							<input type='checkbox' style="border:0px;background:0px;width:auto;" name="claimAfcPlanRelaVOList[<s:property value="0"/>].resignRole"  id="insertResignRoleId" value="立案人/外包商"/>立案人/外包商<input size='1' name="claimAfcPlanRelaVOList[<s:property value="0"/>].resignRoleDeduct" value="" onkeyup="changeThis(this)" onblur="integerCheck(this)"/> 
							<input type='checkbox' style="border:0px;background:0px;width:auto;" name="claimAfcPlanRelaVOList[<s:property value="0"/>].selfRule"    id="insertSelfRuleId" value="自核规则"/>自核规则<input size='1' name="claimAfcPlanRelaVOList[<s:property value="0"/>].selfRuleDeduct" value="" onkeyup="changeThis(this)" onblur="integerCheck(this)"/> 
							<input type='checkbox' style="border:0px;background:0px;width:auto;" name="claimAfcPlanRelaVOList[<s:property value="0"/>].examineRole" id="insertExamineRoleId"  value="审核人"/>审核人<input size='1' name="claimAfcPlanRelaVOList[<s:property value="0"/>].examineRoleDeduct" value="" onkeyup="changeThis(this)" onblur="integerCheck(this)"/>
							<input type='checkbox' style="border:0px;background:0px;width:auto;" name="claimAfcPlanRelaVOList[<s:property value="0"/>].approveRole" id="insertApproveRoleId" value="审批人"/>审批人<input size='1' name="claimAfcPlanRelaVOList[<s:property value="0"/>].approveRoleDeduct" value="" onkeyup="changeThis(this)" onblur="integerCheck(this)"/> 
							<input type='checkbox' style="border:0px;background:0px;width:auto;" name="claimAfcPlanRelaVOList[<s:property value="0"/>].surveyRole"  id="insertSurveyRoleId" value="调查人"/>调查人<input size='1' name="claimAfcPlanRelaVOList[<s:property value="0"/>].surveyRoleDeduct" value="" onkeyup="changeThis(this)" onblur="integerCheck(this)"/>
						</td>
						<td>
							<a href='javascript:;' class='btnDel' onclick='deleteRow_plan(this)'>删除</a>
						</td>
					</tr>
				</tbody>
			</table>
		</div>
		<div class="pageFormdiv">
				<button id="btnAddPlan" type="button" class="but_blue" onclick="addClainAfcPlan()" >添加</button>
		</div>
		<div id="caseFlagId" class="panelPageFormContent">
			<div  class="divfclass">
				<h1><img src="clm/images/tubiao.png">质检计划</h1>
			</div>
				<dl>
					<dt>案件标识</dt>
					<dd style="width:800px"> 
						<span><input type="checkbox"
					<s:if test="11 in strList">checked="checked"</s:if> 
					name="middleCheckConditionVO.caseFlag" value="11"/>自动简易案件</span>
						<span><input type="checkbox" 
					<s:if test="12 in strList">checked="checked"</s:if> 
					name="middleCheckConditionVO.caseFlag" value="12"/>非自动简易案件</span>
						<span><input type="checkbox" 
					<s:if test="13 in strList">checked="checked"</s:if> 
					name="middleCheckConditionVO.caseFlag" value="13"/>普通案件</span>
						<span><input type="checkbox" 
					<s:if test="14 in strList">checked="checked"</s:if> 
					name="middleCheckConditionVO.caseFlag" value="14"/>诉讼案件</span>
						<span><input type="checkbox" 
					<s:if test="15 in strList">checked="checked"</s:if> 
					name="middleCheckConditionVO.caseFlag" value="15"/>疑难案件</span>
						<span><input type="checkbox" 
					<s:if test="16 in strList">checked="checked"</s:if> 
					name="middleCheckConditionVO.caseFlag" value="16"/>调查案件</span>
						<span><input type="checkbox" 
					<s:if test="17 in strList">checked="checked"</s:if> 
					name="middleCheckConditionVO.caseFlag" value="17"/>自动审批案件</span>
						<span><input type="checkbox" 
					<s:if test="18 in strList">checked="checked"</s:if> 
					name="middleCheckConditionVO.caseFlag" value="18"/>直赔案件</span>
					</dd>
				</dl>
		</div>
		<div class="panelPageFormContent">
			<dl style="width: 100%;">
				<dt>有效标识 </dt>
				<dd>
					<span>
					<input type="checkbox"
						<s:if test="claimAfcPlanVO.validFlag eq 1">checked="checked"</s:if>
						name="claimAfcPlanVO.validFlag" value="1" />有效
					</span>
				</dd>
			</dl>
		</div>
		<div class="panelPageFormContent">
			<dl>
				<dt><label><font class="point" color="red">* </font></label>抽检方式</dt>
				<dd>	
					<Field:codeTable cssClass="combox title"  id="extractRateType"
						name="claimAfcPlanVO.extractRateType" value="${claimAfcPlanVO.extractRateType}"
						tableName="APP___CLM__DBUSER.T_QC_EXTRACTRATETYPE" 
						onChange="changeExtractRateType(this.value);" defaultValue="1"/>
				</dd>
			</dl>
			<dl id="extractRateDiv">
				<dt><label><font class="point" color="red">* </font></label>抽检率%</dt>
				<dd>
					<input name="claimAfcPlanVO.extractRate" id="extractRate"  type="text" class="organ" onchange  = "changeeExtractRate(this.value)"/>
				</dd>
			</dl>
			<dl id="inspectionStandardDiv" style="display:none">
				<dt><label><font class="point" color="red">* </font></label>检查水平</dt>
				<dd>
					<Field:codeTable cssClass="combox title"  id="inspectionStandard"
						name="claimAfcPlanVO.inspectionStandard" value="${claimAfcPlanVO.inspectionStandard}"
						tableName="APP___CLM__DBUSER.T_QC_INSPECTIONSTANDARD" 
						onChange="changeInspectionStandard(this.value);" defaultValue="1"/>
				</dd>
			</dl>
			<dl id="inspectionLevelDiv" style="display:none">
				<dt><label><font class="point" color="red">* </font></label>检查级别</dt>
				<dd id="inspectionLevelDd">
					<Field:codeTable cssClass="combox title"  id="inspectionLevelnumber"
						name="claimAfcPlanVO.inspectionLevelnumber" value="${claimAfcPlanVO.inspectionLevelnumber}"
						tableName="APP___CLM__DBUSER.T_INSPECTION_LEVELNUMBER"/>
				</dd>
			</dl>
		</div>
		<div class="panelPageFormContent">
			<div class="divfclass">
				<h1><img src="clm/images/tubiao.png">操作人权限区间</h1>
			</div>
			<div class="tabdivclassbr">
				<table id="tab_auth" class="list" width="100%">
					<thead>
						<tr>
							<th nowrap>序号</th>
							<th nowrap>权限类型</th>
							<th nowrap>权限区间</th>
							<th nowrap>操作</th>
						</tr>
					</thead>
					<tbody id="claimAfcAuthVOTbodyIdId">
						<tr class="thisTR">
							<td>1</td>
							<td>
							<select class="combox title" name="claimAfcAuthVOList[0].permissionTypeCode" 
							onChange="queryApprovePermissionType(this);" >
									<option value=''>请选择</option>
									<option value='1'>审批权限（疑难）</option>
									<option value='2'>审批权限（普通）</option>
									<option value='3'>审核权限</option>
							</select>
							</td>
							<td>
								<dl class="nowrap" >
									<dt></dt>
										<dd style="width: 150px;">
											<select class="combox title" name="claimAfcAuthVOList[<s:property value="0"/>].authMin" id="authMinIdId">
												<option>请选择</option>
												<s:iterator value="findPermissionRegion" var="findPermissionRegionStr">
													<option value="${findPermissionRegionStr}">
														${findPermissionRegionStr}</option>
												</s:iterator>
											</select>
										</dd>
											<span style="padding-left:10px">至</span>
										<dd>	
											<select class="combox title" name="claimAfcAuthVOList[<s:property value="0"/>].authMax" id="authMaxIdId">
												<option>请选择</option>
												<s:iterator value="findPermissionRegion" var="findPermissionRegionStr">
													<option value="${findPermissionRegionStr}">
														${findPermissionRegionStr}</option>
												</s:iterator>
											</select>
										</dd>
								</dl>
							</td>
							<td>
								<a href='javascript:;' class='btnDel' onclick='deleteRow_auth(this)'>删除</a>
							</td>
						</tr>
					</tbody>
				</table>
			</div>
			<div class="pageFormdiv" id="divAddAuth"><button id="btnAddAuth" type="button" class="but_blue" onclick="addClaimAfcAuth()" >添加</button></div>
		</div>
		<div class="divfclass">
			<h1><img src="clm/images/tubiao.png">操作机构</h1>
		</div>
		<div class="tabdivclassbr" style="height:100px;">
			<div  class="table-head" style="padding-right:17px;background-color:#999;color:#000;height: 26%;">
				<table class="list" width="100%">
					<thead>
						<tr>
							<th width="20%">
								序号
								<input type="checkbox" size="5px" id="uwtomessage" value="0"
										style="border: 0px; background: 0px; width: auto; left: 25px; position: relative;" />
							</th>
							<th width="40%">操作机构代码</th>
							<th width="40%">操作机构名称</th>
						</tr>
					</thead>
				</table>
			</div>
			<div class="table-body" style="width:100%; height:74%;overflow-y:scroll;">
				<table id="tab_org" class="list" width="100%"  >
					<tbody id="claimAfcOrgTbId">
						
					</tbody>
				</table>
			</div>
		</div>
		<div class="divfclass">
			<h1><img src="clm/images/tubiao.png">操作员</h1>
		</div>
		<div class="tabdivclassbr">
			<table id="tab_oper" class="list" width="100%">
				<thead>
					<tr>
						<th nowrap>序号</th>
						<th nowrap>操作员代码</th>
						<th nowrap>操作员姓名</th>
						<th nowrap>操作</th>
					</tr>
				</thead>
				<tbody id="TbodyClaimAfcOprId">
					<tr>
						<td>1</td>
						<td>
							<input id="claimAfcOprId" class="data" name="claimAfcOprVOList[<s:property value="0"/>].checkPer"
							 type="hidden"/> 
							<input id="claimAfcOprId" class="data" readonly="readonly" name="claimAfcOprVOList[<s:property value="0"/>].userName"
							 type="text" onkeydown="this.onkeyup();" onkeyup="this.size=(this.value.length>4?this.value.length:4);" size="20" /> 
							<a  class="btnLook" href="clm/inspect/queryOpr_CLM_claimAfcPlanProjectAction.action" lookupGroup="claimAfcOprVOList[<s:property value="0"/>]" >业务人员查询 </a>
						</td>
						<td>
							<input name="claimAfcOprVOList[<s:property value="0"/>].realName" readonly="readonly" type="text" />
						</td>
						<td>
							<a href='javascript:;' class='btnDel' onclick='deleteRow_oper(this)'>删除</a>
						</td>
					</tr>
				</tbody>
			</table>
		</div>
		<div class="pageFormdiv" id="divAddOper"><button id="btnAddOper" type="button" class="but_blue" onclick="addClaimAfcOper()" >添加</button></div>
		<div class="panelPageFormContent">
			<dl>
				<dt>抽取层级</dt>
				<dd>
					<Field:codeTable cssClass="combox title"  id="extractLevelId"
				name="claimAfcPlanVO.extractLevel"
				value="${claimAfcPlanVO.extractLevel}" tableName="APP___CLM__DBUSER.T_QC_LEVEL"
				onChange="getExtractLevel(this)" />
				</dd>
			</dl>
		</div>
		<div id="clmCaseId" class="panelPageFormContent">
			<div class="divfclass">
				<h1><img src="clm/images/tubiao.png">按赔案层级</h1>
			</div>
			<div class="panelPageFormContent">
				<dl style="width: 100%;">
					<dt>理赔类型</dt>
					<dd style="width: 80%;">
						<span>
							<input style="margin-left: 20px" type="checkbox"
							<s:if test="01 in strList">checked="checked"</s:if>
							id="clmGuId" name="middleCheckConditionVO.claimType" value="01"/>身故
						</span>
						<span>
							<input style="margin-left: 10px" type="checkbox"
							<s:if test="02 in strList">checked="checked"</s:if>
							id="clmShangId" name="middleCheckConditionVO.claimType" value="02"/>伤残
						</span>
						<span>
							<input style="margin-left: 10px" type="checkbox"
							<s:if test="03 in strList">checked="checked"</s:if>
							id="clmJiId" name="middleCheckConditionVO.claimType" value="03"/>重大疾病
						</span>
						<span>
							<input style="margin-left: 10px" type="checkbox"
							<s:if test="04 in strList">checked="checked"</s:if>
							id="clmGaoId" name="middleCheckConditionVO.claimType" value="04"/>高残
						</span>
						<span>
							<input style="margin-left: 10px" type="checkbox"
							<s:if test="06 in strList">checked="checked"</s:if>
							id="clmShiId" name="middleCheckConditionVO.claimType" value="06"/>一般失能
						</span>
						<span>
							<input style="margin-left: 10px" type="checkbox"
							<s:if test="07 in strList">checked="checked"</s:if>
							id="clmZhongId" name="middleCheckConditionVO.claimType" value="07"/>重大失能
						</span>
						<span>
							<input style="margin-left: 10px" type="checkbox"
							<s:if test="08 in strList">checked="checked"</s:if>
							id="clmYiId" name="middleCheckConditionVO.claimType" value="08"/>医疗
						</span>
						<span>
							<input style="margin-left: 10px" type="checkbox"
							<s:if test="10 in strList">checked="checked"</s:if>
							id="clmTeId" name="middleCheckConditionVO.claimType" value="10"/>特种疾病
						</span>
					</dd>
				</dl>
				<dl style="width: 100%;">
				<dt>出险原因</dt>
				<dd style="width: 80%;">
					<span>
					<input style="margin-left: 20px" type="checkbox" <s:if test="2 in reasonStrList">checked="checked"</s:if>
						id="clmYiId" name="middleCheckConditionVO.accReason" value="2" />意外
					</span>
					<span>	
					<input style="margin-left: 10px" type="checkbox" <s:if test="1 in reasonStrList">checked="checked"</s:if>
						id="clmId" name="middleCheckConditionVO.accReason" value="1" />疾病
					</span>	
				</dd>
			</dl>
		</div>
		<div class="panelPageFormContent">
			<div class="divfclass">
				<h1><img src="clm/images/tubiao.png">险种</h1>
			</div>
			<div class="main_text">
				<table width="98%">
				<tr>
					<td><span  style="float:left;line-height:1.6;">险种快速查询</span><input type="text" id="businessSearchId"  style="float:left;" /></td>
					<td></td>
					<td></td>
				</tr>
				<tr height="30px;">
					<td>
						<dl class='nowrap'>
							<dt>险种</dt>
						</dl>
					</td>
					<td>
					</td>
					<td>
						<dl class='nowrap'>
							<dt>参与质检的险种</dt>
						</dl>
					</td>
				</tr>
				<tr>
					<td width="40%">
						<div >
							<select size="10" name="allProduct" id="allProduct"
										ondblclick="moveselect('allProduct','selectedProduct',true)"
										multiple style="height:120px; width:100%;">
										<s:iterator value="businessProductVOs" var="product">
											<s:if test="productNameSys != null">
												<option value="${productCodeSys}">
													${productCodeSys}-${productNameSys}
												</option>
											</s:if>
										</s:iterator>
									</select>
						</div>
					</td>
				
					<td align="center" width="8%">
								<div class="buttonContent">
									<button class="but_gray" id="toRightP" name="addOne"
										onclick="moveselect('allProduct','selectedProduct',true)"
										type="button" style="padding-left: 14px; padding-right: 14px;">></button>
								</div>
							<div style="clear: left; margin: 5px 0px 0px;">
								<div class="buttonContent">
									<button class="but_gray" name="addAll"
										onclick="moveselect('allProduct','selectedProduct',true,'1')"
										type="button">>></button>
								</div>
							</div>
							<div style="clear: left; margin: 5px 0px 0px;">
								<div class="buttonContent">
									<button class="but_gray" name="releaseOne"
										onclick="moveselect('selectedProduct','allProduct',true)"
										type="button" style="padding-left: 14px; padding-right: 14px;"><</button>
								</div>
							</div>
							<div  style="clear: left; margin: 5px 0px 0px;">
								<div class="buttonContent">
									<button class="but_gray" name="releaseAll"
										onclick="moveselect('selectedProduct','allProduct',true,'1')"
										type="button"><<</button>
								</div>
							</div>
					</td>	
					
					<td width="40%">
							<div >
								<select size="10" id="selectedProduct"
												name="middleCheckConditionVO.product"
												ondblclick="moveselect('selectedProduct','allProduct',true)"
												multiple style="height:120px; width:100%;">
													<s:iterator value="businessStrList" var="businessStr">
														<option value="${productCodeSys}">
															${productCodeSys}-${productNameSys}</option>
													</s:iterator>
											</select>
							</div>
						</td>
			</table>
		</div>
	</div>
</div>
	<div id="clmCustomerId" class="panelPageFormContent">
		<div class="divfclass">
			<h1><img src="clm/images/tubiao.png">按客户层级</h1>
		</div>
		<div class="panelPageFormContent">
			<dl>
				<dt>保单生效日期</dt>
				<dd>
					<input id="validDateId"  type="expandDateYMD" name="claimAfcPlanVO.validDate" value='<fmt:formatDate value="${claimAfcPlanVO.validDate}"
					pattern="yyyy-MM-dd"></fmt:formatDate>' class="date" readonly="readonly" />
					<a class="inputDateButton" href="javascript:;">选择</a>
				</dd>
			</dl>
			<%-- <dl>
				<dt>客户号</dt>
				<dd>
					<input name="claimAfcPlanVO.customerId"
					value="${claimAfcPlanVO.customerId}" id="customerIdId" type="text" onkeydown="this.onkeyup();" onkeyup="this.size=(this.value.length>4?this.value.length:4);" size="4" />
				</dd>
			</dl> --%>
			<dl>
				<dt>证件号码</dt>
				<dd>
					<input name="claimAfcPlanVO.certiCode"
					value="${claimAfcPlanVO.certiCode}" id="certiCodeId" type="text" onkeydown="this.onkeyup();" onkeyup="this.size=(this.value.length>4?this.value.length:4);" size="4" />
				</dd>
			</dl>
		</div>
		<div class="divfclass">
			<h1><img src="clm/images/tubiao.png">片区</h1>
		</div>
		<div class="main_text">
		<table width="98%">
		<tr>
			<td><span  style="float:left;line-height:1.6;">片区快速查询</span><input type="text" id="appendItemSearchId"  style="float:left;line-height:1.6;" /></td>
			<td></td>
			<td></td>
		</tr>
			<tr height="30px;">
				<td>
					<dl class='nowrap'>
						<dt>片区</dt>
					</dl>
				</td>
				<td>
				</td>
				<td>
					<dl class='nowrap'>
						<dt>参与质检的片区</dt>
					</dl>
				</td>
			</tr>
			<tr>
				<td width="40%">
					<div >
						
						<select  id="selectedArea" name="name"
								multiple="multiple" style="height:120px; width:100%;" size=5
								ondblclick="moveselect('selectedArea', 'areaList', true);">
								<s:iterator value="areaList" var="area">
									<s:if test="areaName != null">
										<option value="${areaCode}">
											${areaCode}-${areaName}</option>
									</s:if>
								</s:iterator>
							</select>
					</div>
				</td>
					<td align="center" width="8%">
							<div class="buttonContent">
								<button id="toRightO"
									onclick="moveselect('selectedArea', 'areaList', true);"
									type="button" class="but_gray" style="padding-left: 14px; padding-right: 14px;">></button>
							</div>
						<div style="clear: left; margin: 5px 0px 0px;">
							<div class="buttonContent">
								<button id="allToRightO" class="but_gray"
									onclick="moveselect('selectedArea','areaList', true,'1');"
									type="button">>></button>
							</div>
						</div>
						<div style="clear: left; margin: 5px 0px 0px;">
							<div class="buttonContent">
								<button id="toleftO" class="but_gray"
									onclick="moveselect('areaList', 'selectedArea', true);"
									type="button" style="padding-left: 14px; padding-right: 14px;"><</button>
							</div>
						</div>
						<div  style="clear: left; margin: 5px 0px 0px;">
							<div class="buttonContent">
								<button id="allToLeftO" class="but_gray"
									onclick="moveselect('areaList','selectedArea', true,'1');"
									type="button"><<</button>
							</div>
						</div>
				</td>
				<td width="40%">
					<div >
						
						<select id="areaList" name="middleCheckConditionVO.area"
									multiple="multiple" style="height:120px; width:100%;"
									size=5
									ondblclick="moveselect('areaList','selectedArea',  true);">
										<s:iterator value="areaStrList" var="areaStr">
											<option value="${areaCode}">
												${areaCode}-${areaName}</option>
										</s:iterator>
								</select>	
					</div>
				</td>
			</tr>
		</table>
	</div>
	<div class="divfclass">
		<h1><img src="clm/images/tubiao.png">管理机构</h1>
	</div>
	<div class="main_text">
		<table width="98%">
			<tr>
				<td><span  style="float:left;line-height:1.6;">管理机构快速查询</span><input type="text" id="organSearchId"  style="float:left;line-height:1.6;" /></td>
				<td></td>
				<td></td>
			</tr>
			<tr height="30px;">
				<td>
					<dl class='nowrap'>
						<dt>管理机构</dt>
					</dl>
				</td>
				<td>
				</td>
				<td>
					<dl class='nowrap'>
						<dt>参与质检的管理机构</dt>
					</dl>
				</td>
			</tr>
			<tr>
				<td width="40%">
					<div>
						<select id="selectedOrganization" name="name"
							multiple="multiple" style="height:120px; width:100%;" size=5
							ondblclick="moveselect('selectedOrganization', 'orgList', true);">
							<s:iterator value="orgVOList" var="org">
								<s:if test="organName != null">
									<option value="${organCode}">
										${organCode}-${organName}</option>
								</s:if>
							</s:iterator>
						</select>
					</div>
				</td>
				<td align="center" width="8%">
							<div class="buttonContent">
								<button class="but_gray" id="toRightO"
									onclick="moveselect('selectedOrganization', 'orgList', true);"
									type="button" style="padding-left: 14px; padding-right: 14px;">></button>
							</div>
						<div style="clear: left; margin: 5px 0px 0px;">
							<div class="buttonContent">
								<button class="but_gray" id="allToRightO"
									onclick="moveselect('selectedOrganization','orgList', true,'1');"
									type="button">>></button>
							</div>
						</div>
						<div style="clear: left; margin: 5px 0px 0px;">
							<div class="buttonContent">
								<button class="but_gray" id="toleftO"
									onclick="moveselect('orgList', 'selectedOrganization', true);"
									type="button" style="padding-left: 14px; padding-right: 14px;"><</button>
							</div>
						</div>
						<div  style="clear: left; margin: 5px 0px 0px;">
							<div class="buttonContent">
								<button class="but_gray" id="allToLeftO"
									onclick="moveselect('orgList','selectedOrganization', true,'1');"
									type="button"><<</button>
							</div>
						</div>
				</td>
				<td width="40%">
					<div >
						<select id="orgList" name="middleCheckConditionVO.organization"
								multiple="multiple" style="width: 100%; height: 120px"
								size=5
								ondblclick="moveselect('orgList','selectedOrganization',  true);">
									<s:iterator value="orgStrList" var="orgStr">
										<option value="${organCode}">
											${organCode}-${organName}</option>
									</s:iterator>
							</select>
					</div>
				</td>
			</tr>
		</table>
	</div>
</div>
		<div class="panelPageFormContent main_tabdiv" >
			<dl style="width: 100%; height: auto;">
				<dt>备注</dt>
				<dd>
					<textarea rows="2" cols="80"   name="claimAfcPlanVO.remark">${claimAfcPlanVO.remark}</textarea>
				</dd>
			</dl>
			<dl>
				<dt>制定日期</dt>
				<dd>
					<input value="<fmt:formatDate value="${claimAfcPlanVO.makeDate}" pattern="yyyy-MM-dd"></fmt:formatDate>"
					name="claimAfcPlanVO.makeDate" readonly="true" />
				</dd>
			</dl>
			<dl>
				<dt>制定人</dt>
				<dd>
					<input style="width: 50px;"	value="${claimAfcPlanVO.makeBy}" name="claimAfcPlanVO.makeBy" readonly="true" />
					<input style="width:120px;" value="<Field:codeValue tableName="APP___CLM__DBUSER.T_UDMP_USER" value="${claimAfcPlanVO.makeBy}"/>" readonly="true" />
				</dd>
			</dl>
		</div>
		<div class="formBarButton">
			<ul>
				<li><button type="button"  class="but_blue" onclick="save();" >保存</button></li>
				<li><button type="button"  class="but_gray" onclick="emptyAllMesage123();" >清空</button></li>
				<li><button type="reset"  class="but_gray" onclick="exit();" >退出</button></li>
			</ul>
		</div>
	</form>
</div>
