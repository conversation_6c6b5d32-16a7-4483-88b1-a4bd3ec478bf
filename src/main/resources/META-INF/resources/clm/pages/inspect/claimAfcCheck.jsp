<%@ page language="java" contentType="text/html; charset=utf-8" pageEncoding="UTF-8"%>
<%@ include file="/clm/pages/common/commonJsCss.jsp" %>
<%@ taglib uri="/struts-tags" prefix="s"%>
<%@ taglib uri="http://www.git.com.cn/taglib/udmp/field" prefix="Field"%>
<%@ page language="java" import="java.util.*"%>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<script type="text/javascript" language="javascript" src="clm/pages/inspect/claimAfcCheck.js"></script>

   <div id="qCheckCommPoolJsp">
   <input type="hidden" id="caseId" name="caseId">
   <form id="pagerForm" method="post"
			action="clm/inspect/queryClaimAfcCheckProject_CLM_claimAfcCheckProjectAction.action?leftFlag=0&menuId=${menuId }">
			<input type="hidden" name="pageNum" value="${pageInfo.currentPage}" />
			<input type="hidden" name="numPerPage" value="${pageInfo.numPerPage}" />
	</form>
	<form id="qCheckCommPoolJspForm"
		action="clm/inspect/queryClaimAfcCheckProject_CLM_claimAfcCheckProjectAction.action?leftFlag=0&menuId=${menuId }"
		method="post" onsubmit="return navTabSearch(this,'qCheckCommPoolJsp')" rel="pagerForm"
		class="pagerForm required-validate" >
		<div class="panelPageFormContent">
			<div class="divfclass">
				<h1>
					<img src="clm/images/tubiao.png">查询条件
				</h1>
			</div>
			<div class="pageFormInfoContent">
				<dl>
					<dt>赔案号</dt>
					<dd>
						<input type="text" name="claimAfcCheckVO.caseNo" id="qCheckPoolCaseNO"
							value="${claimAfcCheckVO.caseNo }" size="25"  maxlength="11" onkeyup="changeCaseNO(this)"/>
					</dd>
				</dl>
				<dl>
					<dt>出险人</dt>
					<dd>
						<input type="text" name="claimAfcCheckVO.customerName" id="qCheckPoolCustomerName"
							value="${claimAfcCheckVO.customerName }" size="25" onkeyup="this.value=this.value.replace(/\s/g,'')"/>
					</dd>
				</dl>
				<dl>
					<dt>质检人</dt>
					<dd>
						<input type="text" name="claimAfcCheckVO.checkName" id="qCheckPoolInspector"
							value="${claimAfcCheckVO.checkName}" size="25" onkeyup="this.value=this.value.replace(/\s/g,'')"/>
					</dd>
				</dl>
				<dl>
					<dt>理赔类型</dt>
					<dd>
					<Field:codeTable cssClass="combox" id="qCheckPoolClaimType" name="claimAfcCheckVO.claimCode" tableName="APP___CLM__DBUSER.T_CLAIM_TYPE"  nullOption="true" value="${claimAfcCheckVO.claimCode}"   whereClause="1=1" orderBy=" decode(code,'08',1,'03',2,'10',3,'04',4,'02',5,'01',6,'11',7,'06',8,'07',9)"/>
					</dd>
				</dl>
			
				<dl>
					<dt>审批起期</dt>
					<dd>
						<input type="expandDateYMD" class="date"
							name="claimAfcCheckVO.approveStartDate" id="qCheckPoolBeginDate"
							value='<s:date name="claimAfcCheckVO.approveStartDate" format="yyyy-MM-dd"/>'
							size="22" /> <a class="inputDateButton" href="javascript:;">选择</a>
					</dd>
				</dl>
				<dl>
					<dt>审批止期</dt>
					<dd>
						<input type="expandDateYMD" class="date"
							name="claimAfcCheckVO.approveEndDate" id="qCheckPoolEndDate"
							value='<s:date name="claimAfcCheckVO.approveEndDate" format="yyyy-MM-dd"/>'
							size="22" /> <a class="inputDateButton" href="javascript:;">选择</a>
					</dd>
				</dl>
				<div class="pageFormdiv"><button type="button" class="but_blue" onclick="qCheckPoolSubmit()">查询</button></div>
			</div>
		 </div>
		</form>
		<!-- 显示数据列表区域 -->
		<div id="qCheckCommPoolJspResult" >
			<div class="divfclass">
				<h1>
					<img src="clm/images/tubiao.png">已质检赔案列表
				</h1>
			</div>
			<div class="tabdivclass" >
				<table class="list sortable" width="100%">
					<thead>
						<tr>
							<th >选择</th>
							<th >序号</th>
							<th >质检计划名称</th>
							<th >赔案号</th>
							<th >理赔类型</th>
							<th >出险人</th>
							<th >审核人</th>
							<th >审批人</th>
							<th >质检人</th>
							<th >案件标识</th>
							<th >管理机构</th>
							<th >审批日期</th>
						</tr>
					</thead>
					<tbody>
						<!-- 循环显示数据 -->
						<s:if test="imageFlag != null">
							<tr>
								<td colspan="12">
									<div class="noRueryResult">请选择条件查询数据！</div>
								</td>
							</tr>
						</s:if> 
							<s:elseif test="currentPage.PageItems == null || currentPage.PageItems.size()==0">
							<tr>
								<td colspan="12">
									<div class="noRueryResult">没有符合条件的查询结果！</div>
								</td>
							</tr>
						</s:elseif>
						<s:iterator value="currentPage.PageItems" status="status">
							<!--双击  <tr ondblclick="reportClick('${caseId}','${bpmTaskId}')" align="center">-->
							<tr>
								<td><div align="center">
										<input name="claimAfcrAdio" type="radio" value="" onclick="claimAfcPlanProjectRadio(${taskId},${planId},${caseId});" style="border:0px;background:0px;width:auto; float: left;" />
									</div></td>
								<td><div align="center"><s:property value="#status.index + 1" /></div></td>
								<td><s:property	value="planName" /></td>
								<td><s:property	value="caseNo" /></td>
								<td><s:property value="claimType" /></td>
								<td><s:property value="customerName" /></td>
								<td><s:property value="auditorName" /></td>
								<td><s:property value="approverName" /></td>
								<td><s:property value="checkName" /></td>
								 <td>
								<div align="center">
									<s:if test="caseFlag eq 4">简易案件</s:if>
									<s:elseif test="caseFlag eq 1">普通案件</s:elseif></div>
								</td>
								<td><s:property value="organCode" /></td>
								<td><s:date name="approveTime" format="yyyy-MM-dd"/></td>
								<td style="display: none;"><s:property value="caseId" /></td>
							</tr>
						</s:iterator>
					</tbody>
				</table>
				<!-- 分页查询区域 -->
				<div class="panelBar">
					<div class="pages">
						<span>显示</span>
						<s:select list="#{5:'5',10:'10',20:'20',50:'50'}"
							name="select" onchange="navTabPageBreak({numPerPage:this.value},'qCheckCommPoolJsp')"
							value="currentPage.pageSize">
						</s:select>
						<span>条，共${currentPage.total}条</span>
					</div>
					<div class="pagination" targetType="navTab"  rel="qCheckCommPoolJsp"
						totalCount="${currentPage.total}"
						numPerPage="${currentPage.pageSize}" pageNumShown="10"
						currentPage="${currentPage.pageNo}"></div>
				</div>
			</div>
		</div>
		
		
	<form id="confirmFormAll" class="pageForm required-validate" novalidate="novalidate"  method="post" onsubmit="return validateCallback(this,confirmFormAllAjaxDone)">
	 <div class="divfclass">
     	 <h1>
       		  <img src="clm/images/tubiao.png">事后质检项目列表
   		 </h1>
    </div>
	
	<div id="afterCheckIntegrationTwo"  class="tabdivclassbr"> 
	<table class="list" style="border: 2px;width:100%" id="tablecheck">
				<thead>
					<tr>
						<th nowrap>序号</th>
						<th nowrap>质检要点说明</th>
						<th nowrap>质检结果</th>
						<th nowrap>备注</th>
						<th nowrap>质检修正</th>
						<th nowrap>修正备注</th>
					</tr>
				</thead>
				<tbody id="afterCheckIntegrationTwoTBody">
				</tbody>
         </table>    
	</div>
	<div class="pageFormdiv">
	       <button type="button" class="but_blue"  onclick="qCheckClaimAfcTask()">质检要点确认完成</button>
	 </div>
</form>
	 <div class="divfclass">
     	 <h1>
       		  <img src="clm/images/tubiao.png">质检结论
   		 </h1>
    </div>
	<form id="saveReviseConclusion" class="pageForm required-validate" novalidate="novalidate"  method="post">
	  <input type="hidden" id="qualityCheckConclusionVOTaskId" name="qualityCheckConclusionVO.taskId" >
	  <input type="hidden" id="claimAfcTaskVOTaskId" name="claimAfcTaskVO.taskId">
		<div class="panelPageFormContent">
				<dl>
					<dt  >质检总评</dt>
					<dd >
				          <select class="combox" id="qCheckConclusion" disabled>
				                <option value="" > </option>	
						  </select>	
				    </dd>
					</dl>
					<dl style="width:100%;height:auto">
						<dt >备注信息</dt>
						<dd>
						   <textarea  cols="100" rows="5"  readonly="readonly"  id="qCheckRemark"></textarea>
						</dd>
					</dl>
					<dl>
					<dt  >质检总评修正<font >* </font></dt>
					<dd >
						<select class="combox" name="qualityCheckConclusionVO.reviseCommentCode"  id="qCheckComment">
				                <option value="" ></option>
				                <option value="1" >01-严重差错</option>
				                <option value="2" >02-一般差错</option>
				                <option value="3" >03-无差错</option>
				        </select>
					</dd>
					</dl>
					<dl style="width:100%;height:auto">
						<dt >修正备注<font >* </font></dt>
						<dd>
						   <textarea  cols="100" rows="5" id="qCheckReviseRemark" name="qualityCheckConclusionVO.reviseRemark">${qualityCheckConclusionVO.reviseRemark}</textarea>
						</dd>
					</dl>
			 
	 </div>
	 
	 <div class="panelPageFormContent">
				   <dl>
						<dt>质检日期</dt>
						<dd >
						<input type="text" name="" id="qCheckDate" readonly="readonly"/>	
					    </dd>
					    </dd>
					</dl> 
					<dl>
						<dt>质检人员机构号</dt>
						<dd >
						<input type="text" name="" id="qCheckOrganCode" readonly="readonly" />
					    </dd>
					</dl>
					<dl>
						<dt>质检人员</dt>
						<dd >
						<input id="qCheckBy"  style="width:30px" type="text" readonly="readonly"/>
				        <input id="qCheckName" type="text"  readonly="readonly" style="width:110px"/>																	
					    </dd>
					</dl>
	  </div> 
	 
	  <div   class="pageFormdiv" id="afterCheckIntegrationThree">
	       <button type="button" class="but_blue" onclick="saveClaimAfcTask()" >保存</button>
	       <button type="button"  class="but_gray"  onclick="exit()">退出</button>
	  </div>
	 
	</form>
 </div>
	