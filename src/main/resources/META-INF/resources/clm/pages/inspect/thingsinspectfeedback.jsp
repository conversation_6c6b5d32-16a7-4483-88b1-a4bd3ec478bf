<%@ page language="java" contentType="text/html; charset=utf-8"
	pageEncoding="UTF-8"%><%-- <%@ include file="/clm/pages/common/commonJsCss.jsp" %> --%>
<%@include file="/udmpCommon.jsp"%>
<%@ taglib uri="/struts-tags" prefix="s"%>
<%@ taglib uri="http://www.git.com.cn/taglib/udmp/field" prefix="Field"%>

<script type="text/javascript">
//分支流程查询 执行
var check = '${claimMidcTaskVO.checkConclusion}';
var queryBranchFlow = "${param.queryBranchFlow}";
if (queryBranchFlow == "1") {
	$("#thingsinspectbutton2", $.pdialog.getCurrent()).css("display","none");
	$("#thingsinspectbutton3", $.pdialog.getCurrent()).css("display","none");
	//初始化通过不通过
	if(check=="1"){
		$("#checkConclusion", $.pdialog.getCurrent()).selectMyComBox("1",'dialog');
// 		$("#checkConclusion", $.pdialog.getCurrent()).find("option").eq(1).attr("selected",true);
	}
	if(check=="2"){
		$("#checkConclusion", $.pdialog.getCurrent()).selectMyComBox("2",'dialog');
// 		$("#checkConclusion", $.pdialog.getCurrent()).find("option").eq(2).attr("selected",true);
	}
	if(check==""){
		$("#checkConclusion", $.pdialog.getCurrent()).selectMyComBox("");
// 		var options="<option value=''>请选择</option>";
// 		$("#checkConclusion", $.pdialog.getCurrent()).loadMyComboxOptions(options,'1','dialog');
	}
	$("#checkConclusion", $.pdialog.getCurrent()).setMyComboxDisabled(true,'dialog');
}
//初始化通过不通过
if(check=="1"){
	$("#checkConclusion", $.pdialog.getCurrent()).selectMyComBox("1",'dialog');
// 	$("#checkConclusion", navTab.getCurrentPanel()).find("option").eq(1).attr("selected",true);
}
if(check=="2"){
	$("#checkConclusion", $.pdialog.getCurrent()).selectMyComBox("2",'dialog');
// 	$("#checkConclusion", navTab.getCurrentPanel()).find("option").eq(2).attr("selected",true);
}


//通过与不通过事件
function conclusion(){
	var flag=$("#checkConclusion", navTab.getCurrentPanel()).val();
	//flag 1为通过不通过原因禁用并显示请选择， 2为不通过，显示不通过原因去除请选择
	if(flag==1){
		$("#checkReason", navTab.getCurrentPanel()).setMyComboxDisabled(true);
		$("#checkReason", navTab.getCurrentPanel()).selectMyComBox("");
	}else if(flag==2){
		$("#checkReason", navTab.getCurrentPanel()).prev().removeAttr("disabled");
		$("#checkReason", navTab.getCurrentPanel()).setMyComboxDisabled(false);
	} 
}

//保存按钮
function thingSave(){
	//判断用户选择的是否为不通过，不通过则校验不通过原因
	var checkConclusion=$("#checkConclusion", navTab.getCurrentPanel()).val();
	if(checkConclusion == ""){
		alertMsg.error("质检结论必填");
		 return;
	}
	if(checkConclusion==2){
		if($("#checkReason", navTab.getCurrentPanel()).val()==""){
			 alertMsg.error("请选择不通过原因后在保存");
			 return;
		};
	}
	//ajax提交表单
	$.ajax({
			'type':'post',
			'url':'clm/inspect/updateClaimMidcTask_CLM_thingsInspectFeedbackAction.action',
			'datatype':'json',
			'data':$("#thingsInspect", navTab.getCurrentPanel()).serialize(),
			'success':function(data){
				var data = eval("(" + data + ")");
				if(data.statusCode=="300"){
					 alertMsg.error(data.message);
				}else{
					  alertMsg.correct(data.message);
					  navTab.closeCurrentTab();
					  //先关闭访问理赔工作台页面
					  navTab.closeTab("20345");
					  //重新打开理赔工作台页面
					  var url = "clm/clmWorkPlatform/clmWorkPlatformInitialize_CLM_clmWorkPlatformAction.action";
					  navTab.openTab("20345", url, {title:'访问理赔工作台'});
				}
			},
			'error':function(){
				alert("出错了！");
			}});
}
//退出方法
/* function thingsExit(){
	 alertMsg.confirm("当前页面录入的信息将不被保存，是否确定退出？", {
	  	okCall : function() {
			navTab.closeCurrentTab();
	 	}
	 });

} */
</script>
	<form id="thingsInspect"
		action="clm/inspect/updateClaimMidcTask_CLM_thingsInspectFeedbackAction.action"
		method="post" onsubmit="return validateCallback(this,dialogAjaxDone);">
		<div class="panelPageFormContent main_tabdiv">
			<dl>
				<dt>赔 案 号</dt> 
				<dd>
					<input name="claimMidcTaskVO.caseNo" readonly="readonly"
						type="text" readonly="readonly" value="${claimMidcTaskVO.caseNo}" />
						<input name="claimMidcTaskVO.taskId" type="hidden" value="${claimMidcTaskVO.taskId}">
						<input name="claimMidcTaskVO.planId" type="hidden" value="${claimMidcTaskVO.planId}">
						<input name="claimMidcTaskVO.caseId" type="hidden" value="${claimMidcTaskVO.caseId}">
						<input name="claimMidcTaskVO.makeDate" type="hidden" value="<s:date name='claimMidcTaskVO.makeDate'
											format='yyyy-MM-dd' />">
				</dd>						
			</dl>
			<dl>
				<dt>事 件 号</dt> 
				<dd>
					<input name="claimMidcTaskVO.accidentNo" readonly="readonly" type="text" readonly="readonly" value="${claimMidcTaskVO.accidentNo }" />
				</dd>	
			</dl>
			</div>
<!-- 		<div class="panel"  > -->
			<div class="divfclass">
				<h1>
					<img src="clm/images/tubiao.png">质检结论
				</h1>
			</div>
				<div class="panelPageFormContent">
					<dl>
						<dt><font class="point" color="red">* </font>质检结论</dt>
						<dd>
							<select class="combox title"  id="checkConclusion" name="claimMidcTaskVO.checkConclusion" onchange="conclusion();">
								<option value="">请选择</option>
								<option value="1">通过</option>
								<option value="2">不通过</option>
							</select>
						</dd>
					</dl>
					<dl>
						<dt>不通过原因</dt>
						<dd>
							<s:if test="queryBranchFlow eq 1">
								<input class="combox" disabled="disabled"  title="<Field:codeValue tableName="APP___CLM__DBUSER.T_CLAIM_APPROVE_REJECT" value="${claimMidcTaskVO.checkReason}"/>"  value="<Field:codeValue tableName="APP___CLM__DBUSER.T_CLAIM_APPROVE_REJECT" value="${claimMidcTaskVO.checkReason}"/>">
							</s:if>
							<s:else>
								<Field:codeTable cssClass="combox title" disabled="true"  id="checkReason"  value="${claimMidcTaskVO.checkReason}" nullOption="true"
									name="claimMidcTaskVO.checkReason"
									 tableName="APP___CLM__DBUSER.T_CLAIM_APPROVE_REJECT"
									></Field:codeTable>
								</s:else>
						</dd>
					</dl>
					<dl style="width: 100%;height: auto;">
						<dt>备注信息</dt>
						<dd>
							<textarea name="claimMidcTaskVO.remark" rows="3" cols="70">${claimMidcTaskVO.remark}</textarea>
						</dd>
					</dl>
					<dl>
						<dt>质检日期</dt>
						<dd>
							<input type="expandDateYMD" id="date8" flag="flag" class="date" readonly="readonly" disabled="disabled"
								name="claimMidcTaskVO.makeDate" size="17" value="<s:date name='claimMidcTaskVO.makeDate'
										format='yyyy-MM-dd' />" /> <a  disabled="disabled"
								class="inputDateButton" href="javascript:;">选择</a>
						</dd>
					</dl>
					<dl>
						<dt>质检人员机构号</dt>
						<dd>
							<input type="text" name="claimMidcTaskVO.organCode" readonly="readonly"
								 value="${claimMidcTaskVO.organCode }" />
						</dd>
					</dl>
					<dl>
						<dt>质检人员</dt>
						<dd>
							<input type="text" readonly="readonly"
								value="${claimMidcTaskVO.checkName}"/>
							<input type="hidden" name="claimMidcTaskVO.checkBy" value="${claimMidcTaskVO.checkBy}">
						</dd>
					</dl>
				</div>
	</form>
		<div class="formBarButton main_bottom">
			<ul>
				<li id="thingsinspectbutton2">
					<button type="button" class="but_blue" onclick="thingSave()">保存</button>
				</li>
				<li id="thingsinspectbutton3">
					<button type="button" class="but_gray" onclick="exit()">退出</button>
				</li>
			</ul>
		</div>
