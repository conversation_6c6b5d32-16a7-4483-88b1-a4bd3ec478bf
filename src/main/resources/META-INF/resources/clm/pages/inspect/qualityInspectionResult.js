//输入框的非空验证
function notNull() {
	var inspectResult = document.getElementById("inspectResult");
	var unPassReason = document.getElementById("unPassReason").value;
	if (inspectResult.value == "") {
		alertMsg.error("请输入质检结论");
		return false;
	} else if (inspectResult.options[inspectResult.selectedIndex].text == "不通过"
			&& unPassReason == "") {
		alertMsg.error("质检结论为不通过时,必须输入不通过原因");
		return false;
	} else {
		return true;
	}
}
// 动态改变未通过原因的可输入状态
function changeAvalable(id, changedId) {
	var element = document.getElementById(id);
	var changedElement = document.getElementById(changedId);
	//alert(element.options[element.selectedIndex].text);
	if (element.options[element.selectedIndex].text == "不通过") {
		changedElement.disabled = false;
	} else {
		changedElement.disabled = true;
	}
}
