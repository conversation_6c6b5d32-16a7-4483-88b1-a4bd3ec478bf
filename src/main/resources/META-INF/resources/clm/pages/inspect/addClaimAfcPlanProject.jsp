<%@ page language="java" contentType="text/html; charset=utf-8"
	pageEncoding="UTF-8"%><%@ include file="/clm/pages/common/commonJsCss.jsp" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/fmt" prefix="fmt"%>
<%@ taglib uri="/struts-tags" prefix="s"%>
<%@ taglib uri="http://www.git.com.cn/taglib/udmp/field" prefix="Field"%>
<script type="text/javascript"
	src="clm/pages/inspect/addClaimAfcPlanProject.js"></script>
<script type="text/javascript">

//险种快速查询
var val_flag = "";  //定义一个标识，防止多次重复验证
$("#businessSearchId", navTab.getCurrentPanel()).bind("input propertychange",function(){
	var value = $("#businessSearchId", navTab.getCurrentPanel()).val();
	if(value != val_flag){
		val_flag = value;
		var optionStr = "";
		for(var i=0;i<_businessArray.length;i++){
			var obj = _businessArray[i];
			var text = obj.productCodeSys + "-" + obj.productNameSys;
			if(text.indexOf(value) != "-1"){
				optionStr = optionStr + "<option value='"+ obj.productCodeSys +"'>"+ obj.productCodeSys + "-" + obj.productNameSys + "</option>";
			}
		}
		$("#allProduct", navTab.getCurrentPanel()).html("");
		$("#allProduct", navTab.getCurrentPanel()).append(optionStr);
	}
});

//片区快速查询
var appendItem_flag = "";  //定义一个标识，防止多次重复验证
$("#appendItemSearchId", navTab.getCurrentPanel()).bind("input propertychange",function(){
	var value = $("#appendItemSearchId", navTab.getCurrentPanel()).val();
	if(value != appendItem_flag){
		appendItem_flag = value;
		var optionStr = "";
		for(var i=0;i<_apendItemArray.length;i++){
			var obj = _apendItemArray[i];
			var text = obj.areaCode + "-" +  obj.areaName;
			if(text.indexOf(value) != "-1"){
				optionStr = optionStr + "<option value='" + obj.areaCode + "'>" + obj.areaCode + "-" + obj.areaName + "</option>";
			};
		};
		$("#selectedArea", navTab.getCurrentPanel()).html("");
		$("#selectedArea", navTab.getCurrentPanel()).append(optionStr);
	}
});


//机构快速查询
var organ_flag = "";  //定义一个标识，防止多次重复验证
$("#organSearchId", navTab.getCurrentPanel()).bind("input propertychange", function(){
	var value = $("#organSearchId", navTab.getCurrentPanel()).val();
	if(value != organ_flag){
		organ_flag = value;
		var optionStr = "";
		for(var i=0;i<_organArray.length;i++){
			var obj = _organArray[i];
			var text = obj.organCode + "-" +  obj.organName;
			if(text.indexOf(value) != "-1"){
				optionStr = optionStr + "<option value='" + obj.organCode + "'>" + obj.organCode + "-" + obj.organName + "</option>";
			};
		};
		$("#selectedOrganization", navTab.getCurrentPanel()).html("");
		$("#selectedOrganization", navTab.getCurrentPanel()).append(optionStr);
	};
});


//定义险种集合
var _businessArray = new Array();

//定义片区集合
var _apendItemArray = new Array();

//定义机构集合
var _organArray = new Array();

// 抽检方式为按国标抽检显示检查水平和检查级别
var extractRateType = $("#extractRateType").val();
if(extractRateType == "1"){// 抽检方式为1，按比例抽检：
	// 抽检率显示
	$("#extractRateDiv").css("display",'block');
	// 检查水平隐藏
	$("#inspectionStandardDiv").css("display",'none');
	// 检查级别隐藏
	$("#inspectionLevelDiv").css("display",'none');
}else if(extractRateType == "2"){// 抽检方式为1，按国标抽检：
	// 抽检率隐藏
	$("#extractRateDiv").css("display",'none');
	// 抽检率置为空
	$("#extractRate").val("");
	// 检查水平显示
	$("#inspectionStandardDiv").css("display",'block');
	// 检查级别显示
	$("#inspectionLevelDiv").css("display",'block');
}
//初始化页面时调用检查水平change事件
claimFireEvent($("#inspectionStandard"));

//操作机构全选按钮实现
$("#uwtomessage", navTab.getCurrentPanel()).change(function(){
	  var check=$(this).attr("checked");
	  if(check=="checked"){
		  $('input[flag="uwflag"]', navTab.getCurrentPanel()).each(function(){
				if($(this).prop('checked') != true){
					$(this).attr("checked",true);
					$(this).val(1);
				}
		  });
	  }else{
		  $('input[flag="uwflag"]', navTab.getCurrentPanel()).each(function(){
				if($(this).prop('checked') != false){
					$(this).attr("checked",false);
					$(this).val(0);
				}
		  });
	  }
});
</script>
	
<s:iterator value="businessProductVOs" var="bus">
	<script>
		var obj = new Object();
		obj.productCodeSys = '<s:property value="#bus.productCodeSys"/>';
		obj.productNameSys = '<s:property value="#bus.productNameSys"/>';
		_businessArray.push(obj);
	</script>
</s:iterator>	

<s:iterator value="areaList" var="item">
	<script>
	var obj = new Object();
	obj.areaCode = '<s:property value="#item.areaCode"/>';
	obj.areaName = '<s:property value="#item.areaName"/>';
	_apendItemArray.push(obj);
	</script>
</s:iterator>	

<s:iterator value="orgVOList" var="org">
	<script>
		var obj = new Object();
		obj.organCode = '<s:property value="#org.organCode"/>';
		obj.organName = '<s:property value="#org.organName"/>';
		_organArray.push(obj);
	</script>
</s:iterator>	
	

<form
	action="clm/inspect/claimAfcPlanProjectRadio_CLM_claimAfcPlanProjectAction.action"
	method="post" class="pageForm required-validate" id="claimAfcPlanProjectRadioId"
	onsubmit="return  navTabSearch(this,navTabAjaxDone)">
	<input name="claimAfcPlanVO.planId" id="claimAfcPlanVOPlanId"
		type="hidden" />
</form>
<div layouth="10">
	<form id="addClaimAfcPlanProjectInitId" method="post"
			onsubmit="return navTabSearch(this)"
			action="clm/inspect/addClaimAfcPlanProjectInit_CLM_claimAfcPlanProjectAction.action">
	</form>
	<form
		action="clm/inspect/insertClaimAfcPlanProject_CLM_claimAfcPlanProjectAction.action"
		method="post" class="pageForm required-validate"
		id="insertClaimAfcPlanProjectId"
		onsubmit="return  validateCallback(this)">
		<div>
			<div class="divfclass">
				<h1><img src="clm/images/tubiao.png">维护质检计划</h1>
			</div>
			<div class="panelPageFormContent" >
				<dl>
					<dt><font class="point" color="red">* </font>质 检 计 划 名 称</dt>
					<dd>
					<input name="claimAfcPlanVO.planName" id="claimAfcPlanProjecPlanNameId" value="${claimAfcPlanVO.planName}" />
					<input type="hidden" name="claimAfcPlanVO.planId" value="${claimAfcPlanVO.planId}" id="claimAfcProjectPlanId" />
					<input value="${strList}" id="strListId" type="hidden"/>
					</dd>
				</dl>
				<dl>
					<dt><font class="point" color="red">* </font>质 检 类 型 </dt>
					<dd>
					<Field:codeTable cssClass="combox title"  id="claimAfcPlanProjecPlanTypeId"
						name="claimAfcPlanVO.planType" value="${claimAfcPlanVO.planType}"
						tableName="APP___CLM__DBUSER.T_QC_TYPE"></Field:codeTable>
					</dd>	
				</dl>
			</div>
			<div class="divfclass">
				<h1><img src="clm/images/tubiao.png">质检要点列表</h1>
			</div>
			<div class="tabdivclassbr">
				<table id="tab_plan" class="list" width="100%">
					<thead>
						<tr>
							<th nowrap>序号</th>
							<th nowrap>质检项目名称</th>
							<th nowrap>质检要点</th>
							<th nowrap>受检岗位-扣分标准</th>
							<th nowrap>操作</th>
						</tr>
					</thead>
					<tbody id="tbodyClaimAfcPlanId">
						<s:iterator value="claimAfcPlanRelaVOList" var="claimAfcPlanRela" status="st">
							<tr>
								<td>${st.index+1}</td>
								<td>
									<div id="selectItem">${itemName}
										<input type="hidden" name="claimAfcPlanRelaVOList[<s:property value="#st.index"/>].itemId" value="${itemId}" />
									</div>
								</td>
								<td >
									${gistDesc}
										<input type="hidden" name="claimAfcPlanRelaVOList[<s:property value="#st.index"/>].gistId" value="${gistId}" />
								</td>
								<td width="76%">
									<span>
										<input id="insertSignRoleId" type="checkbox" <s:if test="signRole eq '签收人/移动签收人'">checked="checked"</s:if>
											value="签收人/移动签收人" name="claimAfcPlanRelaVOList[<s:property value="#st.index"/>].signRole"/>签收人/移动签收人
										<input value="<s:if test="signRole eq '签收人/移动签收人'" >${signRoleDeduct}</s:if>" size="1" onkeyup="changeThis(this)" name="claimAfcPlanRelaVOList[<s:property value="#st.index"/>].signRoleDeduct" onblur="integerCheck(this)"/>
									</span> 
									<span>
									<input id="insertAuditOrganId" type="checkbox" <s:if test="auditOrgan eq '外包商/医疗复核岗'">checked="checked"</s:if>
										value="外包商/医疗复核岗" name="claimAfcPlanRelaVOList[<s:property value="#st.index"/>].auditOrgan"/>外包商/医疗复核岗
									<input value="<s:if test="auditOrgan == '外包商/医疗复核岗'">${auditOrganDeduct}</s:if>" size="1" onkeyup="changeThis(this)" name="claimAfcPlanRelaVOList[<s:property value="#st.index"/>].auditOrganDeduct" onblur="integerCheck(this)"/>
									</span> 
									<span>
									<input id="insertResignRoleId" type="checkbox" <s:if test="resignRole eq '立案人/外包商'">checked="checked"</s:if>
										value="立案人/外包商" name="claimAfcPlanRelaVOList[<s:property value="#st.index"/>].resignRole"/>立案人/外包商
									<input value="<s:if test="resignRole eq '立案人/外包商'">${resignRoleDeduct}</s:if>" size="1" onkeyup="changeThis(this)" name="claimAfcPlanRelaVOList[<s:property value="#st.index"/>].resignRoleDeduct" onblur="integerCheck(this)"/>
									</span> 
									<span>
									<input id="insertSelfRuleId" type="checkbox" <s:if test="selfRule eq '自核规则'">checked="checked"</s:if>
										value="自核规则" name="claimAfcPlanRelaVOList[<s:property value="#st.index"/>].selfRule"/>自核规则
									<input value="<s:if test="selfRule eq '自核规则'">${selfRuleDeduct}</s:if>" size="1" onkeyup="changeThis(this)" name="claimAfcPlanRelaVOList[<s:property value="#st.index"/>].selfRuleDeduct" onblur="integerCheck(this)"/>
									</span> 
									<span> 
									<input id="insertExamineRoleId" type="checkbox" <s:if test="examineRole eq '审核人'">checked="checked"</s:if>
										value="审核人" name="claimAfcPlanRelaVOList[<s:property value="#st.index"/>].examineRole"/>审核人
									<input value="<s:if test="examineRole eq '审核人'">${examineRoleDeduct}</s:if>" size="1" onkeyup="changeThis(this)" name="claimAfcPlanRelaVOList[<s:property value="#st.index"/>].examineRoleDeduct" onblur="integerCheck(this)"/>
									</span> 
									<span>
									<input id="insertApproveRoleId" type="checkbox" <s:if test="approveRole eq '审批人'">checked="checked"</s:if>
										value="审批人" name="claimAfcPlanRelaVOList[<s:property value="#st.index"/>].approveRole"/>审批人
									<input value="<s:if test="approveRole eq '审批人'">${approveRoleDeduct}</s:if>" size="1" onkeyup="changeThis(this)" name="claimAfcPlanRelaVOList[<s:property value="#st.index"/>].approveRoleDeduct" onblur="integerCheck(this)"/>
									</span> 
									<span> 
									<input id="insertSurveyRoleId" type="checkbox" <s:if test="surveyRole eq '调查人'">checked="checked"</s:if>
										value="调查人" name="claimAfcPlanRelaVOList[<s:property value="#st.index"/>].surveyRole"/>调查人
									<input value="<s:if test="surveyRole eq '调查人'">${surveyRoleDeduct}</s:if>" size="1" onkeyup="changeThis(this)" name="claimAfcPlanRelaVOList[<s:property value="#st.index"/>].surveyRoleDeduct" onblur="integerCheck(this)"/>
									</span> 
								</td>
								<td >
									<input type="hidden" value="${listId}" name="claimAfcPlanRelaVOList[<s:property value="#st.index"/>].listId"/> 
									<a title='编辑' class='btnEdit' id='editButtonId' href='javascript:void(0);' onclick='edClainAfcPlanDel(this,${st.index+1});'>编辑</a>
									<a title='删除' class='btnDel' id='delButton' href='javascript:void(0);' onclick='deleteClaimAfcPlanRela(this);'>删除</a>
								</td>
							</tr>
						</s:iterator>
					</tbody>
				</table>
			</div>
					<div id="divAddPlan" class="pageFormdiv">
							<button id="btnAddPlan" class="but_blue" type="button" onclick="addClainAfcPlan()">添加</button>
					</div>
			<div id="caseFlagId" class="panelPageFormContent">
				<div class="divfclass">
					<h1>
						<img src="clm/images/tubiao.png">质检计划
					</h1>
				</div>
				<div class="panelPageFormContent" id="caseFlagId">
					<dl style="width: 100%;" id="caseFlagId1">
						<dt>案件标识</dt>
						<dd style="width: 80%;">
						</dd>
					</dl>
					<dl style="width: 100%;">
						<dt>有效标识 </dt>
						<dd>
							<span>
									<input style='margin-left: 30px' type="checkbox"
										<s:if test="claimAfcPlanVO.validFlag eq 1">checked="checked"</s:if>
										name="claimAfcPlanVO.validFlag" value="1" />有效
									</span>
						</dd>
					</dl>
				</div>
			</div>
			
			<div class="panelPageFormContent">
				<dl>
					<dt><label><font class="point" color="red">* </font></label>抽检方式</dt>
					<dd>	
						<Field:codeTable cssClass="combox title"  id="extractRateType"
							name="claimAfcPlanVO.extractRateType" value="${claimAfcPlanVO.extractRateType}"
							tableName="APP___CLM__DBUSER.T_QC_EXTRACTRATETYPE" 
							onChange="changeExtractRateType(this.value);"/>
					</dd>
				</dl>
				<dl id="extractRateDiv">
					<dt><label><font class="point" color="red">* </font></label>抽检率%</dt>
					<dd>
						<input name="claimAfcPlanVO.extractRate" id="extractRate"
							type="text" class="organ" value="${claimAfcPlanVO.extractRate}"
							onchange="changeeExtractRate(this.value)" />
					</dd>
				</dl>
				<dl id="inspectionStandardDiv" style="display:none">
					<dt><label><font class="point" color="red">* </font></label>检查水平</dt>
					<dd>
						<Field:codeTable cssClass="combox title"  id="inspectionStandard"
							name="claimAfcPlanVO.inspectionStandard" value="${claimAfcPlanVO.inspectionStandard}"
							tableName="APP___CLM__DBUSER.T_QC_INSPECTIONSTANDARD" 
							onChange="changeInspectionStandard(this.value);" defaultValue="1"/>
					</dd>
				</dl>
				<dl id="inspectionLevelDiv" style="display:none">
					<dt><label><font class="point" color="red">* </font></label>检查级别</dt>
					<dd id="inspectionLevelDd">
						<Field:codeTable cssClass="combox title"  id="inspectionLevelnumber"
							name="claimAfcPlanVO.inspectionLevelnumber" value="${claimAfcPlanVO.inspectionLevelnumber}"
							tableName="APP___CLM__DBUSER.T_INSPECTION_LEVELNUMBER"/>
					</dd>
				</dl>
			</div>
			<div class="panelPageFormContent">
				<div class="divfclass">
					<h1>
						<img src="clm/images/tubiao.png">操作人权限区间
					</h1>
				</div>
				<div class="tabdivclassbr">
					<table id="tab_auth" class="list" width="100%">
						<thead>
							<tr>
								<th nowrap>序号</th>
								<th nowrap>权限类型</th>
								<th nowrap>权限区间</th>
								<th nowrap>操作</th>
							</tr>
						</thead>
						<tbody id="claimAfcAuthVOTbodyId">
							<s:iterator value="claimAfcAuthVOList" var="claimAfcAuth"
								status="st">
								<tr class='thisTR'>
									<td>${st.index+1}</td>
									<td>
									    <%-- <Field:codeValue value="${permissionTypeCode }" tableName="APP___CLM__DBUSER.T_APPROVE_PERMISSION_TYPE"/> --%>
									    <s:if test="permissionTypeCode eq 1">审批权限（疑难）</s:if>
									    <s:if test="permissionTypeCode eq 2">审批权限（普通）</s:if>
									    <s:if test="permissionTypeCode eq 3">审核权限</s:if>
									    <input type="hidden" value="${permissionTypeCode }" name="claimAfcAuthVOList[<s:property value="#st.index"/>].permissionTypeCode" id="addPermissionTypeCode"/>
										
									</td>
									<td>${authMin}至${authMax}<input name="claimAfcAuthVOList[<s:property value="#st.index"/>].authMin" value="${authMin}" type="hidden"/><input name="claimAfcAuthVOList[<s:property value="#st.index"/>].authMax" value="${authMax}" type="hidden"/></td>
									<td><input type="hidden" value="${listId}" name="claimAfcAuthVOList[<s:property value="#st.index"/>].listId"/>
										<a title='删除' class='btnDel' id='delButton' href='javascript:void(0);' onclick='deleteClaimAfcAuth(this);'>删除</a>
										<a title='编辑' class='btnEdit' id='editButton' href='javascript:void(0);' onclick='editClaimAfcProject(this);'>编辑</a>
									</td>
									<input type="hidden" id="authMinHid" value="${authMin}"/>
									<input type="hidden" id="authMaxHid" value="${authMax}"/>
								</tr>
							</s:iterator>
						</tbody>
					</table>
				</div>
					<div id="divAddAuth" class="pageFormdiv">
							<button class="but_blue" id="btnAddAuth" type="button" onclick="addClaimAfcAuthVO()">添加</button>
					</div>
			</div>
			<div class="panelPageFormContent">
				<div class="divfclass">
					<h1>
						<img src="clm/images/tubiao.png">操作机构
					</h1>
				</div>
				<div class="tabdivclassbr"  style="height:100px;">
					<div  class="table-head" style="padding-right:17px;color:#000;height: 26%;width: 100%;">
						<table class="list" width="100%">
							<thead>
								<tr>
									<th width="20%">
										序号
										<input type="checkbox" size="5px" id="uwtomessage" value="0"
											style="left: 70%;" />
									</th>
									<th width="40%">操作机构代码</th>
									<th width="40%">操作机构名称</th>
								</tr>
							</thead>
						</table>
					</div>
					<div class="table-body" style="width:100%; height:74%;overflow-y:scroll;">
						<table id="tab_org" class="list" width="100%"  >
							<tbody id="claimAfcOrgTbId">
								<s:iterator value="claimAfcOrgVOList" var="claimAfcOrg"
									status="st">
									<tr>
										<td width="20%">
											${st.index+1}
											<input type="checkbox" flag="uwflag" style="left: 70%;" 
											<s:if test="isChecked != null"> checked="checked" </s:if>
											name="claimAfcOrgVOList[<s:property value="#st.index"/>].isChecked">
										</td>
										<td width="40%">
											${organCode}
												<input name="claimAfcOrgVOList[<s:property value="#st.index"/>].organCode" value="${organCode}" type="hidden"/>
										</td>
										<td width="40%">
												<Field:codeValue tableName="APP___CLM__DBUSER.T_UDMP_ORG" value="${organCode}" />
												<input value="<Field:codeValue tableName="APP___CLM__DBUSER.T_UDMP_ORG" value="${organCode}"/>" type="hidden"/>
										</td>
									</tr>
								</s:iterator>
							</tbody>
						</table>
					</div>
				</div>
			</div>
			<div class="panelPageFormContent">
				<div class="divfclass">
					<h1>
						<img src="clm/images/tubiao.png">操作员
					</h1>
				</div>
				<div class="tabdivclassbr">
					<table id="tab_oper" class="list" width="100%">
						<thead>
							<tr>
								<th nowrap>序号</th>
								<th nowrap>操作员代码</th>
								<th nowrap>操作员姓名</th>
								<th nowrap>操作</th>
							</tr>
						</thead>
						<tbody id="claimAfcOprVId">
							<s:iterator value="claimAfcOprVOList" var="claimAfcOpr"
								status="st">
								<tr>
									<td>
										${st.index+1}
									</td>
									<td>
										<input size="9" value="${checkPer}" name="claimAfcOprVOList[<s:property value="#st.index"/>].checkPer" type="hidden"/>
										${userName}
									</td>
									<td>
										${realName}
									</td>
									<td>
										<input type="hidden" value="${listId}" name="claimAfcOprVOList[<s:property value="#st.index"/>].listId"/>
										<a title='删除' class='btnDel' id='delButton' href='javascript:void(0);' onclick='deleteClaimAfcOpr(this);'>删除</a>
									</td>
								</tr>
							</s:iterator>
						</tbody>
					</table>
				</div>
					<div id="divAddOper" class="pageFormdiv">
							<button id="btnAddOper" class="but_blue" type="button" onclick="insertClaimAfcOprO()">添加</button>
					</div>
			</div>
			<div class="panelPageFormContent">
				<dl>
					<dt>抽取层级</dt>
					<dd>
						<span>
							<Field:codeTable cssClass="combox title"  id="extractLevelId"
								name="claimAfcPlanVO.extractLevel"
								value="${claimAfcPlanVO.extractLevel}" tableName="APP___CLM__DBUSER.T_QC_LEVEL"
								onChange="getExtractLevel(this)" />
							</span>	
					</dd>
				</dl>
			</div>	
				<div id="clmCaseId" class="panelPageFormContent">
				<div class="divfclass">
					<h1>
						<img src="clm/images/tubiao.png">按赔案层级
					</h1>
				</div>
				<div class="panelPageFormContent">
					<dl style="width: 100%;">
						<dt>理赔类型</dt>
						<dd style="width: 80%;">
							<span>
							<input style="margin-left: 20px" type="checkbox" <s:if test="1 in strListType">checked="checked"</s:if>
								id="clmGuId" name="middleCheckConditionVO.claimType" value="01" />身故
							</span>	
							<span>
							<input style="margin-left: 10px" type="checkbox" <s:if test="4 in strListType">checked="checked"</s:if>
								id="clmGaoId" name="middleCheckConditionVO.claimType" value="04" />高残
								</span>	
							<span>
							<input style="margin-left: 10px" type="checkbox" <s:if test="3 in strListType">checked="checked"</s:if>
								id="clmJiId" name="middleCheckConditionVO.claimType" value="03" />重大疾病
								</span>	
							<span>
							<input style="margin-left: 10px" type="checkbox" <s:if test="2 in strListType">checked="checked"</s:if>
								id="clmShangId" name="middleCheckConditionVO.claimType" value="02" />伤残
								</span>	
							<span>
							<input style="margin-left: 10px" type="checkbox" <s:if test="8 in strListType">checked="checked"</s:if> 
								id="clmYiId" name="middleCheckConditionVO.claimType" value="08" />医疗
								</span>	
							<span>
							<input style="margin-left: 10px" type="checkbox" <s:if test="10 in strListType">checked="checked"</s:if>
								id="clmTeId" name="middleCheckConditionVO.claimType" value="10" />特种疾病
								</span>	
							<span>
							<input style="margin-left: 10px" type="checkbox" <s:if test="6 in strListType">checked="checked"</s:if>
								id="clmShiId" name="middleCheckConditionVO.claimType" value="06" />一般失能
								</span>	
							<span>
							<input style="margin-left: 10px" type="checkbox" <s:if test="7 in strListType">checked="checked"</s:if>
								id="clmZhongId" name="middleCheckConditionVO.claimType" value="07" />重大失能
								</span>	
						</dd>
					</dl>
					<dl style="width: 100%;">
						<dt>出险原因</dt>
						<dd style="width: 80%;">
							<span>
							<input style="margin-left: 20px" type="checkbox" <s:if test="2 in reasonStrList">checked="checked"</s:if>
								id="clmYiId" name="middleCheckConditionVO.accReason" value="2" />意外
							</span>
							<span>	
							<input style="margin-left: 10px" type="checkbox" <s:if test="1 in reasonStrList">checked="checked"</s:if>
								id="clmId" name="middleCheckConditionVO.accReason" value="1" />疾病
							</span>	
						</dd>
					</dl>
				</div>
				<div class="panelPageFormContent">
					<div class="divfclass">
						<h1>
							<img src="clm/images/tubiao.png">险种
						</h1>
					</div>
					
					<div id="productForm" class="main_text">
			<table width="98%">
				<tr>
					<td><span  style="float:left;line-height:1.6;">险种快速查询</span><input type="text" id="businessSearchId"  style="float:left;" /></td>
					<td></td>
					<td></td>
				</tr>
				<tr height="30px;">
					<td>
						<dl class='nowrap'>
							<dt>险种</dt>
						</dl>
					</td>
					<td>
					</td>
					<td>
						<dl class='nowrap'>
							<dt>参与质检的险种</dt>
						</dl>
					</td>
				</tr>
				<tr>
					<td width="40%">
						<div >
							<select name="allProduct" id="allProduct" multiple="multiple"
							style="height:120px; width:100%;" size=5
							ondblclick="moveselect('allProduct','selectedProduct',true)">
								<s:iterator value="businessProductVOs" var="product">
													<s:if test="productNameSys != null">
														<option value="${productCodeSys}">
															${productCodeSys}-${productNameSys}</option>
													</s:if>
												</s:iterator>		
							
							</select>
						</div>
					</td>
					<td align="center" width="8%">
								<div class="buttonContent">
									<button class="but_gray" id="toRightP" name="addOne"
										onclick="moveselect('allProduct','selectedProduct',true)"
										type="button" style="padding-left: 14px; padding-right: 14px;">></button>
								</div>
							<div style="clear: left; margin: 5px 0px 0px;">
								<div class="buttonContent">
									<button class="but_gray" name="addAll"
										onclick="moveselect('allProduct','selectedProduct',true,'1')"
										type="button">>></button>
								</div>
							</div>
							<div style="clear: left; margin: 5px 0px 0px;">
								<div class="buttonContent">
									<button class="but_gray" name="releaseOne"
										onclick="moveselect('selectedProduct','allProduct',true)"
										type="button" style="padding-left: 14px; padding-right: 14px;"><</button>
								</div>
							</div>
							<div  style="clear: left; margin: 5px 0px 0px;">
								<div class="buttonContent">
									<button class="but_gray" name="releaseAll"
										onclick="moveselect('selectedProduct','allProduct',true,'1')"
										type="button"><<</button>
								</div>
							</div>
<!-- 							</div> -->
					</td>
					<td width="40%">
						<div >
							<select size="10" id="selectedProduct"
											name="middleCheckConditionVO.product"
											ondblclick="moveselect('selectedProduct','allProduct',true)"
											multiple style="height:120px; width:100%;">
												<s:iterator value="businessStrList" var="businessStr">
													<option value="${productCodeSys}">
														${productCodeSys}-${productNameSys}</option>
												</s:iterator>
										</select>
						</div>
					</td>
				</tr>
			</table>
		</div>
				</div>
			</div>
			<div id="clmCustomerId" class="panelPageFormContent">
				<div class="divfclass">
					<h1>
						<img src="clm/images/tubiao.png">按客户层级
					</h1>
				</div>
				<div class="panelPageFormContent">
					<dl>
						<dt>保单生效日期</dt> 
						<dd>
						<input id="validDateId"  type="expandDateYMD" name="claimAfcPlanVO.validDate" value='<fmt:formatDate value="${claimAfcPlanVO.validDate}"
							pattern="yyyy-MM-dd"></fmt:formatDate>' class="date" readonly="readonly" />
						<a class="inputDateButton" href="javascript:;">选择</a>
						</dd>
					</dl>
					<dl>
						<dt>客户号</dt>
						<dd>
						<input name="claimAfcPlanVO.customerId" value="${claimAfcPlanVO.customerId}" id="customerIdId" />
						</dd>
					</dl>
					<dl>
						<dt>证件号码</dt>
						<dd>
						<input name="claimAfcPlanVO.certiCode"
							value="${claimAfcPlanVO.certiCode}" id="certiCodeId" />
						</dd>	
					</dl>
				</div>
				<div class="divfclass">
						<h1>
							<img src="clm/images/tubiao.png">片区
						</h1>
					</div>
				<div class="main_text">
					<table width="98%">
				<tr>
					<td><span  style="float:left;line-height:1.6;">片区快速查询</span><input type="text" id="appendItemSearchId"  style="float:left;line-height:1.6;" /></td>
					<td></td>
					<td></td>
				</tr>
				<tr height="30px;">
					<td>
						<dl class='nowrap'>
							<dt>片区</dt>
						</dl>
					</td>
					<td>
					</td>
					<td>
						<dl class='nowrap'>
							<dt>参与质检的片区</dt>
						</dl>
					</td>
				</tr>
				<tr>
					<td width="40%">
						<div >
							<select id="selectedArea" name="selectedArea" multiple="multiple"
							style="height:120px; width:100%;" size=5
							ondblclick="moveselect('selectedArea', 'areaList', true);">
								<s:iterator value="areaList" var="area">
												<s:if test="areaName != null">
													<option value="${areaCode}">
														${areaCode}-${areaName}</option>
												</s:if>
											</s:iterator>	
							</select>
						</div>
					</td>
					<td align="center" width="8%">
								<div class="buttonContent">
									<button id="toRightO"
										onclick="moveselect('selectedArea', 'areaList', true);"
										type="button" class="but_gray" style="padding-left: 14px; padding-right: 14px;">></button>
								</div>
							<div style="clear: left; margin: 5px 0px 0px;">
								<div class="buttonContent">
									<button id="allToRightO" class="but_gray"
										onclick="moveselect('selectedArea','areaList', true,'1');"
										type="button">>></button>
								</div>
							</div>
							<div style="clear: left; margin: 5px 0px 0px;">
								<div class="buttonContent">
									<button id="toleftO" class="but_gray"
										onclick="moveselect('areaList', 'selectedArea', true);"
										type="button" style="padding-left: 14px; padding-right: 14px;"><</button>
								</div>
							</div>
							<div  style="clear: left; margin: 5px 0px 0px;">
								<div class="buttonContent">
									<button id="allToLeftO" class="but_gray"
										onclick="moveselect('areaList','selectedArea', true,'1');"
										type="button"><<</button>
								</div>
							</div>
					</td>
					<td width="40%">
						<div >
							<select id="areaList" name="middleCheckConditionVO.area"
									multiple="multiple" style="height:120px; width:100%;" size=5
									ondblclick="moveselect('areaList','selectedArea',  true);">
									<s:iterator value="areaStrList" var="areaStr">
											<option value="${areaCode}">${areaCode}-${areaName}</option>
									</s:iterator>
							</select>
						</div>
					</td>
				</tr>
			</table>
		</div>
				<div class="panelPageFormContent">
					<div class="divfclass">
						<h1>
							<img src="clm/images/tubiao.png">管理机构
						</h1>
					</div>
					
			<div id="productForm" class="main_text">
			<table width="98%">
				<tr>
					<td><span  style="float:left;line-height:1.6;">管理机构快速查询</span><input type="text" id="organSearchId"  style="float:left;line-height:1.6;" /></td>
					<td></td>
					<td></td>
				</tr>
				<tr height="30px;">
					<td>
						<dl class='nowrap'>
							<dt>管理机构</dt>
						</dl>
					</td>
					<td>
					</td>
					<td>
						<dl class='nowrap'>
							<dt>参与质检的管理机构</dt>
						</dl>
					</td>
				</tr>
				<tr>
					<td width="40%">
						<div>
							<select id="selectedOrganization" name="name" multiple="multiple"
									style="height:120px; width:100%;" size=5
									ondblclick="moveselect('selectedOrganization', 'orgList', true);">
								<s:iterator value="orgVOList" var="org">
									<s:if test="organName != null">
											<option value="${organCode}">
												${organCode}-${organName}</option>
									</s:if>
								</s:iterator>	
							</select>
						</div>
					</td>
					<td align="center" width="8%">
								<div class="buttonContent">
									<button class="but_gray" id="toRightO"
										onclick="moveselect('selectedOrganization', 'orgList', true);"
										type="button" style="padding-left: 14px; padding-right: 14px;">></button>
								</div>
							<div style="clear: left; margin: 5px 0px 0px;">
								<div class="buttonContent">
									<button class="but_gray" id="allToRightO"
										onclick="moveselect('selectedOrganization','orgList', true,'1');"
										type="button">>></button>
								</div>
							</div>
							<div style="clear: left; margin: 5px 0px 0px;">
								<div class="buttonContent">
									<button class="but_gray" id="toleftO"
										onclick="moveselect('orgList', 'selectedOrganization', true);"
										type="button" style="padding-left: 14px; padding-right: 14px;"><</button>
								</div>
							</div>
							<div  style="clear: left; margin: 5px 0px 0px;">
								<div class="buttonContent">
									<button class="but_gray" id="allToLeftO"
										onclick="moveselect('orgList','selectedOrganization', true,'1');"
										type="button"><<</button>
								</div>
							</div>
<!-- 							</div> -->
					</td>
					<td width="40%">
						<div >
							<select id="orgList" name="middleCheckConditionVO.organization"
									multiple="multiple" style="height:120px; width:100%;" size=5
									ondblclick="moveselect('orgList','selectedOrganization',  true);">
									<s:iterator value="orgStrList" var="orgStr">
										<option value="${organCode}">${organCode}-${organName}</option>
									</s:iterator>
							</select>
						</div>
					</td>
				</tr>
			</table>
		</div>
				</div>
			</div>
		<div class="panelPageFormContent main_tabdiv">
			<dl>
				<dt>制定日期</dt>
				<dd>
					<input value="<fmt:formatDate value="${claimAfcPlanVO.makeDate}" pattern="yyyy-MM-dd"></fmt:formatDate>"
					name="claimAfcPlanVO.makeDate" readonly="true" />
				</dd>
			</dl>
			<dl>
				<dt>制定人</dt>
				<dd>
					<input style="width: 50px;" size="4" value="${claimAfcPlanVO.makeBy}" name="claimAfcPlanVO.makeBy" readonly="true" />
					<input style="width: 120px;" value="<Field:codeValue tableName="APP___CLM__DBUSER.T_UDMP_USER" value="${claimAfcPlanVO.makeBy}"/>" readonly="true" />
				</dd>
			</dl>
			<dl style="width: 100%;height: auto;">
				<dt>备注</dt>
				<dd>
					<textarea rows="2" cols="80" style="margin-left: 15px;" name="claimAfcPlanVO.remark">${claimAfcPlanVO.remark}</textarea>
				</dd>
			</dl>
		</div>
		</div>	
		<div class="formBarButton">
			<ul>
				<li>
					<button class="but_blue" type="button" onclick="inserClaimAfc();">保存</button>
				</li>
				<li>
					<button class="but_gray" type="button"  onclick="emptyAllMesage123()">清空</button>
				</li>
				<li>
					<button class="but_gray" type="reset" onclick="exit();">退出</button>
				</li>
			</ul>
		</div>
	</form>
</div>