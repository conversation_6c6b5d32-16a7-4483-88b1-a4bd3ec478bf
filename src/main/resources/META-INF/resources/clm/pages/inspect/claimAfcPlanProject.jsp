<%@ page language="java" contentType="text/html; charset=utf-8"
	pageEncoding="UTF-8"%><%@ include file="/clm/pages/common/commonJsCss.jsp" %>
<%@ taglib uri="/struts-tags" prefix="s"%>

<%@ taglib uri="http://www.git.com.cn/taglib/udmp/field" prefix="Field"%>
<script type="text/javascript"
	src="clm/pages/inspect/claimAfcPlanProject.js"></script>

<div layouth="10">
	<div class="divfclass">
		<h1><img src="clm/images/tubiao.png">质检计划查询</h1>
	</div>
	<form id="pagerForm" method="post"
			action="clm/inspect/queryClaimAfcPlanProject_CLM_claimAfcPlanProjectAction.action">
			<input type="hidden" name="pageNum" value="${pageInfo.currentPage}" />
			<input type="hidden" name="numPerPage" value="${pageInfo.numPerPage}" />
	</form>
	<form id="addClaimAfcPlanProject" method="post"
		onsubmit="return navTabSearch(this)"
		action="clm/inspect/addClaimAfcPlanProjectInit_CLM_claimAfcPlanProjectAction.action">
	</form>
	<form id="claimAfcPlanProjectRadio" method="post"
		onsubmit="return navTabSearch(this)"
		action="clm/inspect/claimAfcPlanProjectRadio_CLM_claimAfcPlanProjectAction.action">
		<input id="claimAfcPlanProjecPlanType" name="claimAfcPlanVO.planType" type="hidden"/>
		<input id="claimAfcPlanProjecPlanName" name="claimAfcPlanVO.planName" type="hidden"/>
		<input id="claimAfcPlanProjecPlanId" name="claimAfcPlanVO.planId" type="hidden"/>
	</form>
	<form id="queryClaimAfcPlanProject" method="post"
		onsubmit="return navTabSearch(this)"
		action="clm/inspect/queryClaimAfcPlanProject_CLM_claimAfcPlanProjectAction.action"
		rel="pagerForm">
				<div class="divfclass">
					<h1><img src="clm/images/tubiao.png">查询条件</h1>
				</div>
			<div class="pageFormInfoContent">
				<dl>
					<dt> 质 检 计 划 名 称 </dt>
					<dd>
						<input name="claimAfcPlanVO.planName" id="a" value="${claimAfcPlanVO.planName }" onkeyup="this.value=this.value.replace(/\s/g,'')"/>
					</dd>
				</dl>
				<dl>
					<dt> <font class="point" color="red">* </font>质 检 类 型  </dt>
					<dd>
						<Field:codeTable cssClass="combox title"  name="claimAfcPlanVO.planType"
							tableName="APP___CLM__DBUSER.T_QC_TYPE" value="${claimAfcPlanVO.planType}"></Field:codeTable>
					</dd>
				</dl>
				<div class="pageFormdiv">
					<button type="button" class="but_blue" onclick="queryClaimAfcPlanProject();">查询</button>
					<button type="button" class="but_blue" onclick="addClaimAfcPlanProject();">新增</button>
				</div>				
			</div>
	</form>
	<div>
		<div class="divfclass">
			<h1><img src="clm/images/tubiao.png">查询质检计划结果</h1>
		</div>
			<div class="tabdivclassbr">
				<table class="list main_dbottom" width="100%">
					<thead>
						<tr>
							<th nowrap>序号</th>
							<th nowrap>质检类型</th>
							<th nowrap>质检计划名称</th>
							<th nowrap>操作</th>
						</tr>
					</thead>
					<tbody>
						<s:if test="imageFlag != null">
							<tr>
								<td colspan="3">
									<div class="noRueryResult">请选择条件查询数据！</div>
								</td>
							</tr>
						</s:if>
						<s:elseif test="currentPage.PageItems == null || currentPage.PageItems.size()==0">
							<tr>
								<td colspan="3">
									<div class="noRueryResult">没有符合条件的查询结果！</div>
								</td>
							</tr>
						</s:elseif>
						<s:iterator value="currentPage.pageItems" var="claimAfcPlan"
							status="st">
							<tr>
								<td><div align="center">
										${st.index+1}<input name="claimAfcrAdio" type="radio" value="${planName}:${planType}:${planId}" onclick="claimAfcPlanProjectRadio(this);" style="border:0px;background:0px;width:auto; float: left;" />
									</div></td>
								<td><s:if test="planType eq 1">
										<div align="center">
											<span>常规</span>
										</div>
									</s:if> <s:if test="planType eq 2">
										<div align="center">
											<span>专项</span>
										</div>
									</s:if></td>
								<td><div align="center">${planName}</div></td>
								<td><div align="center"><a title='删除' class='btnDel' id='delButton' value="${planName}:${planType}:${planId}" href='javascript:void(0);' onclick='deleteClaimAfcPlanResult(this);'>删除</a></div></td>
							</tr>
						</s:iterator>
					</tbody>
				</table>
			<div class="panelBar" >
			<div class="pages">
					<span>显示</span>
					<s:select   list="#{5:'5',10:'10',20:'20',50:'50'}"  name="select" onchange="navTabPageBreak({numPerPage:this.value})" value="currentPage.pageSize">
		  				</s:select>
					<span>条，共${currentPage.total}条</span>		
			</div>
			<div style="margin-right: 230px" class="pagination" targetType="navTab"
				totalCount="${currentPage.total}"
				numPerPage="${currentPage.pageSize}" pageNumShown="10"
				currentPage="${currentPage.pageNo}"></div>
			</div>
			</div>
	</div>
</div>
