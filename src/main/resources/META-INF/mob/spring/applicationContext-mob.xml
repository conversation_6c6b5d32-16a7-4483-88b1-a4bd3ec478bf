<?xml version="1.0" encoding="UTF-8"?><beans xmlns="http://www.springframework.org/schema/beans" xmlns:aop="http://www.springframework.org/schema/aop" xmlns:batch="http://www.springframework.org/schema/batch" xmlns:c="http://www.springframework.org/schema/c" xmlns:cache="http://www.springframework.org/schema/cache" xmlns:context="http://www.springframework.org/schema/context" xmlns:p="http://www.springframework.org/schema/p" xmlns:task="http://www.springframework.org/schema/task" xmlns:tx="http://www.springframework.org/schema/tx" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.springframework.org/schema/task http://www.springframework.org/schema/task/spring-task-3.0.xsd   http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd   http://www.springframework.org/schema/batch http://www.springframework.org/schema/batch/spring-batch-2.1.xsd   http://www.springframework.org/schema/cache http://www.springframework.org/schema/cache/spring-cache-3.1.xsd   http://www.springframework.org/schema/aop http://www.springframework.org/schema/aop/spring-aop-3.0.xsd   http://www.springframework.org/schema/tx http://www.springframework.org/schema/tx/spring-tx-3.0.xsd   http://www.springframework.org/schema/context http://www.springframework.org/schema/context/spring-context-3.0.xsd">
	<!-- 保全录入 -->
	<bean class="com.nci.tunan.mobcss.web.controller.MobCsEntryAction" id="PA_mobCsEntryAction" scope="prototype">
		<property name="cusCustomerUCC" ref="PA_cusCustomerUCC"/>
		<property name="cusApplicationUCC" ref="PA_cusApplicationUCC"/>
		<property name="cusAcceptUCC" ref="PA_cusAcceptUCC"/>
		<!-- 收付费 -->
		<property name="csPremArapUCC" ref="PA_csPremArapUCC"/>
	</bean>

	<!-- 打印凭证 -->
	<bean class="com.nci.tunan.mobcss.web.controller.MobCsEndorseApplyVoucherAction" id="PA_mobCsEndorseApplyVoucherAction" scope="prototype">
		<property name="csApplyVoucherUCC" ref="PA_csApplyVoucherUCC"/>
	</bean>

	<!-- 退保试算 -->
	<bean class="com.nci.tunan.mobcss.web.controller.MobSurrenderQuotAppAction" id="PA_mobSurrenderQuotAppAction" scope="prototype">
		<property name="surrenderQuotAppUCC" ref="PA_surrenderQuotAppUCC"/>
	</bean>

	<bean class="com.nci.tunan.mobcss.web.controller.MobBankAccountInfoAction" id="PA_mobBankAccountInfoAction" scope="prototype">
		<property name="bankAccountInfoUCC" ref="PA_bankAccountInfoUCC"/>
	</bean>

	<!-- 客户基本信息变更 -->
	<bean class="com.nci.tunan.mobcss.web.controller.MobCsEndorseCCAction" id="PA_mobCsEndorseCCAction" scope="prototype">
		<property name="custBaseInfoChgUCC" ref="PA_custBaseInfoChgUCC"/>
		<property name="districtUCC" ref="PA_districtUCC"/>
	</bean>
	<!-- 交费信息变更 -->
	<bean class="com.nci.tunan.mobcss.web.controller.MobCsEndorsePCAction" id="PA_mobCsEndorsePCAction" scope="prototype">
		<property name="paymentInfoChgUCC" ref="PA_paymentInfoChgUCC"/>
		<property name="bankAccountInfoUCC" ref="PA_bankAccountInfoUCC"/>
	</bean>
	<bean class="com.nci.tunan.mobcss.web.controller.MobCsEndorseAGAction" id="PA_mobCsEndorseAGAction" scope="prototype">
		<property name="csEndorseAGUCC" ref="PA_csEndorseAGUCC"/>
	</bean>
	<!-- fujiayan 受益人变更 -->
	<bean class="com.nci.tunan.mobcss.web.controller.MobCsEndorseBCAction" id="PA_mobCsEndorseBCAction" scope="prototype">
		<!-- <property name="mcBasicRemarkChgUCC" ref="mcBasicRemarkChgUCC"></property> -->
		<property name="bcBeneficiaryUpdateUCC" ref="PA_csEndorseBCUCC"/>
		<property name="csPolicyBeneUCC" ref="PA_csPolicyBeneUCC"/>
	</bean>
	<!-- qianyx_wb 保全 新增附加险 -->
	<bean class="com.nci.tunan.mobcss.web.controller.MobCsEndorseNSAction" id="PA_mobCsEndorseNSAction" scope="prototype">
		<property name="csEndorseNSUCC" ref="PA_csEndorseNSUCC"/>
		<property name="csSurveyUCC" ref="PA_csSurveyUCC"/>
	</bean>
	<!-- qianyx_wb 保全 保单补发 -->
	<bean class="com.nci.tunan.mobcss.web.controller.MobCsEndorseLRAction" id="PA_mobCsEndorseLRAction" scope="prototype">
		<property name="csEndorseLRUCC" ref="PA_csEndorseLRUCC"/>
	</bean>
	<!-- qianyx_wb 保全 减保 -->
	<bean class="com.nci.tunan.mobcss.web.controller.MobCsEndorsePTAction" id="PA_mobCsEndorsePTAction" scope="prototype">
		<property name="csEndorsePTUCC" ref="PA_csEndorsePTUCC"/>
	</bean>
	<!-- qianyx_wb 保全 减额缴清 -->
	<bean class="com.nci.tunan.mobcss.web.controller.MobCsEndorsePUAction" id="PA_mobCsEndorsePUAction" scope="prototype">
		<property name="csEndorsePUUCC" ref="PA_csEndorsePUUCC"/>
	</bean>
	<!-- qianyx_wb 万能险基本保额减少 -->
	<bean class="com.nci.tunan.mobcss.web.controller.MobCsEndorseCBAction" id="PA_mobCsEndorseCBAction" scope="prototype">
		<property name="csEndorseCBUCC" ref="PA_csEndorseCBUCC"/>
	</bean>
	<!-- qianyx_wb 万能险基本保额增加 -->
	<bean class="com.nci.tunan.mobcss.web.controller.MobCsEndorseCAAction" id="PA_mobCsEndorseCAAction" scope="prototype">
		<property name="csEndorseCAUCC" ref="PA_csEndorseCAUCC"/>
	</bean>
	<!-- qianyx_wb 被保险人职业类别变更 -->
	<bean class="com.nci.tunan.mobcss.web.controller.MobCsEndorseIOAction" id="PA_mobCsEndorseIOAction" scope="prototype">
		<property name="cateGoryUpdateUCC" ref="PA_cateGoryUpdateUCC"/>
		<property name="cusApplicationUCC" ref="PA_cusApplicationUCC"/>
	</bean>
	<!-- qianyx_wb 转养老金 -->
	<bean class="com.nci.tunan.mobcss.web.controller.MobCsEndorseTAAction" id="PA_mobCsEndorseTAAction" scope="prototype">
		<property name="csEndorseTAUCC" ref="PA_csEndorseTAUCC"/>
	</bean>
	<!-- qianyx_wb 缴费方式及期限变更 -->
	<bean class="com.nci.tunan.mobcss.web.controller.MobCsEndorseFMAction" id="PA_mobCsEndorseFMAction" scope="prototype">
		<property name="csEndorseFMUCC" ref="PA_csEndorseFMUCC"/>
	</bean>
	<!-- qianyx_wb 缴费方式及期限变更 -->
	<bean class="com.nci.tunan.mobcss.web.controller.MobCsEndorseLNAction" id="PA_mobCsEndorseLNAction" scope="prototype">
		<property name="csEndorseLNUCC" ref="PA_csEndorseLNUCC"/>
		<property name="csEndorseREUCC" ref="PA_csEndorseREUCC"/>
		<property name="csEndorseTRUCC" ref="PA_csEndorseTRUCC"/>
		<property name="csEndorseRLUCC" ref="PA_csEndorseRLUCC"/>
		<property name="csEndorseCTUCC" ref="PA_csEndorseCTUCC"/>
	</bean>
	<!-- qianyx_wb 满期不续保申请 -->
	<bean class="com.nci.tunan.mobcss.web.controller.MobCsEndorseENAction" id="PA_mobCsEndorseENAction" scope="prototype">
		<property name="enUCCImpl" ref="PA_enUCCImpl"/>
	</bean>
	<!-- qianyx_wb 保全 减额缴清 -->
	<bean class="com.nci.tunan.mobcss.web.controller.MobCsEndorseFKAction" id="PA_mobCsEndorseFKAction" scope="prototype">
		<property name="csEndorseFKUCC" ref="PA_csEndorseFKUCC"/>
	</bean>
	<!-- qianyx_wb 增补告知 -->
	<bean class="com.nci.tunan.mobcss.web.controller.MobCsEndorseHIAction" id="PA_mobCsEndorseHIAction" scope="prototype">
		<property name="csEndorseHIUCC" ref="PA_csEndorseHIUCC"/>
		<property name="csSurveyUCC" ref="PA_csSurveyUCC"/>
		<property name="toUWParameterUcc" ref="PA_toUWParameterUcc"/>
	</bean>
	<!-- qianyx_wb 保单冻结 -->
	<bean class="com.nci.tunan.mobcss.web.controller.MobCsEndorseCFAction" id="PA_mobCsEndorseCFAction" scope="prototype">
		<property name="cfFreezePolicyUCC" ref="PA_cfFreezePolicyUCC"/>
	</bean>
	<!-- qianyx_wb 保单解冻 -->
	<bean class="com.nci.tunan.mobcss.web.controller.MobCsEndorseCWAction" id="PA_mobCsEndorseCWAction" scope="prototype">
		<property name="cfFreezePolicyUCC" ref="PA_cfFreezePolicyUCC"/>
	</bean>
	<!--  主险续保 -->
	<bean class="com.nci.tunan.mobcss.web.controller.MobCsEndorseMRAction" id="PA_mobCsEndorseMRAction" scope="prototype">
		<property name="mrMainProdRenewalUCCImpl" ref="PA_mrMainProdRenewalUCCImpl"/>
	</bean>
	<!--保单解挂 -->
	<bean class="com.nci.tunan.mobcss.web.controller.MobCsEndorsePFAction" id="PA_mobCsEndorsePFAction" scope="prototype">
		<property name="ICsEndorsePFUCC" ref="PA_ICsEndorsePFUCC"/>
	</bean>
	<!--保单挂失 -->
	<bean class="com.nci.tunan.mobcss.web.controller.MobCsEndorsePLAction" id="PA_mobCsEndorsePLAction" scope="prototype">
		<property name="ICsEndorsePLUCC" ref="PA_ICsEndorsePLUCC"/>
	</bean>
	 <!--  保单起期变更（个人） -->
	<bean class="com.nci.tunan.mobcss.web.controller.MobCsEndorseYSAction" id="PA_mobCsEndorseYSAction" scope="prototype">
	      <property name="csEndorseYSUCC" ref="PA_csEndorseYSUCC"/>
	</bean>	
	<!-- lvwq 保单贷款清偿 -->
	<bean class="com.nci.tunan.mobcss.web.controller.MobCsEndorseRFAction" id="PA_mobCsEndorseRFAction" scope="prototype">
		<property name="csEndorseRFUCC" ref="PA_csEndorseRFUCC"/>
		<property name="csEndorseRLUCC" ref="PA_csEndorseRLUCC"/>
		<property name="csEndorseCTUCC" ref="PA_csEndorseCTUCC"/>
	</bean>
	<!-- sunlr 退保 -->
	<bean class="com.nci.tunan.mobcss.web.controller.MobCsEndorseCTAction" id="PA_mobCsEndorseCTAction" scope="prototype">
		<property name="csEndorseCTUCC" ref="PA_csEndorseCTUCC"/>
	</bean>

	<!-- sunlr 保单复效 -->
	<bean class="com.nci.tunan.mobcss.web.controller.MobCsEndorseREAction" id="PA_mobCsEndorseREAction" scope="prototype">
		<property name="csEndorseREUCC" ref="PA_csEndorseREUCC"/>
		<property name="csSurveyUCC" ref="PA_csSurveyUCC"/>
		<property name="csEndorsePRUCC" ref="PA_csEndorsePRUCC"/>
		<property name="toUWParameterUcc" ref="PA_toUWParameterUcc"/>
	</bean>
	<!--保单迁移 -->
	<bean class="com.nci.tunan.mobcss.web.controller.MobCsEndorsePRAction" id="PA_mobCsEndorsePRAction" scope="prototype">
		<property name="csEndorsePRUCC" ref="PA_csEndorsePRUCC"/>
		<property name="csEndorseLRUCC" ref="PA_csEndorseLRUCC"/>
	</bean>
	<!-- 领取形式变更 -->
	<bean class="com.nci.tunan.mobcss.web.controller.MobCsEndorseGCAction" id="PA_mobCsEndorseGCAction" scope="prototype">
		<property name="csEndorseGCUCC" ref="PA_csEndorseGCUCC"/>
	</bean>
	<!-- 添加银行信息 -->
	<bean class="com.nci.tunan.mobcss.web.controller.MobBankAccountInfoAction" id="PA_mobBankInfoAction" scope="prototype">
		<property name="bankAccountInfoUCC" ref="PA_bankAccountInfoUCC"/>
	</bean>
	
	<!-- 万能险基本保险金额约定变更DC -->
	<bean class="com.nci.tunan.mobcss.web.controller.MobCsEndorseDCAction" id="PA_mobCsEndorseDCAction" scope="prototype">
		<property name="csEndorseDCUCC" ref="PA_csEndorseDCUCC"/>
	
	</bean>
	<!--chenln_wb 保全 客户重要资料变更 -->
	<bean class="com.nci.tunan.mobcss.web.controller.MobCsEndorseCMAction" id="PA_mobCsEndorseCMAction" scope="prototype">
		<property name="cusImportInfoChgUCC" ref="PA_csEndorseCMUCC"/>
	</bean>
	<!-- yuzw 投保人变更 -->
	<bean class="com.nci.tunan.mobcss.web.controller.MobCsEndorseAEAction" id="PA_mobCsEndorseAEAction" scope="prototype">
		<property name="csEndorseAEUCC" ref="PA_csEndorseAEUCC"/>
		<property name="policyHolderUCC" ref="PA_policyHolderUCC"/>
	</bean>
	<!-- 生存保险金追回 -->
	<bean class="com.nci.tunan.mobcss.web.controller.MobCsEndorseRGAction" id="PA_mobCsEndorseRGAction" scope="prototype">
		<property name="csEndorseRGUCC" ref="PA_csEndorseRGUCC"/>
	</bean>

	<!-- 新增可选责任 -->
	<bean class="com.nci.tunan.mobcss.web.controller.MobCsEndorseXDAction" id="PA_mobCsEndorseXDAction" scope="prototype">
		<property name="csEndorseXDUCC" ref="PA_csEndorseXDUCC"/>
	</bean>
	<!-- 被保人告知 -->
	<bean class="com.nci.tunan.mobcss.web.controller.MobCsSurveyAction" id="PA_mobCsSurveyAction" scope="prototype">
		<property name="toUWParameterUcc" ref="PA_toUWParameterUcc"/>
		<property name="csSurveyUCC" ref="PA_csSurveyUCC"/>
	</bean>
	<!-- 领取方式变更 -->
	<bean class="com.nci.tunan.mobcss.web.controller.MobCsEndorseGMAction" id="PA_mobCsEndorseGMAction" scope="prototype">
		<property name="csEndorseGMUCC" ref="PA_csEndorseGMUCC"/>
	</bean>
	<!-- 领取日期变更 -->
	<bean class="com.nci.tunan.mobcss.web.controller.MobCsEndorseLCAction" id="PA_mobCsEndorseLCAction" scope="prototype">
		<property name="csEndorseLCUCC" ref="PA_csEndorseLCUCC"/>
	</bean>
	<!-- 领取年龄变更 -->
	<bean class="com.nci.tunan.mobcss.web.controller.MobCsEndorseGBAction" id="PA_mobCsEndorseGBAction" scope="prototype">
		<property name="csEndorseGBUCC" ref="PA_csEndorseGBUCC"/>
	</bean>
	<!-- wangsy 保全回退 -->
	<bean class="com.nci.tunan.mobcss.web.controller.MobCsEndorseRBAction" id="PA_mobCsEndorseRBAction" scope="prototype">
		<property name="csEndorseRBUCC" ref="PA_csEndorseRBUCC"/>
	</bean>
	<!-- 附加特约终止 -->
	<bean class="com.nci.tunan.mobcss.web.controller.MobCsEndorseDTAction" id="PA_mobCsEndorseDTAction" scope="prototype">
		<property name="dtAddSubPolicyUCC" ref="PA_dtAddSubPolicyUCC"/>
	</bean>
		
		<!-- 追加保费 -->
		<bean class="com.nci.tunan.mobcss.web.controller.MobCsEndorseAMAction" id="PA_mobCsEndorseAMAction" scope="prototype">
			<property name="csEndorseAMUCC" ref="PA_csEndorseAMUCC"/>
		
		</bean>
	<!-- 增加附加特约责任 -->
	<bean class="com.nci.tunan.mobcss.web.controller.MobCsEndorseDAAction" id="PA_mobCsEndorseDAAction" scope="prototype">
		<property name="daAddSubPolicyUCC" ref="PA_daAddSubPolicyUCC"/>
	</bean>
	<!-- 保费自垫申请和终止 -->
	<bean class="com.nci.tunan.mobcss.web.controller.MobCsEndorseAPAction" id="PA_mobCsEndorseAPAction" scope="prototype">
		<property name="modifyAplPermitUCC" ref="PA_modifyAplPermitUCC"/>
	</bean>
	<!-- 累积生息账户领取与注销 -->
	<bean class="com.nci.tunan.mobcss.web.controller.MobCsEndorseAIAction" id="PA_mobCsEndorseAIAction" scope="prototype">
		<property name="csEndorseAIUCC" ref="PA_csEndorseAIUCC"/>
		<property name="csEndorseREUCC" ref="PA_csEndorseREUCC"/>
	</bean>
	<!-- 保单贷款续贷 -->
	<bean class="com.nci.tunan.mobcss.web.controller.MobCsEndorseRLAction" id="PA_mobCsEndorseRLAction" scope="prototype">
		<property name="csEndorseRLUCC" ref="PA_csEndorseRLUCC"/>
		<property name="csEndorseREUCC" ref="PA_csEndorseREUCC"/>
		<property name="csEndorseCTUCC" ref="PA_csEndorseCTUCC"/>
		<property name="csEndorseTRUCC" ref="PA_csEndorseTRUCC"/>
	</bean>
	<!-- 关联银行卡 -->
	<bean class="com.nci.tunan.mobcss.web.controller.MobCsEndorseCDAction" id="PA_mobCsEndorseCDAction" scope="prototype">
		<property name="csEndorseCDUCC" ref="PA_csEndorseCDUCC"/>
	</bean>
	
	<!-- 新增领取责任 -->
	<bean class="com.nci.tunan.mobcss.web.controller.MobCsEndorseAZAction" id="PA_mobCsEndorseAZAction" scope="prototype">
		<property name="csEndorseAZUCC" ref="PA_csEndorseAZUCC"/>
	</bean>
	
	<!-- 附加险满期降低保额续保 -->
	<bean class="com.nci.tunan.mobcss.web.controller.MobCsEndorseERAction" id="PA_mobCsEndorseERAction" scope="prototype">
		<property name="csEndorseErUCCImpl" ref="PA_csEndorseErUCCImpl"/>
	</bean>
	
	<!-- 投连险退保 -->
	<bean class="com.nci.tunan.mobcss.web.controller.MobCsEndorseITAction" id="PA_mobCsEndorseITAction" scope="prototype">
		<property name="csEndorseITUCC" ref="PA_csEndorseITUCC"/>
		<property name="csEndorseCTUCC" ref="PA_csEndorseCTUCC"/>
	</bean>
		<!-- 保单质押第三方止付 -->
	<bean class="com.nci.tunan.mobcss.web.controller.MobCsEndorseCSAction" id="PA_mobCsEndorseCSAction" scope="prototype">
		<property name="csEndorseCSUCC" ref="PA_csEndorseCSUCC"/>
	</bean>
		<!-- 保单质押第三方解付 
	<bean class="com.nci.tunan.mobcss.web.controller.MobCsEndorseCPAction" id="PA_mobCsEndorseCPAction" scope="prototype">
		<property name="csEndorseCPUCC" ref="PA_csEndorseCPUCC"/>
	</bean>
-->		<!-- 公司解约 -->
	<bean class="com.nci.tunan.mobcss.web.controller.MobCsEndorseEAAction" id="PA_mobCsEndorseEAAction" scope="prototype">
		 <property name="csEndorseCTUCC" ref="PA_csEndorseCTUCC"/>
		 <property name="csEndorseEAUCC" ref="PA_csEndorseEAUCC"/>
	</bean>
	
		<!-- 续保险种转换 -->
	<bean class="com.nci.tunan.mobcss.web.controller.MobCsEndorseRRAction" id="PA_mobCsEndorseRRAction" scope="prototype">
		<property name="csEndorseRRUccImpl" ref="PA_csEndorseRRUccImpl"/>
	</bean>
	<!--基本信息备注项变更 -->
	<bean class="com.nci.tunan.mobcss.web.controller.MobCsEndorseMCAction" id="PA_mobCsEndorseMCAction" scope="prototype">
		<property name="csEndorseMCUCC" ref="csEndorseMCUCC"/>
	</bean>
	
	<!-- 万能险、投连险部分领取 -->
	<bean class="com.nci.tunan.mobcss.web.controller.MobCsEndorsePGAction" id="PA_mobCsEndorsePGAction" scope="prototype">
		<property name="csEndorsePGUCC" ref="PA_csEndorsePGUCC"/>
	</bean>
	
	<!-- 保单复缴 -->
	<bean class="com.nci.tunan.mobcss.web.controller.MobCsEndorseRAAction" id="PA_mobCsEndorseRAAction" scope="prototype">
		<property name="csEndorseRAUCC" ref="PA_csEndorseRAUCC"/>
		<property name="csEndorseAMUCC" ref="PA_csEndorseAMUCC"/>
	</bean>
	<!-- 协议退保 -->
	<bean class="com.nci.tunan.mobcss.web.controller.MobCsEndorseXTAction" id="PA_mobCsEndorseXTAction" scope="prototype">
		<property name="csEndorseCTUCC" ref="PA_csEndorseCTUCC"/>
	</bean>
	<!-- 预填单 -->
	<bean class="com.nci.tunan.mobcss.web.controller.MobPrefillBillAction" id="PA_prefillBillPasAction" scope="prototype">
		<property name="prefillBillPasUCC" ref="PA_prefillBillPasUCC"/>
	</bean>
	<bean class="com.nci.tunan.mobcss.web.controller.MobPrefillBillCCEntryAction" id="PA_prefillBillCCEntryAction" scope="prototype">
		<property name="prefillBillCCEntryUCC" ref="PA_prefillBillCCEntryUCC"/>
		<property name="districtUCC" ref="PA_districtUCC"/>
	</bean>
	<bean class="com.nci.tunan.mobcss.web.controller.MobPrefillBillBCEntryAction" id="PA_prefillBillBCEntryAction" scope="prototype">
		<property name="prefillBillBCEntryUCC" ref="PA_prefillBillBCEntryUCC"/>
		<property name="districtUCC" ref="PA_districtUCC"/>
	</bean>
	<bean class="com.nci.tunan.mobcss.web.controller.MobPrefillBillPCEntryAction" id="PA_prefillBillPCEntryAction" scope="prototype">
		<property name="prefillBillPCEntryUCC" ref="PA_prefillBillPCEntryUCC"/>
	</bean>
    <!-- 发起回访 -->
    <bean class="com.nci.tunan.mobcss.web.controller.MobBizCallClmAction" id="PA_mobBizCallClmAction" scope="prototype">
        <property name="cusApplicationUCC" ref="PA_cusApplicationUCC"/>
    </bean> 
    <!-- 生存调查-->
    <bean class="com.nci.tunan.mobcss.web.controller.MobCsSurvivalSurveyAction" id="PA_mobCsSurvivalSurveyAction" scope="prototype">
        <property name="csSurvivalSurveyUCC" ref="PA_csSurvivalSurveyUCC"/>
        <property name="repealSurveyUCC" ref="PA_repealSurveyUCC"/>
    </bean>
</beans>