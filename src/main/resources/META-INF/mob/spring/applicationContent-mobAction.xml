<?xml version="1.0" encoding="UTF-8"?><beans xmlns="http://www.springframework.org/schema/beans" xmlns:batch="http://www.springframework.org/schema/batch" xmlns:c="http://www.springframework.org/schema/c" xmlns:cache="http://www.springframework.org/schema/cache" xmlns:context="http://www.springframework.org/schema/context" xmlns:p="http://www.springframework.org/schema/p" xmlns:task="http://www.springframework.org/schema/task" xmlns:tx="http://www.springframework.org/schema/tx" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.springframework.org/schema/task http://www.springframework.org/schema/task/spring-task-3.0.xsd   http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd   http://www.springframework.org/schema/batch http://www.springframework.org/schema/batch/spring-batch-2.1.xsd   http://www.springframework.org/schema/cache http://www.springframework.org/schema/cache/spring-cache-3.1.xsd   http://www.springframework.org/schema/tx http://www.springframework.org/schema/tx/spring-tx-3.0.xsd   http://www.springframework.org/schema/context http://www.springframework.org/schema/context/spring-context-3.0.xsd">

	<!-- 保全受理 -->
	<bean class="com.nci.tunan.mobcss.web.controller.MobAcceptAction" id="PA_mobAcceptAction" scope="prototype">
		<property name="cusApplicationUCC" ref="PA_cusApplicationUCC"/>
		<property name="cusCustomerUCC" ref="PA_cusCustomerUCC"/>
		<property name="cusAcceptUCC" ref="PA_cusAcceptUCC"/>
		<property name="csServiceOtherCfgUCC" ref="PA_csServiceOtherCfgUCC"/>
		<property name="csSurvivalSurveyUCC" ref="PA_csSurvivalSurveyUCC"/>
		<property name="bizTurnUCC" ref="bizTurnUCC"/>
		<property name="bizSignUCC" ref="bizSignUCC"/>
		<property name="bizSignAttachmentUCC" ref="bizSignAttachmentUCC"/>
		<property name="bizCallUCC" ref="bizCallUCC"/>
		<property name="newCusAcceptUCC" ref="PA_newCusAcceptUCCImpl" />
<!-- 		<property name="bankCCChangeService" ref="PA_bankCCChangeService"/> -->
	</bean>

	<!-- liuxingle 客户职业类别变更 -->
	<bean class="com.nci.tunan.mobcss.web.controller.MobCsEndorseIOAction" id="PA_mobCustJobCateGoryUpdateAction" scope="prototype">
		<property name="cateGoryUpdateUCC" ref="PA_cateGoryUpdateUCC"/>
		<property name="cusApplicationUCC" ref="PA_cusApplicationUCC"/>
	</bean>

	<bean class="com.nci.tunan.mobcss.web.controller.MobUploadSave" id="PA_uploadSave" scope="prototype">
	</bean>
</beans>