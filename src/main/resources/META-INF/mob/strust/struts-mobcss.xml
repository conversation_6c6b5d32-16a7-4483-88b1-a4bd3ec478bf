<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE struts PUBLIC "-//Apache Software Foundation//DTD Struts Configuration 2.3//EN" "http://struts.apache.org/dtds/struts-2.3.dtd">
<struts>
	<package name="struts-mobaccept" extends="default" namespace="/mob/csAccept">
		<action name="*_*" method="{1}" class="{2}">
			<!-- 保全受理 -->
			<result name="step1AcceptInfoEntry">/mobcss/pages/bqshouli/step1AcceptInfoEntry.jsp</result>
			<result name="step2AcceptInfoEntry">/mobcss/pages/bqshouli/step2AcceptInfoEntry.jsp</result>
			<result name="step3AcceptInfoEntry">/mobcss/pages/bqshouli/step3AcceptInfoEntry.jsp</result>
			<result name="csAppDocInfo">/mobcss/pages/bqshouli/acceptEntry_appDoc.jsp</result>
			<result name="acceptComplete">/mobcss/pages/bqshouli/acceptComplete.jsp</result>
			<result name="mobShowAcceptAndArapInfo">/mobcss/pages/bqshouli/acceptModify.jsp</result>
			<result name="mobEntryFinish">/mobcss/pages/bqshouli/mobFinish.jsp</result>
			<result name="loadBankAccountInfoPremArap">/mobcss/pages/bqshouli/customerPremAccount.jsp</result>
			<result name="loadBankAccountInfo">/mobcss/pages/bqshouli/bankAccountInfo.jsp</result>
			 
			<result name="mobShowInformDesc">/mobcss/pages/bqshouli/mobSurvey_desc.jsp</result>
			 <!-- 新增附件险更新后页面 -->
		    <result name = "loadMobAddSubBusiProdNewPage">/mobcss/pages/bqshouli/loadNSPage_new.jsp</result>
			<!-- 保全项加载 -->
			<result name="mobLoadCustBaseInfoChgPage">/mobcss/pages/bqshouli/loadCCPage.jsp</result> 
			<result name="loadAGPage">/mobcss/pages/bqshouli/loadAGPage.jsp</result>
			<result name="loadLRPage">/mobcss/pages/bqshouli/loadLRPage.jsp</result>
			<result name="loadPTPage">/mobcss/pages/bqshouli/loadPTPage.jsp</result>
			<result name="loadPTDetail">/mobcss/pages/bqshouli/loadPTPage_detail.jsp</result>
			<result name="loadMobPUPage">/mobcss/pages/bqshouli/loadPUPage.jsp</result>
			<result name="loadMobReduceChange">/mobcss/pages/bqshouli/loadPUPage_change.jsp</result>
			<result name="loadMobReducePainUpChange">/mobcss/pages/bqshouli/loadPUPage_new.jsp</result>
			<!-- 万能险减少页面 -->
			<result name="loadCBMainPage">/mobcss/pages/bqshouli/loadCBPage.jsp</result>
			<result name="loadCBAfterPage">/mobcss/pages/bqshouli/CsEndorseCB_after.jsp</result>
			<!-- 被保险人职业类别变更 -->		
			<result name="initIOCustJobCateGoryUpdatePage">/mobcss/pages/bqshouli/loadIOPage.jsp</result>
			<result name="findUpdateEndInfo">/mobcss/pages/bqshouli/loadIOPageUpdateEnd.jsp</result>
			<!-- 转养老金页面 -->			
			<result name="loadTAPage">/mobcss/pages/bqshouli/loadTAPage.jsp</result>
			<result name="saveCsPayPlanDue">/mobcss/pages/bqshouli/loadTAPage_saveResult.jsp</result>
			<!-- 缴费方式及期限变更-->	
			<result name="showMainPage">/mobcss/pages/bqshouli/loadFMPage.jsp</result>
			<result name="showAFPage">/mobcss/pages/bqshouli/loadFMPage_query.jsp</result>
			<!-- 保单质押贷款页面 -->
			<result name="queryPolicyLoan">/mobcss/pages/bqshouli/loadLNPageForQuery.jsp</result>
			<result name="policyLoanInit">/mobcss/pages/bqshouli/loadLNPage.jsp</result> 
			<result name="policyloadAddNew">/mobcss/pages/bqshouli/loadLNPage_after.jsp</result>
			<result name="policyloadAddNew1">/mobcss/pages/bqshouli/loadLNPage_after1.jsp</result>
			<!-- 万能险增加页面 -->
			<result name = "loadCAMainPage">/mobcss/pages/bqshouli/loadCAPage.jsp</result>
		    <result name = "loadCAAfterPage">/mobcss/pages/bqshouli/loadCAPage_after.jsp</result>
			<!-- 保单贷款清偿-->
		 	<result name="policyLoanPayOff">/mobcss/pages/bqshouli/loadRFPage.jsp</result> 
			<result name="policyLoanPayOff1">/mobcss/pages/bqshouli/loadRFPage1.jsp</result> 
			<result name="policyLoanPayOff2">/mobcss/pages/bqshouli/loadRFPage2.jsp</result> 
			<result name="loadPolicyLoanPayOff">/mobcss/pages/bqshouli/loadRFPage_after.jsp</result>
			<!-- 满期不续保申请-->
			<result name="loadENPage">/mobcss/pages/bqshouli/loadENPage.jsp</result>
			<result name="loadENPageafter">/mobcss/pages/bqshouli/loadENPage_after.jsp</result>
			<!--客户层交易密码设置/修改 -->
			<result name="initCsCustomerPasswordResult" >/mobcss/pages/bqshouli/loadFKPage.jsp</result>
			<result name="queryCsCustomerPasswordResult">/mobcss/pages/bqshouli/loadFKPage_query.jsp</result>
			<!--增补告知-->	
			<result name="loadAddNofity">/mobcss/pages/bqshouli/loadHIPage.jsp</result>
			<!--保单冻结解冻-->	
			<result name="showFreezePolicyPage">/mobcss/pages/bqshouli/loadCFPage.jsp</result>			
		    <result name="showUnfreezePolicyPage">/mobcss/pages/bqshouli/loadCWPage.jsp</result>
		    <!--主险续保-->
			<result name="loadMrMainProdRenewalPage">/mobcss/pages/bqshouli/loadMRPage.jsp</result>
			<!-- 保单挂失 -->
			<result name="showPolicyLossPage">/mobcss/pages/bqshouli/loadPLPage.jsp</result>
			<!-- 保单解挂 -->
			<result name="showUnlockUpPage">/mobcss/pages/bqshouli/loadPFPage.jsp</result>
			<!-- 保险起期变更 -->
			<result name="showPStDateChgPage">/mobcss/pages/bqshouli/loadYSPage.jsp</result>
			<result name="showPStDateChgAFPage">/mobcss/pages/bqshouli/loadYSPage_after.jsp</result>
			
			<result name="loadpaymentInfoChg">/mobcss/pages/bqshouli/loadPCPage.jsp</result>
			
			<!-- 受益人变更  start -->
			<result name="showBeneficiaryUpdatePages">/mobcss/pages/bqshouli/loadBCPage.jsp</result>
			<result name="showBeneficiaryUpdatePage">/mobcss/pages/bqshouli/mobCsEndorseBC.jsp</result>
			<result name="showBeneficiaryBaodan">/mobcss/pages/bqshouli/loadBCPage_policy.jsp</result>
			<result name="showBeneficiaryCustomer">/mobcss/pages/bqshouli/loadBCPage_customerQuery.jsp</result>
			<result name="showBeneficiaryInfo">/mobcss/pages/bqshouli/loadBCPage_busi.jsp</result>
		    <result name="showPolicyAfterChange">/mobcss/pages/bqshouli/loadBCPage_afterChange.jsp</result>
		    <result name="showBeneMessage">/mobcss/pages/bqshouli/loadBCPage_query.jsp</result>
		    <result name="beneResult">/mobcss/pages/bqshouli/mobCsPolicyBeneResult.jsp</result>
			<!-- 受益人变更  end -->
			
			<!-- 保单复效  start -->
				<result name="showPolicyRevivalMain">/mobcss/pages/bqshouli/loadREPage.jsp</result>
				<!-- 保单贷款和自垫信息 -->
				<result name="showPolicyRevivalAll">/mobcss/pages/bqshouli/loadREPage_all.jsp</result>
				<!-- 保单贷款信息 -->
				<result name="showPolicyRevivalLoan">/mobcss/pages/bqshouli/loadREPage_loan.jsp</result>
				<!-- 增补告知 -->
				<result name="showInformPage">/mobcss/pages/bqshouli/mobCsSurveyNew.jsp</result> 
				<result name="showInformDesc">/mobcss/pages/bqshouli/mobCsSurvey_desc.jsp</result>
			<!-- 保单复效  end -->
			
			<result name="loadXTPage">/mobcss/pages/bqshouli/loadCTPage.jsp</result>
			<result name="loadAddSubBusiProdPage">/mobcss/pages/bqshouli/loadNSPage.jsp</result>
			 
			<!-- 保全试算 -->
			<result name="tryInfoEntry">/mobcss/pages/bqshisuan/try1.jsp</result>
			<result name="tryAcceptAndArapInfo">/mobcss/pages/bqshisuan/inBigSave.jsp</result> 
			
			<!-- 发起生调查询列表 -->
			<result name="survivalSurveyList">/mobcss/pages/paAccept/mobCsSurivalSuryveyList.jsp</result>
			<!-- 发起调查 -->
			<result name="survivalSurveyPage">/mobcss/pages/paAccept/mobCsSurivalSuryveyPage.jsp</result>
			<!-- 发起回访 -->
			<result name="toBizCallClmPage">/mobcss/pages/paAccept/mobBizCallClmlPage.jsp</result>
			<result name="csBizCall">/mobcss/pages/paAccept/mobCsBizCall.jsp</result>
			<result name="otherBizCall">/mobcss/pages/paAccept/mobOtherBizCall.jsp</result>
			<!-- 发起转办 -->
			<result name="toTurnZHPhysicalPage">/mobcss/pages/paAccept/mobTurnZHPhysicalPage.jsp</result>
			<!-- 关联影像 -->
			<result name="relateImage">/mobcss/pages/paAccept/mobRelateImage.jsp</result>
			<!-- 扫描 -->
			<result name="toMobAcceptScanPage">/mobcss/pages/paAccept/mobAcceptScan.jsp</result>
			<!-- 发起签报 -->
			<result name="showInsertBizSignPage">/mobcss/pages/paAccept/showInsertBizSignPage.jsp</result>
			
			<!-- 退保 -->
			<!-- 变更前信息 -->
			<result name="mobLoadXTPage">/mobcss/pages/bqshouli/loadCTPage.jsp</result>
			<!-- 正常退保金额 -->
			<result name="mobshowNarmalPremiumDetail">/mobcss/pages/bqshouli/loadCTPage_normalPremDetail.jsp</result>
			<!-- 变更后信息 -->
			<result name="mobShowAfterSurrenderInfo">/mobcss/pages/bqshouli/loadCTPage_afterInfo.jsp</result>
			
			<!--保单迁移 -->
			<result name="loadPRPage" >/mobcss/pages/bqshouli/loadPRPage.jsp</result>
			
			<!-- 领取形式变更 -->
			<result name="mobLoadGCPage">/mobcss/pages/bqshouli/mobEndorseGC.jsp</result>
			<!-- 客户重要信息变更 -->
			<result name="loadCustImpporInfoChgPage">/mobcss/pages/bqshouli/loadLCMage.jsp</result>
			<result name="updateCustomer">/mobcss/pages/bqshouli/loadLCMage_busiItemUpdate.jsp</result>
			<result name="contractMasterNewInfo">/mobcss/pages/bqshouli/loadLCMage_ContractMasterNewInfo.jsp</result>
			<!--投保人变更 -->
			<result name="loadAEPage">/mobcss/pages/bqshouli/loadAEPage.jsp</result>
			<result name="afterLoadHolderChang">/mobcss/pages/bqshouli/loadAEPage_query.jsp</result>
			<result name="checkCustomerInfo">/mobcss/pages/bqshouli/loadAEPage_customer.jsp</result>
			<result name="checkPolicyInfo">/mobcss/pages/bqshouli/loadAEPage_checkPolicy.jsp</result>
<!-- 			<result name="customerInfo">/cs/pages/serviceitem/CsEndorseAE_customerChange.jsp</result> -->
<!-- 			<result name="csPayer">/cs/pages/serviceitem/CsEndorseAE_bank.jsp</result> -->
			<result name="loadPagePolicyHolder">/mobcss/pages/bqshouli/loadAEPage_customerChange.jsp</result>
						
			<!--生存保险金追回  -->
			<!-- 初始化 -->
			<result name="mobshowRGPage">/mobcss/pages/bqshouli/loadRGPage.jsp</result>
			<!-- 生存金追回 -->
			<!-- <result name="mobshowRGPage_af">/mobcss/pages/bqshouli/loadRGPage_after.jsp</result> -->
			
			<!-- 新增可选责任 -->
			<result name="mobLoadPage">/mobcss/pages/bqshouli/loadXDPage.jsp</result>
			 <result name = "mobloadOptionLiabilityNewPage">/mobcss/pages/bqshouli/mobCsEndorseXD_new.jsp</result>
			<!-- 被保人健康告知 -->
			<result name="mobshowInformPage">/mobcss/pages/bqshouli/mobCsSurveyXD.jsp</result>
			<result name="mobshowInformDesc">/mobcss/pages/bqshouli/mobCsSurveyXD_desc.jsp</result>
			
			<!-- 万能险基本保险金额约定变更DC -->
			<result name="mobloadDCPage">/mobcss/pages/bqshouli/loadDCPage.jsp</result>
			<result name="mobsaveDC">/mobcss/pages/bqshouli/loadDCPage_after.jsp</result>
			
			<!-- 领取方式变更 -->
			<result name="mobloadGMPage">/mobcss/pages/bqshouli/loadGMPage.jsp</result>
			<!-- 领取方式变更后 -->
			<result name="mobsaveCsPayPlan">/mobcss/pages/bqshouli/loadGMPage_after.jsp</result>
		
			<!-- 领取日期变更 -->
			<result name="mobloadLCPage">/mobcss/pages/bqshouli/loadLCPage.jsp</result>
			<!-- 领取日期变更后 -->
			<result name="mobchangeGetDate">/mobcss/pages/bqshouli/loadLCPage_after.jsp</result>
						<!-- 保全回退 -->
			<result name="loadRBPage">/mobcss/pages/bqshouli/loadRBPage.jsp</result>
			<!-- 领取年龄变更 -->
			<result name="mobloadGBPage">/mobcss/pages/bqshouli/loadGBPage.jsp</result>
			<!-- 领取年龄变更后 -->
			<result name="mobsaveGetAgeChange">/mobcss/pages/bqshouli/loadGBPage_after.jsp</result>
				
			<!-- 附加特约终止 -->
			<result name="mobloadDTPage">/mobcss/pages/bqshouli/loadDTPage.jsp</result>
			<result name="showAfPage">/mobcss/pages/bqshouli/mobCsEndorseDT_show.jsp</result>
			<result name="showBfPage">/mobcss/pages/bqshouli/mobCsEndorseDT_shows.jsp</result>
			<!-- 保单贷款续贷 -->
			<result name="mobshowCsEndorseRLPage">/mobcss/pages/bqshouli/loadRLPage.jsp</result>
			<result name="mobpolicyLoanRevivalPage">/mobcss/pages/bqshouli/loadRLPage_after.jsp</result>
			
			<!-- 增加附加特约责任 -->
			<result name="mobshowAddSubPolicyPage">/mobcss/pages/bqshouli/mobCsEndorseDA_info.jsp</result>
			<result name="mobshowBfChangePolicyPage">/mobcss/pages/bqshouli/mobCsEndorseDA_bf.jsp</result>
			<result name="showAfChangePolicyPage">/mobcss/pages/bqshouli/mobCsEndorseDA_af.jsp</result>
			
			<!-- 保费自垫申请和终止 -->
			<result name="showPremiumApplyandStopPage">/mobcss/pages/bqshouli/loadAPPage.jsp</result>
			<result name="showResult">/mobcss/pages/bqshouli/loadAPPage_result.jsp</result>
			
			<!-- 累计生息账户领取注销 -->
			<result name="showAccumulatePage">/mobcss/pages/bqshouli/loadAIPage.jsp</result>
			<!-- 显示变更后的信息 -->
			<result name="showChangeAfInfo">/mobcss/pages/bqshouli/loadAIPage_after.jsp</result>
			
			<!-- 追加保费 -->
			<result name="mobloadAMMainPage">/mobcss/pages/bqshouli/loadAMPage.jsp</result>

			<!-- 保单质押第三方解付初始化界面 -->
			<result name="loadCPPage">/mobcss/pages/bqshouli/loadCPPage.jsp</result>
			<!-- 保单质押第三方解下一步 -->
			<result name="cpItemPolicyPledgeNextStep" type="redirect">/cs/csEntry/showAcceptAndArapInfo_csEntryAction.action?changeId=${changeId}&amp;acceptId=${acceptId}&amp;customerId=${customerId}</result>
			<!-- 保单质押第三方解保存 -->
			<result name="saveCsEndorseCP" type="redirect">/cs/csEntry/saveCsEndorseCP_CsEndorseCPAction.action?changeId=${changeId}&amp;acceptId=${acceptId}&amp;customerId=${customerId}</result>

			<!-- 保单质押第三方止付初始化界面 -->
			<result name="loadCSPage">/mobcss/pages/bqshouli/loadCSPage.jsp</result>
			<!-- 保单质押第三方止付下一步 -->
			<result name="nextStep" type="redirect">/cs/csEntry/showAcceptAndArapInfo_csEntryAction.action?changeId=${changeId}&amp;acceptId=${acceptId}&amp;customerId=${customerId}</result>

			<!-- 公司解约 -->
			<result name="loadEAPage">/mobcss/pages/bqshouli/loadEAPage.jsp</result>
			<!-- 正常退保金额 -->
			<result name="showNarmalPremiumDetail">/mobcss/pages/bqshouli/loadEAPage_normalPremDetail.jsp</result>
			<!-- 变更后信息 -->
			<result name="showAfterSurrenderInfo">/mobcss/pages/bqshouli/loadEAPage_afterInfo.jsp</result>
			<!-- 续保险种转换 -->
			<result name="loadRRPage">/mobcss/pages/bqshouli/loadRRPage.jsp</result>
			<result name="rrUpdateEndShow">/mobcss/pages/bqshouli/CsEndorseRR_query.jsp</result>
			<!-- 续保险种转换 -->
						
			<!-- 关联银行卡 -->
			<result name="mobLoadCDPage">/mobcss/pages/bqshouli/loadCDPage.jsp</result>
			
			<!-- 新增领取责任 -->
			<result name="mobloadAZPage">/mobcss/pages/bqshouli/loadAZPage.jsp</result>
			<result name="mobpolicyBankMsg">/mobcss/pages/bqshouli/mobCsEndorseAZ_save.jsp</result>
			<result name="mobresChangeBankMsgPage">/mobcss/pages/bqshouli/mobCsEndorseAZ_Banksave.jsp</result>
		
			<!-- 基本信息备注项变更 -->
			<result name="mobbasicRemarkChgInfo">/mobcss/pages/bqshouli/loadMCPage.jsp</result>
			
			<!-- 万能险、投连险部分领取 -->
			<result name="mobshowPGPage">/mobcss/pages/bqshouli/loadPGPage.jsp</result>
			<result name="mobshowResult">/mobcss/pages/bqshouli/loadPGPage_after.jsp</result>
			
			<!-- 保单复缴 -->
			<result name="mobshowPolicyRestorePayPage">/mobcss/pages/bqshouli/loadRAPage.jsp</result>
			
			<!-- 协议退保 -->
			<result name="mobloadXTPage">/mobcss/pages/bqshouli/loadXTPage.jsp</result>
			<!-- 投连险账户明细 -->
			<result name="mobshowNarmalPremiumDetail">/mobcss/pages/bqshouli/loadXTPage_normal.jsp</result>
			<!-- 变更后 -->
			<result name="mobshowAfterSurrenderInfo">/mobcss/pages/bqshouli/loadXTPage_after.jsp</result>
		</action>
	</package>
</struts>