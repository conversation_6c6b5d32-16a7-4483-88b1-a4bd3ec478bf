<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE struts PUBLIC "-//Apache Software Foundation//DTD Struts Configuration 2.3//EN" "http://struts.apache.org/dtds/struts-2.3.dtd">
<struts>
	<!-- 指定truts2请求处理的后缀，多个则以英文逗号分隔，如华夏银行缩写是hxb，我们设置个性化后缀hxb且同时支持action后缀 -->
	<constant name="struts.action.extension" value="nci,action"/>
	<!-- truts2用来指定默认编码的 ,相当于用HttpRequest的setCharacterEncoding -->
	<constant name="struts.i18n.encoding" value="UTF-8" /> 
	<!--设置浏览器是否缓存静态内容，默认为TRUE  开发阶段最好关闭  ,发布时改成TRUE-->
    <constant name="struts.server.static.browserCache" value="false"/>
    <!-- Struts2配置文件后不许重启服务器，开发模式下最好开启，到生产时回复默认false-->
	<constant name="struts.configuration.xml.reload" value="false"/>
	<!-- 默认的视图主题-->
	<constant name="struts.ui.theme" value="simple"/>
	<!-- action 交给spring管理 -->
    <constant name="struts.objectFactory" value="org.apache.struts2.spring.StrutsSpringObjectFactory" />
	<constant name="struts.objectFactory.spring.autoWire" value="type" /> 
	<constant name="struts.multipart.maxSize" value="10485760" />
	<package name="default" namespace="/" extends="struts-default">
		<!-- 定义拦截器 -->
		<interceptors>
			<interceptor name="exceptionInterceptor" class="com.nci.udmp.framework.interceptor.ExceptionInterceptor" />
			<interceptor name="dealManagementInterceptor" class="com.nci.udmp.framework.interceptor.DealManagementInterceptor">
				<!-- 初始化参数，配置过滤清单 -->
				<param name="ignoreDealStr">//login,//clickLogin_loginAction,//main,//pwd/forgetPassWord_forgetPasswordEmailAction.action,//pwd/sendEmailToUDMP_forgetPasswordEmailAction,
					//pwd/modEmailPassWord_forgetPasswordEmailAction,//pwd/modEmailPassWordSubmit_forgetPasswordEmailAction.action</param>
			</interceptor>
			
			     <interceptor name="specificInterceptor" 
			    class="com.nci.udmp.framework.interceptor.SpecificInterceptor">
			    <param name="ignoreDealStr">//login,//clickLogin_loginAction,//main,//pwd/forgetPassWord_forgetPasswordEmailAction.action,//imain,//pwd/sendEmailToUDMP_forgetPasswordEmailAction,
			    	//pwd/modEmailPassWord_forgetPasswordEmailAction,//pwd/modEmailPassWordSubmit_forgetPasswordEmailAction.action</param>
		</interceptor>
			
			
			<!-- 自定义token拦截器 -->
<!-- 			<interceptor name="tokenInterceptor" class="com.nci.udmp.framework.interceptor.TokenActionInterceptor"/> 
 -->			
			<interceptor-stack name="CommonsStack">
			<!-- 	<interceptor-ref name="tokenInterceptor"/>  -->
				<interceptor-ref name="dealManagementInterceptor"/>
				   <interceptor-ref name="specificInterceptor" />
				<interceptor-ref name="defaultStack" />
				<interceptor-ref name="exceptionInterceptor" />
			</interceptor-stack>
		</interceptors>
		<!-- 默认调用拦截器 -->
		<default-interceptor-ref name="CommonsStack" />
		<global-results>
			<result name="error" type="redirect">/udmp/pages/common/error/error.jsp</result>
			<result name="login" type="redirect">/login.jsp</result>
			<result name="invalid.token" type="redirect">/udmp/pages/common/error/error.jsp</result>
		</global-results>
		<global-exception-mappings>
			<exception-mapping result="error" exception="java.lang.Exception"/>
		</global-exception-mappings>
	</package>
	
	<!-- <package name="default" namespace="/" extends="struts-default">

		<global-results>
			<result name="error">/udmp/pages/common/error/error.jsp</result>
			<result name="login">/login1.jsp</result>
		</global-results>
		<global-exception-mappings>
			<exception-mapping result="error" exception="com.nci.udmp.framework.exception.app.BizException"/>
		</global-exception-mappings>
	</package> -->
	<include file="META-INF/common/struts2/struts-highCustomerSignHigh.xml"/>
	<include file="META-INF/common/struts2/struts-quality.xml" />
	<include file="META-INF/common/struts2/struts-bizSign.xml" />
	<include file="META-INF/common/struts2/struts-bizTurn.xml" />
<!-- 	<include file="META-INF/common/struts2/struts-agentquality.xml" /> -->
<!-- 	<include file="META-INF/common/struts2/struts-customerquality.xml" /> -->
	<include file="META-INF/common/struts2/struts-commonnote.xml" />
	<include file="META-INF/common/struts2/struts-bizCall.xml" />
	<include file="META-INF/common/struts2/struts-bizblacklist.xml"/>
	<include file="META-INF/common/struts2/struts-bizorder.xml" />
	<include file="META-INF/common/struts2/struts-countryblacklist.xml"/>
	<include file="META-INF/common/struts2/struts-orgblacklist.xml"/>
	<include file="META-INF/common/struts2/struts-personalblacklist.xml"/>
	<include file="META-INF/common/struts2/struts-businessblacklist.xml"/>
	<include file="META-INF/common/struts2/struts-orgfreeze.xml"/>
	<include file="META-INF/common/struts2/struts-upload.xml"></include>
	<include file="META-INF/common/struts2/struts-checkresultdownload.xml" />
	<include file="META-INF/common/struts2/struts-identitybatch.xml" />
	<include file="META-INF/common/struts2/struts-identitycheckmain.xml" />
	<include file="META-INF/common/struts2/struts-identitymultitermcheck.xml" />
	<include file="META-INF/common/struts2/struts-identityswitch.xml" />
	<include file="META-INF/common/struts2/struts-identitytrackquery.xml"/>
	<include file="META-INF/common/struts2/struts-logoutdelaychecklist.xml" />
	<include file="META-INF/common/struts2/struts-logoutpersonnelcheck.xml"/>
	<include file="META-INF/common/struts2/struts-photocompare.xml"/>
	<include file="META-INF/common/struts2/struts-sameaddresscheck.xml"/>
	<include file="META-INF/common/struts2/struts-simpleidentity.xml"/>
	<include file="META-INF/common/struts2/struts-artiDistriTask.xml"/>
	<include file="META-INF/common/struts2/struts-artiGetTask.xml"/>
	<include file="META-INF/common/struts2/struts-bizothercallmanage.xml"/>
	<include file="META-INF/common/struts2/struts-organListTask.xml"/>
	<include file="META-INF/common/struts2/struts-commondemand.xml"/>
	<include file="META-INF/common/struts2/struts-pdsconfig.xml"/>
  <include file="META-INF/common/struts2/struts-minsaconfig.xml"/>
	<!-- add by yuanpt_wb -->
	<include file="META-INF/common/struts2/struts-customerMarger.xml" />
	<!-- add by jinll_wb -->
	<include file="META-INF/common/struts2/struts-networkconfig-config.xml"/>
    <!-- add by zhangzz_wb -->
    <include file="META-INF/common/struts2/struts-importZyAgentConfig.xml"/>
    <include file="META-INF/common/struts2/struts-agentLevelQuery.xml"/>
	<include file="META-INF/common/struts2/struts-initImportAgBancas.xml"/>
	<include file="META-INF/common/struts2/struts-common.xml"/>
<!-- 	<include file="META-INF/nb/struts2/struts-preaudit.xml" />
	<include file="META-INF/nb/struts2/struts-test.xml" />
	<include file="META-INF/nb/struts2/struts-bpo.xml" />
	<include file="META-INF/nb/struts2/struts-document.xml" />
	<include file="META-INF/nb/struts2/struts-process.xml" />
	<include file="META-INF/nb/struts2/struts-qa.xml" />
	<include file="META-INF/nb/struts2/struts-policyprint.xml" />
	<include file="META-INF/nb/struts2/struts-taskreminder.xml" />
	<include file="META-INF/nb/struts2/struts-question.xml" />
	<include file="META-INF/nb/struts2/struts-unionquery.xml" />
	<include file="META-INF/nb/struts2/struts-report.xml" />
	<include file="META-INF/nb/struts2/struts-prevcharge.xml" />
	<include file="META-INF/uw/struts2/struts-nbmanualuw.xml" />
	<include file="META-INF/uw/struts2/struts-csmanualuw.xml" />
	<include file="META-INF/uw/struts2/struts-uwriskaccorhis.xml" />
	<include file="META-INF/uw/struts2/struts-physicalHospital.xml" />
	<include file="META-INF/uw/struts2/struts-authorityDeploy.xml" />
	<include file="META-INF/uw/struts2/struts-uwAmendIndi.xml" />
	<include file="META-INF/uw/struts2/struts-physicalResult.xml" />
	<include file="META-INF/uw/struts2/struts-taskApply.xml" />
	<include file="META-INF/uw/struts2/struts-qualityCheck.xml" />
	<include file="META-INF/uw/struts2/struts-uwDemo.xml" />
	<include file="META-INF/uw/struts2/struts-taskPreservation.xml" />
	<include file="META-INF/uw/struts2/struts-remanualuw.xml" />
	<include file="META-INF/uw/struts2/struts-report.xml" /> -->
	
	<include file="META-INF/cap/struts2/struts-pointofsalesinfo.xml" /> 
	<include file="META-INF/cap/struts2/struts-banktrans.xml" /> 
	<include file="META-INF/cap/struts2/struts-billingprint.xml" /> 
	<include file="META-INF/cap/struts2/struts-common.xml" /> 
	<include file="META-INF/cap/struts2/struts-counterchagre.xml" /> 
	<include file="META-INF/cap/struts2/struts-filialebanktextfinition.xml" /> 
	<include file="META-INF/cap/struts2/struts-generalledger.xml" /> 
	<include file="META-INF/cap/struts2/struts-invoiceprint.xml" /> 
	<include file="META-INF/cap/struts2/struts-organbanktrans.xml" /> 
	<include file="META-INF/cap/struts2/struts-paymentvoucherprint.xml" /> 
	<include file="META-INF/cap/struts2/struts-chargevoucherprint.xml" /> 
	<include file="META-INF/cap/struts2/struts-pointofsalesinfo.xml" /> 
	<include file="META-INF/cap/struts2/struts-query.xml" /> 
	<include file="META-INF/cap/struts2/struts-receiptprint.xml" /> 
	<include file="META-INF/cap/struts2/struts-report.xml" /> 
	<include file="META-INF/cap/struts2/struts-socialsecurity.xml" />
		
	<include file="META-INF/clm/struts2/struts-advancepay.xml" /> 
	<include file="META-INF/clm/struts2/struts-approve.xml" /> 
	<include file="META-INF/clm/struts2/struts-audit.xml" /> 
	<include file="META-INF/clm/struts2/struts-channel.xml" /> 
	<include file="META-INF/clm/struts2/struts-claim-care.xml" /> 
	<include file="META-INF/clm/struts2/struts-claimBizSign.xml" /> 
	<include file="META-INF/clm/struts2/struts-claimBizTurn.xml" /> 
	<include file="META-INF/clm/struts2/struts-claimList.xml" /> 
	<include file="META-INF/clm/struts2/struts-claimredo.xml" /> 
	<include file="META-INF/clm/struts2/struts-claimTelService.xml" /> 
	<include file="META-INF/clm/struts2/struts-clm-care.xml" /> 
	<include file="META-INF/clm/struts2/struts-clmWorkPlatform.xml" /> 
	<include file="META-INF/clm/struts2/struts-common.xml" /> 
	<include file="META-INF/clm/struts2/struts-demo.xml" /> 
	<include file="META-INF/clm/struts2/struts-extractRecheckPlan.xml" /> 
	<include file="META-INF/clm/struts2/struts-handExtractSurvey.xml" /> 
	<include file="META-INF/clm/struts2/struts-handworkAssignTask.xml" /> 
	<include file="META-INF/clm/struts2/struts-inspect.xml" /> 
	<include file="META-INF/clm/struts2/struts-instalment.xml" /> 
	<include file="META-INF/clm/struts2/struts-invoice.xml" /> 
	<include file="META-INF/clm/struts2/struts-maintainRecheckPlan.xml" /> 
	<include file="META-INF/clm/struts2/struts-memo.xml" /> 
	<include file="META-INF/clm/struts2/struts-monitor.xml" /> 
	<include file="META-INF/clm/struts2/struts-pay.xml" /> 
	<include file="META-INF/clm/struts2/struts-paymentplan.xml" /> 
	<include file="META-INF/clm/struts2/struts-performanceTaskMonitoring.xml" /> 
	<include file="META-INF/clm/struts2/struts-register.xml" /> 
	<include file="META-INF/clm/struts2/struts-report.xml" /> 
	<include file="META-INF/clm/struts2/struts-sign.xml" /> 
	<include file="META-INF/clm/struts2/struts-clm-innerUtil.xml" />
	<include file="META-INF/clm/struts2/struts-survey.xml" /> 
	<include file="META-INF/clm/struts2/struts-taskManage.xml" /> 
	<include file="META-INF/clm/struts2/struts-uploadPolicySurvey.xml" /> 
	<include file="META-INF/clm/struts2/struts-parameter.xml" /> 
	<include file="META-INF/clm/struts2/struts-renewal.xml" /> 
	<include file="META-INF/mob/struts2/struts-mob.xml" />
	<!-- 实物单证收齐 -->
	<include file="META-INF/clm/struts2/struts-physicalDocument.xml" />
	<include file="META-INF/clm/struts2/struts-overdue.xml" />
    <include file="META-INF/clm/struts2/struts-legalPerson.xml" />
	<include file="META-INF/clm/struts2/struts-directConn.xml" />
	<include file="META-INF/clm/struts2/struts-capacityClaim.xml" />
	<!-- 历史赔案调取 -->
	<include file="META-INF/clm/struts2/struts-hiscaseRetrieval.xml" />
		<!-- 风控管理 -->
	<include file="META-INF/clm/struts2/struts-drawInspectTask.xml" />
	<include file="META-INF/pa/struts2/struts-commonQuery.xml" />
	<include file="META-INF/pa/struts2/struts-renewal.xml" />
	<include file="META-INF/pa/struts2/struts-renewCollection.xml" />
	<include file="META-INF/pa/struts2/struts-policyAccount.xml" />
	<include file="META-INF/pa/struts2/struts-investUnitPriceManage.xml" />

	<!-- fujy_wb 保全综合查询 -->
	<include file="META-INF/cs/struts2/struts-csCommonQuery.xml" />
	<!-- 183586追加保费限额计算 -->
	<include file="META-INF/cs/struts2/struts-calculateAMLimit.xml" />
	
	<include file="META-INF/cs/struts2/struts-manualitem.xml" />
	<!-- 保全struts配置 -->
	
	<!-- 保全开发支持配置文件fordevsuport -->
	<include file="META-INF/cs/struts2/struts-devSupport.xml" />


	<!-- 保全struts配置 -->
	<include file="META-INF/cs/struts2/struts-config.xml" />
	<include file="META-INF/cs/struts2/struts-common.xml" />
	<include file="META-INF/cs/struts2/struts-process.xml" />
	<include file="META-INF/cs/struts2/struts-serviceitem.xml" />
	<!-- 保全录入 -->
	<include file="META-INF/cs/struts2/struts-csEntry.xml" />
	<!-- 保全配置 -->
	<include file="META-INF/cs/struts2/struts-csConfig.xml" />
	<!-- 分红打印配置 -->
	<include file="META-INF/cs/struts2/struts-csBonusPrint.xml" />
	<!-- 保全受理 -->
	<include file="META-INF/cs/struts2/struts-accept.xml" />
	<!-- 保全复核 -->
	<include file="META-INF/cs/struts2/struts-csCheck.xml" />
	<!-- 保全任务池 -->
	<include file="META-INF/cs/struts2/struts-csTask.xml" />
	<!-- 保全撤销 -->
	<include file="META-INF/cs/struts2/struts-csCancel.xml" />
	<!-- 客户基本资料变更 -->
	<include file="META-INF/cs/struts2/struts-serviceitem-cc.xml" />
	<!-- 快捷客户基本资料变更 -->
	<include file="META-INF/cs/struts2/struts-serviceitem-cchk.xml" />
	<!-- 变更目的地国家 -->
	<include file="META-INF/cs/struts2/struts-serviceitem-md.xml" />
	<!-- 保单复效 -->
	<include file="META-INF/cs/struts2/struts-serviceitem-re.xml" />
	<!-- 基本信息备注项变更 -->
	<include file="META-INF/cs/struts2/struts-serviceitem-mc.xml" />


	<!-- 保单挂失 -->
	<include file="META-INF/cs/struts2/struts-serviceitem-pl.xml" />

	<!-- 投保人变更 -->
	<include file="META-INF/cs/struts2/struts-serviceitem-ae.xml" />
	<!-- 指定第二投保人 -->
	<include file="META-INF/cs/struts2/struts-serviceitem-sh.xml" />
	<!-- 交费信息变更 -->
	<include file="META-INF/cs/struts2/struts-serviceitem-pc.xml" />
	<!-- 受益人信息变更 -->
	<include file="META-INF/cs/struts2/struts-serviceitem-bc.xml" /> 
	<include file="META-INF/cs/struts2/struts-serviceitem-tc.xml" /> 
	
	<!-- 保全录入——客户层交易密码设置/修改 -->
	<include file="META-INF/cs/struts2/struts-serviceitem-fk.xml" />

	<!-- 保费自垫申请和终止 -->
	<include file="META-INF/cs/struts2/struts-serviceitem-ap.xml" />

	<!-- 新增附加特约 -->
	<include file="META-INF/cs/struts2/struts-serviceitem-da.xml" />
	<!-- 附加特约终止 -->
	<include file="META-INF/cs/struts2/struts-serviceitem-dt.xml" />
	<!-- 年金满期金给付 -->
	<include file="META-INF/cs/struts2/struts-serviceitem-ag.xml" />
	<!-- 万能险、投连险部分领取 -->
	<include file="META-INF/cs/struts2/struts-serviceitem-pg.xml" />
	<!-- 保单质押第三方止付 -->
	<include file="META-INF/cs/struts2/struts-serviceitem-cs.xml" />
	<!-- 保单质押第三方解付 -->
	<include file="META-INF/cs/struts2/struts-serviceitem-cp.xml" />

	<!-- 客户职业类别变更 -->
	<include file="META-INF/cs/struts2/struts-serviceitem-io.xml" />

	<!--保全 客户重要资料变更 -->
	<include file="META-INF/cs/struts2/struts-serviceitem-cm.xml" />


	<!-- 保单挂失 -->
	<include file="META-INF/cs/struts2/struts-serviceitem-pl.xml" />


	<!-- 保单解挂 -->
	<include file="META-INF/cs/struts2/struts-serviceitem-pf.xml" />

	<!--冻结保单 张颖杰 -->
	<include file="META-INF/cs/struts2/struts-serviceitem-cf.xml" />

	<!--保单解冻 张颖杰 -->
	<include file="META-INF/cs/struts2/struts-serviceitem-cw.xml"/>

	<!-- 保全 加保 -->
	<include file="META-INF/cs/struts2/struts-serviceitem-pa.xml" />

	<!-- liuxl 主险续保 -->
	<include file="META-INF/cs/struts2/struts-serviceitem-mr.xml" />

	<!-- zhulh_wb 保单迁移 -->
	<include file="META-INF/cs/struts2/struts-serviceitem-pr.xml" />


	<!-- liuxl 附加险满期不续保 -->
	<include file="META-INF/cs/struts2/struts-serviceitem-en.xml" />
	<!-- liuxl 附加险满期降低保额续保 -->
	<include file="META-INF/cs/struts2/struts-serviceitem-er.xml" />
	<!-- 领取方式变更 -->
	<include file="META-INF/cs/struts2/struts-serviceitem-gm.xml" />
	<!-- 领取领取 -->
	<include file="META-INF/cs/struts2/struts-serviceitem-gr.xml" />
	<!-- 延长宽限期-->
	<include file="META-INF/cs/struts2/struts-serviceitem-ep.xml" />
	<!-- 交费标准变更-->
	<include file="META-INF/cs/struts2/struts-serviceitem-fs.xml" />
	
	<!-- 保单贷款 -->
	<include file="META-INF/cs/struts2/struts-serviceitem-ln.xml" />
	<!-- 保单贷款清偿 -->
	<include file="META-INF/cs/struts2/struts-serviceitem-rf.xml" />




	<!-- 保全 减保 -->
	<include file="META-INF/cs/struts2/struts-serviceitem-pt.xml" />
	<!-- 保单挂失 -->
	<!-- 减额交清/险种转换 -->
	<include file="META-INF/cs/struts2/struts-serviceitem-pu.xml" />

	<!-- liuxl 续保险种转换 -->
	<include file="META-INF/cs/struts2/struts-serviceitem-rr.xml" />
	<!--特殊复效-->
	<include file="META-INF/cs/struts2/struts-serviceitem-sr.xml" />
	<!-- 新增附加险 -->
	<include file="META-INF/cs/struts2/struts-serviceitem-ns.xml" />
	<!-- 保单补发 -->
	<include file="META-INF/cs/struts2/struts-serviceitem-lr.xml" />
	<!-- 保险起期变更（个人） -->
	<include file="META-INF/cs/struts2/struts-serviceitem-ys.xml" />
	<!-- 生存保险金追回 -->
	<include file="META-INF/cs/struts2/struts-serviceitem-rg.xml" />

	<!-- 增补告知 -->
	<include file="META-INF/cs/struts2/struts-serviceitem-hi.xml" />
	<!-- 保障计划约定变更 -->
	<include file="META-INF/cs/struts2/struts-serviceitem-xx.xml" />
	<!-- 关联银行卡 -->
	<include file="META-INF/cs/struts2/struts-serviceitem-cd.xml" />


	<!-- wangsy-领取形式变更 -->
	<include file="META-INF/cs/struts2/struts-serviceitem-gc.xml" />

	<!-- 累积生息账户领取与注销 -->
	<include file="META-INF/cs/struts2/struts-serviceitem-ai.xml" />

	<!-- 新增可选责任 -->
	<include file="META-INF/cs/struts2/struts-serviceitem-xd.xml" />

	<!-- chenaq新增领取责任 -->
	<include file="META-INF/cs/struts2/struts-serviceitem-az.xml" />


	<!-- 保单贷款续贷 -->
	<include file="META-INF/cs/struts2/struts-serviceitem-rl.xml" />

	<!-- 保单自动垫交 -->
	<include file="META-INF/cs/struts2/struts-serviceitem-tr.xml" />


	<!-- wangsy-领取年龄变更 -->
	<include file="META-INF/cs/struts2/struts-serviceitem-gb.xml" />

	<!-- 交费方式及期限变更 -->
	<include file="META-INF/cs/struts2/struts-serviceitem-fm.xml" />

	<!-- 退保 -->
	<include file="META-INF/cs/struts2/struts-serviceitem-ct.xml" />
	
	<!-- 是否是试算 -->
	<include file="META-INF/cs/struts2/struts-serviceitem-trial.xml" />
	
	<!-- wangsy-领取日期变更 -->
	<include file="META-INF/cs/struts2/struts-serviceitem-lc.xml" />
	<!-- zhangyj 直付卡补付 -->
	<include file="META-INF/cs/struts2/struts-serviceitem-sp.xml" />
	<!-- dijm 保单复缴 -->
	<include file="META-INF/cs/struts2/struts-serviceitem-ra.xml" />

	<!-- chenaq-转增养老金 -->
	<include file="META-INF/cs/struts2/struts-serviceitem-ta.xml" />

	<!--chenln_wb 追加保费 AM -->
	<include file="META-INF/cs/struts2/struts-serviceitem-am.xml" />
	<!--chenln_wb 万能险 基本保额减少 -->
	<include file="META-INF/cs/struts2/struts-serviceitem-cb.xml" />
	<!-- liuxl-复效清单 -->
	<include file="META-INF/cs/struts2/struts-csDetailList-re.xml" />
	<!-- sunzh_wb 保单质押贷款清单 -->
	<include file="META-INF/cs/struts2/struts-csDetailList-pl.xml" />
	<!-- sunzh_wb 保单质押贷款应还清单 -->
	<include file="META-INF/cs/struts2/struts-csDetailList-sa.xml" />
	<!-- sunzh_wb 保单质押贷款应还清单 -->
	<include file="META-INF/cs/struts2/struts-csDetailList-lr.xml" />
	<!-- sunzh_wb 银行还款清单 -->
	<include file="META-INF/cs/struts2/struts-csDetailList-br.xml" />
	<!-- fujy 险种加保清单 -->
	<include file="META-INF/cs/struts2/struts-csDetailList-pa.xml" />
	<!-- fujy 退减保清单 -->
	<include file="META-INF/cs/struts2/struts-csDetailList-pt.xml" />
	<!-- xianf-保费自垫清单 -->
	<include file="META-INF/cs/struts2/struts-csDetailList-prs.xml" />
	<!-- xianf-保费自垫清偿清单 -->
	<include file="META-INF/cs/struts2/struts-csDetailList-psp.xml" />
	<!-- xianf-保全质检清单 -->
	<include file="META-INF/cs/struts2/struts-csDetailList-qt.xml" />
	<!-- yuzw 万能险基本保额约定变更 -->
	<include file="META-INF/cs/struts2/struts-serviceitem-dc.xml" />
	<!-- zhulh 投连险部分账户转换 -->
	<include file="META-INF/cs/struts2/struts-serviceitem-ti.xml" />
	<!-- fujy 协议退保 -->
	<include file="META-INF/cs/struts2/struts-serviceitem-xt.xml" />
	<!-- mengyanan 公司解约 -->
	<include file="META-INF/cs/struts2/struts-serviceitem-ea.xml" />

	<!--chenln_wb 万能险 基本保额增加 -->
	<include file="META-INF/cs/struts2/struts-serviceitem-ca.xml" />
	<!-- liuxl-复效清单 -->
	<!--zz 保单终止清单 -->
	<include
		file="META-INF/cs/struts2/struts-csDetailList-policyTermination.xml" />

	<!-- wangsy保全回退 -->
	<include file="META-INF/cs/struts2/struts-serviceitem-rb.xml" />
	
	<!-- guanzc预受理业务统计清单 -->
	<include file="META-INF/cs/struts2/struts-csDetailList-ad.xml"/>

	<!-- 客户重要资料变更清单 -->
	<include file="META-INF/cs/struts2/struts-csDetailList-dc.xml" />

	<!-- 特殊保全业务统计清单 -->
	<include file="META-INF/cs/struts2/struts-csDetailList-sc.xml" />

	<!-- fujy 批量退保 -->
	<include file="META-INF/cs/struts2/struts-serviceitem-bs.xml" />


	<!-- lianghxit 保单迁移清单 -->
	<include file="META-INF/cs/struts2/struts-csDetailList-pr.xml" />
	<!-- lianghxit 万能险变更清单 -->
	<include
		file="META-INF/cs/struts2/struts-csDetailList-universalAlteration.xml" />

	<!--sunlr 投连险退保 -->
	<include file="META-INF/cs/struts2/struts-serviceitem-it.xml" />

	<!-- xuyp年金满期金领取清单 -->
	<include file="META-INF/cs/struts2/struts-csDetailList-ar.xml" />
	<!-- xuyp减额交清清单 -->
	<include file="META-INF/cs/struts2/struts-csDetailList-dp.xml" />
	<!--dijm 保单失效清单 -->
	<include file="META-INF/cs/struts2/struts-csDetailList-policyInvalid.xml" />
	<!-- jingang 基本信息变更清单-客户类 -->
	<include file="META-INF/cs/struts2/struts-csDetailList-bc.xml" />
	<!-- jingang 基本信息变更清单-保单类 -->
	<include file="META-INF/cs/struts2/struts-csDetailList-bb.xml" />
	<!-- jingang 基本信息变更清单-险种类 -->
	<include file="META-INF/cs/struts2/struts-csDetailList-bp.xml" />
	<!-- jingang 保全回退清单 -->
	<include file="META-INF/cs/struts2/struts-csDetailList-rb.xml" />
	<!-- lianghxit 工作时效清单 -->
	<include file="META-INF/cs/struts2/struts-csDetailList-workEffect.xml" />
	<!-- xuyp保全扫描清单 -->
	<include file="META-INF/cs/struts2/struts-csDetailList-scan.xml" />
	<!-- xuyp保全业务明细清单 -->
	<include file="META-INF/cs/struts2/struts-csDetailList-bd.xml" />
	<!-- liuxl保全凭证打印 -->
	<include file="META-INF/cs/struts2/struts-applyvoucher.xml" />
	<!-- 保全查询 -->
	<include file="META-INF/cs/struts2/struts-csInfonationQuery.xml" />
	<!-- xianf满期不续保和降低保额续保清单 -->
	<include file="META-INF/cs/struts2/struts-csDetailList-ener.xml" />
	<!-- 上载保单后自动前置调查 -->
	<include file="META-INF/pa/struts2/struts-uploadPolicySurvey.xml" />
	<!-- 手动抽取前置调查任务 -->
	<include file="META-INF/pa/struts2/struts-handExtractSurvey.xml" />
	<!-- 维护前置调查计划 -->
	<include file="META-INF/pa/struts2/struts-claimBfSurveyPlan.xml" />
	<!-- 历史失效清单 -->
	<include file="META-INF/cs/struts2/struts-csDetailList-historyInvalidate.xml" />



	<include file="META-INF/mob/strust/struts-prefill.xml" />
	<include file="META-INF/mob/strust/struts-mobcss.xml" />
	<include file="META-INF/mob/strust/struts-mob-csSurrenderQuotApp.xml" />
	
	
	<!-- zhulh 客户认证 -->
	<include file="META-INF/cs/struts2/struts-serviceitem-rz.xml" />
	<!-- 质检差错 -->
	<include file="META-INF/cs/struts2/struts-csQuality.xml" />
	<!-- 质检计划管理 -->
	<include file="META-INF/cs/struts2/struts-csQuality-qpm.xml"></include>
	<!-- 保全事后抽检抽档 -->
	<include file="META-INF/cs/struts2/struts-csCheckPemp.xml"></include>
	<!-- 续期回退-->
	<include file="META-INF/cs/struts2/struts-serviceitem-xq.xml"></include> 
	<!--社保状态变更-->
	<include file="META-INF/cs/struts2/struts-serviceitem-so.xml"></include>
    <!--红利领取形式变更-->
	<include file="META-INF/cs/struts2/struts-serviceitem-hc.xml" />
	<!--保单关联-->
	<include file="META-INF/cs/struts2/struts-serviceitem-rs.xml"></include>
	<!--重新出单-->
	<include file="META-INF/cs/struts2/struts-serviceitem-rn.xml"></include>
	
	
	
	<!-- 保单 -->
<!-- 	<include file="META-INF/pa/struts2/struts-claimBfSurveyPlan.xml" />
	<include file="META-INF/pa/struts2/struts-commonQuery.xml" />
	<include file="META-INF/pa/struts2/struts-handExtractSurvey.xml" />
	<include file="META-INF/pa/struts2/struts-investUnitPriceManage.xml" />
	<include file="META-INF/pa/struts2/struts-renewal.xml" />
	<include file="META-INF/pa/struts2/struts-renewCollection.xml" />
	<include file="META-INF/pa/struts2/struts-reportQuery.xml" />
	<include file="META-INF/pa/struts2/struts-uploadPolicySurvey.xml" /> -->
	
	<!-- 保全 -->
	
<!-- 	<include file="META-INF/cs/struts2/old/struts-demo.xml" />
	<include file="META-INF/cs/struts2/old/struts-drawPartOfAccount.xml" />
	<include file="META-INF/cs/struts2/old/struts-effect.xml" />
	<include file="META-INF/cs/struts2/old/struts-enter.xml" />
	<include file="META-INF/cs/struts2/old/struts-serviceitem-ct.xml" />
	<include file="META-INF/cs/struts2/old/struts-surrender.xml" />
	<include file="META-INF/cs/struts2/old/struts-test.xml" />
	<include file="META-INF/cs/struts2/struts-accept.xml" />
	<include file="META-INF/cs/struts2/struts-applyvoucher.xml" />
	<include file="META-INF/cs/struts2/struts-common.xml" />
	<include file="META-INF/cs/struts2/struts-config.xml" />
	<include file="META-INF/cs/struts2/struts-csCancel.xml" />
	<include file="META-INF/cs/struts2/struts-csCheck.xml" />
	<include file="META-INF/cs/struts2/struts-csCheckPemp.xml" />
	<include file="META-INF/cs/struts2/struts-csCommonQuery.xml" />
	<include file="META-INF/cs/struts2/struts-csConfig.xml" />
	<include file="META-INF/cs/struts2/struts-csDetailList-ad.xml" />
	<include file="META-INF/cs/struts2/struts-csDetailList-ar.xml" />
	
	<include file="META-INF/cs/struts2/struts-csDetailList-bb.xml" />
	<include file="META-INF/cs/struts2/struts-csDetailList-bc.xml" />
	<include file="META-INF/cs/struts2/struts-csDetailList-bd.xml" />
	<include file="META-INF/cs/struts2/struts-csDetailList-bp.xml" />
	<include file="META-INF/cs/struts2/struts-csDetailList-br.xml" />
	<include file="META-INF/cs/struts2/struts-csDetailList-dc.xml" />
	<include file="META-INF/cs/struts2/struts-csDetailList-dp.xml" />
	<include file="META-INF/cs/struts2/struts-csDetailList-ener.xml" />
	<include file="META-INF/cs/struts2/struts-csDetailList-historyInvalidate.xml" />
	<include file="META-INF/cs/struts2/struts-csDetailList-lr.xml" />
	<include file="META-INF/cs/struts2/struts-csDetailList-pa.xml" />
	<include file="META-INF/cs/struts2/struts-csDetailList-pl.xml" />
	<include file="META-INF/cs/struts2/struts-csDetailList-policyInvalid.xml" />
	<include file="META-INF/cs/struts2/struts-csDetailList-policyTermination.xml" />
	
	<include file="META-INF/cs/struts2/struts-csDetailList-pr.xml" />
	<include file="META-INF/cs/struts2/struts-csDetailList-prs.xml" />
	<include file="META-INF/cs/struts2/struts-csDetailList-psp.xml" />
	<include file="META-INF/cs/struts2/struts-csDetailList-pt.xml" />
	<include file="META-INF/cs/struts2/struts-csDetailList-qt.xml" />
	<include file="META-INF/cs/struts2/struts-csDetailList-rb.xml" />
	<include file="META-INF/cs/struts2/struts-csDetailList-re.xml" />
	<include file="META-INF/cs/struts2/struts-csDetailList-sa.xml" />
	
	
    <include file="META-INF/cs/struts2/struts-csDetailList-sc.xml" />
    <include file="META-INF/cs/struts2/struts-csDetailList-scan.xmll" />
    <include file="META-INF/cs/struts2/struts-csDetailList-universalAlteration.xml" />
    <include file="META-INF/cs/struts2/struts-csDetailList-workEffect.xml" />
    <include file="META-INF/cs/struts2/struts-csDetailList-xh.xml" />
    <include file="META-INF/cs/struts2/struts-csEntry.xml" />
    <include file="META-INF/cs/struts2/struts-csInfonationQuery.xml" />
    <include file="META-INF/cs/struts2/struts-csQuality-qpm.xml" />
    <include file="META-INF/cs/struts2/struts-csQuality.xml" />
    <include file="META-INF/cs/struts2/struts-csTask.xml" />
    <include file="META-INF/cs/struts2/struts-manualitem.xml" />
    <include file="META-INF/cs/struts2/struts-process.xml" />
    <include file="META-INF/cs/struts2/struts-serviceitem-ae.xml" />
    <include file="META-INF/cs/struts2/struts-serviceitem-ag.xml" />
    <include file="META-INF/cs/struts2/struts-serviceitem-ai.xml" />
    <include file="META-INF/cs/struts2/struts-serviceitem-am.xml" />
    <include file="META-INF/cs/struts2/struts-serviceitem-ap.xml" />
    <include file="META-INF/cs/struts2/struts-serviceitem-az.xml" />
    <include file="META-INF/cs/struts2/struts-serviceitem-bc.xml" />
    <include file="META-INF/cs/struts2/struts-serviceitem-bs.xml" />
    <include file="META-INF/cs/struts2/struts-serviceitem-ca.xml" />
    <include file="META-INF/cs/struts2/struts-serviceitem-cb.xml" />
    <include file="META-INF/cs/struts2/struts-serviceitem-cc.xml" />
    <include file="META-INF/cs/struts2/struts-serviceitem-cd.xml" />
    <include file="META-INF/cs/struts2/struts-serviceitem-cf.xml" />
    <include file="META-INF/cs/struts2/struts-serviceitem-cm.xml" />
    <include file="META-INF/cs/struts2/struts-serviceitem-cp.xml" />
    <include file="META-INF/cs/struts2/struts-serviceitem-cs.xml" />
    
    <include file="META-INF/cs/struts2/struts-serviceitem-ct.xml" />
    <include file="META-INF/cs/struts2/struts-serviceitem-da.xml" />
    <include file="META-INF/cs/struts2/struts-serviceitem-dc.xml" />
    <include file="META-INF/cs/struts2/struts-serviceitem-dt.xml" />
    <include file="META-INF/cs/struts2/struts-serviceitem-ea.xml" />
    <include file="META-INF/cs/struts2/struts-serviceitem-en.xml" />
    <include file="META-INF/cs/struts2/struts-serviceitem-er.xml" />
    <include file="META-INF/cs/struts2/struts-serviceitem-fk.xml" />
    <include file="META-INF/cs/struts2/struts-serviceitem-fm.xml" />
    <include file="META-INF/cs/struts2/struts-serviceitem-gb.xml" />
    <include file="META-INF/cs/struts2/struts-serviceitem-gc.xml" />
    <include file="META-INF/cs/struts2/struts-serviceitem-gm.xml" />
    <include file="META-INF/cs/struts2/struts-serviceitem-hi.xml" />
    <include file="META-INF/cs/struts2/struts-serviceitem-io.xml" />
    <include file="META-INF/cs/struts2/struts-serviceitem-it.xml" />
    <include file="META-INF/cs/struts2/struts-serviceitem-ic.xml" />
   
    
    <include file="META-INF/cs/struts2/struts-serviceitem-in.xml" />
    <include file="META-INF/cs/struts2/struts-serviceitem-lr.xml" />
    <include file="META-INF/cs/struts2/struts-serviceitem-mc.xml" />
    <include file="META-INF/cs/struts2/struts-serviceitem-md.xml" />
    <include file="META-INF/cs/struts2/struts-serviceitem-mr.xml" />
    <include file="META-INF/cs/struts2/struts-serviceitem-ns.xml" />
    <include file="META-INF/cs/struts2/struts-serviceitem-pa.xml" />
    <include file="META-INF/cs/struts2/struts-serviceitem-pc.xml" />
    <include file="META-INF/cs/struts2/struts-serviceitem-pf.xml" />
    <include file="META-INF/cs/struts2/struts-serviceitem-pg.xml" />
    <include file="META-INF/cs/struts2/struts-serviceitem-pl.xml" />
    <include file="META-INF/cs/struts2/struts-serviceitem-pr.xml" />
    <include file="META-INF/cs/struts2/struts-serviceitem-pt.xml" />
    <include file="META-INF/cs/struts2/struts-serviceitem-pu.xml" />
    <include file="META-INF/cs/struts2/struts-serviceitem-ra.xml" />
    <include file="META-INF/cs/struts2/struts-serviceitem-rb.xml" />
    
    <include file="META-INF/cs/struts2/struts-serviceitem-re.xml" />
    <include file="META-INF/cs/struts2/struts-serviceitem-rf.xml" />
    <include file="META-INF/cs/struts2/struts-serviceitem-rg.xml" />
    <include file="META-INF/cs/struts2/struts-serviceitem-rl.xml" />
    <include file="META-INF/cs/struts2/struts-serviceitem-rr.xml" />
    <include file="META-INF/cs/struts2/struts-serviceitem-rz.xml" />
    <include file="META-INF/cs/struts2/struts-serviceitem-sp.xml" />
    <include file="META-INF/cs/struts2/struts-serviceitem-ta.xml" />
    <include file="META-INF/cs/struts2/struts-serviceitem-ti.xml" />
    <include file="META-INF/cs/struts2/struts-serviceitem-tr.xml" />
    <include file="META-INF/cs/struts2/struts-serviceitem-trial.xml" />
    <include file="META-INF/cs/struts2/struts-serviceitem-xd.xml" />
    <include file="META-INF/cs/struts2/struts-serviceitem-xq.xml" />
    <include file="META-INF/cs/struts2/struts-serviceitem-xt.xml" />
    <include file="META-INF/cs/struts2/struts-serviceitem-xx.xml" />
    <include file="META-INF/cs/struts2/struts-serviceitem-ys.xml" />
    <include file="META-INF/cs/struts2/struts-serviceitem.xml" />
        
    <include file="META-INF/mob/strust/struts-mob-csSurrenderQuotApp.xml" />
    <include file="META-INF/mob/strust/struts-mobcss.xml" />
    <include file="META-INF/mob/strust/struts-prefill.xml" />
      -->
	<!-- 保全通知书打印 -->
	<include file="META-INF/cs/struts2/struts-csDocument.xml"></include> 
	<include file="META-INF/cs/struts2/struts-csNote.xml"></include> 	
	<!-- zhulh -->
	<include file="META-INF/cs/struts2/struts-csPriorScan.xml"></include> 
	<!-- 柜面退保劝阻 -->
	<include file="META-INF/cs/struts2/struts-preventsurrend.xml"></include>
	<!-- 柜面签收单打印-->
	<include file="META-INF/cs/struts2/struts-cssprint.xml"></include>
	<!-- 柜外清配置 -->
	<include file="META-INF/css/struts2/struts-panelQut.xml"></include>
	<!-- 客户信息验真阀值 -->
	<include file="META-INF/cs/struts2/struts-checkthreshlod.xml"></include>
	<!-- 柜面预填单-->
	<include file="META-INF/css/struts2/struts-cssPreFilled.xml"></include>
	<!-- 续保状态变更 -->
	<include file="META-INF/cs/struts2/struts-serviceitem-es.xml"></include>
	 <!-- 保全运营视图 -->  
	<include file="META-INF/cs/struts2/struts-csOperationsQuery.xml"></include>
	<!-- songdf_wb 客户电话变更批量后台导入（河南） -->
	<include file="META-INF/cs/struts2/struts-serviceitem-ip.xml" />
	<!-- 保全资料回收 -->
	<include file="META-INF/cs/struts2/struts-serviceitem-dr.xml" />
	<!-- 系统账户-->
	<include file="META-INF/cs/struts2/struts-csUser.xml" />
	<!-- BOX账户 -->
	<include file="META-INF/pa/struts2/struts-box.xml" /> 
	<!-- 保全双录管理 -->
	<include file="META-INF/cs/struts2/struts-csDR.xml" />
		
	<!-- uc险种变更 -->
	<include file="./META-INF/cs/struts2/struts-serviceitem-uc.xml" />

	<include file="META-INF/cs/struts2/struts-SZYB.xml" />
	<!-- 万能险结算 -->
	<include file="META-INF/cs/struts2/struts-univerRepList.xml" />
	<!-- #REM 85577继续率导入界面 -->
	<include file="META-INF/common/struts2/struts-continueImport.xml"/>
	<!-- 销售限额配置 -->
	<include file="META-INF/pa/struts2/struts-salesAmountAccCfg.xml" />
	<!--RM89507 关于保单补发打印外包——基础搭建优化需求-->
	<include file="META-INF/cs/struts2/struts-csPolicyPrint.xml"></include>
	<!-- 核验开关配置 -->
	<include file="META-INF/common/struts2/struts-CheckSwitch.xml"/>
	<!-- 重疾险险种风险级别设置 -->
	<include file="META-INF/pa/struts2/struts-riskScoreConfig.xml" />
	<!-- 黑名单意思匹配查询 -->
	<include file="META-INF/common/struts2/struts-blacklistmatch.xml"/>
	<!-- 分红险红利通知书 -->
	<include file="META-INF/cs/struts2/struts-bonusDocList.xml" />
	<!-- 信托反洗钱 -->
	<include file="META-INF/cs/struts2/struts-TrustContractMaintenanceQuery.xml" />
	<!-- #133983 厦门分公司客户新市民身份识别需求 -->
	<include file="META-INF/common/struts2/struts-customerAuth.xml"/>
	<include file="META-INF/common/struts2/struts-customerAttributeQry.xml"/>
	<!-- 测试工具 -->
	<include file="META-INF/common/struts2/struts-testCommonUtil.xml"/>
	<!-- AL投保人变更（信托）  -->
	<include file="./META-INF/cs/struts2/struts-serviceitem-al.xml" />
	<!-- 续期年金生存调查结果确认  -->
	<include file="META-INF/pa/struts2/struts-payConfirmList.xml" />
	<!-- 企业交费 -->
	<include file="META-INF/cs/struts2/struts-csPayCompany.xml" />
	   <!-- 需求分析任务 #147285: 睡眠保单项目需求-核心系统睡眠保单标识更新需求-->
	<include file="META-INF/pa/struts2/struts-sleepypolicy.xml" />
	<!-- 需求分析任务 #172886关于新增银代渠道分公司佣金率管理等相关功能的需求-二期-->
	<include file="META-INF/pa/struts2/struts-bankorgancommratecfg.xml" />
	<include file="META-INF/pa/struts2/struts-laratecommisionrate.xml" />
	<include file="META-INF/pa/struts2/struts-discountpremrate.xml" />
</struts>