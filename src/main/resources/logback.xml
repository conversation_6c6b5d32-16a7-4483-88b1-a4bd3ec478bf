<?xml version="1.0" encoding="UTF-8"?>

<!-- For assistance related to logback-translator or configuration -->
<!-- files in general, please contact the logback user mailing list -->
<!-- at http://www.qos.ch/mailman/listdebug/logback-user -->
<!-- -->
<!-- For professional support please see -->
<!-- http://www.qos.ch/shop/products/professionalSupport -->
<!-- -->
<configuration scan="true" scanPeriod="1 seconds">
	<appender name="stdout" class="ch.qos.logback.core.ConsoleAppender">
		<encoder>
			<pattern> %d{yyyy-M-d HH:mm:ss}|%t|%p|%X{bizSeq}|%X{transCode}|%X{systemNo}|%X{invokeSeq}|%m|%F|%L|%n
			</pattern>
		</encoder>
	</appender>

	<appender name="stdout2" class="ch.qos.logback.core.ConsoleAppender">
		<encoder>
			<pattern>%d{yyyy-M-d HH:mm:ss}|%t|%p|%m|%F|%L|%n</pattern>
		</encoder>
	</appender>

	<logger name="com.atomikos" level="warn" />
	<logger name="com.nci.udmp" level="debug" />
	
	<!-- MyBatis SQL日志配置 -->
	<logger name="com.nci.tunan.clm" level="debug" additivity="false">
		<appender-ref ref="stdout2" />
	</logger>
	<logger name="org.mybatis" level="debug" additivity="false">
		<appender-ref ref="stdout2" />
	</logger>
	<logger name="org.apache.ibatis" level="debug" additivity="false">
		<appender-ref ref="stdout2" />
	</logger>
	<logger name="java.sql" level="debug" additivity="false">
		<appender-ref ref="stdout2" />
	</logger>
	<logger name="jdbc.sqlonly" level="debug" additivity="false">
		<appender-ref ref="stdout2" />
	</logger>
	<logger name="jdbc.sqltiming" level="debug" additivity="false">
		<appender-ref ref="stdout2" />
	</logger>
	<logger name="jdbc.audit" level="debug" additivity="false">
		<appender-ref ref="stdout2" />
	</logger>
	<logger name="jdbc.resultset" level="debug" additivity="false">
		<appender-ref ref="stdout2" />
	</logger>
	<logger name="jdbc.connection" level="debug" additivity="false">
		<appender-ref ref="stdout2" />
	</logger>


<!-- ILOG interface log start -->
	<appender name="ILOG"
		class="ch.qos.logback.core.rolling.RollingFileAppender">
		<Encoding>UTF-8</Encoding>
		<rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
			<FileNamePattern>./logs/ilog/ILOG-%d{yyyy-M-d}.log
			</FileNamePattern>
			<MaxHistory>3</MaxHistory>
		</rollingPolicy>
		<encoder>
			<pattern>%d{yyyy-M-d HH:mm:ss SSS}|%t|%p|%m|%F|%L|%n</pattern>
		</encoder>
		<tiggeringPolicy
			class="ch.qos.logback.core.rolling.SizeBasedTriggeringPolicy">
			<MaxFileSize>500MB</MaxFileSize>
		</tiggeringPolicy>
	</appender>
	<!-- 异步输出 -->
	<appender name="ASYNC" class="ch.qos.logback.classic.AsyncAppender">
		<!-- 不丢失日志.默认的,如果队列的80%已满,则会丢弃TRACT、DEBUG、INFO级别的日志 -->
		<discardingThreshold>0</discardingThreshold>
		<!-- 更改默认的队列的深度,该值会影响性能.默认值为256 -->
		<queueSize>256</queueSize>
		<!-- 输出文件以及行号信息需要 stacktrace 获取 callerdata，因为性能原因 logback 的 AsyncAppender
			默认是不记录类名和行号 -->
		<includeCallerData>true</includeCallerData>
		<!-- 避免线程阻塞 -->
		<neverBlock>true</neverBlock>
		<!-- 添加附加的appender,最多只能添加一个 -->
		<appender-ref ref="ILOG" />
	</appender>
	<logger name="ILOG" level="debug">
		<appender-ref ref="ASYNC" />
	</logger>
	<!-- ILOG interface log end -->


	<!-- BPM interface log start -->
	<appender name="BPM"
		class="ch.qos.logback.core.rolling.RollingFileAppender">
		<Encoding>UTF-8</Encoding>
		<rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
			<FileNamePattern>./logs/bpm/BPM-%d{yyyy-M-d}.log</FileNamePattern>
			<MaxHistory>3</MaxHistory>
		</rollingPolicy>
		<encoder>
			<pattern>%d{yyyy-M-d HH:mm:ss SSS}|%t|%p|%m|%F|%L|%n</pattern>
		</encoder>
		<tiggeringPolicy
			class="ch.qos.logback.core.rolling.SizeBasedTriggeringPolicy">
			<MaxFileSize>500MB</MaxFileSize>
		</tiggeringPolicy>
	</appender>
	<!-- 异步输出 -->
	<appender name="ASYNC_1" class="ch.qos.logback.classic.AsyncAppender">
		<!-- 不丢失日志.默认的,如果队列的80%已满,则会丢弃TRACT、DEBUG、INFO级别的日志 -->
		<discardingThreshold>0</discardingThreshold>
		<!-- 更改默认的队列的深度,该值会影响性能.默认值为256 -->
		<queueSize>256</queueSize>
		<!-- 输出文件以及行号信息需要 stacktrace 获取 callerdata，因为性能原因 logback 的 AsyncAppender
			默认是不记录类名和行号 -->
		<includeCallerData>true</includeCallerData>
		<!-- 避免线程阻塞 -->
		<neverBlock>true</neverBlock>
		<!-- 添加附加的appender,最多只能添加一个 -->
		<appender-ref ref="BPM" />
	</appender>
	<logger name="BPM" level="debug">
		<appender-ref ref="ASYNC_1" />
	</logger>
	<!-- BPM interface log end -->


	<!-- PRD interface log start -->
	<appender name="PRD"
		class="ch.qos.logback.core.rolling.RollingFileAppender">
		<Encoding>UTF-8</Encoding>
		<rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
			<FileNamePattern>./logs/prd/PRD-%d{yyyy-M-d}.log</FileNamePattern>
			<MaxHistory>3</MaxHistory>
		</rollingPolicy>
		<encoder>
			<pattern>%d{yyyy-M-d HH:mm:ss SSS}|%t|%p|%m|%F|%L|%n</pattern>
		</encoder>
		<tiggeringPolicy
			class="ch.qos.logback.core.rolling.SizeBasedTriggeringPolicy">
			<MaxFileSize>500MB</MaxFileSize>
		</tiggeringPolicy>
	</appender>
	<!-- 异步输出 -->
	<appender name="ASYNC_2" class="ch.qos.logback.classic.AsyncAppender">
		<!-- 不丢失日志.默认的,如果队列的80%已满,则会丢弃TRACT、DEBUG、INFO级别的日志 -->
		<discardingThreshold>0</discardingThreshold>
		<!-- 更改默认的队列的深度,该值会影响性能.默认值为256 -->
		<queueSize>256</queueSize>
		<!-- 添加附加的appender,最多只能添加一个 -->
		<appender-ref ref="PRD" />
	</appender>
	<logger name="PRD" level="debug">
		<appender-ref ref="ASYNC_2" />
	</logger>
	<!-- PRD interface log end -->


	<!-- PRINT interface log start -->
	<appender name="PRINT"
		class="ch.qos.logback.core.rolling.RollingFileAppender">
		<Encoding>UTF-8</Encoding>
		<rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
			<FileNamePattern>./logs/print/PRINT-%d{yyyy-M-d}.log
			</FileNamePattern>
			<MaxHistory>3</MaxHistory>
		</rollingPolicy>
		<encoder>
			<pattern>%d{yyyy-M-d HH:mm:ss SSS}|%t|%p|%m|%F|%L|%n</pattern>
		</encoder>
		<tiggeringPolicy
			class="ch.qos.logback.core.rolling.SizeBasedTriggeringPolicy">
			<MaxFileSize>500MB</MaxFileSize>
		</tiggeringPolicy>
	</appender>
	<!-- 异步输出 -->
	<appender name="ASYNC_3" class="ch.qos.logback.classic.AsyncAppender">
		<!-- 不丢失日志.默认的,如果队列的80%已满,则会丢弃TRACT、DEBUG、INFO级别的日志 -->
		<discardingThreshold>0</discardingThreshold>
		<!-- 更改默认的队列的深度,该值会影响性能.默认值为256 -->
		<queueSize>256</queueSize>
		<includeCallerData>true</includeCallerData>
		<!-- 避免线程阻塞 -->
		<neverBlock>true</neverBlock>
		<!-- 添加附加的appender,最多只能添加一个 -->
		<appender-ref ref="PRINT" />
	</appender>
	<logger name="PRINT" level="debug">
		<appender-ref ref="ASYNC_3" />
	</logger>
	<!-- PRINT interface log end -->


	<!-- CAP interface log start -->
	<appender name="CAP"
		class="ch.qos.logback.core.rolling.RollingFileAppender">
		<Encoding>UTF-8</Encoding>
		<rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
			<FileNamePattern>./logs/cap/CAP-%d{yyyy-M-d}.log</FileNamePattern>
			<MaxHistory>3</MaxHistory>
		</rollingPolicy>
		<encoder>
			<pattern>%d{yyyy-M-d HH:mm:ss SSS}|%t|%p|%m|%F|%L|%n</pattern>
		</encoder>
		<tiggeringPolicy
			class="ch.qos.logback.core.rolling.SizeBasedTriggeringPolicy">
			<MaxFileSize>500MB</MaxFileSize>
		</tiggeringPolicy>
	</appender>
	<!-- 异步输出 -->
	<appender name="ASYNC_4" class="ch.qos.logback.classic.AsyncAppender">
		<!-- 不丢失日志.默认的,如果队列的80%已满,则会丢弃TRACT、DEBUG、INFO级别的日志 -->
		<discardingThreshold>0</discardingThreshold>
		<!-- 更改默认的队列的深度,该值会影响性能.默认值为256 -->
		<queueSize>256</queueSize>
		<!-- 输出文件以及行号信息需要 stacktrace 获取 callerdata，因为性能原因 logback 的 AsyncAppender
			默认是不记录类名和行号 -->
		<includeCallerData>true</includeCallerData>
		<!-- 避免线程阻塞 -->
		<neverBlock>true</neverBlock>
		<!-- 添加附加的appender,最多只能添加一个 -->
		<appender-ref ref="CAP" />
	</appender>
	<logger name="CAP" level="debug">
		<appender-ref ref="ASYNC_4" />
	</logger>
	<!-- CAP interface log end -->


	<!-- UDMP interface log start -->
	<appender name="UDMPINT"
		class="ch.qos.logback.core.rolling.RollingFileAppender">
		<Encoding>UTF-8</Encoding>
		<rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
			<FileNamePattern>./logs/udmpint/UDMPINT-%d{yyyy-M-d}.log
			</FileNamePattern>
			<MaxHistory>3</MaxHistory>
		</rollingPolicy>
		<encoder>
			<pattern>%d{yyyy-M-d HH:mm:ss SSS}|%t|%p|%m|%F|%L|%n</pattern>
		</encoder>
		<tiggeringPolicy
			class="ch.qos.logback.core.rolling.SizeBasedTriggeringPolicy">
			<MaxFileSize>500MB</MaxFileSize>
		</tiggeringPolicy>
	</appender>
	<!-- 异步输出 -->
	<appender name="ASYNC_5" class="ch.qos.logback.classic.AsyncAppender">
		<!-- 不丢失日志.默认的,如果队列的80%已满,则会丢弃TRACT、DEBUG、INFO级别的日志 -->
		<discardingThreshold>0</discardingThreshold>
		<!-- 更改默认的队列的深度,该值会影响性能.默认值为256 -->
		<queueSize>256</queueSize>
		<!-- 输出文件以及行号信息需要 stacktrace 获取 callerdata，因为性能原因 logback 的 AsyncAppender
			默认是不记录类名和行号 -->
		<includeCallerData>true</includeCallerData>
		<!-- 避免线程阻塞 -->
		<neverBlock>true</neverBlock>
		<!-- 添加附加的appender,最多只能添加一个 -->
		<appender-ref ref="UDMPINT" />
	</appender>
	<logger name="UDMPINT" level="debug">
		<appender-ref ref="ASYNC_5" />
	</logger>
	<!-- UDMP interface log end -->



	<!-- COUNTER interface log start -->
	<appender name="COUNTER"
		class="ch.qos.logback.core.rolling.RollingFileAppender">
		<Encoding>UTF-8</Encoding>
		<rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
			<FileNamePattern>./logs/counter/COUNTER-%d{yyyy-M-d}.log
			</FileNamePattern>
			<MaxHistory>3</MaxHistory>
		</rollingPolicy>
		<encoder>
			<pattern>%d{yyyy-M-d HH:mm:ss SSS}|%t|%p|%m|%F|%L|%n</pattern>
		</encoder>
		<tiggeringPolicy
			class="ch.qos.logback.core.rolling.SizeBasedTriggeringPolicy">
			<MaxFileSize>500MB</MaxFileSize>
		</tiggeringPolicy>
	</appender>
	<!-- 异步输出 -->
	<appender name="ASYNC_COUNTER" class="ch.qos.logback.classic.AsyncAppender">
		<!-- 不丢失日志.默认的,如果队列的80%已满,则会丢弃TRACT、DEBUG、INFO级别的日志 -->
		<discardingThreshold>0</discardingThreshold>
		<!-- 更改默认的队列的深度,该值会影响性能.默认值为256 -->
		<queueSize>256</queueSize>
		<!-- 输出文件以及行号信息需要 stacktrace 获取 callerdata，因为性能原因 logback 的 AsyncAppender
			默认是不记录类名和行号 -->
		<includeCallerData>true</includeCallerData>
		<!-- 避免线程阻塞 -->
		<neverBlock>true</neverBlock>
		<!-- 添加附加的appender,最多只能添加一个 -->
		<appender-ref ref="COUNTER" />
	</appender>
	<logger name="COUNTER" level="debug">
		<appender-ref ref="ASYNC_COUNTER" />
	</logger>
	<!-- COUNTER interface log end -->


	<!-- CS interface log start -->
	<appender name="interface"
		class="ch.qos.logback.core.rolling.RollingFileAppender">
		<Encoding>UTF-8</Encoding>
		<rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
			<FileNamePattern>./logs/interface/interface-%d{yyyy-M-d}.log
			</FileNamePattern>
			<MaxHistory>3</MaxHistory>
		</rollingPolicy>
		<encoder>
			<pattern>%d{yyyy-M-d HH:mm:ss SSS}|%t|%p|%m|%F|%L|%n</pattern>
		</encoder>
		<tiggeringPolicy
			class="ch.qos.logback.core.rolling.SizeBasedTriggeringPolicy">
			<MaxFileSize>500MB</MaxFileSize>
		</tiggeringPolicy>
	</appender>
	<!-- 异步输出 -->
	<appender name="ASYNC_interface" class="ch.qos.logback.classic.AsyncAppender">
		<!-- 不丢失日志.默认的,如果队列的80%已满,则会丢弃TRACT、DEBUG、INFO级别的日志 -->
		<discardingThreshold>0</discardingThreshold>
		<!-- 更改默认的队列的深度,该值会影响性能.默认值为256 -->
		<queueSize>256</queueSize>
		<!-- 输出文件以及行号信息需要 stacktrace 获取 callerdata，因为性能原因 logback 的 AsyncAppender
			默认是不记录类名和行号 -->
		<includeCallerData>true</includeCallerData>
		<!-- 避免线程阻塞 -->
		<neverBlock>true</neverBlock>
		<!-- 添加附加的appender,最多只能添加一个 -->
		<appender-ref ref="interface" />
	</appender>
	<logger name="interface" level="debug">
		<appender-ref ref="ASYNC_interface" />
	</logger>
	<!-- CS interface log end -->

	<!-- ######################################保全日志输出 start###################################### -->


	<!-- 保全贷款状态下生存金自动抵扣批处理 start -->
	<appender name="surDeduRLAndRFJob"
		class="ch.qos.logback.core.rolling.RollingFileAppender">
		<Encoding>UTF-8</Encoding>
		<rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
			<FileNamePattern>./logs/cus/autoSurDeduRLAndRFJob/logtest-%d{yyyy-M-d}.log
			</FileNamePattern>
			<MaxHistory>3</MaxHistory>
		</rollingPolicy>
		<encoder>
			<pattern>%d{yyyy-M-d HH:mm:ss SSS}|%t|%p|%m|%F|%L|%n
			</pattern>
		</encoder>
		<tiggeringPolicy
			class="ch.qos.logback.core.rolling.SizeBasedTriggeringPolicy">
			<MaxFileSize>500MB</MaxFileSize>
		</tiggeringPolicy>
	</appender>
	<!-- 异步输出 -->
	<appender name="ASYNC_surDeduRLAndRFJob" class="ch.qos.logback.classic.AsyncAppender">
		<!-- 不丢失日志.默认的,如果队列的80%已满,则会丢弃TRACT、DEBUG、INFO级别的日志 -->
		<discardingThreshold>0</discardingThreshold>
		<!-- 更改默认的队列的深度,该值会影响性能.默认值为256 -->
		<queueSize>256</queueSize>
		<!-- 输出文件以及行号信息需要 stacktrace 获取 callerdata，因为性能原因 logback 的 AsyncAppender 
			默认是不记录类名和行号 -->
		<includeCallerData>true</includeCallerData>
		<!-- 避免线程阻塞 -->
		<neverBlock>true</neverBlock>
		<!-- 添加附加的appender,最多只能添加一个 -->
		<appender-ref ref="surDeduRLAndRFJob" />
	</appender>
	<logger name="com.nci.tunan.cs.batch.survivalDeduRLAndRFJob"
		level="debug">
		<!-- <appender-ref ref="stdout2" /> -->
		<appender-ref ref="ASYNC_surDeduRLAndRFJob" />
	</logger>
	<!-- 保全贷款状态下生存金自动抵扣批处理 end-->


	<!-- 保全自动清偿和续贷批处理日志 -->
	<appender name="autoRFandRLJob"
		class="ch.qos.logback.core.rolling.RollingFileAppender">
		<Encoding>UTF-8</Encoding>
		<rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
			<FileNamePattern>./logs/cus/autoRFandRLJobFile/logtest-%d{yyyy-M-d}.log
			</FileNamePattern>
			<MaxHistory>3</MaxHistory>
		</rollingPolicy>
		<encoder>
			<pattern>%d{yyyy-M-d HH:mm:ss SSS}|%t|%p|%m|%F|%L|%n
			</pattern>
		</encoder>
		<tiggeringPolicy
			class="ch.qos.logback.core.rolling.SizeBasedTriggeringPolicy">
			<MaxFileSize>500MB</MaxFileSize>
		</tiggeringPolicy>
	</appender>
	<!-- 异步输出 -->
	<appender name="ASYNC_63" class="ch.qos.logback.classic.AsyncAppender">
		<!-- 不丢失日志.默认的,如果队列的80%已满,则会丢弃TRACT、DEBUG、INFO级别的日志 -->
		<discardingThreshold>0</discardingThreshold>
		<!-- 更改默认的队列的深度,该值会影响性能.默认值为256 -->
		<queueSize>256</queueSize>
		<!-- 输出文件以及行号信息需要 stacktrace 获取 callerdata，因为性能原因 logback 的 AsyncAppender
			默认是不记录类名和行号 -->
		<includeCallerData>true</includeCallerData>
		<!-- 避免线程阻塞 -->
		<neverBlock>true</neverBlock>
		<!-- 添加附加的appender,最多只能添加一个 -->
		<appender-ref ref="autoRFandRLJob" />
	</appender>
	<logger name="com.nci.tunan.cs.batch.autoRFandRL.AutoRFandRLJob"
		level="debug">
		<!-- <appender-ref ref="stdout2" /> -->
		<appender-ref ref="ASYNC_63" />
	</logger>
	<!-- 保全自动清偿和续贷批处理日志 -->

	<!-- 保全自动撤销批处理 start -->
	<appender name="csCancelLapseJob"
		class="ch.qos.logback.core.rolling.RollingFileAppender">
		<Encoding>UTF-8</Encoding>
		<rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
			<FileNamePattern>./logs/cus/csCancelLapseJob/logtest-%d{yyyy-M-d}.log
			</FileNamePattern>
			<MaxHistory>3</MaxHistory>
		</rollingPolicy>
		<encoder>
			<pattern>%d{yyyy-M-d HH:mm:ss SSS}|%t|%p|%m|%F|%L|%n</pattern>
		</encoder>
		<tiggeringPolicy
			class="ch.qos.logback.core.rolling.SizeBasedTriggeringPolicy">
			<MaxFileSize>500MB</MaxFileSize>
		</tiggeringPolicy>
	</appender>
	<!-- 异步输出 -->
	<appender name="ASYNC_78" class="ch.qos.logback.classic.AsyncAppender">
		<!-- 不丢失日志.默认的,如果队列的80%已满,则会丢弃TRACT、DEBUG、INFO级别的日志 -->
		<discardingThreshold>0</discardingThreshold>
		<!-- 更改默认的队列的深度,该值会影响性能.默认值为256 -->
		<queueSize>256</queueSize>
		<!-- 输出文件以及行号信息需要 stacktrace 获取 callerdata，因为性能原因 logback 的 AsyncAppender
			默认是不记录类名和行号 -->
		<includeCallerData>true</includeCallerData>
		<!-- 避免线程阻塞 -->
		<neverBlock>true</neverBlock>
		<!-- 添加附加的appender,最多只能添加一个 -->
		<appender-ref ref="csCancelLapseJob" />
	</appender>
	<logger name="com.nci.tunan.pa.batch.cancellapse" level="debug">
		<appender-ref ref="ASYNC_78" />
	</logger>
	<!-- 保全自动撤销批处理 end -->

	<!-- leihong noImageSendMailJob 日志记录 start -->
	<appender name="noImageSendMailJob"
		class="ch.qos.logback.core.rolling.RollingFileAppender">
		<Encoding>UTF-8</Encoding>
		<rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
			<FileNamePattern>./logs/cus/noImageSendMailJob/logtest-%d{yyyy-M-d}.log
			</FileNamePattern>
			<MaxHistory>3</MaxHistory>
		</rollingPolicy>
		<encoder>
			<pattern>%d{yyyy-M-d HH:mm:ss SSS}|%t|%p|%m|%F|%L|%n
			</pattern>
		</encoder>
		<tiggeringPolicy
			class="ch.qos.logback.core.rolling.SizeBasedTriggeringPolicy">
			<MaxFileSize>500MB</MaxFileSize>
		</tiggeringPolicy>
	</appender>
	<!-- 异步输出 -->
	<appender name="ASYNC_76" class="ch.qos.logback.classic.AsyncAppender">
		<!-- 不丢失日志.默认的,如果队列的80%已满,则会丢弃TRACT、DEBUG、INFO级别的日志 -->
		<discardingThreshold>0</discardingThreshold>
		<!-- 更改默认的队列的深度,该值会影响性能.默认值为256 -->
		<queueSize>256</queueSize>
		<!-- 输出文件以及行号信息需要 stacktrace 获取 callerdata，因为性能原因 logback 的 AsyncAppender
			默认是不记录类名和行号 -->
		<includeCallerData>true</includeCallerData>
		<!-- 避免线程阻塞 -->
		<neverBlock>true</neverBlock>
		<!-- 添加附加的appender,最多只能添加一个 -->
		<appender-ref ref="noImageSendMailJob" />
	</appender>
	<logger name="com.nci.tunan.cs.batch.noimagesendmail" level="debug">
		<appender-ref ref="ASYNC_76" />
	</logger>
	<!-- noImageSendMailJob end -->


	<!-- leihong noCheckFinishSendMailJob start -->
	<appender name="noCheckFinishSendMailJob"
		class="ch.qos.logback.core.rolling.RollingFileAppender">
		<Encoding>UTF-8</Encoding>
		<rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
			<FileNamePattern>./logs/cus/noCheckFinishSendMailJob/logtest-%d{yyyy-M-d}.log
			</FileNamePattern>
			<MaxHistory>3</MaxHistory>
		</rollingPolicy>
		<encoder>
			<pattern>%d{yyyy-M-d HH:mm:ss SSS}|%t|%p|%m|%F|%L|%n
			</pattern>
		</encoder>
		<tiggeringPolicy
			class="ch.qos.logback.core.rolling.SizeBasedTriggeringPolicy">
			<MaxFileSize>500MB</MaxFileSize>
		</tiggeringPolicy>
	</appender>
	<!-- 异步输出 -->
	<appender name="ASYNC_77" class="ch.qos.logback.classic.AsyncAppender">
		<!-- 不丢失日志.默认的,如果队列的80%已满,则会丢弃TRACT、DEBUG、INFO级别的日志 -->
		<discardingThreshold>0</discardingThreshold>
		<!-- 更改默认的队列的深度,该值会影响性能.默认值为256 -->
		<queueSize>256</queueSize>
		<!-- 输出文件以及行号信息需要 stacktrace 获取 callerdata，因为性能原因 logback 的 AsyncAppender
			默认是不记录类名和行号 -->
		<includeCallerData>true</includeCallerData>
		<!-- 避免线程阻塞 -->
		<neverBlock>true</neverBlock>
		<!-- 添加附加的appender,最多只能添加一个 -->
		<appender-ref ref="noCheckFinishSendMailJob" />
	</appender>
	<logger name="com.nci.tunan.cs.batch.nocheckfinishsendmail"
		level="debug">
		<appender-ref ref="ASYNC_77" />
	</logger>
	<!-- noCheckFinishSendMailJob end -->

	<!-- 保全自核接口记录日志 start -->
	<appender name="autoInspectInfo"
		class="ch.qos.logback.core.rolling.RollingFileAppender">
		<Encoding>UTF-8</Encoding>
		<rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
			<FileNamePattern>./logs/cus/autoInspectInfoFile/logtest-%d{yyyy-M-d}.log
			</FileNamePattern>
			<MaxHistory>3</MaxHistory>
		</rollingPolicy>
		<encoder>
			<pattern>%d{yyyy-M-d HH:mm:ss SSS}|%t|%p|%m|%F|%L|%n
			</pattern>
		</encoder>
		<tiggeringPolicy
			class="ch.qos.logback.core.rolling.SizeBasedTriggeringPolicy">
			<MaxFileSize>500MB</MaxFileSize>
		</tiggeringPolicy>
	</appender>
	<!-- 异步输出 -->
	<appender name="ASYNC_62" class="ch.qos.logback.classic.AsyncAppender">
		<!-- 不丢失日志.默认的,如果队列的80%已满,则会丢弃TRACT、DEBUG、INFO级别的日志 -->
		<discardingThreshold>0</discardingThreshold>
		<!-- 更改默认的队列的深度,该值会影响性能.默认值为256 -->
		<queueSize>256</queueSize>
		<!-- 输出文件以及行号信息需要 stacktrace 获取 callerdata，因为性能原因 logback 的 AsyncAppender
			默认是不记录类名和行号 -->
		<includeCallerData>true</includeCallerData>
		<!-- 避免线程阻塞 -->
		<neverBlock>true</neverBlock>
		<!-- 添加附加的appender,最多只能添加一个 -->
		<appender-ref ref="autoInspectInfo" />
	</appender>
	<logger name="com.nci.tunan.cs.impl.autoInspect" level="debug">
		<!-- <appender-ref ref="stdout2" /> -->
		<appender-ref ref="ASYNC_62" />
	</logger>
	<!-- 保全自核接口记录日志 end -->

	<!-- 集成接口在线保全start -->
	<appender name="CUS_OUTTER"
		class="ch.qos.logback.core.rolling.RollingFileAppender">
		<Encoding>UTF-8</Encoding>
		<rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
			<FileNamePattern>./logs/cus/cus-outter-%d{yyyy-M-d}-%i.log</FileNamePattern>
			<MaxHistory>3</MaxHistory>
			<TimeBasedFileNamingAndTriggeringPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedFNATP">
            	<MaxFileSize>500MB</MaxFileSize>
            </TimeBasedFileNamingAndTriggeringPolicy>
		</rollingPolicy>
		<encoder>
			<pattern>%d{yyyy-M-d HH:mm:ss SSS}|%t|%p|%X|%m|%n
			</pattern>
		</encoder>
		<tiggeringPolicy
			class="ch.qos.logback.core.rolling.SizeBasedTriggeringPolicy">
			<MaxFileSize>500MB</MaxFileSize>
		</tiggeringPolicy>
	</appender>
	<!-- 异步输出 -->
	<appender name="ASYNC_OUTTER" class="ch.qos.logback.classic.AsyncAppender">
		<!-- 不丢失日志.默认的,如果队列的80%已满,则会丢弃TRACT、DEBUG、INFO级别的日志 -->
		<discardingThreshold>0</discardingThreshold>
		<!-- 更改默认的队列的深度,该值会影响性能.默认值为256 -->
		<queueSize>256</queueSize>
		<!-- 输出文件以及行号信息需要 stacktrace 获取 callerdata，因为性能原因 logback 的 AsyncAppender
			默认是不记录类名和行号 -->
		<includeCallerData>true</includeCallerData>
		<!-- 避免线程阻塞 -->
		<neverBlock>true</neverBlock>
		<!-- 添加附加的appender,最多只能添加一个 -->
		<appender-ref ref="CUS_OUTTER" />
	</appender>
	<logger name="CUS_OUTTER" level="debug">
		<appender-ref ref="ASYNC_OUTTER" />
	</logger>
	<!-- 集成接口在线保全end -->


	<!-- 通知书打印日志 start -->
	<appender name="csNoticePrint"
		class="ch.qos.logback.core.rolling.RollingFileAppender">
		<Encoding>UTF-8</Encoding>
		<rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
			<FileNamePattern>./logs/cus/csNoticePrint/logtest-%d{yyyy-M-d}.log
			</FileNamePattern>
			<MaxHistory>3</MaxHistory>
		</rollingPolicy>
		<encoder>
			<pattern>%d{yyyy-M-d HH:mm:ss SSS}|%t|%p|%m|%F|%L|%n
			</pattern>
		</encoder>
		<tiggeringPolicy
			class="ch.qos.logback.core.rolling.SizeBasedTriggeringPolicy">
			<MaxFileSize>500MB</MaxFileSize>
		</tiggeringPolicy>
	</appender>
	<!-- 异步输出 -->
	<appender name="ASYNC_36" class="ch.qos.logback.classic.AsyncAppender">
		<!-- 不丢失日志.默认的,如果队列的80%已满,则会丢弃TRACT、DEBUG、INFO级别的日志 -->
		<discardingThreshold>0</discardingThreshold>
		<!-- 更改默认的队列的深度,该值会影响性能.默认值为256 -->
		<queueSize>256</queueSize>
		<!-- 输出文件以及行号信息需要 stacktrace 获取 callerdata，因为性能原因 logback 的 AsyncAppender
			默认是不记录类名和行号 -->
		<includeCallerData>true</includeCallerData>
		<!-- 避免线程阻塞 -->
		<neverBlock>true</neverBlock>
		<!-- 添加附加的appender,最多只能添加一个 -->
		<appender-ref ref="csNoticePrint" />
	</appender>

	<logger name="com.nci.tunan.cs.web.controller.manualitem" level="debug">
		<appender-ref ref="ASYNC_36" />
	</logger>
	<!-- 通知书打印日志 end -->

	<!-- 通知书打印日志 start -->
	<appender name="csNoticePrintService"
		class="ch.qos.logback.core.rolling.RollingFileAppender">
		<Encoding>UTF-8</Encoding>
		<rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
			<FileNamePattern>./logs/cus/csNoticePrintService/logtest-%d{yyyy-M-d}.log
			</FileNamePattern>
			<MaxHistory>3</MaxHistory>
		</rollingPolicy>
		<encoder>
			<pattern>%d{yyyy-M-d HH:mm:ss SSS}|%t|%p|%m|%F|%L|%n
			</pattern>
		</encoder>
		<tiggeringPolicy
			class="ch.qos.logback.core.rolling.SizeBasedTriggeringPolicy">
			<MaxFileSize>500MB</MaxFileSize>
		</tiggeringPolicy>
	</appender>
	<!-- 异步输出 -->
	<appender name="ASYNC_37" class="ch.qos.logback.classic.AsyncAppender">
		<!-- 不丢失日志.默认的,如果队列的80%已满,则会丢弃TRACT、DEBUG、INFO级别的日志 -->
		<discardingThreshold>0</discardingThreshold>
		<!-- 更改默认的队列的深度,该值会影响性能.默认值为256 -->
		<queueSize>256</queueSize>
		<!-- 输出文件以及行号信息需要 stacktrace 获取 callerdata，因为性能原因 logback 的 AsyncAppender
			默认是不记录类名和行号 -->
		<includeCallerData>true</includeCallerData>
		<!-- 避免线程阻塞 -->
		<neverBlock>true</neverBlock>
		<!-- 添加附加的appender,最多只能添加一个 -->
		<appender-ref ref="csNoticePrintService" />
	</appender>
	<logger name="com.nci.tunan.cs.impl.filedispose.service.impl"
		level="debug">
		<appender-ref ref="ASYNC_37" />
	</logger>
	<!-- 通知书打印日志 end -->


	<!-- 保全生效 start -->
	<appender name="cseffectjob"
		class="ch.qos.logback.core.rolling.RollingFileAppender">
		<Encoding>UTF-8</Encoding>
		<rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
			<FileNamePattern>./logs/cus/cseffectjob/logtest-%d{yyyy-M-d}.log
			</FileNamePattern>
			<MaxHistory>3</MaxHistory>
		</rollingPolicy>
		<encoder>
			<pattern>%d{yyyy-M-d HH:mm:ss SSS}|%p|%X|%m|%n
			</pattern>
		</encoder>
		<tiggeringPolicy
			class="ch.qos.logback.core.rolling.SizeBasedTriggeringPolicy">
			<MaxFileSize>500MB</MaxFileSize>
		</tiggeringPolicy>
	</appender>
	<!-- 异步输出 -->
	<appender name="ASYNC_cseffectjob" class="ch.qos.logback.classic.AsyncAppender">
		<!-- 不丢失日志.默认的,如果队列的80%已满,则会丢弃TRACT、DEBUG、INFO级别的日志 -->
		<discardingThreshold>0</discardingThreshold>
		<!-- 更改默认的队列的深度,该值会影响性能.默认值为256 -->
		<queueSize>256</queueSize>
		<!-- 输出文件以及行号信息需要 stacktrace 获取 callerdata，因为性能原因 logback 的 AsyncAppender
			默认是不记录类名和行号 -->
		<includeCallerData>true</includeCallerData>
		<!-- 避免线程阻塞 -->
		<neverBlock>true</neverBlock>
		<!-- 添加附加的appender,最多只能添加一个 -->
		<appender-ref ref="cseffectjob" />
	</appender>

	<logger name="com.nci.tunan.cs.impl.csEffect.service.impl.CsEffectServiceImpl" level="debug">
		<appender-ref ref="ASYNC_cseffectjob" />
	</logger>

	<logger name="com.nci.tunan.cs.batch.premArapFeedBack" level="debug">
		<appender-ref ref="ASYNC_cseffectjob" />
	</logger>
	<!--保全生效 end -->

	<!-- 保全自动撤销批处理 start -->
	<appender name="csCancelLapseJob"
		class="ch.qos.logback.core.rolling.RollingFileAppender">
		<Encoding>UTF-8</Encoding>
		<rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
			<FileNamePattern>./logs/cus/csCancelLapseJob/logtest-%d{yyyy-M-d}.log
			</FileNamePattern>
			<MaxHistory>3</MaxHistory>
		</rollingPolicy>
		<encoder>
			<pattern>%d{yyyy-M-d HH:mm:ss SSS}|%p|%X|%m|%n
			</pattern>
		</encoder>
		<tiggeringPolicy
			class="ch.qos.logback.core.rolling.SizeBasedTriggeringPolicy">
			<MaxFileSize>500MB</MaxFileSize>
		</tiggeringPolicy>
	</appender>

	<appender name="ASYNC_csCancelLapseJob" class="ch.qos.logback.classic.AsyncAppender">
		<!-- 不丢失日志.默认的,如果队列的80%已满,则会丢弃TRACT、DEBUG、INFO级别的日志 -->
		<discardingThreshold>0</discardingThreshold>
		<!-- 更改默认的队列的深度,该值会影响性能.默认值为256 -->
		<queueSize>256</queueSize>
		<!-- 输出文件以及行号信息需要 stacktrace 获取 callerdata，因为性能原因 logback 的 AsyncAppender
			默认是不记录类名和行号 -->
		<includeCallerData>true</includeCallerData>
		<!-- 避免线程阻塞 -->
		<neverBlock>true</neverBlock>
		<!-- 添加附加的appender,最多只能添加一个 -->
		<appender-ref ref="csCancelLapseJob" />
	</appender>
	<logger name="com.nci.tunan.pa.batch.cancellapse" level="debug">
		<appender-ref ref="ASYNC_csCancelLapseJob" />
	</logger>
	<!--保全自动撤销批处理  end -->


	 <!-- 保全质检批处理 start -->
	<appender name="csQualityBatch"
		class="ch.qos.logback.core.rolling.RollingFileAppender">
		<Encoding>UTF-8</Encoding>
		<rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
			<FileNamePattern>./logs/cus/csQualityBatch/logtest-%d{yyyy-M-d}.log
			</FileNamePattern>
			<MaxHistory>3</MaxHistory>
		</rollingPolicy>
		<encoder>
			<pattern>%d{yyyy-M-d HH:mm:ss SSS}|%p|%X|%m|%n
			</pattern>
		</encoder>
		<tiggeringPolicy
			class="ch.qos.logback.core.rolling.SizeBasedTriggeringPolicy">
			<MaxFileSize>500MB</MaxFileSize>
		</tiggeringPolicy>
	</appender>

	<appender name="ASYNC_csQualityBatch" class="ch.qos.logback.classic.AsyncAppender">
		<!-- 不丢失日志.默认的,如果队列的80%已满,则会丢弃TRACT、DEBUG、INFO级别的日志 -->
		<discardingThreshold>0</discardingThreshold>
		<!-- 更改默认的队列的深度,该值会影响性能.默认值为256 -->
		<queueSize>256</queueSize>
		<!-- 输出文件以及行号信息需要 stacktrace 获取 callerdata，因为性能原因 logback 的 AsyncAppender
			默认是不记录类名和行号 -->
		<includeCallerData>true</includeCallerData>
		<!-- 避免线程阻塞 -->
		<neverBlock>true</neverBlock>
		<!-- 添加附加的appender,最多只能添加一个 -->
		<appender-ref ref="csQualityBatch" />
	</appender>
	<logger name="com.nci.tunan.cs.batch.qualityPlanBatch" level="debug">
		<appender-ref ref="ASYNC_csQualityBatch" />
	</logger>
	<!--保全质检批处理  end -->



	<!-- 复核任务池 自动分配  start -->
	<appender name="CUS_autoAssignWork"
		class="ch.qos.logback.core.rolling.RollingFileAppender">
		<Encoding>UTF-8</Encoding>
		<rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
			<FileNamePattern>./logs/cus/autoAssignWork/logtest-%d{yyyy-M-d}-%i.log</FileNamePattern>
			<MaxHistory>3</MaxHistory>
			<TimeBasedFileNamingAndTriggeringPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedFNATP">
            	<MaxFileSize>800MB</MaxFileSize>
            </TimeBasedFileNamingAndTriggeringPolicy>
		</rollingPolicy>
		<encoder>
			<pattern>%d{yyyy-M-d HH:mm:ss SSS}|%t|%p|%X|%m|%n
			</pattern>
		</encoder>
		<tiggeringPolicy
			class="ch.qos.logback.core.rolling.SizeBasedTriggeringPolicy">
			<MaxFileSize>800MB</MaxFileSize>
		</tiggeringPolicy>
	</appender>
	<appender name="ASYNC_autoAssignWork" class="ch.qos.logback.classic.AsyncAppender">
		<!-- 不丢失日志.默认的,如果队列的80%已满,则会丢弃TRACT、DEBUG、INFO级别的日志 -->
		<discardingThreshold>0</discardingThreshold>
		<!-- 更改默认的队列的深度,该值会影响性能.默认值为256 -->
		<queueSize>256</queueSize>
		<!-- 输出文件以及行号信息需要 stacktrace 获取 callerdata，因为性能原因 logback 的 AsyncAppender
			默认是不记录类名和行号 -->
		<includeCallerData>true</includeCallerData>
		<!-- 避免线程阻塞 -->
		<neverBlock>true</neverBlock>
		<!-- 添加附加的appender,最多只能添加一个 -->
		<appender-ref ref="CUS_autoAssignWork" />
	</appender>

	<logger name="com.nci.tunan.cs.batch.autoassignwork" level="debug">
		<appender-ref ref="ASYNC_autoAssignWork" />
	</logger>
	<!-- 复核任务池 自动分配 end -->

	<!-- 保全自动核保通过往核保推送数据 start -->
	<appender name="autoSendDataToUWJob"
		class="ch.qos.logback.core.rolling.RollingFileAppender">
		<Encoding>UTF-8</Encoding>
		<rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
			<FileNamePattern>./logs/cus/autoSendDataToUWJobFile/logtest-%d{yyyy-M-d}.log
			</FileNamePattern>
			<MaxHistory>3</MaxHistory>
		</rollingPolicy>
		<encoder>
			<pattern>%d{yyyy-M-d HH:mm:ss SSS}|%t|%p|%m|%F|%L|%n
			</pattern>
		</encoder>
		<tiggeringPolicy
			class="ch.qos.logback.core.rolling.SizeBasedTriggeringPolicy">
			<MaxFileSize>500MB</MaxFileSize>
		</tiggeringPolicy>
	</appender>

	<appender name="ASYNC_autoSendDataToUWJob" class="ch.qos.logback.classic.AsyncAppender">
		<!-- 不丢失日志.默认的,如果队列的80%已满,则会丢弃TRACT、DEBUG、INFO级别的日志 -->
		<discardingThreshold>0</discardingThreshold>
		<!-- 更改默认的队列的深度,该值会影响性能.默认值为256 -->
		<queueSize>256</queueSize>
		<!-- 输出文件以及行号信息需要 stacktrace 获取 callerdata，因为性能原因 logback 的 AsyncAppender
			默认是不记录类名和行号 -->
		<includeCallerData>true</includeCallerData>
		<!-- 避免线程阻塞 -->
		<neverBlock>true</neverBlock>
		<!-- 添加附加的appender,最多只能添加一个 -->
		<appender-ref ref="autoSendDataToUWJob" />
	</appender>

	<logger name="com.nci.tunan.cs.batch.autosenddatetouw.AutoSendDataToUWJob" level="debug">
		<appender-ref ref="ASYNC_autoSendDataToUWJob" />
	</logger>
	<!-- 保全自动核保通过往核保推送数据 end -->

	<!-- ######################################保全日志输出 end###################################### -->


	<!-- ######################################保单日志输出 start###################################### -->

	<!-- 分红批处理日志记录 start -->
	<appender name="bonusAllocateLogFile"
		class="ch.qos.logback.core.rolling.RollingFileAppender">
		<Encoding>UTF-8</Encoding>
		<rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
			<FileNamePattern>./logstan/bonusAllocate/logtest-%d{yyyy-M-d}.log
			</FileNamePattern>
			<MaxHistory>3</MaxHistory>
		</rollingPolicy>
		<encoder>
			<pattern>%d{yyyy-M-d HH:mm:ss SSS}|%t|%p|%m|%F|%L|%n
			</pattern>
		</encoder>
		<tiggeringPolicy
			class="ch.qos.logback.core.rolling.SizeBasedTriggeringPolicy">
			<MaxFileSize>500MB</MaxFileSize>
		</tiggeringPolicy>
	</appender>
	<!-- 异步输出 -->
	<appender name="ASYNC_bonusAllocateLogFile" class="ch.qos.logback.classic.AsyncAppender">
		<!-- 不丢失日志.默认的,如果队列的80%已满,则会丢弃TRACT、DEBUG、INFO级别的日志 -->
		<discardingThreshold>0</discardingThreshold>
		<!-- 更改默认的队列的深度,该值会影响性能.默认值为256 -->
		<queueSize>256</queueSize>
		<!-- 输出文件以及行号信息需要 stacktrace 获取 callerdata，因为性能原因 logback 的 AsyncAppender
			默认是不记录类名和行号 -->
		<includeCallerData>true</includeCallerData>
		<!-- 避免线程阻塞 -->
		<neverBlock>true</neverBlock>
		<!-- 添加附加的appender,最多只能添加一个 -->
		<appender-ref ref="bonusAllocateLogFile" />
	</appender>
	<logger name="com.nci.tunan.pa.batch.allocation" level="debug">
		<!-- <appender-ref ref="stdout2"/> -->
		<appender-ref ref="ASYNC_bonusAllocateLogFile" />
	</logger>
	<!-- 分红批处理日志记录 end -->


	<!-- 银保通对账功能日志记录 start -->
	<appender name="ybtdzLogFile"
		class="ch.qos.logback.core.rolling.RollingFileAppender">
		<Encoding>UTF-8</Encoding>
		<rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
			<FileNamePattern>./logstan/ybtdzLogFile/logtest-%d{yyyy-M-d}.log
			</FileNamePattern>
			<MaxHistory>3</MaxHistory>
		</rollingPolicy>
		<encoder>
			<pattern>%d{yyyy-M-d HH:mm:ss SSS}|%t|%p|%m|%F|%L|%n
			</pattern>
		</encoder>
		<tiggeringPolicy
			class="ch.qos.logback.core.rolling.SizeBasedTriggeringPolicy">
			<MaxFileSize>500MB</MaxFileSize>
		</tiggeringPolicy>
	</appender>
	<!-- 异步输出 -->
	<appender name="ASYNC_6" class="ch.qos.logback.classic.AsyncAppender">
		<!-- 不丢失日志.默认的,如果队列的80%已满,则会丢弃TRACT、DEBUG、INFO级别的日志 -->
		<discardingThreshold>0</discardingThreshold>
		<!-- 更改默认的队列的深度,该值会影响性能.默认值为256 -->
		<queueSize>256</queueSize>
		<!-- 输出文件以及行号信息需要 stacktrace 获取 callerdata，因为性能原因 logback 的 AsyncAppender
			默认是不记录类名和行号 -->
		<includeCallerData>true</includeCallerData>
		<!-- 避免线程阻塞 -->
		<neverBlock>true</neverBlock>
		<!-- 添加附加的appender,最多只能添加一个 -->
		<appender-ref ref="ybtdzLogFile" />
	</appender>
	<logger name="com.nci.tunan.cs.impl.ybtupdateacceptstuts" level="debug">
		<!-- <appender-ref ref="stdout2"/> -->
		<appender-ref ref="ASYNC_6" />
	</logger>
	<!-- 分红批处理日志记录 end -->


	<!-- 理赔抄单日志记录 start -->
	<appender name="clmquerypolicyFile"
		class="ch.qos.logback.core.rolling.RollingFileAppender">
		<Encoding>UTF-8</Encoding>
		<rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
			<FileNamePattern>./logstan/clmquerypolicy/logtest-%d{yyyy-M-d}.log
			</FileNamePattern>
			<MaxHistory>3</MaxHistory>
		</rollingPolicy>
		<encoder>
			<pattern>%d{yyyy-M-d HH:mm:ss SSS}|%t|%p|%m|%F|%L|%n
			</pattern>
		</encoder>
		<tiggeringPolicy
			class="ch.qos.logback.core.rolling.SizeBasedTriggeringPolicy">
			<MaxFileSize>500MB</MaxFileSize>
		</tiggeringPolicy>
	</appender>
	<!-- 异步输出 -->
	<appender name="ASYNC_7" class="ch.qos.logback.classic.AsyncAppender">
		<!-- 不丢失日志.默认的,如果队列的80%已满,则会丢弃TRACT、DEBUG、INFO级别的日志 -->
		<discardingThreshold>0</discardingThreshold>
		<!-- 更改默认的队列的深度,该值会影响性能.默认值为256 -->
		<queueSize>256</queueSize>
		<!-- 输出文件以及行号信息需要 stacktrace 获取 callerdata，因为性能原因 logback 的 AsyncAppender
			默认是不记录类名和行号 -->
		<includeCallerData>true</includeCallerData>
		<!-- 避免线程阻塞 -->
		<neverBlock>true</neverBlock>
		<!-- 添加附加的appender,最多只能添加一个 -->
		<appender-ref ref="clmquerypolicyFile" />
	</appender>
	<logger name="com.nci.tunan.pa.impl.querypolicy" level="debug">
		<!-- <appender-ref ref="stdout2"/> -->
		<appender-ref ref="ASYNC_7" />
	</logger>
	<!-- 理赔抄单日志记录 end -->

	<!-- 利差批处理日志记录 start -->
	<appender name="interestmarginLogFile"
		class="ch.qos.logback.core.rolling.RollingFileAppender">
		<Encoding>UTF-8</Encoding>
		<rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
			<FileNamePattern>./logstan/interestmargin/logtest-%d{yyyy-M-d}.log
			</FileNamePattern>
			<MaxHistory>3</MaxHistory>
		</rollingPolicy>
		<encoder>
			<pattern>%d{yyyy-M-d HH:mm:ss SSS}|%t|%p|%m|%F|%L|%n
			</pattern>
		</encoder>
		<tiggeringPolicy
			class="ch.qos.logback.core.rolling.SizeBasedTriggeringPolicy">
			<MaxFileSize>500MB</MaxFileSize>
		</tiggeringPolicy>
	</appender>
	<!-- 异步输出 -->
	<appender name="ASYNC_interestmarginLogFile" class="ch.qos.logback.classic.AsyncAppender">
		<!-- 不丢失日志.默认的,如果队列的80%已满,则会丢弃TRACT、DEBUG、INFO级别的日志 -->
		<discardingThreshold>0</discardingThreshold>
		<!-- 更改默认的队列的深度,该值会影响性能.默认值为256 -->
		<queueSize>256</queueSize>
		<!-- 输出文件以及行号信息需要 stacktrace 获取 callerdata，因为性能原因 logback 的 AsyncAppender
			默认是不记录类名和行号 -->
		<includeCallerData>true</includeCallerData>
		<!-- 避免线程阻塞 -->
		<neverBlock>true</neverBlock>
		<!-- 添加附加的appender,最多只能添加一个 -->
		<appender-ref ref="interestmarginLogFile" />
	</appender>
	<logger name="com.nci.tunan.pa.batch.interestmargin" level="debug">
		<!-- <appender-ref ref="stdout2"/> -->
		<appender-ref ref="ASYNC_interestmarginLogFile" />
	</logger>
	<!-- 利差批处理日志记录 end -->

	<!-- 约定年金日志记录 start -->
	<appender name="uniPolicyLogFile"
		class="ch.qos.logback.core.rolling.RollingFileAppender">
		<Encoding>UTF-8</Encoding>
		<rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
			<FileNamePattern>./logstan/uniPolicyLogFile/logtest-%d{yyyy-M-d}.log
			</FileNamePattern>
			<MaxHistory>3</MaxHistory>
		</rollingPolicy>
		<encoder>
			<pattern>%d{yyyy-M-d HH:mm:ss SSS}|%t|%p|%m|%F|%L|%n
			</pattern>
		</encoder>
		<tiggeringPolicy
			class="ch.qos.logback.core.rolling.SizeBasedTriggeringPolicy">
			<MaxFileSize>500MB</MaxFileSize>
		</tiggeringPolicy>
	</appender>
	<!-- 异步输出 -->
	<appender name="ASYNC_8" class="ch.qos.logback.classic.AsyncAppender">
		<!-- 不丢失日志.默认的,如果队列的80%已满,则会丢弃TRACT、DEBUG、INFO级别的日志 -->
		<discardingThreshold>0</discardingThreshold>
		<!-- 更改默认的队列的深度,该值会影响性能.默认值为256 -->
		<queueSize>256</queueSize>
		<!-- 输出文件以及行号信息需要 stacktrace 获取 callerdata，因为性能原因 logback 的 AsyncAppender
			默认是不记录类名和行号 -->
		<includeCallerData>true</includeCallerData>
		<!-- 避免线程阻塞 -->
		<neverBlock>true</neverBlock>
		<!-- 添加附加的appender,最多只能添加一个 -->
		<appender-ref ref="uniPolicyLogFile" />
	</appender>
	<logger name="com.nci.tunan.pa.batch.unipolicyinstallment"
		level="debug">
		<!-- <appender-ref ref="stdout2"/> -->
		<appender-ref ref="ASYNC_8" />
	</logger>
	<!-- 约定年金日志记录 end -->

	<!-- 电商批量承包日志记录 start -->
	<appender name="seckillpolicydatesynLogFile"
		class="ch.qos.logback.core.rolling.RollingFileAppender">
		<Encoding>UTF-8</Encoding>
		<rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
			<FileNamePattern>./logstan/seckillpolicydatesyn/logtest-%d{yyyy-M-d}.log
			</FileNamePattern>
			<MaxHistory>3</MaxHistory>
		</rollingPolicy>
		<encoder>
			<pattern>%d{yyyy-M-d HH:mm:ss SSS}|%t|%p|%m|%F|%L|%n
			</pattern>
		</encoder>
		<tiggeringPolicy
			class="ch.qos.logback.core.rolling.SizeBasedTriggeringPolicy">
			<MaxFileSize>500MB</MaxFileSize>
		</tiggeringPolicy>
	</appender>
	<!-- 异步输出 -->
	<appender name="ASYNC_9" class="ch.qos.logback.classic.AsyncAppender">
		<!-- 不丢失日志.默认的,如果队列的80%已满,则会丢弃TRACT、DEBUG、INFO级别的日志 -->
		<discardingThreshold>0</discardingThreshold>
		<!-- 更改默认的队列的深度,该值会影响性能.默认值为256 -->
		<queueSize>256</queueSize>
		<!-- 输出文件以及行号信息需要 stacktrace 获取 callerdata，因为性能原因 logback 的 AsyncAppender
			默认是不记录类名和行号 -->
		<includeCallerData>true</includeCallerData>
		<!-- 避免线程阻塞 -->
		<neverBlock>true</neverBlock>
		<!-- 添加附加的appender,最多只能添加一个 -->
		<appender-ref ref="seckillpolicydatesynLogFile" />
	</appender>
	<logger name="com.nci.tunan.pa.batch.seckillpolicydatesyn"
		level="debug">
		<!-- <appender-ref ref="stdout2"/> -->
		<appender-ref ref="ASYNC_9" />
	</logger>
	<!-- 电商批量承包日志记录 end -->


	<!-- 保单履历日志记录 start -->
	<appender name="policyChgLogFile"
		class="ch.qos.logback.core.rolling.RollingFileAppender">
		<Encoding>UTF-8</Encoding>
		<rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
			<FileNamePattern>./logstan/policyChgLogFile/logtest-%d{yyyy-M-d}.log
			</FileNamePattern>
			<MaxHistory>3</MaxHistory>
		</rollingPolicy>
		<encoder>
			<pattern>%d{yyyy-M-d HH:mm:ss SSS}|%t|%p|%m|%F|%L|%n
			</pattern>
		</encoder>
		<tiggeringPolicy
			class="ch.qos.logback.core.rolling.SizeBasedTriggeringPolicy">
			<MaxFileSize>500MB</MaxFileSize>
		</tiggeringPolicy>
	</appender>
	<!-- 异步输出 -->
	<appender name="ASYNC_10" class="ch.qos.logback.classic.AsyncAppender">
		<!-- 不丢失日志.默认的,如果队列的80%已满,则会丢弃TRACT、DEBUG、INFO级别的日志 -->
		<discardingThreshold>0</discardingThreshold>
		<!-- 更改默认的队列的深度,该值会影响性能.默认值为256 -->
		<queueSize>256</queueSize>
		<!-- 输出文件以及行号信息需要 stacktrace 获取 callerdata，因为性能原因 logback 的 AsyncAppender
			默认是不记录类名和行号 -->
		<includeCallerData>true</includeCallerData>
		<!-- 避免线程阻塞 -->
		<neverBlock>true</neverBlock>
		<!-- 添加附加的appender,最多只能添加一个 -->
		<appender-ref ref="policyChgLogFile" />
	</appender>
	<logger name="com.nci.tunan.pa.impl.policyLog" level="debug">
		<!-- <appender-ref ref="stdout2"/> -->
		<appender-ref ref="ASYNC_10" />
	</logger>
	<!-- 保单履历日志记录 end -->




	<!-- 续期抽挡 add by liangpl start -->
	<appender name="RenewalAutomaticExtraJobLogFile"
		class="ch.qos.logback.core.rolling.RollingFileAppender">
		<Encoding>UTF-8</Encoding>
		<rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
			<FileNamePattern>./logstan/RenewalAutomaticExtraJobLogFile/logtest-%d{yyyy-M-d}.log
			</FileNamePattern>
			<MaxHistory>3</MaxHistory>
		</rollingPolicy>
		<encoder>
			<pattern>%d{yyyy-M-d HH:mm:ss SSS}|%t|%p|%m|%F|%L|%n
			</pattern>
		</encoder>
		<tiggeringPolicy
			class="ch.qos.logback.core.rolling.SizeBasedTriggeringPolicy">
			<MaxFileSize>500MB</MaxFileSize>
		</tiggeringPolicy>
	</appender>
	<!-- 异步输出 -->
	<appender name="ASYNC_11" class="ch.qos.logback.classic.AsyncAppender">
		<!-- 不丢失日志.默认的,如果队列的80%已满,则会丢弃TRACT、DEBUG、INFO级别的日志 -->
		<discardingThreshold>0</discardingThreshold>
		<!-- 更改默认的队列的深度,该值会影响性能.默认值为256 -->
		<queueSize>256</queueSize>
		<!-- 输出文件以及行号信息需要 stacktrace 获取 callerdata，因为性能原因 logback 的 AsyncAppender
			默认是不记录类名和行号 -->
		<includeCallerData>true</includeCallerData>
		<!-- 避免线程阻塞 -->
		<neverBlock>true</neverBlock>
		<!-- 添加附加的appender,最多只能添加一个 -->
		<appender-ref ref="RenewalAutomaticExtraJobLogFile" />
	</appender>
	<logger name="com.nci.tunan.pa.batch.automaticextra" level="debug">
		<!-- <appender-ref ref="stdout2" /> -->
		<appender-ref ref="ASYNC_11" />
	</logger>
	<!-- 续期 抽挡end -->


	<!-- 续保抽挡start -->
	<appender name="AutomaticRenewalExtractionJobLogFile"
		class="ch.qos.logback.core.rolling.RollingFileAppender">
		<Encoding>UTF-8</Encoding>
		<rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
			<FileNamePattern>./logstan/AutomaticRenewalExtractionJobLogFile/logtest-%d{yyyy-M-d}.log
			</FileNamePattern>
			<MaxHistory>3</MaxHistory>
		</rollingPolicy>
		<encoder>
			<pattern>%d{yyyy-M-d HH:mm:ss SSS}|%t|%p|%m|%F|%L|%n
			</pattern>
		</encoder>
		<tiggeringPolicy
			class="ch.qos.logback.core.rolling.SizeBasedTriggeringPolicy">
			<MaxFileSize>500MB</MaxFileSize>
		</tiggeringPolicy>
	</appender>
	<!-- 异步输出 -->
	<appender name="ASYNC_12" class="ch.qos.logback.classic.AsyncAppender">
		<!-- 不丢失日志.默认的,如果队列的80%已满,则会丢弃TRACT、DEBUG、INFO级别的日志 -->
		<discardingThreshold>0</discardingThreshold>
		<!-- 更改默认的队列的深度,该值会影响性能.默认值为256 -->
		<queueSize>256</queueSize>
		<!-- 输出文件以及行号信息需要 stacktrace 获取 callerdata，因为性能原因 logback 的 AsyncAppender
			默认是不记录类名和行号 -->
		<includeCallerData>true</includeCallerData>
		<!-- 避免线程阻塞 -->
		<neverBlock>true</neverBlock>
		<!-- 添加附加的appender,最多只能添加一个 -->
		<appender-ref ref="AutomaticRenewalExtractionJobLogFile" />
	</appender>
	<logger name="com.nci.tunan.pa.batch.automaticrenewalextraction"
		level="debug">
		<!-- <appender-ref ref="stdout2" /> -->
		<appender-ref ref="ASYNC_12" />
	</logger>
	<!-- 续保抽挡start -->

	<!-- 续保处理start -->
	<appender name="RenewalProcessJobLogFile"
		class="ch.qos.logback.core.rolling.RollingFileAppender">
		<Encoding>UTF-8</Encoding>
		<rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
			<FileNamePattern>./logstan/RenewalProcessJobLogFile/logtest-%d{yyyy-M-d}.log
			</FileNamePattern>
			<MaxHistory>3</MaxHistory>
		</rollingPolicy>
		<encoder>
			<pattern>%d{yyyy-M-d HH:mm:ss SSS}|%t|%p|%m|%F|%L|%n
			</pattern>
		</encoder>
		<tiggeringPolicy
			class="ch.qos.logback.core.rolling.SizeBasedTriggeringPolicy">
			<MaxFileSize>500MB</MaxFileSize>
		</tiggeringPolicy>
	</appender>
	<!-- 异步输出 -->
	<appender name="ASYNC_13" class="ch.qos.logback.classic.AsyncAppender">
		<!-- 不丢失日志.默认的,如果队列的80%已满,则会丢弃TRACT、DEBUG、INFO级别的日志 -->
		<discardingThreshold>0</discardingThreshold>
		<!-- 更改默认的队列的深度,该值会影响性能.默认值为256 -->
		<queueSize>256</queueSize>
		<!-- 输出文件以及行号信息需要 stacktrace 获取 callerdata，因为性能原因 logback 的 AsyncAppender
			默认是不记录类名和行号 -->
		<includeCallerData>true</includeCallerData>
		<!-- 避免线程阻塞 -->
		<neverBlock>true</neverBlock>
		<!-- 添加附加的appender,最多只能添加一个 -->
		<appender-ref ref="RenewalProcessJobLogFile" />
	</appender>
	<logger name="com.nci.tunan.pa.batch.renewalprocess" level="debug">
		<!-- <appender-ref ref="stdout2" /> -->
		<appender-ref ref="ASYNC_13" />
	</logger>
	<!-- 续保处理 end -->

	<!-- 跨月抽挡 add by liangpl start -->
	<appender name="RenewalAutomaticExtraKuayueJobLogFile"
		class="ch.qos.logback.core.rolling.RollingFileAppender">
		<Encoding>UTF-8</Encoding>
		<rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
			<FileNamePattern>./logstan/RenewalAutomaticExtraKuayueJobLogFile/logtest-%d{yyyy-M-d}.log
			</FileNamePattern>
			<MaxHistory>3</MaxHistory>
		</rollingPolicy>
		<encoder>
			<pattern>%d{yyyy-M-d HH:mm:ss SSS}|%t|%p|%m|%F|%L|%n
			</pattern>
		</encoder>
		<tiggeringPolicy
			class="ch.qos.logback.core.rolling.SizeBasedTriggeringPolicy">
			<MaxFileSize>500MB</MaxFileSize>
		</tiggeringPolicy>
	</appender>
	<!-- 异步输出 -->
	<appender name="ASYNC_14" class="ch.qos.logback.classic.AsyncAppender">
		<!-- 不丢失日志.默认的,如果队列的80%已满,则会丢弃TRACT、DEBUG、INFO级别的日志 -->
		<discardingThreshold>0</discardingThreshold>
		<!-- 更改默认的队列的深度,该值会影响性能.默认值为256 -->
		<queueSize>256</queueSize>
		<!-- 输出文件以及行号信息需要 stacktrace 获取 callerdata，因为性能原因 logback 的 AsyncAppender
			默认是不记录类名和行号 -->
		<includeCallerData>true</includeCallerData>
		<!-- 避免线程阻塞 -->
		<neverBlock>true</neverBlock>
		<!-- 添加附加的appender,最多只能添加一个 -->
		<appender-ref ref="RenewalAutomaticExtraKuayueJobLogFile" />
	</appender>
	<logger name="com.nci.tunan.pa.batch.automaticexrkuayue" level="debug">
		<!-- <appender-ref ref="stdout2" /> -->
		<appender-ref ref="ASYNC_14" />
	</logger>
	<!-- 跨月 抽挡end -->

	<!-- 长期险续保批处理 start -->
	<appender name="SecularAutomaticRenewalJobLogFile"
		class="ch.qos.logback.core.rolling.RollingFileAppender">
		<Encoding>UTF-8</Encoding>
		<rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
			<FileNamePattern>./logstan/SecularAutomaticRenewalJobLogFile/logtest-%d{yyyy-M-d}.log
			</FileNamePattern>
			<MaxHistory>3</MaxHistory>
		</rollingPolicy>
		<encoder>
			<pattern>%d{yyyy-M-d HH:mm:ss SSS}|%t|%p|%m|%F|%L|%n
			</pattern>
		</encoder>
		<tiggeringPolicy
			class="ch.qos.logback.core.rolling.SizeBasedTriggeringPolicy">
			<MaxFileSize>500MB</MaxFileSize>
		</tiggeringPolicy>
	</appender>
	<!-- 异步输出 -->
	<appender name="ASYNC_15" class="ch.qos.logback.classic.AsyncAppender">
		<!-- 不丢失日志.默认的,如果队列的80%已满,则会丢弃TRACT、DEBUG、INFO级别的日志 -->
		<discardingThreshold>0</discardingThreshold>
		<!-- 更改默认的队列的深度,该值会影响性能.默认值为256 -->
		<queueSize>256</queueSize>
		<!-- 输出文件以及行号信息需要 stacktrace 获取 callerdata，因为性能原因 logback 的 AsyncAppender
			默认是不记录类名和行号 -->
		<includeCallerData>true</includeCallerData>
		<!-- 避免线程阻塞 -->
		<neverBlock>true</neverBlock>
		<!-- 添加附加的appender,最多只能添加一个 -->
		<appender-ref ref="SecularAutomaticRenewalJobLogFile" />
	</appender>
	<logger name="com.nci.tunan.pa.batch.secularAutomaticRenewal"
		level="debug">
		<!-- <appender-ref ref="stdout2" /> -->
		<appender-ref ref="ASYNC_15" />
	</logger>
	<!-- 长期险续保批处理 add by liangpl end -->


	<!-- leihong 生存金年金、满期金 日志记录 start -->
	<appender name="surAnnMatJobFile"
		class="ch.qos.logback.core.rolling.RollingFileAppender">
		<Encoding>UTF-8</Encoding>
		<rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
			<FileNamePattern>./logstan/surAnnMatJobFile/logtest-%d{yyyy-M-d}.log
			</FileNamePattern>
			<MaxHistory>3</MaxHistory>
		</rollingPolicy>
		<encoder>
			<pattern>%d{yyyy-M-d HH:mm:ss SSS}|%t|%p|%m|%F|%L|%n
			</pattern>
		</encoder>
		<tiggeringPolicy
			class="ch.qos.logback.core.rolling.SizeBasedTriggeringPolicy">
			<MaxFileSize>500MB</MaxFileSize>
		</tiggeringPolicy>
	</appender>
	<!-- 异步输出 -->
	<appender name="ASYNC_16" class="ch.qos.logback.classic.AsyncAppender">
		<!-- 不丢失日志.默认的,如果队列的80%已满,则会丢弃TRACT、DEBUG、INFO级别的日志 -->
		<discardingThreshold>0</discardingThreshold>
		<!-- 更改默认的队列的深度,该值会影响性能.默认值为256 -->
		<queueSize>256</queueSize>
		<!-- 输出文件以及行号信息需要 stacktrace 获取 callerdata，因为性能原因 logback 的 AsyncAppender
			默认是不记录类名和行号 -->
		<includeCallerData>true</includeCallerData>
		<!-- 避免线程阻塞 -->
		<neverBlock>true</neverBlock>
		<!-- 添加附加的appender,最多只能添加一个 -->
		<appender-ref ref="surAnnMatJobFile" />
	</appender>
	<logger name="com.nci.tunan.pa.batch.survivalAnnuityMaturity"
		level="debug">
		<appender-ref ref="ASYNC_16" />
	</logger>
	<!-- 生存金年金、满期金 日志记录 end -->



	<!-- qinhb 生存金年金、满期金 公共方法日志记录 start -->
	<appender name="surAnnMatJobCommonFile"
		class="ch.qos.logback.core.rolling.RollingFileAppender">
		<Encoding>UTF-8</Encoding>
		<rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
			<FileNamePattern>./logstan/surAnnMatJobCommonFile/logtest-%d{yyyy-M-d}.log
			</FileNamePattern>
			<MaxHistory>3</MaxHistory>
		</rollingPolicy>
		<encoder>
			<pattern>%d{yyyy-M-d HH:mm:ss SSS}|%t|%p|%m|%F|%L|%n
			</pattern>
		</encoder>
		<tiggeringPolicy
			class="ch.qos.logback.core.rolling.SizeBasedTriggeringPolicy">
			<MaxFileSize>500MB</MaxFileSize>
		</tiggeringPolicy>
	</appender>
	<!-- 异步输出 -->
	<appender name="ASYNC_17" class="ch.qos.logback.classic.AsyncAppender">
		<!-- 不丢失日志.默认的,如果队列的80%已满,则会丢弃TRACT、DEBUG、INFO级别的日志 -->
		<discardingThreshold>0</discardingThreshold>
		<!-- 更改默认的队列的深度,该值会影响性能.默认值为256 -->
		<queueSize>256</queueSize>
		<!-- 输出文件以及行号信息需要 stacktrace 获取 callerdata，因为性能原因 logback 的 AsyncAppender
			默认是不记录类名和行号 -->
		<includeCallerData>true</includeCallerData>
		<!-- 避免线程阻塞 -->
		<neverBlock>true</neverBlock>
		<!-- 添加附加的appender,最多只能添加一个 -->
		<appender-ref ref="surAnnMatJobCommonFile" />
	</appender>
	<logger name="com.nci.tunan.pa.common.service.impl" level="debug">
		<appender-ref ref="ASYNC_17" />
	</logger>
	<!-- 生存金年金、满期金公共方法 日志记录 end -->

	<!-- leihong 投连万能费用扣除日志记录 start -->
	<appender name="ilpuchargedeductJobFile"
		class="ch.qos.logback.core.rolling.RollingFileAppender">
		<Encoding>UTF-8</Encoding>
		<rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
			<FileNamePattern>./logstan/ilpuchargedeductJobFile/logtest-%d{yyyy-M-d}.log
			</FileNamePattern>
			<MaxHistory>3</MaxHistory>
		</rollingPolicy>
		<encoder>
			<pattern>%d{yyyy-M-d HH:mm:ss SSS}|%t|%p|%m|%F|%L|%n
			</pattern>
		</encoder>
		<tiggeringPolicy
			class="ch.qos.logback.core.rolling.SizeBasedTriggeringPolicy">
			<MaxFileSize>500MB</MaxFileSize>
		</tiggeringPolicy>
	</appender>
	<!-- 异步输出 -->
	<appender name="ASYNC_18" class="ch.qos.logback.classic.AsyncAppender">
		<!-- 不丢失日志.默认的,如果队列的80%已满,则会丢弃TRACT、DEBUG、INFO级别的日志 -->
		<discardingThreshold>0</discardingThreshold>
		<!-- 更改默认的队列的深度,该值会影响性能.默认值为256 -->
		<queueSize>256</queueSize>
		<!-- 输出文件以及行号信息需要 stacktrace 获取 callerdata，因为性能原因 logback 的 AsyncAppender
			默认是不记录类名和行号 -->
		<includeCallerData>true</includeCallerData>
		<!-- 避免线程阻塞 -->
		<neverBlock>true</neverBlock>
		<!-- 添加附加的appender,最多只能添加一个 -->
		<appender-ref ref="ilpuchargedeductJobFile" />
	</appender>
	<logger name="com.nci.tunan.pa.batch.ilpuchargededuction" level="debug">
		<appender-ref ref="ASYNC_18" />
	</logger>
	<!-- 投连万能费用扣除日志记录 end -->


	<!-- leihong 投连万能状态报告书日志记录 start -->
	<appender name="iLPUChargeDeductionNoticeJobFile"
		class="ch.qos.logback.core.rolling.RollingFileAppender">
		<Encoding>UTF-8</Encoding>
		<rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
			<FileNamePattern>./logstan/iLPUChargeDeductionNoticeJobFile/logtest-%d{yyyy-M-d}.log
			</FileNamePattern>
			<MaxHistory>3</MaxHistory>
		</rollingPolicy>
		<encoder>
			<pattern>%d{yyyy-M-d HH:mm:ss SSS}|%t|%p|%m|%F|%L|%n
			</pattern>
		</encoder>
		<tiggeringPolicy
			class="ch.qos.logback.core.rolling.SizeBasedTriggeringPolicy">
			<MaxFileSize>500MB</MaxFileSize>
		</tiggeringPolicy>
	</appender>
	<!-- 异步输出 -->
	<appender name="ASYNC_19" class="ch.qos.logback.classic.AsyncAppender">
		<!-- 不丢失日志.默认的,如果队列的80%已满,则会丢弃TRACT、DEBUG、INFO级别的日志 -->
		<discardingThreshold>0</discardingThreshold>
		<!-- 更改默认的队列的深度,该值会影响性能.默认值为256 -->
		<queueSize>256</queueSize>
		<!-- 输出文件以及行号信息需要 stacktrace 获取 callerdata，因为性能原因 logback 的 AsyncAppender
			默认是不记录类名和行号 -->
		<includeCallerData>true</includeCallerData>
		<!-- 避免线程阻塞 -->
		<neverBlock>true</neverBlock>
		<!-- 添加附加的appender,最多只能添加一个 -->
		<appender-ref ref="iLPUChargeDeductionNoticeJobFile" />
	</appender>
	<logger name="com.nci.tunan.pa.batch.ilpuchargedeductionnotice"
		level="debug">
		<appender-ref ref="ASYNC_19" />
	</logger>
	<!-- 投连万能状态报告书日志记录 end -->

	<!-- 万能结算日志记录 start -->
	<appender name="universalsettlementJobFile"
		class="ch.qos.logback.core.rolling.RollingFileAppender">
		<Encoding>UTF-8</Encoding>
		<rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
			<FileNamePattern>./logstan/universalsettlementJobFile/logtest-%d{yyyy-M-d}.log
			</FileNamePattern>
			<MaxHistory>3</MaxHistory>
		</rollingPolicy>
		<encoder>
			<pattern>%d{yyyy-M-d HH:mm:ss SSS}|%t|%p|%m|%F|%L|%n
			</pattern>
		</encoder>
		<tiggeringPolicy
			class="ch.qos.logback.core.rolling.SizeBasedTriggeringPolicy">
			<MaxFileSize>500MB</MaxFileSize>
		</tiggeringPolicy>
	</appender>
	<!-- 异步输出 -->
	<appender name="ASYNC_20" class="ch.qos.logback.classic.AsyncAppender">
		<!-- 不丢失日志.默认的,如果队列的80%已满,则会丢弃TRACT、DEBUG、INFO级别的日志 -->
		<discardingThreshold>0</discardingThreshold>
		<!-- 更改默认的队列的深度,该值会影响性能.默认值为256 -->
		<queueSize>256</queueSize>
		<!-- 输出文件以及行号信息需要 stacktrace 获取 callerdata，因为性能原因 logback 的 AsyncAppender
			默认是不记录类名和行号 -->
		<includeCallerData>true</includeCallerData>
		<!-- 添加附加的appender,最多只能添加一个 -->
		<appender-ref ref="universalsettlementJobFile" />
	</appender>
	<logger name="com.nci.tunan.pa.batch.universalsettlement" level="debug">
		<appender-ref ref="ASYNC_20" />
	</logger>
	<!-- 万能结算日志记录 end -->

	<!-- 保单暂缓批处理日志记录 start -->
	<appender name="policyPauseJobFile"
		class="ch.qos.logback.core.rolling.RollingFileAppender">
		<Encoding>UTF-8</Encoding>
		<rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
			<FileNamePattern>./logstan/policyPauseJobFile/logtest-%d{yyyy-M-d}.log
			</FileNamePattern>
			<MaxHistory>3</MaxHistory>
		</rollingPolicy>
		<encoder>
			<pattern>%d{yyyy-M-d HH:mm:ss SSS}|%t|%p|%m|%F|%L|%n
			</pattern>
		</encoder>
		<tiggeringPolicy
			class="ch.qos.logback.core.rolling.SizeBasedTriggeringPolicy">
			<MaxFileSize>500MB</MaxFileSize>
		</tiggeringPolicy>
	</appender>
	<!-- 异步输出 -->
	<appender name="ASYNC_21" class="ch.qos.logback.classic.AsyncAppender">
		<!-- 不丢失日志.默认的,如果队列的80%已满,则会丢弃TRACT、DEBUG、INFO级别的日志 -->
		<discardingThreshold>0</discardingThreshold>
		<!-- 更改默认的队列的深度,该值会影响性能.默认值为256 -->
		<queueSize>256</queueSize>
		<!-- 输出文件以及行号信息需要 stacktrace 获取 callerdata，因为性能原因 logback 的 AsyncAppender
			默认是不记录类名和行号 -->
		<includeCallerData>true</includeCallerData>
		<!-- 避免线程阻塞 -->
		<neverBlock>true</neverBlock>
		<!-- 添加附加的appender,最多只能添加一个 -->
		<appender-ref ref="policyPauseJobFile" />
	</appender>
	<logger name="com.nci.tunan.pa.batch.policypause" level="debug">
		<appender-ref ref="ASYNC_21" />
	</logger>
	<!-- 保单暂缓批处理日志记录 end -->

	<!-- leihong 投连计价日志记录 start -->
	<appender name="ilpDealDeliveryJobFile"
		class="ch.qos.logback.core.rolling.RollingFileAppender">
		<Encoding>UTF-8</Encoding>
		<rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
			<FileNamePattern>./logstan/ilpDealDeliveryJobFile/logtest-%d{yyyy-M-d}.log
			</FileNamePattern>
			<MaxHistory>3</MaxHistory>
		</rollingPolicy>
		<encoder>
			<pattern>%d{yyyy-M-d HH:mm:ss SSS}|%t|%p|%m|%F|%L|%n
			</pattern>
		</encoder>
		<tiggeringPolicy
			class="ch.qos.logback.core.rolling.SizeBasedTriggeringPolicy">
			<MaxFileSize>500MB</MaxFileSize>
		</tiggeringPolicy>
	</appender>
	<!-- 异步输出 -->
	<appender name="ASYNC_22" class="ch.qos.logback.classic.AsyncAppender">
		<!-- 不丢失日志.默认的,如果队列的80%已满,则会丢弃TRACT、DEBUG、INFO级别的日志 -->
		<discardingThreshold>0</discardingThreshold>
		<!-- 更改默认的队列的深度,该值会影响性能.默认值为256 -->
		<queueSize>256</queueSize>
		<!-- 输出文件以及行号信息需要 stacktrace 获取 callerdata，因为性能原因 logback 的 AsyncAppender
			默认是不记录类名和行号 -->
		<includeCallerData>true</includeCallerData>
		<!-- 避免线程阻塞 -->
		<neverBlock>true</neverBlock>
		<!-- 添加附加的appender,最多只能添加一个 -->
		<appender-ref ref="ilpDealDeliveryJobFile" />
	</appender>
	<logger name="com.nci.tunan.pa.batch.ilpdealdelivery" level="debug">
		<appender-ref ref="ASYNC_22" />
	</logger>
	<!-- 投连计价日志记录 end -->

	<!-- leihong 自垫批处理日志记录 start -->
	<appender name="autoPaymentJobFile"
		class="ch.qos.logback.core.rolling.RollingFileAppender">
		<Encoding>UTF-8</Encoding>
		<rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
			<FileNamePattern>./logstan/autoPaymentJobFile/logtest-%d{yyyy-M-d}.log
			</FileNamePattern>
			<MaxHistory>3</MaxHistory>
		</rollingPolicy>
		<encoder>
			<pattern>%d{yyyy-M-d HH:mm:ss SSS}|%t|%p|%m|%F|%L|%n
			</pattern>
		</encoder>
		<tiggeringPolicy
			class="ch.qos.logback.core.rolling.SizeBasedTriggeringPolicy">
			<MaxFileSize>500MB</MaxFileSize>
		</tiggeringPolicy>
	</appender>
	<!-- 异步输出 -->
	<appender name="ASYNC_24" class="ch.qos.logback.classic.AsyncAppender">
		<!-- 不丢失日志.默认的,如果队列的80%已满,则会丢弃TRACT、DEBUG、INFO级别的日志 -->
		<discardingThreshold>0</discardingThreshold>
		<!-- 更改默认的队列的深度,该值会影响性能.默认值为256 -->
		<queueSize>256</queueSize>
		<!-- 输出文件以及行号信息需要 stacktrace 获取 callerdata，因为性能原因 logback 的 AsyncAppender
			默认是不记录类名和行号 -->
		<includeCallerData>true</includeCallerData>
		<!-- 避免线程阻塞 -->
		<neverBlock>true</neverBlock>
		<!-- 添加附加的appender,最多只能添加一个 -->
		<appender-ref ref="autoPaymentJobFile" />
	</appender>
	<logger name="com.nci.tunan.pa.batch.automaticpayment" level="debug">
		<appender-ref ref="ASYNC_24" />
	</logger>
	<!-- 自垫批处理日志记录 end -->

	<!-- songdf 生调批处理 start -->
	<appender name="checkLiveStatusJobFile"
		class="ch.qos.logback.core.rolling.RollingFileAppender">
		<Encoding>UTF-8</Encoding>
		<rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
			<FileNamePattern>./logstan/checkLiveStatusJobFile/logtest-%d{yyyy-M-d}.log
			</FileNamePattern>
			<MaxHistory>3</MaxHistory>
		</rollingPolicy>
		<encoder>
			<pattern>%d{yyyy-M-d HH:mm:ss SSS}|%t|%p|%m|%F|%L|%n
			</pattern>
		</encoder>
		<tiggeringPolicy
			class="ch.qos.logback.core.rolling.SizeBasedTriggeringPolicy">
			<MaxFileSize>500MB</MaxFileSize>
		</tiggeringPolicy>
	</appender>
	<!-- 异步输出 -->
	<appender name="ASYNC_25" class="ch.qos.logback.classic.AsyncAppender">
		<!-- 不丢失日志.默认的,如果队列的80%已满,则会丢弃TRACT、DEBUG、INFO级别的日志 -->
		<discardingThreshold>0</discardingThreshold>
		<!-- 更改默认的队列的深度,该值会影响性能.默认值为256 -->
		<queueSize>256</queueSize>
		<!-- 输出文件以及行号信息需要 stacktrace 获取 callerdata，因为性能原因 logback 的 AsyncAppender
			默认是不记录类名和行号 -->
		<includeCallerData>true</includeCallerData>
		<!-- 避免线程阻塞 -->
		<neverBlock>true</neverBlock>
		<!-- 添加附加的appender,最多只能添加一个 -->
		<appender-ref ref="checkLiveStatusJobFile" />
	</appender>
	<logger name="com.nci.tunan.pa.batch.checklivestatus" level="debug">
		<appender-ref ref="ASYNC_25" />
	</logger>
	<!-- 生调批处理 end -->

	<!-- leihong 贷款自垫预终止日志记录 start -->
	<appender name="loanPaymentAdvanceJobFile"
		class="ch.qos.logback.core.rolling.RollingFileAppender">
		<Encoding>UTF-8</Encoding>
		<rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
			<FileNamePattern>./logstan/loanPaymentAdvanceJobFile/logtest-%d{yyyy-M-d}.log
			</FileNamePattern>
			<MaxHistory>3</MaxHistory>
		</rollingPolicy>
		<encoder>
			<pattern>%d{yyyy-M-d HH:mm:ss SSS}|%t|%p|%m|%F|%L|%n
			</pattern>
		</encoder>
		<tiggeringPolicy
			class="ch.qos.logback.core.rolling.SizeBasedTriggeringPolicy">
			<MaxFileSize>500MB</MaxFileSize>
		</tiggeringPolicy>
	</appender>
	<!-- 异步输出 -->
	<appender name="ASYNC_26" class="ch.qos.logback.classic.AsyncAppender">
		<!-- 不丢失日志.默认的,如果队列的80%已满,则会丢弃TRACT、DEBUG、INFO级别的日志 -->
		<discardingThreshold>0</discardingThreshold>
		<!-- 更改默认的队列的深度,该值会影响性能.默认值为256 -->
		<queueSize>256</queueSize>
		<!-- 输出文件以及行号信息需要 stacktrace 获取 callerdata，因为性能原因 logback 的 AsyncAppender
			默认是不记录类名和行号 -->
		<includeCallerData>true</includeCallerData>
		<!-- 避免线程阻塞 -->
		<neverBlock>true</neverBlock>
		<!-- 添加附加的appender,最多只能添加一个 -->
		<appender-ref ref="loanPaymentAdvanceJobFile" />
	</appender>
	<logger name="com.nci.tunan.pa.batch.loanpaymentadvance" level="debug">
		<appender-ref ref="ASYNC_26" />
	</logger>
	<!-- 贷款自垫预终止日志记录 end -->

	<!-- 超限终止 日志记录 start -->
	<appender name="loanPaymentAdvanceJobFile"
		class="ch.qos.logback.core.rolling.RollingFileAppender">
		<Encoding>UTF-8</Encoding>
		<rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
			<FileNamePattern>./logstan/terminate/terminate-%d{yyyy-M-d}.log
			</FileNamePattern>
			<MaxHistory>3</MaxHistory>
		</rollingPolicy>
		<encoder>
			<pattern>%d{yyyy-M-d HH:mm:ss SSS}|%t|%p|%m|%F|%L|%n
			</pattern>
		</encoder>
		<tiggeringPolicy
			class="ch.qos.logback.core.rolling.SizeBasedTriggeringPolicy">
			<MaxFileSize>500MB</MaxFileSize>
		</tiggeringPolicy>
	</appender>
	<!-- 异步输出 -->
	<appender name="ASYNC_27" class="ch.qos.logback.classic.AsyncAppender">
		<!-- 不丢失日志.默认的,如果队列的80%已满,则会丢弃TRACT、DEBUG、INFO级别的日志 -->
		<discardingThreshold>0</discardingThreshold>
		<!-- 更改默认的队列的深度,该值会影响性能.默认值为256 -->
		<queueSize>256</queueSize>
		<!-- 输出文件以及行号信息需要 stacktrace 获取 callerdata，因为性能原因 logback 的 AsyncAppender
			默认是不记录类名和行号 -->
		<includeCallerData>true</includeCallerData>
		<!-- 避免线程阻塞 -->
		<neverBlock>true</neverBlock>
		<!-- 添加附加的appender,最多只能添加一个 -->
		<appender-ref ref="loanPaymentAdvanceJobFile" />
	</appender>
	<logger name="com.nci.tunan.pa.batch.terminate" level="debug">
		<appender-ref ref="ASYNC_27" />
	</logger>
	<!-- 超限终止 日志记录 end -->

	<!-- leihong 贷款自垫终止日志记录 start -->
	<appender name="loanPaymentStopJobFile"
		class="ch.qos.logback.core.rolling.RollingFileAppender">
		<Encoding>UTF-8</Encoding>
		<rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
			<FileNamePattern>./logstan/loanPaymentStopJobFile/logtest-%d{yyyy-M-d}.log
			</FileNamePattern>
			<MaxHistory>3</MaxHistory>
		</rollingPolicy>
		<encoder>
			<pattern>%d{yyyy-M-d HH:mm:ss SSS}|%t|%p|%m|%F|%L|%n
			</pattern>
		</encoder>
		<tiggeringPolicy
			class="ch.qos.logback.core.rolling.SizeBasedTriggeringPolicy">
			<MaxFileSize>500MB</MaxFileSize>
		</tiggeringPolicy>
	</appender>
	<!-- 异步输出 -->
	<appender name="ASYNC_28" class="ch.qos.logback.classic.AsyncAppender">
		<!-- 不丢失日志.默认的,如果队列的80%已满,则会丢弃TRACT、DEBUG、INFO级别的日志 -->
		<discardingThreshold>0</discardingThreshold>
		<!-- 更改默认的队列的深度,该值会影响性能.默认值为256 -->
		<queueSize>256</queueSize>
		<!-- 输出文件以及行号信息需要 stacktrace 获取 callerdata，因为性能原因 logback 的 AsyncAppender
			默认是不记录类名和行号 -->
		<includeCallerData>true</includeCallerData>
		<!-- 避免线程阻塞 -->
		<neverBlock>true</neverBlock>
		<!-- 添加附加的appender,最多只能添加一个 -->
		<appender-ref ref="loanPaymentStopJobFile" />
	</appender>
	<logger name="com.nci.tunan.pa.batch.loanpaymentstop" level="debug">
		<appender-ref ref="ASYNC_28" />
	</logger>
	<!-- 贷款自垫终止日志记录 end -->



	<!-- 保单风险批处理start -->
	<appender name="PolicyRiskFile"
		class="ch.qos.logback.core.rolling.RollingFileAppender">
		<Encoding>UTF-8</Encoding>
		<rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
			<FileNamePattern>./logstan/PolicyRiskFile/logtest-%d{yyyy-M-d}.log
			</FileNamePattern>
			<MaxHistory>3</MaxHistory>
		</rollingPolicy>
		<encoder>
			<pattern>%d{yyyy-M-d HH:mm:ss SSS}|%t|%p|%m|%F|%L|%n
			</pattern>
		</encoder>
		<tiggeringPolicy
			class="ch.qos.logback.core.rolling.SizeBasedTriggeringPolicy">
			<MaxFileSize>500MB</MaxFileSize>
		</tiggeringPolicy>
	</appender>
	<!-- 异步输出 -->
	<appender name="ASYNC_29" class="ch.qos.logback.classic.AsyncAppender">
		<!-- 不丢失日志.默认的,如果队列的80%已满,则会丢弃TRACT、DEBUG、INFO级别的日志 -->
		<discardingThreshold>0</discardingThreshold>
		<!-- 更改默认的队列的深度,该值会影响性能.默认值为256 -->
		<queueSize>256</queueSize>
		<!-- 输出文件以及行号信息需要 stacktrace 获取 callerdata，因为性能原因 logback 的 AsyncAppender
			默认是不记录类名和行号 -->
		<includeCallerData>true</includeCallerData>
		<!-- 避免线程阻塞 -->
		<neverBlock>true</neverBlock>
		<!-- 添加附加的appender,最多只能添加一个 -->
		<appender-ref ref="PolicyRiskFile" />
	</appender>
	<logger name="com.nci.tunan.pa.batch.policyrisk" level="debug"
		additivity="false">
		<!-- <appender-ref ref="stdout2" /> -->
		<appender-ref ref="ASYNC_29" />
	</logger>
	<!-- 保单风险批处理end -->

	<!-- 转保单日志 start -->
	<appender name="createPolicyFile"
		class="ch.qos.logback.core.rolling.RollingFileAppender">
		<Encoding>UTF-8</Encoding>
		<rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
			<FileNamePattern>./logstan/createPolicyFile/logtest-%d{yyyy-M-d}.log
			</FileNamePattern>
			<MaxHistory>3</MaxHistory>
		</rollingPolicy>
		<encoder>
			<pattern>%d{yyyy-M-d HH:mm:ss SSS}|%t|%p|%m|%F|%L|%n
			</pattern>
		</encoder>
		<tiggeringPolicy
			class="ch.qos.logback.core.rolling.SizeBasedTriggeringPolicy">
			<MaxFileSize>500MB</MaxFileSize>
		</tiggeringPolicy>
	</appender>
	<!-- 异步输出 -->
	<appender name="ASYNC_30" class="ch.qos.logback.classic.AsyncAppender">
		<!-- 不丢失日志.默认的,如果队列的80%已满,则会丢弃TRACT、DEBUG、INFO级别的日志 -->
		<discardingThreshold>0</discardingThreshold>
		<!-- 更改默认的队列的深度,该值会影响性能.默认值为256 -->
		<queueSize>256</queueSize>
		<!-- 输出文件以及行号信息需要 stacktrace 获取 callerdata，因为性能原因 logback 的 AsyncAppender
			默认是不记录类名和行号 -->
		<includeCallerData>true</includeCallerData>
		<!-- 避免线程阻塞 -->
		<neverBlock>true</neverBlock>
		<!-- 添加附加的appender,最多只能添加一个 -->
		<appender-ref ref="createPolicyFile" />
	</appender>
	<logger name="com.nci.tunan.pa.impl.createpolicy" level="debug">
		<!-- <appender-ref ref="stdout2" /> -->
		<appender-ref ref="ASYNC_30" />
	</logger>
	<!-- 转保单日志 end -->

	<!-- 理赔回退日志 start -->
	<appender name="clmRollBack"
		class="ch.qos.logback.core.rolling.RollingFileAppender">
		<Encoding>UTF-8</Encoding>
		<rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
			<FileNamePattern>./logstan/clmRollBack/logtest-%d{yyyy-M-d}.log
			</FileNamePattern>
			<MaxHistory>3</MaxHistory>
		</rollingPolicy>
		<encoder>
			<pattern>%d{yyyy-M-d HH:mm:ss SSS}|%t|%p|%m|%F|%L|%n
			</pattern>
		</encoder>
		<tiggeringPolicy
			class="ch.qos.logback.core.rolling.SizeBasedTriggeringPolicy">
			<MaxFileSize>500MB</MaxFileSize>
		</tiggeringPolicy>
	</appender>
	<!-- 异步输出 -->
	<appender name="ASYNC_31" class="ch.qos.logback.classic.AsyncAppender">
		<!-- 不丢失日志.默认的,如果队列的80%已满,则会丢弃TRACT、DEBUG、INFO级别的日志 -->
		<discardingThreshold>0</discardingThreshold>
		<!-- 更改默认的队列的深度,该值会影响性能.默认值为256 -->
		<queueSize>256</queueSize>
		<!-- 输出文件以及行号信息需要 stacktrace 获取 callerdata，因为性能原因 logback 的 AsyncAppender
			默认是不记录类名和行号 -->
		<includeCallerData>true</includeCallerData>
		<!-- 避免线程阻塞 -->
		<neverBlock>true</neverBlock>
		<!-- 添加附加的appender,最多只能添加一个 -->
		<appender-ref ref="clmRollBack" />
	</appender>
	<logger name="com.nci.tunan.pa.impl.clmrollback" level="debug">
		<!-- <appender-ref ref="stdout2" /> -->
		<appender-ref ref="ASYNC_31" />
	</logger>
	<!-- 理赔回退日志 end -->

	<!-- 核销日志 start -->
	<appender name="changeTotalPremAF"
		class="ch.qos.logback.core.rolling.RollingFileAppender">
		<Encoding>UTF-8</Encoding>
		<rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
			<FileNamePattern>./logstan/changeTotalPremAFile/logtest-%d{yyyy-M-d}.log
			</FileNamePattern>
			<MaxHistory>3</MaxHistory>
		</rollingPolicy>
		<encoder>
			<pattern>%d{yyyy-M-d HH:mm:ss SSS}|%t|%p|%m|%F|%L|%n
			</pattern>
		</encoder>
		<tiggeringPolicy
			class="ch.qos.logback.core.rolling.SizeBasedTriggeringPolicy">
			<MaxFileSize>500MB</MaxFileSize>
		</tiggeringPolicy>
	</appender>
	<!-- 异步输出 -->
	<appender name="ASYNC_32" class="ch.qos.logback.classic.AsyncAppender">
		<!-- 不丢失日志.默认的,如果队列的80%已满,则会丢弃TRACT、DEBUG、INFO级别的日志 -->
		<discardingThreshold>0</discardingThreshold>
		<!-- 更改默认的队列的深度,该值会影响性能.默认值为256 -->
		<queueSize>256</queueSize>
		<!-- 输出文件以及行号信息需要 stacktrace 获取 callerdata，因为性能原因 logback 的 AsyncAppender
			默认是不记录类名和行号 -->
		<includeCallerData>true</includeCallerData>
		<!-- 避免线程阻塞 -->
		<neverBlock>true</neverBlock>
		<!-- 添加附加的appender,最多只能添加一个 -->
		<appender-ref ref="changeTotalPremAF" />
	</appender>
	<logger name="com.nci.tunan.pa.impl.renewal" level="debug">
		<!-- <appender-ref ref="stdout2" /> -->
		<appender-ref ref="ASYNC_32" />
	</logger>
	<!-- 核销日志 end -->

	<!-- 账户结息批处理日志 start -->
	<appender name="accountInterest"
		class="ch.qos.logback.core.rolling.RollingFileAppender">
		<Encoding>UTF-8</Encoding>
		<rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
			<FileNamePattern>./logstan/AccountInterestFile/logtest-%d{yyyy-M-d}.log
			</FileNamePattern>
			<MaxHistory>3</MaxHistory>
		</rollingPolicy>
		<encoder>
			<pattern>%d{yyyy-M-d HH:mm:ss SSS}|%t|%p|%m|%F|%L|%n
			</pattern>
		</encoder>
		<tiggeringPolicy
			class="ch.qos.logback.core.rolling.SizeBasedTriggeringPolicy">
			<MaxFileSize>500MB</MaxFileSize>
		</tiggeringPolicy>
	</appender>
	<!-- 异步输出 -->
	<appender name="ASYNC_33" class="ch.qos.logback.classic.AsyncAppender">
		<!-- 不丢失日志.默认的,如果队列的80%已满,则会丢弃TRACT、DEBUG、INFO级别的日志 -->
		<discardingThreshold>0</discardingThreshold>
		<!-- 更改默认的队列的深度,该值会影响性能.默认值为256 -->
		<queueSize>256</queueSize>
		<!-- 输出文件以及行号信息需要 stacktrace 获取 callerdata，因为性能原因 logback 的 AsyncAppender
			默认是不记录类名和行号 -->
		<includeCallerData>true</includeCallerData>
		<!-- 避免线程阻塞 -->
		<neverBlock>true</neverBlock>
		<!-- 添加附加的appender,最多只能添加一个 -->
		<appender-ref ref="accountInterest" />
	</appender>
	<logger name="com.nci.tunan.pa.batch.accountinterest" level="debug">
		<!-- <appender-ref ref="stdout2" /> -->
		<appender-ref ref="ASYNC_33" />
	</logger>
	<!-- 核销日志 end -->

	<!-- 核销批处理日志 start -->
	<appender name="renewcollect"
		class="ch.qos.logback.core.rolling.RollingFileAppender">
		<Encoding>UTF-8</Encoding>
		<rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
			<FileNamePattern>./logstan/RenewcollectFile/logtest-%d{yyyy-M-d}.log
			</FileNamePattern>
			<MaxHistory>3</MaxHistory>
		</rollingPolicy>
		<encoder>
			<pattern>%d{yyyy-M-d HH:mm:ss SSS}|%t|%p|%m|%F|%L|%n
			</pattern>
		</encoder>
		<tiggeringPolicy
			class="ch.qos.logback.core.rolling.SizeBasedTriggeringPolicy">
			<MaxFileSize>500MB</MaxFileSize>
		</tiggeringPolicy>
	</appender>
	<!-- 异步输出 -->
	<appender name="ASYNC_34" class="ch.qos.logback.classic.AsyncAppender">
		<!-- 不丢失日志.默认的,如果队列的80%已满,则会丢弃TRACT、DEBUG、INFO级别的日志 -->
		<discardingThreshold>0</discardingThreshold>
		<!-- 更改默认的队列的深度,该值会影响性能.默认值为256 -->
		<queueSize>256</queueSize>
		<!-- 输出文件以及行号信息需要 stacktrace 获取 callerdata，因为性能原因 logback 的 AsyncAppender
			默认是不记录类名和行号 -->
		<includeCallerData>true</includeCallerData>
		<!-- 避免线程阻塞 -->
		<neverBlock>true</neverBlock>
		<!-- 添加附加的appender,最多只能添加一个 -->
		<appender-ref ref="renewcollect" />
	</appender>
	<logger name="com.nci.tunan.pa.batch.renewcollect" level="debug">
		<!-- <appender-ref ref="stdout2" /> -->
		<appender-ref ref="ASYNC_34" />
	</logger>
	<!-- 核销批处理日志 end -->

	<!-- 保单余额批处理日志 start -->
	<appender name="paBalanceToPrem"
		class="ch.qos.logback.core.rolling.RollingFileAppender">
		<Encoding>UTF-8</Encoding>
		<rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
			<FileNamePattern>./logstan/PaBalanceToPremFile/logtest-%d{yyyy-M-d}.log
			</FileNamePattern>
			<MaxHistory>3</MaxHistory>
		</rollingPolicy>
		<encoder>
			<pattern>%d{yyyy-M-d HH:mm:ss SSS}|%t|%p|%m|%F|%L|%n
			</pattern>
		</encoder>
		<tiggeringPolicy
			class="ch.qos.logback.core.rolling.SizeBasedTriggeringPolicy">
			<MaxFileSize>500MB</MaxFileSize>
		</tiggeringPolicy>
	</appender>
	<!-- 异步输出 -->
	<appender name="ASYNC_35" class="ch.qos.logback.classic.AsyncAppender">
		<!-- 不丢失日志.默认的,如果队列的80%已满,则会丢弃TRACT、DEBUG、INFO级别的日志 -->
		<discardingThreshold>0</discardingThreshold>
		<!-- 更改默认的队列的深度,该值会影响性能.默认值为256 -->
		<queueSize>256</queueSize>
		<!-- 输出文件以及行号信息需要 stacktrace 获取 callerdata，因为性能原因 logback 的 AsyncAppender
			默认是不记录类名和行号 -->
		<includeCallerData>true</includeCallerData>
		<!-- 避免线程阻塞 -->
		<neverBlock>true</neverBlock>
		<!-- 添加附加的appender,最多只能添加一个 -->
		<appender-ref ref="paBalanceToPrem" />
	</appender>
	<logger name="com.nci.tunan.pa.batch.balancetransprem" level="debug">
		<!-- <appender-ref ref="stdout2" /> -->
		<appender-ref ref="ASYNC_35" />
	</logger>
	<!-- 保单余额日志 end -->


	<!-- 投资单位价格 日志 start -->
	<appender name="calInterestMargin"
		class="ch.qos.logback.core.rolling.RollingFileAppender">
		<Encoding>UTF-8</Encoding>
		<rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
			<FileNamePattern>./logstan/calInterestMarginFile/logtest-%d{yyyy-M-d}.log
			</FileNamePattern>
			<MaxHistory>3</MaxHistory>
		</rollingPolicy>
		<encoder>
			<pattern>%d{yyyy-M-d HH:mm:ss SSS}|%t|%p|%m|%F|%L|%n
			</pattern>
		</encoder>
		<tiggeringPolicy
			class="ch.qos.logback.core.rolling.SizeBasedTriggeringPolicy">
			<MaxFileSize>500MB</MaxFileSize>
		</tiggeringPolicy>
	</appender>
	<!-- 异步输出 -->
	<appender name="ASYNC_38" class="ch.qos.logback.classic.AsyncAppender">
		<!-- 不丢失日志.默认的,如果队列的80%已满,则会丢弃TRACT、DEBUG、INFO级别的日志 -->
		<discardingThreshold>0</discardingThreshold>
		<!-- 更改默认的队列的深度,该值会影响性能.默认值为256 -->
		<queueSize>256</queueSize>
		<!-- 输出文件以及行号信息需要 stacktrace 获取 callerdata，因为性能原因 logback 的 AsyncAppender
			默认是不记录类名和行号 -->
		<includeCallerData>true</includeCallerData>
		<!-- 避免线程阻塞 -->
		<neverBlock>true</neverBlock>
		<!-- 添加附加的appender,最多只能添加一个 -->
		<appender-ref ref="calInterestMargin" />
	</appender>
	<logger name="com.nci.tunan.pa.batch.calinterestmargin" level="debug">
		<!-- <appender-ref ref="stdout2" /> -->
		<appender-ref ref="ASYNC_38" />
	</logger>
	<!-- 投资单位价格 日志 end -->

	<!-- 更新客户风险等级日志start -->
	<appender name="updateCustRiskLevel"
		class="ch.qos.logback.core.rolling.RollingFileAppender">
		<Encoding>UTF-8</Encoding>
		<rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
			<FileNamePattern>./logstan/updateCustRiskLevel/logtest-%d{yyyy-M-d}.log
			</FileNamePattern>
			<MaxHistory>3</MaxHistory>
		</rollingPolicy>
		<encoder>
			<pattern>%d{yyyy-M-d HH:mm:ss SSS}|%t|%p|%m|%F|%L|%n
			</pattern>
		</encoder>
		<tiggeringPolicy
			class="ch.qos.logback.core.rolling.SizeBasedTriggeringPolicy">
			<MaxFileSize>500MB</MaxFileSize>
		</tiggeringPolicy>
	</appender>
	<!-- 异步输出 -->
	<appender name="ASYNC_39" class="ch.qos.logback.classic.AsyncAppender">
		<!-- 不丢失日志.默认的,如果队列的80%已满,则会丢弃TRACT、DEBUG、INFO级别的日志 -->
		<discardingThreshold>0</discardingThreshold>
		<!-- 更改默认的队列的深度,该值会影响性能.默认值为256 -->
		<queueSize>256</queueSize>
		<!-- 输出文件以及行号信息需要 stacktrace 获取 callerdata，因为性能原因 logback 的 AsyncAppender
			默认是不记录类名和行号 -->
		<includeCallerData>true</includeCallerData>
		<!-- 避免线程阻塞 -->
		<neverBlock>true</neverBlock>
		<!-- 添加附加的appender,最多只能添加一个 -->
		<appender-ref ref="updateCustRiskLevel" />
	</appender>
	<logger name="com.nci.tunan.pa.batch.updatecustrisklevel" level="debug">
		<!-- <appender-ref ref="stdout2" /> -->
		<appender-ref ref="ASYNC_39" />
	</logger>
	<!-- 更新客户风险等级日志end -->

	<!-- 投连万能失效批处理日志 start -->
	<appender name="iLPULapseJobFile"
		class="ch.qos.logback.core.rolling.RollingFileAppender">
		<Encoding>UTF-8</Encoding>
		<rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
			<FileNamePattern>./logstan/iLPULapseJobFile/logtest-%d{yyyy-M-d}.log
			</FileNamePattern>
			<MaxHistory>3</MaxHistory>
		</rollingPolicy>
		<encoder>
			<pattern>%d{yyyy-M-d HH:mm:ss SSS}|%t|%p|%m|%F|%L|%n
			</pattern>
		</encoder>
		<tiggeringPolicy
			class="ch.qos.logback.core.rolling.SizeBasedTriggeringPolicy">
			<MaxFileSize>500MB</MaxFileSize>
		</tiggeringPolicy>
	</appender>
	<!-- 异步输出 -->
	<appender name="ASYNC_40" class="ch.qos.logback.classic.AsyncAppender">
		<!-- 不丢失日志.默认的,如果队列的80%已满,则会丢弃TRACT、DEBUG、INFO级别的日志 -->
		<discardingThreshold>0</discardingThreshold>
		<!-- 更改默认的队列的深度,该值会影响性能.默认值为256 -->
		<queueSize>256</queueSize>
		<!-- 输出文件以及行号信息需要 stacktrace 获取 callerdata，因为性能原因 logback 的 AsyncAppender
			默认是不记录类名和行号 -->
		<includeCallerData>true</includeCallerData>
		<!-- 避免线程阻塞 -->
		<neverBlock>true</neverBlock>
		<!-- 添加附加的appender,最多只能添加一个 -->
		<appender-ref ref="iLPULapseJobFile" />
	</appender>
	<logger name="com.nci.tunan.pa.batch.ilpulapse" level="debug">
		<!-- <appender-ref ref="stdout2" /> -->
		<appender-ref ref="ASYNC_40" />
	</logger>
	<!-- 投连万能失效批处理日志 end -->

	<!-- 普通失效批处理日志 start -->
	<appender name="normalLapseJobFile"
		class="ch.qos.logback.core.rolling.RollingFileAppender">
		<Encoding>UTF-8</Encoding>
		<rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
			<FileNamePattern>./logstan/normalLapseJobFile/logtest-%d{yyyy-M-d}.log
			</FileNamePattern>
			<MaxHistory>3</MaxHistory>
		</rollingPolicy>
		<encoder>
			<pattern>%d{yyyy-M-d HH:mm:ss SSS}|%t|%p|%m|%F|%L|%n
			</pattern>
		</encoder>
		<tiggeringPolicy
			class="ch.qos.logback.core.rolling.SizeBasedTriggeringPolicy">
			<MaxFileSize>500MB</MaxFileSize>
		</tiggeringPolicy>
	</appender>
	<!-- 异步输出 -->
	<appender name="ASYNC_41" class="ch.qos.logback.classic.AsyncAppender">
		<!-- 不丢失日志.默认的,如果队列的80%已满,则会丢弃TRACT、DEBUG、INFO级别的日志 -->
		<discardingThreshold>0</discardingThreshold>
		<!-- 更改默认的队列的深度,该值会影响性能.默认值为256 -->
		<queueSize>256</queueSize>
		<!-- 输出文件以及行号信息需要 stacktrace 获取 callerdata，因为性能原因 logback 的 AsyncAppender
			默认是不记录类名和行号 -->
		<includeCallerData>true</includeCallerData>
		<!-- 避免线程阻塞 -->
		<neverBlock>true</neverBlock>
		<!-- 添加附加的appender,最多只能添加一个 -->
		<appender-ref ref="normalLapseJobFile" />
	</appender>
	<logger name="com.nci.tunan.pa.batch.normallapse" level="debug">
		<!-- <appender-ref ref="stdout2" /> -->
		<appender-ref ref="ASYNC_41" />
	</logger>
	<!-- 普通失效批处理日志 end -->

	<!-- 续期续保短信 start -->
	<appender name="RenewalSMSJob"
		class="ch.qos.logback.core.rolling.RollingFileAppender">
		<Encoding>UTF-8</Encoding>
		<rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
			<FileNamePattern>./logstan/RenewalSMSJob/logtest-%d{yyyy-M-d}.log
			</FileNamePattern>
			<MaxHistory>3</MaxHistory>
		</rollingPolicy>
		<encoder>
			<pattern>%d{yyyy-M-d HH:mm:ss SSS}|%t|%p|%m|%F|%L|%n
			</pattern>
		</encoder>
		<tiggeringPolicy
			class="ch.qos.logback.core.rolling.SizeBasedTriggeringPolicy">
			<MaxFileSize>500MB</MaxFileSize>
		</tiggeringPolicy>
	</appender>
	<!-- 异步输出 -->
	<appender name="ASYNC_75" class="ch.qos.logback.classic.AsyncAppender">
		<!-- 不丢失日志.默认的,如果队列的80%已满,则会丢弃TRACT、DEBUG、INFO级别的日志 -->
		<discardingThreshold>0</discardingThreshold>
		<!-- 更改默认的队列的深度,该值会影响性能.默认值为256 -->
		<queueSize>256</queueSize>
		<!-- 输出文件以及行号信息需要 stacktrace 获取 callerdata，因为性能原因 logback 的 AsyncAppender
			默认是不记录类名和行号 -->
		<includeCallerData>true</includeCallerData>
		<!-- 避免线程阻塞 -->
		<neverBlock>true</neverBlock>
		<!-- 添加附加的appender,最多只能添加一个 -->
		<appender-ref ref="RenewalSMSJob" />
	</appender>
	<logger name="com.nci.tunan.pa.batch.renewalSMS" level="info">
		<!-- <appender-ref ref="stdout2" /> -->
		<appender-ref ref="ASYNC_75" />
	</logger>
	<!-- 续期续保短信end -->

	<!-- 孤儿单日志start -->
	<appender name="orphanpolicy"
		class="ch.qos.logback.core.rolling.RollingFileAppender">
		<Encoding>UTF-8</Encoding>
		<rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
			<FileNamePattern>./logstan/orphanpolicy/logtest-%d{yyyy-M-d}.log
			</FileNamePattern>
			<MaxHistory>3</MaxHistory>
		</rollingPolicy>
		<encoder>
			<pattern>%d{yyyy-M-d HH:mm:ss SSS}|%t|%p|%m|%F|%L|%n
			</pattern>
		</encoder>
		<tiggeringPolicy
			class="ch.qos.logback.core.rolling.SizeBasedTriggeringPolicy">
			<MaxFileSize>500MB</MaxFileSize>
		</tiggeringPolicy>
	</appender>
	<!-- 异步输出 -->
	<appender name="ASYNC_74" class="ch.qos.logback.classic.AsyncAppender">
		<!-- 不丢失日志.默认的,如果队列的80%已满,则会丢弃TRACT、DEBUG、INFO级别的日志 -->
		<discardingThreshold>0</discardingThreshold>
		<!-- 更改默认的队列的深度,该值会影响性能.默认值为256 -->
		<queueSize>256</queueSize>
		<!-- 输出文件以及行号信息需要 stacktrace 获取 callerdata，因为性能原因 logback 的 AsyncAppender
			默认是不记录类名和行号 -->
		<includeCallerData>true</includeCallerData>
		<!-- 避免线程阻塞 -->
		<neverBlock>true</neverBlock>
		<!-- 添加附加的appender,最多只能添加一个 -->
		<appender-ref ref="orphanpolicy" />
	</appender>
	<logger name="com.nci.tunan.pa.batch.orphanpolicy" level="info">
		<!-- <appender-ref ref="stdout2" /> -->
		<appender-ref ref="ASYNC_74" />
	</logger>
	<!-- 孤儿单日志end -->

	<!-- 邮件发送start -->
	<appender name="sendMailJobFile"
		class="ch.qos.logback.core.rolling.RollingFileAppender">
		<Encoding>UTF-8</Encoding>
		<rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
			<FileNamePattern>./logstan/sendMailJobFile/logtest-%d{yyyy-M-d}.log
			</FileNamePattern>
			<MaxHistory>3</MaxHistory>
		</rollingPolicy>
		<encoder>
			<pattern>%d{yyyy-M-d HH:mm:ss SSS}|%t|%p|%m|%F|%L|%n
			</pattern>
		</encoder>
		<tiggeringPolicy
			class="ch.qos.logback.core.rolling.SizeBasedTriggeringPolicy">
			<MaxFileSize>500MB</MaxFileSize>
		</tiggeringPolicy>
	</appender>
	<!-- 异步输出 -->
	<appender name="ASYNC_42" class="ch.qos.logback.classic.AsyncAppender">
		<!-- 不丢失日志.默认的,如果队列的80%已满,则会丢弃TRACT、DEBUG、INFO级别的日志 -->
		<discardingThreshold>0</discardingThreshold>
		<!-- 更改默认的队列的深度,该值会影响性能.默认值为256 -->
		<queueSize>256</queueSize>
		<!-- 输出文件以及行号信息需要 stacktrace 获取 callerdata，因为性能原因 logback 的 AsyncAppender
			默认是不记录类名和行号 -->
		<includeCallerData>true</includeCallerData>
		<!-- 避免线程阻塞 -->
		<neverBlock>true</neverBlock>
		<!-- 添加附加的appender,最多只能添加一个 -->
		<appender-ref ref="sendMailJobFile" />
	</appender>
	<logger name="com.nci.tunan.pa.batch.sendmail" level="debug">
		<!-- <appender-ref ref="stdout2" /> -->
		<appender-ref ref="ASYNC_42" />
	</logger>
	<!-- 保单邮件发送（snedMail）end -->

	<!-- 短信发送start -->
	<appender name="sendNoteJobFile"
		class="ch.qos.logback.core.rolling.RollingFileAppender">
		<Encoding>UTF-8</Encoding>
		<rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
			<FileNamePattern>./logstan/sendNoteJobFile/logtest-%d{yyyy-M-d}.log
			</FileNamePattern>
			<MaxHistory>3</MaxHistory>
		</rollingPolicy>
		<encoder>
			<pattern>%d{yyyy-M-d HH:mm:ss SSS}|%t|%p|%m|%F|%L|%n
			</pattern>
		</encoder>
		<tiggeringPolicy
			class="ch.qos.logback.core.rolling.SizeBasedTriggeringPolicy">
			<MaxFileSize>500MB</MaxFileSize>
		</tiggeringPolicy>
	</appender>
	<!-- 异步输出 -->
	<appender name="ASYNC_43" class="ch.qos.logback.classic.AsyncAppender">
		<!-- 不丢失日志.默认的,如果队列的80%已满,则会丢弃TRACT、DEBUG、INFO级别的日志 -->
		<discardingThreshold>0</discardingThreshold>
		<!-- 更改默认的队列的深度,该值会影响性能.默认值为256 -->
		<queueSize>256</queueSize>
		<!-- 输出文件以及行号信息需要 stacktrace 获取 callerdata，因为性能原因 logback 的 AsyncAppender
			默认是不记录类名和行号 -->
		<includeCallerData>true</includeCallerData>
		<!-- 避免线程阻塞 -->
		<neverBlock>true</neverBlock>
		<!-- 添加附加的appender,最多只能添加一个 -->
		<appender-ref ref="sendNoteJobFile" />
	</appender>
	<logger name="com.nci.tunan.pa.batch.sendnote" level="debug">
		<!-- <appender-ref ref="stdout2" /> -->
		<appender-ref ref="ASYNC_43" />
	</logger>
	<!-- 保单短信发送（snedNote）end -->

	<!-- 客户证件过期批处理日志 start -->
	<appender name="CertiExpiredNoteFile"
		class="ch.qos.logback.core.rolling.RollingFileAppender">
		<Encoding>UTF-8</Encoding>
		<rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
			<FileNamePattern>./logstan/policymanagementFile/logtest-%d{yyyy-M-d}.log
			</FileNamePattern>
			<MaxHistory>3</MaxHistory>
		</rollingPolicy>
		<encoder>
			<pattern>%d{yyyy-M-d HH:mm:ss SSS}|%t|%p|%m|%F|%L|%n
			</pattern>
		</encoder>
		<tiggeringPolicy
			class="ch.qos.logback.core.rolling.SizeBasedTriggeringPolicy">
			<MaxFileSize>500MB</MaxFileSize>
		</tiggeringPolicy>
	</appender>
	<!-- 异步输出 -->
	<appender name="ASYNC_44" class="ch.qos.logback.classic.AsyncAppender">
		<!-- 不丢失日志.默认的,如果队列的80%已满,则会丢弃TRACT、DEBUG、INFO级别的日志 -->
		<discardingThreshold>0</discardingThreshold>
		<!-- 更改默认的队列的深度,该值会影响性能.默认值为256 -->
		<queueSize>256</queueSize>
		<!-- 输出文件以及行号信息需要 stacktrace 获取 callerdata，因为性能原因 logback 的 AsyncAppender
			默认是不记录类名和行号 -->
		<includeCallerData>true</includeCallerData>
		<!-- 避免线程阻塞 -->
		<neverBlock>true</neverBlock>
		<!-- 添加附加的appender,最多只能添加一个 -->
		<appender-ref ref="CertiExpiredNoteFile" />
	</appender>
	<logger name="com.nci.tunan.pa.batch.sendnote" level="debug">
		<!-- <appender-ref ref="stdout2" /> -->
		<appender-ref ref="ASYNC_44" />
	</logger>
	<!-- 客户证件过期批处理日志 end -->

	<!-- 续期保单短信提醒批处理日志 start -->
	<appender name="renewalMissionByChannelJobFile"
		class="ch.qos.logback.core.rolling.RollingFileAppender">
		<Encoding>UTF-8</Encoding>
		<rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
			<FileNamePattern>./logstan/renewalMissionByChannelJobFile/logtest-%d{yyyy-M-d}.log
			</FileNamePattern>
			<MaxHistory>3</MaxHistory>
		</rollingPolicy>
		<encoder>
			<pattern>%d{yyyy-M-d HH:mm:ss SSS}|%t|%p|%m|%F|%L|%n
			</pattern>
		</encoder>
		<tiggeringPolicy
			class="ch.qos.logback.core.rolling.SizeBasedTriggeringPolicy">
			<MaxFileSize>500MB</MaxFileSize>
		</tiggeringPolicy>
	</appender>
	<!-- 异步输出 -->
	<appender name="ASYNC_45" class="ch.qos.logback.classic.AsyncAppender">
		<!-- 不丢失日志.默认的,如果队列的80%已满,则会丢弃TRACT、DEBUG、INFO级别的日志 -->
		<discardingThreshold>0</discardingThreshold>
		<!-- 更改默认的队列的深度,该值会影响性能.默认值为256 -->
		<queueSize>256</queueSize>
		<!-- 输出文件以及行号信息需要 stacktrace 获取 callerdata，因为性能原因 logback 的 AsyncAppender
			默认是不记录类名和行号 -->
		<includeCallerData>true</includeCallerData>
		<!-- 避免线程阻塞 -->
		<neverBlock>true</neverBlock>
		<!-- 添加附加的appender,最多只能添加一个 -->
		<appender-ref ref="renewalMissionByChannelJobFile" />
	</appender>
	<logger name="com.nci.tunan.pa.batch.sendnote" level="debug">
		<!-- <appender-ref ref="stdout2" /> -->
		<appender-ref ref="ASYNC_45" />
	</logger>
	<!-- 续期保单短信提醒批处理日志 end -->


	<!-- 满期终止批处理日志 start -->
	<appender name="maturityJobFile"
		class="ch.qos.logback.core.rolling.RollingFileAppender">
		<Encoding>UTF-8</Encoding>
		<rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
			<FileNamePattern>./logstan/maturityJobFile/logtest-%d{yyyy-M-d}.log
			</FileNamePattern>
			<MaxHistory>3</MaxHistory>
		</rollingPolicy>
		<encoder>
			<pattern>%d{yyyy-M-d HH:mm:ss SSS}|%t|%p|%m|%F|%L|%n
			</pattern>
		</encoder>
		<tiggeringPolicy
			class="ch.qos.logback.core.rolling.SizeBasedTriggeringPolicy">
			<MaxFileSize>500MB</MaxFileSize>
		</tiggeringPolicy>
	</appender>
	<!-- 异步输出 -->
	<appender name="ASYNC_46" class="ch.qos.logback.classic.AsyncAppender">
		<!-- 不丢失日志.默认的,如果队列的80%已满,则会丢弃TRACT、DEBUG、INFO级别的日志 -->
		<discardingThreshold>0</discardingThreshold>
		<!-- 更改默认的队列的深度,该值会影响性能.默认值为256 -->
		<queueSize>256</queueSize>
		<!-- 输出文件以及行号信息需要 stacktrace 获取 callerdata，因为性能原因 logback 的 AsyncAppender
			默认是不记录类名和行号 -->
		<includeCallerData>true</includeCallerData>
		<!-- 避免线程阻塞 -->
		<neverBlock>true</neverBlock>
		<!-- 添加附加的appender,最多只能添加一个 -->
		<appender-ref ref="maturityJobFile" />
	</appender>
	<logger name="com.nci.tunan.pa.batch.maturityoperation" level="debug">
		<!-- <appender-ref ref="stdout2" /> -->
		<appender-ref ref="ASYNC_46" />
	</logger>
	<!-- 满期终止批处理日志 end -->

	<!-- 查询保单列表 start -->
	<appender name="queryPolicyList"
		class="ch.qos.logback.core.rolling.RollingFileAppender">
		<Encoding>UTF-8</Encoding>
		<rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
			<FileNamePattern>./logstan/queryPolicyList/logtest-%d{yyyy-M-d}.log
			</FileNamePattern>
			<MaxHistory>3</MaxHistory>
		</rollingPolicy>
		<encoder>
			<pattern>%d{yyyy-M-d HH:mm:ss SSS}|%t|%p|%m|%F|%L|%n
			</pattern>
		</encoder>
		<tiggeringPolicy
			class="ch.qos.logback.core.rolling.SizeBasedTriggeringPolicy">
			<MaxFileSize>500MB</MaxFileSize>
		</tiggeringPolicy>
	</appender>
	<!-- 异步输出 -->
	<appender name="ASYNC_47" class="ch.qos.logback.classic.AsyncAppender">
		<!-- 不丢失日志.默认的,如果队列的80%已满,则会丢弃TRACT、DEBUG、INFO级别的日志 -->
		<discardingThreshold>0</discardingThreshold>
		<!-- 更改默认的队列的深度,该值会影响性能.默认值为256 -->
		<queueSize>256</queueSize>
		<!-- 输出文件以及行号信息需要 stacktrace 获取 callerdata，因为性能原因 logback 的 AsyncAppender
			默认是不记录类名和行号 -->
		<includeCallerData>true</includeCallerData>
		<!-- 避免线程阻塞 -->
		<neverBlock>true</neverBlock>
		<!-- 添加附加的appender,最多只能添加一个 -->
		<appender-ref ref="queryPolicyList" />
	</appender>
	<logger name="com.nci.tunan.pa.impl.querypolicylist" level="debug">
		<!-- <appender-ref ref="stdout2" /> -->
		<appender-ref ref="ASYNC_47" />
	</logger>
	<!-- 查询保单列表 end -->

	<!-- 查询出险人 start -->
	<appender name="queryinjured"
		class="ch.qos.logback.core.rolling.RollingFileAppender">
		<Encoding>UTF-8</Encoding>
		<rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
			<FileNamePattern>./logstan/queryinjured/logtest-%d{yyyy-M-d}.log
			</FileNamePattern>
			<MaxHistory>3</MaxHistory>
		</rollingPolicy>
		<encoder>
			<pattern>%d{yyyy-M-d HH:mm:ss SSS}|%t|%p|%m|%F|%L|%n
			</pattern>
		</encoder>
		<tiggeringPolicy
			class="ch.qos.logback.core.rolling.SizeBasedTriggeringPolicy">
			<MaxFileSize>500MB</MaxFileSize>
		</tiggeringPolicy>
	</appender>
	<!-- 异步输出 -->
	<appender name="ASYNC_48" class="ch.qos.logback.classic.AsyncAppender">
		<!-- 不丢失日志.默认的,如果队列的80%已满,则会丢弃TRACT、DEBUG、INFO级别的日志 -->
		<discardingThreshold>0</discardingThreshold>
		<!-- 更改默认的队列的深度,该值会影响性能.默认值为256 -->
		<queueSize>256</queueSize>
		<!-- 输出文件以及行号信息需要 stacktrace 获取 callerdata，因为性能原因 logback 的 AsyncAppender
			默认是不记录类名和行号 -->
		<includeCallerData>true</includeCallerData>
		<!-- 避免线程阻塞 -->
		<neverBlock>true</neverBlock>
		<!-- 添加附加的appender,最多只能添加一个 -->
		<appender-ref ref="queryinjured" />
	</appender>
	<logger name="com.nci.tunan.pa.impl.queryinjured" level="debug">
		<!-- <appender-ref ref="stdout2" /> -->
		<appender-ref ref="ASYNC_48" />
	</logger>
	<!-- 查询出险人 end -->

	<!-- 抄单start -->
	<appender name="queryPolicy"
		class="ch.qos.logback.core.rolling.RollingFileAppender">
		<Encoding>UTF-8</Encoding>
		<rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
			<FileNamePattern>./logstan/queryPolicy/logtest-%d{yyyy-M-d}.log
			</FileNamePattern>
			<MaxHistory>3</MaxHistory>
		</rollingPolicy>
		<encoder>
			<pattern>%d{yyyy-M-d HH:mm:ss SSS}|%t|%p|%m|%F|%L|%n
			</pattern>
		</encoder>
		<tiggeringPolicy
			class="ch.qos.logback.core.rolling.SizeBasedTriggeringPolicy">
			<MaxFileSize>500MB</MaxFileSize>
		</tiggeringPolicy>
	</appender>
	<!-- 异步输出 -->
	<appender name="ASYNC_49" class="ch.qos.logback.classic.AsyncAppender">
		<!-- 不丢失日志.默认的,如果队列的80%已满,则会丢弃TRACT、DEBUG、INFO级别的日志 -->
		<discardingThreshold>0</discardingThreshold>
		<!-- 更改默认的队列的深度,该值会影响性能.默认值为256 -->
		<queueSize>256</queueSize>
		<!-- 输出文件以及行号信息需要 stacktrace 获取 callerdata，因为性能原因 logback 的 AsyncAppender
			默认是不记录类名和行号 -->
		<includeCallerData>true</includeCallerData>
		<!-- 避免线程阻塞 -->
		<neverBlock>true</neverBlock>
		<!-- 添加附加的appender,最多只能添加一个 -->
		<appender-ref ref="queryPolicy" />
	</appender>
	<logger name="com.nci.tunan.pa.impl.querypolicy" level="debug">
		<!-- <appender-ref ref="stdout2" /> -->
		<appender-ref ref="ASYNC_49" />
	</logger>
	<!-- 抄单 end -->

	<!-- 欠缴保费接口start -->
	<appender name="debtPrem"
		class="ch.qos.logback.core.rolling.RollingFileAppender">
		<Encoding>UTF-8</Encoding>
		<rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
			<FileNamePattern>./logstan/debtPrem/logtest-%d{yyyy-M-d}.log
			</FileNamePattern>
			<MaxHistory>3</MaxHistory>
		</rollingPolicy>
		<encoder>
			<pattern>%d{yyyy-M-d HH:mm:ss SSS}|%t|%p|%m|%F|%L|%n
			</pattern>
		</encoder>
		<tiggeringPolicy
			class="ch.qos.logback.core.rolling.SizeBasedTriggeringPolicy">
			<MaxFileSize>500MB</MaxFileSize>
		</tiggeringPolicy>
	</appender>
	<!-- 异步输出 -->
	<appender name="ASYNC_50" class="ch.qos.logback.classic.AsyncAppender">
		<!-- 不丢失日志.默认的,如果队列的80%已满,则会丢弃TRACT、DEBUG、INFO级别的日志 -->
		<discardingThreshold>0</discardingThreshold>
		<!-- 更改默认的队列的深度,该值会影响性能.默认值为256 -->
		<queueSize>256</queueSize>
		<!-- 输出文件以及行号信息需要 stacktrace 获取 callerdata，因为性能原因 logback 的 AsyncAppender
			默认是不记录类名和行号 -->
		<includeCallerData>true</includeCallerData>
		<!-- 避免线程阻塞 -->
		<neverBlock>true</neverBlock>
		<!-- 添加附加的appender,最多只能添加一个 -->
		<appender-ref ref="debtPrem" />
	</appender>
	<logger name="com.nci.tunan.pa.impl.debtprem" level="debug">
		<!-- <appender-ref ref="stdout2" /> -->
		<appender-ref ref="ASYNC_50" />
	</logger>
	<!-- 欠缴保费接口 end -->

	<!-- 查询生存给付应领记录接口start -->
	<appender name="queryPayDue"
		class="ch.qos.logback.core.rolling.RollingFileAppender">
		<Encoding>UTF-8</Encoding>
		<rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
			<FileNamePattern>./logstan/queryPayDue/logtest-%d{yyyy-M-d}.log
			</FileNamePattern>
			<MaxHistory>3</MaxHistory>
		</rollingPolicy>
		<encoder>
			<pattern>%d{yyyy-M-d HH:mm:ss SSS}|%t|%p|%m|%F|%L|%n
			</pattern>
		</encoder>
		<tiggeringPolicy
			class="ch.qos.logback.core.rolling.SizeBasedTriggeringPolicy">
			<MaxFileSize>500MB</MaxFileSize>
		</tiggeringPolicy>
	</appender>
	<!-- 异步输出 -->
	<appender name="ASYNC_51" class="ch.qos.logback.classic.AsyncAppender">
		<!-- 不丢失日志.默认的,如果队列的80%已满,则会丢弃TRACT、DEBUG、INFO级别的日志 -->
		<discardingThreshold>0</discardingThreshold>
		<!-- 更改默认的队列的深度,该值会影响性能.默认值为256 -->
		<queueSize>256</queueSize>
		<!-- 输出文件以及行号信息需要 stacktrace 获取 callerdata，因为性能原因 logback 的 AsyncAppender
			默认是不记录类名和行号 -->
		<includeCallerData>true</includeCallerData>
		<!-- 避免线程阻塞 -->
		<neverBlock>true</neverBlock>
		<!-- 添加附加的appender,最多只能添加一个 -->
		<appender-ref ref="queryPayDue" />
	</appender>
	<logger name="com.nci.tunan.pa.impl.querypaydue" level="debug">
		<!-- <appender-ref ref="stdout2" /> -->
		<appender-ref ref="ASYNC_51" />
	</logger>
	<!-- 查询生存给付应领记录接口 end -->

	<!-- 查询生存给付应领记录接口start -->
	<appender name="operateQuery"
		class="ch.qos.logback.core.rolling.RollingFileAppender">
		<Encoding>UTF-8</Encoding>
		<rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
			<FileNamePattern>./logstan/operateQuery/logtest-%d{yyyy-M-d}.log
			</FileNamePattern>
			<MaxHistory>3</MaxHistory>
		</rollingPolicy>
		<encoder>
			<pattern>%d{yyyy-M-d HH:mm:ss SSS}|%t|%p|%m|%F|%L|%n
			</pattern>
		</encoder>
		<tiggeringPolicy
			class="ch.qos.logback.core.rolling.SizeBasedTriggeringPolicy">
			<MaxFileSize>500MB</MaxFileSize>
		</tiggeringPolicy>
	</appender>
	<!-- 异步输出 -->
	<appender name="ASYNC_52" class="ch.qos.logback.classic.AsyncAppender">
		<!-- 不丢失日志.默认的,如果队列的80%已满,则会丢弃TRACT、DEBUG、INFO级别的日志 -->
		<discardingThreshold>0</discardingThreshold>
		<!-- 更改默认的队列的深度,该值会影响性能.默认值为256 -->
		<queueSize>256</queueSize>
		<!-- 输出文件以及行号信息需要 stacktrace 获取 callerdata，因为性能原因 logback 的 AsyncAppender
			默认是不记录类名和行号 -->
		<includeCallerData>true</includeCallerData>
		<!-- 避免线程阻塞 -->
		<neverBlock>true</neverBlock>
		<!-- 添加附加的appender,最多只能添加一个 -->
		<appender-ref ref="operateQuery" />
	</appender>
	<logger name="com.nci.tunan.pa.impl.operatequery" level="debug">
		<!-- <appender-ref ref="stdout2" /> -->
		<appender-ref ref="ASYNC_52" />
	</logger>
	<!-- 查询生存给付应领记录接口 end -->


	<!-- 查询生存给付应领记录接口start -->
	<appender name="operateQuery"
		class="ch.qos.logback.core.rolling.RollingFileAppender">
		<Encoding>UTF-8</Encoding>
		<rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
			<FileNamePattern>./logstan/operateQuery/logtest-%d{yyyy-M-d}.log
			</FileNamePattern>
			<MaxHistory>3</MaxHistory>
		</rollingPolicy>
		<encoder>
			<pattern>%d{yyyy-M-d HH:mm:ss SSS}|%t|%p|%m|%F|%L|%n
			</pattern>
		</encoder>
		<tiggeringPolicy
			class="ch.qos.logback.core.rolling.SizeBasedTriggeringPolicy">
			<MaxFileSize>500MB</MaxFileSize>
		</tiggeringPolicy>
	</appender>
	<!-- 异步输出 -->
	<appender name="ASYNC_53" class="ch.qos.logback.classic.AsyncAppender">
		<!-- 不丢失日志.默认的,如果队列的80%已满,则会丢弃TRACT、DEBUG、INFO级别的日志 -->
		<discardingThreshold>0</discardingThreshold>
		<!-- 更改默认的队列的深度,该值会影响性能.默认值为256 -->
		<queueSize>256</queueSize>
		<!-- 输出文件以及行号信息需要 stacktrace 获取 callerdata，因为性能原因 logback 的 AsyncAppender
			默认是不记录类名和行号 -->
		<includeCallerData>true</includeCallerData>
		<!-- 避免线程阻塞 -->
		<neverBlock>true</neverBlock>
		<!-- 添加附加的appender,最多只能添加一个 -->
		<appender-ref ref="operateQuery" />
	</appender>
	<logger name="com.nci.tunan.pa.impl.operatequery" level="debug">
		<!-- <appender-ref ref="stdout2" /> -->
		<appender-ref ref="ASYNC_53" />
	</logger>
	<!-- 查询生存给付应领记录接口 end -->

	<!-- 账户价值接口start -->
	<appender name="accountCheck"
		class="ch.qos.logback.core.rolling.RollingFileAppender">
		<Encoding>UTF-8</Encoding>
		<rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
			<FileNamePattern>./logstan/accountCheck/logtest-%d{yyyy-M-d}.log
			</FileNamePattern>
			<MaxHistory>3</MaxHistory>
		</rollingPolicy>
		<encoder>
			<pattern>%d{yyyy-M-d HH:mm:ss SSS}|%t|%p|%m|%F|%L|%n
			</pattern>
		</encoder>
		<tiggeringPolicy
			class="ch.qos.logback.core.rolling.SizeBasedTriggeringPolicy">
			<MaxFileSize>500MB</MaxFileSize>
		</tiggeringPolicy>
	</appender>
	<!-- 异步输出 -->
	<appender name="ASYNC_54" class="ch.qos.logback.classic.AsyncAppender">
		<!-- 不丢失日志.默认的,如果队列的80%已满,则会丢弃TRACT、DEBUG、INFO级别的日志 -->
		<discardingThreshold>0</discardingThreshold>
		<!-- 更改默认的队列的深度,该值会影响性能.默认值为256 -->
		<queueSize>256</queueSize>
		<!-- 输出文件以及行号信息需要 stacktrace 获取 callerdata，因为性能原因 logback 的 AsyncAppender
			默认是不记录类名和行号 -->
		<includeCallerData>true</includeCallerData>
		<!-- 避免线程阻塞 -->
		<neverBlock>true</neverBlock>
		<!-- 添加附加的appender,最多只能添加一个 -->
		<appender-ref ref="accountCheck" />
	</appender>
	<logger name="com.nci.tunan.pa.impl.accountcheck" level="debug">
		<!-- <appender-ref ref="stdout2" /> -->
		<appender-ref ref="ASYNC_54" />
	</logger>
	<!-- 账户价值接口 end -->

	<!-- 账户价值接口start -->
	<appender name="guaranteePeriodParam"
		class="ch.qos.logback.core.rolling.RollingFileAppender">
		<Encoding>UTF-8</Encoding>
		<rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
			<FileNamePattern>./logstan/guaranteePeriodParam/logtest-%d{yyyy-M-d}.log
			</FileNamePattern>
			<MaxHistory>3</MaxHistory>
		</rollingPolicy>
		<encoder>
			<pattern>%d{yyyy-M-d HH:mm:ss SSS}|%t|%p|%m|%F|%L|%n
			</pattern>
		</encoder>
		<tiggeringPolicy
			class="ch.qos.logback.core.rolling.SizeBasedTriggeringPolicy">
			<MaxFileSize>500MB</MaxFileSize>
		</tiggeringPolicy>
	</appender>
	<!-- 异步输出 -->
	<appender name="ASYNC_55" class="ch.qos.logback.classic.AsyncAppender">
		<!-- 不丢失日志.默认的,如果队列的80%已满,则会丢弃TRACT、DEBUG、INFO级别的日志 -->
		<discardingThreshold>0</discardingThreshold>
		<!-- 更改默认的队列的深度,该值会影响性能.默认值为256 -->
		<queueSize>256</queueSize>
		<!-- 输出文件以及行号信息需要 stacktrace 获取 callerdata，因为性能原因 logback 的 AsyncAppender
			默认是不记录类名和行号 -->
		<includeCallerData>true</includeCallerData>
		<!-- 避免线程阻塞 -->
		<neverBlock>true</neverBlock>
		<!-- 添加附加的appender,最多只能添加一个 -->
		<appender-ref ref="guaranteePeriodParam" />
	</appender>
	<logger name="com.nci.tunan.pa.impl.guaranteePeriod" level="debug">
		<!-- <appender-ref ref="stdout2" /> -->
		<appender-ref ref="ASYNC_55" />
	</logger>
	<!-- 账户价值接口 end -->


	<!-- 理赔查询保单信息接口start -->
	<appender name="claimQueryPolicydebug"
		class="ch.qos.logback.core.rolling.RollingFileAppender">
		<Encoding>UTF-8</Encoding>
		<rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
			<FileNamePattern>./logstan/claimQueryPolicydebug/logtest-%d{yyyy-M-d}.log
			</FileNamePattern>
			<MaxHistory>3</MaxHistory>
		</rollingPolicy>
		<encoder>
			<pattern>%d{yyyy-M-d HH:mm:ss SSS}|%t|%p|%m|%F|%L|%n
			</pattern>
		</encoder>
		<tiggeringPolicy
			class="ch.qos.logback.core.rolling.SizeBasedTriggeringPolicy">
			<MaxFileSize>500MB</MaxFileSize>
		</tiggeringPolicy>
	</appender>
	<!-- 异步输出 -->
	<appender name="ASYNC_56" class="ch.qos.logback.classic.AsyncAppender">
		<!-- 不丢失日志.默认的,如果队列的80%已满,则会丢弃TRACT、DEBUG、INFO级别的日志 -->
		<discardingThreshold>0</discardingThreshold>
		<!-- 更改默认的队列的深度,该值会影响性能.默认值为256 -->
		<queueSize>256</queueSize>
		<!-- 输出文件以及行号信息需要 stacktrace 获取 callerdata，因为性能原因 logback 的 AsyncAppender
			默认是不记录类名和行号 -->
		<includeCallerData>true</includeCallerData>
		<!-- 避免线程阻塞 -->
		<neverBlock>true</neverBlock>
		<!-- 添加附加的appender,最多只能添加一个 -->
		<appender-ref ref="claimQueryPolicydebug" />
	</appender>
	<logger name="com.nci.tunan.pa.impl.claimquerypolicydebug"
		level="debug">
		<!-- <appender-ref ref="stdout2" /> -->
		<appender-ref ref="ASYNC_56" />
	</logger>
	<!-- 理赔查询保单信息接口 end -->

	<!-- 查询风险保额接口 -->
	<appender name="queryRisk"
		class="ch.qos.logback.core.rolling.RollingFileAppender">
		<Encoding>UTF-8</Encoding>
		<rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
			<FileNamePattern>./logstan/queryRiskDetail/logtest-%d{yyyy-M-d}.log
			</FileNamePattern>
			<MaxHistory>3</MaxHistory>
		</rollingPolicy>
		<encoder>
			<pattern>%d{yyyy-M-d HH:mm:ss SSS}|%t|%p|%m|%F|%L|%n
			</pattern>
		</encoder>
		<tiggeringPolicy
			class="ch.qos.logback.core.rolling.SizeBasedTriggeringPolicy">
			<MaxFileSize>500MB</MaxFileSize>
		</tiggeringPolicy>
	</appender>
	<!-- 异步输出 -->
	<appender name="ASYNC_57" class="ch.qos.logback.classic.AsyncAppender">
		<!-- 不丢失日志.默认的,如果队列的80%已满,则会丢弃TRACT、DEBUG、INFO级别的日志 -->
		<discardingThreshold>0</discardingThreshold>
		<!-- 更改默认的队列的深度,该值会影响性能.默认值为256 -->
		<queueSize>256</queueSize>
		<!-- 输出文件以及行号信息需要 stacktrace 获取 callerdata，因为性能原因 logback 的 AsyncAppender
			默认是不记录类名和行号 -->
		<includeCallerData>true</includeCallerData>
		<!-- 避免线程阻塞 -->
		<neverBlock>true</neverBlock>
		<!-- 添加附加的appender,最多只能添加一个 -->
		<appender-ref ref="queryRisk" />
	</appender>
	<logger name="com.nci.tunan.pa.impl.risk" level="debug">
		<!-- <appender-ref ref="stdout2" /> -->
		<appender-ref ref="ASYNC_57" />
	</logger>
	<!-- 查询风险保额接口 -->

	<!-- 理赔结案接口start -->
	<appender name="claimSettlement"
		class="ch.qos.logback.core.rolling.RollingFileAppender">
		<Encoding>UTF-8</Encoding>
		<rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
			<FileNamePattern>./logstan/claimSettlement/logtest-%d{yyyy-M-d}.log
			</FileNamePattern>
			<MaxHistory>3</MaxHistory>
		</rollingPolicy>
		<encoder>
			<pattern>%d{yyyy-M-d HH:mm:ss SSS}|%t|%p|%m|%F|%L|%n
			</pattern>
		</encoder>
		<tiggeringPolicy
			class="ch.qos.logback.core.rolling.SizeBasedTriggeringPolicy">
			<MaxFileSize>500MB</MaxFileSize>
		</tiggeringPolicy>
	</appender>
	<!-- 异步输出 -->
	<appender name="ASYNC_58" class="ch.qos.logback.classic.AsyncAppender">
		<!-- 不丢失日志.默认的,如果队列的80%已满,则会丢弃TRACT、DEBUG、INFO级别的日志 -->
		<discardingThreshold>0</discardingThreshold>
		<!-- 更改默认的队列的深度,该值会影响性能.默认值为256 -->
		<queueSize>256</queueSize>
		<!-- 输出文件以及行号信息需要 stacktrace 获取 callerdata，因为性能原因 logback 的 AsyncAppender
			默认是不记录类名和行号 -->
		<includeCallerData>true</includeCallerData>
		<!-- 避免线程阻塞 -->
		<neverBlock>true</neverBlock>
		<!-- 添加附加的appender,最多只能添加一个 -->
		<appender-ref ref="claimSettlement" />
	</appender>
	<logger name="com.nci.tunan.pa.impl.claimsettlement" level="debug">
		<!-- <appender-ref ref="stdout2" /> -->
		<appender-ref ref="ASYNC_58" />
	</logger>
	<!-- 理赔结案接口 end -->

	<!-- 新续期抽挡批处理start -->
	<appender name="newRenewAutomaticExtra"
		class="ch.qos.logback.core.rolling.RollingFileAppender">
		<Encoding>UTF-8</Encoding>
		<rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
			<FileNamePattern>./logstan/newRenewAutomaticExtra/logtest-%d{yyyy-M-d}.log
			</FileNamePattern>
			<MaxHistory>3</MaxHistory>
		</rollingPolicy>
		<encoder>
			<pattern>%d{yyyy-M-d HH:mm:ss SSS}|%t|%p|%m|%F|%L|%n
			</pattern>
		</encoder>
		<tiggeringPolicy
			class="ch.qos.logback.core.rolling.SizeBasedTriggeringPolicy">
			<MaxFileSize>500MB</MaxFileSize>
		</tiggeringPolicy>
	</appender>
	<!-- 异步输出 -->
	<appender name="ASYNC_59" class="ch.qos.logback.classic.AsyncAppender">
		<!-- 不丢失日志.默认的,如果队列的80%已满,则会丢弃TRACT、DEBUG、INFO级别的日志 -->
		<discardingThreshold>0</discardingThreshold>
		<!-- 更改默认的队列的深度,该值会影响性能.默认值为256 -->
		<queueSize>256</queueSize>
		<!-- 输出文件以及行号信息需要 stacktrace 获取 callerdata，因为性能原因 logback 的 AsyncAppender
			默认是不记录类名和行号 -->
		<includeCallerData>true</includeCallerData>
		<!-- 避免线程阻塞 -->
		<neverBlock>true</neverBlock>
		<!-- 添加附加的appender,最多只能添加一个 -->
		<appender-ref ref="newRenewAutomaticExtra" />
	</appender>
	<logger name="com.nci.tunan.pa.batch.automaticextraTest" level="DEBUG">
		<!-- <appender-ref ref="stdout2" /> -->
		<appender-ref ref="ASYNC_59" />
	</logger>
	<!-- 新续期抽挡批处理end -->


	<!-- 理赔结案接口start -->
	<appender name="investValue"
		class="ch.qos.logback.core.rolling.RollingFileAppender">
		<Encoding>UTF-8</Encoding>
		<rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
			<FileNamePattern>./logstan/investValue/logtest-%d{yyyy-M-d}.log
			</FileNamePattern>
			<MaxHistory>3</MaxHistory>
		</rollingPolicy>
		<encoder>
			<pattern>%d{yyyy-M-d HH:mm:ss SSS}|%t|%p|%m|%F|%L|%n
			</pattern>
		</encoder>
		<tiggeringPolicy
			class="ch.qos.logback.core.rolling.SizeBasedTriggeringPolicy">
			<MaxFileSize>500MB</MaxFileSize>
		</tiggeringPolicy>
	</appender>
	<!-- 异步输出 -->
	<appender name="ASYNC_60" class="ch.qos.logback.classic.AsyncAppender">
		<!-- 不丢失日志.默认的,如果队列的80%已满,则会丢弃TRACT、DEBUG、INFO级别的日志 -->
		<discardingThreshold>0</discardingThreshold>
		<!-- 更改默认的队列的深度,该值会影响性能.默认值为256 -->
		<queueSize>256</queueSize>
		<!-- 输出文件以及行号信息需要 stacktrace 获取 callerdata，因为性能原因 logback 的 AsyncAppender
			默认是不记录类名和行号 -->
		<includeCallerData>true</includeCallerData>
		<!-- 避免线程阻塞 -->
		<neverBlock>true</neverBlock>
		<!-- 添加附加的appender,最多只能添加一个 -->
		<appender-ref ref="investValue" />
	</appender>
	<logger name="com.nci.tunan.pa.impl.investvalue" level="debug">
		<!-- <appender-ref ref="stdout2" /> -->
		<appender-ref ref="ASYNC_60" />
	</logger>
	<!-- 理赔结案接口 end -->

	<!-- 自动核销接口start -->
	<appender name="autoCollection"
		class="ch.qos.logback.core.rolling.RollingFileAppender">
		<Encoding>UTF-8</Encoding>
		<rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
			<FileNamePattern>./logstan/autoCollectionFile/logtest-%d{yyyy-M-d}.log
			</FileNamePattern>
			<MaxHistory>3</MaxHistory>
		</rollingPolicy>
		<encoder>
			<pattern>%d{yyyy-M-d HH:mm:ss SSS}|%t|%p|%m|%F|%L|%n
			</pattern>
		</encoder>
		<tiggeringPolicy
			class="ch.qos.logback.core.rolling.SizeBasedTriggeringPolicy">
			<MaxFileSize>500MB</MaxFileSize>
		</tiggeringPolicy>
	</appender>
	<!-- 异步输出 -->
	<appender name="ASYNC_61" class="ch.qos.logback.classic.AsyncAppender">
		<!-- 不丢失日志.默认的,如果队列的80%已满,则会丢弃TRACT、DEBUG、INFO级别的日志 -->
		<discardingThreshold>0</discardingThreshold>
		<!-- 更改默认的队列的深度,该值会影响性能.默认值为256 -->
		<queueSize>256</queueSize>
		<!-- 输出文件以及行号信息需要 stacktrace 获取 callerdata，因为性能原因 logback 的 AsyncAppender
			默认是不记录类名和行号 -->
		<includeCallerData>true</includeCallerData>
		<!-- 避免线程阻塞 -->
		<neverBlock>true</neverBlock>
		<!-- 添加附加的appender,最多只能添加一个 -->
		<appender-ref ref="autoCollection" />
	</appender>
	<logger name="com.nci.tunan.pa.impl.autoCollection" level="debug">
		<!-- <appender-ref ref="stdout2" /> -->
		<appender-ref ref="ASYNC_61" />
	</logger>
	<!-- 自动核销接口 end -->

	<!-- 保单打印接口start -->
	<appender name="policyPrint"
		class="ch.qos.logback.core.rolling.RollingFileAppender">
		<Encoding>UTF-8</Encoding>
		<rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
			<FileNamePattern>./logstan/policyPrint/logtest-%d{yyyy-M-d}.log
			</FileNamePattern>
			<MaxHistory>3</MaxHistory>
		</rollingPolicy>
		<encoder>
			<pattern>%d{yyyy-M-d HH:mm:ss SSS}|%t|%p|%m|%F|%L|%n
			</pattern>
		</encoder>
		<tiggeringPolicy
			class="ch.qos.logback.core.rolling.SizeBasedTriggeringPolicy">
			<MaxFileSize>500MB</MaxFileSize>
		</tiggeringPolicy>
	</appender>
	<!-- 异步输出 -->
	<appender name="ASYNC_64" class="ch.qos.logback.classic.AsyncAppender">
		<!-- 不丢失日志.默认的,如果队列的80%已满,则会丢弃TRACT、DEBUG、INFO级别的日志 -->
		<discardingThreshold>0</discardingThreshold>
		<!-- 更改默认的队列的深度,该值会影响性能.默认值为256 -->
		<queueSize>256</queueSize>
		<!-- 输出文件以及行号信息需要 stacktrace 获取 callerdata，因为性能原因 logback 的 AsyncAppender
			默认是不记录类名和行号 -->
		<includeCallerData>true</includeCallerData>
		<!-- 避免线程阻塞 -->
		<neverBlock>true</neverBlock>
		<!-- 添加附加的appender,最多只能添加一个 -->
		<appender-ref ref="policyPrint" />
	</appender>
	<logger name="com.nci.tunan.pa.impl.queryforprint" level="debug">
		<!-- <appender-ref ref="stdout2" /> -->
		<appender-ref ref="ASYNC_64" />
	</logger>
	<!-- 保单打印接口end -->

	<!-- 接入渠道接口日志 start -->
	<appender name="interfaceforchannel"
		class="ch.qos.logback.core.rolling.RollingFileAppender">
		<Encoding>UTF-8</Encoding>
		<rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
			<FileNamePattern>./logstan/interfaceforchannel/logtest-%d{yyyy-M-d}.log
			</FileNamePattern>
			<MaxHistory>3</MaxHistory>
		</rollingPolicy>
		<encoder>
			<pattern>%d{yyyy-M-d HH:mm:ss SSS}|%t|%p|%m|%F|%L|%n
			</pattern>
		</encoder>
		<tiggeringPolicy
			class="ch.qos.logback.core.rolling.SizeBasedTriggeringPolicy">
			<MaxFileSize>500MB</MaxFileSize>
		</tiggeringPolicy>
	</appender>
	<!-- 异步输出 -->
	<appender name="ASYNC_72" class="ch.qos.logback.classic.AsyncAppender">
		<!-- 不丢失日志.默认的,如果队列的80%已满,则会丢弃TRACT、DEBUG、INFO级别的日志 -->
		<discardingThreshold>0</discardingThreshold>
		<!-- 更改默认的队列的深度,该值会影响性能.默认值为256 -->
		<queueSize>256</queueSize>
		<!-- 输出文件以及行号信息需要 stacktrace 获取 callerdata，因为性能原因 logback 的 AsyncAppender
			默认是不记录类名和行号 -->
		<includeCallerData>true</includeCallerData>
		<!-- 避免线程阻塞 -->
		<neverBlock>true</neverBlock>
		<!-- 添加附加的appender,最多只能添加一个 -->
		<appender-ref ref="interfaceforchannel" />
	</appender>
	<logger name="com.nci.tunan.pa.impl.interfaceforchannel" level="info">
		<appender-ref ref="ASYNC_72" />
	</logger>
	<!-- 接入渠道接口日志 end -->

	<!-- ######################################保单日志输出 end###################################### -->



	<!-- ############################ 以下为全局log文件，勿动，add by liangpl start ############################ -->
	<!-- 日志输出到文件 -->
	<appender name="file"
		class="ch.qos.logback.core.rolling.RollingFileAppender">
		<!--控制日志安全写入文件 -->
		<prudent>true</prudent>
		<!-- 基于时间的滚动输出 -->
		<rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
			<FileNamePattern>./logs/pas-web-%d{yyyy-M-d}-%i.log</FileNamePattern>
			<MaxHistory>3</MaxHistory>
			<TimeBasedFileNamingAndTriggeringPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedFNATP">
            	<MaxFileSize>500MB</MaxFileSize>
            </TimeBasedFileNamingAndTriggeringPolicy>
		</rollingPolicy>
		<!--添加控制log文件大小 -->
		<tiggeringPolicy
			class="ch.qos.logback.core.rolling.SizeBasedTriggeringPolicy">
			<MaxFileSize>500MB</MaxFileSize>
		</tiggeringPolicy>
		<!--输出格式的配置，同控制台输出 -->
		<encoder>
			<pattern>%d{yyyy-M-d HH:mm:ss SSS}|%t|%p|%m|%F|%L|%n
			</pattern>
		</encoder>
	</appender>
	<!-- 异步输出 -->
	<appender name="ASYNC_ROOT" class="ch.qos.logback.classic.AsyncAppender">
		<!-- 不丢失日志.默认的,如果队列的80%已满,则会丢弃TRACT、DEBUG、INFO级别的日志 -->
		<discardingThreshold>0</discardingThreshold>
		<!-- 更改默认的队列的深度,该值会影响性能.默认值为256 -->
		<queueSize>1024</queueSize>
		<!-- 输出文件以及行号信息需要 stacktrace 获取 callerdata，因为性能原因 logback 的 AsyncAppender
			默认是不记录类名和行号 -->
		<includeCallerData>true</includeCallerData>
		<!-- 避免线程阻塞 -->
		<neverBlock>true</neverBlock>
		<!-- 添加附加的appender,最多只能添加一个 -->
		<appender-ref ref="file" />
	</appender>


	<!-- log4spring 过滤框架打印的大量debug信息 -->
	<logger name="org.mybatis.spring.SqlSessionFactoryBean" level="warn" />
	<!-- log4struts 过滤框架打印的大量debug信息 -->
	<logger name="com.opensymphony.xwork2" level="warn" />
	<logger name="org.apache.struts2" level="warn" />

	<logger name="com.nci.tunan.cs" level="info" additivity="false">
		<appender-ref ref="ASYNC_ROOT" />
	</logger>
	<logger name="com.nci.tunan.pa" level="info"  additivity="false">
		<appender-ref ref="ASYNC_ROOT" />
	</logger>
	<root level="info">
		<!-- 输出到控制台 -->
		<appender-ref ref="stdout2" />
		<!-- 输出到文件 -->
		<appender-ref ref="ASYNC_ROOT" />
	</root>

	<!-- ############################ 以下为全局log文件，勿动，add by liangpl end ############################ -->


	<!-- ######################################业务公共日志输出 start###################################### -->

	<!-- 业务公共客户相关 start -->
	<!--
	<appender name="commoncustomer"
		class="ch.qos.logback.core.rolling.RollingFileAppender">
		<Encoding>UTF-8</Encoding>
		<rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
			<FileNamePattern>./logstan/commoncustomer/logtest-%d{yyyy-M-d}.log
			</FileNamePattern>
			<MaxHistory>3</MaxHistory>
		</rollingPolicy>
		<encoder>
			<pattern>%d{yyyy-M-d HH:mm:ss SSS}|%t|%p|%m|%F|%L|%n
			</pattern>
		</encoder>
		<tiggeringPolicy
			class="ch.qos.logback.core.rolling.SizeBasedTriggeringPolicy">
			<MaxFileSize>500MB</MaxFileSize>
		</tiggeringPolicy>
	</appender>
	-->
	<!-- 异步输出 -->
	<appender name="ASYNC_65" class="ch.qos.logback.classic.AsyncAppender">
		<!-- 不丢失日志.默认的,如果队列的80%已满,则会丢弃TRACT、DEBUG、INFO级别的日志 -->
		<discardingThreshold>0</discardingThreshold>
		<!-- 更改默认的队列的深度,该值会影响性能.默认值为256 -->
		<queueSize>256</queueSize>
		<!-- 输出文件以及行号信息需要 stacktrace 获取 callerdata，因为性能原因 logback 的 AsyncAppender
			默认是不记录类名和行号 -->
		<includeCallerData>true</includeCallerData>
		<!-- 避免线程阻塞 -->
		<neverBlock>true</neverBlock>
		<!-- 添加附加的appender,最多只能添加一个 -->
		<appender-ref ref="commoncustomer" />
	</appender>
	<logger name="com.nci.core.common.impl.customer" level="DEBUG">
		<!-- <appender-ref ref="stdout2" /> -->
		<appender-ref ref="ASYNC_65" />
	</logger>
	<!-- 业务公共客户相关 end -->
	<!-- ######################################业务公共日志输出 end###################################### -->

	<!-- ######################################理赔日志输出 start###################################### -->

	<!-- 匹配理算日志 start -->
	<appender name="matchCalc"
		class="ch.qos.logback.core.rolling.RollingFileAppender">
		<Encoding>UTF-8</Encoding>
		<rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
			<FileNamePattern>./logs/clm/matchCalc/clm-web-%d{yyyy-M-d}.log
			</FileNamePattern>
			<MaxHistory>3</MaxHistory>
		</rollingPolicy>
		<encoder>
			<pattern>%d{yyyy-M-d HH:mm:ss SSS}|%t|%p|%m|%F|%L|%n
			</pattern>
		</encoder>
		<tiggeringPolicy
			class="ch.qos.logback.core.rolling.SizeBasedTriggeringPolicy">
			<MaxFileSize>500MB</MaxFileSize>
		</tiggeringPolicy>
	</appender>
	<!-- 异步输出 -->
	<appender name="ASYNC_68" class="ch.qos.logback.classic.AsyncAppender">
		<!-- 不丢失日志.默认的,如果队列的80%已满,则会丢弃TRACT、DEBUG、INFO级别的日志 -->
		<discardingThreshold>0</discardingThreshold>
		<!-- 更改默认的队列的深度,该值会影响性能.默认值为256 -->
		<queueSize>256</queueSize>
		<!-- 输出文件以及行号信息需要 stacktrace 获取 callerdata，因为性能原因 logback 的 AsyncAppender
			默认是不记录类名和行号 -->
		<includeCallerData>true</includeCallerData>
		<!-- 避免线程阻塞 -->
		<neverBlock>true</neverBlock>
		<!-- 添加附加的appender,最多只能添加一个 -->
		<appender-ref ref="matchCalc" />
	</appender>
	<!-- PDSService日志 start -->

	<appender name="matchCalcPds"
		class="ch.qos.logback.core.rolling.RollingFileAppender">
		<Encoding>UTF-8</Encoding>
		<rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
			<FileNamePattern>./logs/clm/matchCalcPds/clm-web-%d{yyyy-M-d}.log
			</FileNamePattern>
			<MaxHistory>3</MaxHistory>
		</rollingPolicy>
		<encoder>
			<pattern>%d{yyyy-M-d HH:mm:ss SSS}|%t|%p|%m|%F|%L|%n
			</pattern>
		</encoder>
		<tiggeringPolicy
			class="ch.qos.logback.core.rolling.SizeBasedTriggeringPolicy">
			<MaxFileSize>500MB</MaxFileSize>
		</tiggeringPolicy>
	</appender>
	<!-- 异步输出 -->
	<appender name="ASYNC_69" class="ch.qos.logback.classic.AsyncAppender">
		<!-- 不丢失日志.默认的,如果队列的80%已满,则会丢弃TRACT、DEBUG、INFO级别的日志 -->
		<discardingThreshold>0</discardingThreshold>
		<!-- 更改默认的队列的深度,该值会影响性能.默认值为256 -->
		<queueSize>256</queueSize>
		<!-- 输出文件以及行号信息需要 stacktrace 获取 callerdata，因为性能原因 logback 的 AsyncAppender
			默认是不记录类名和行号 -->
		<includeCallerData>true</includeCallerData>
		<!-- 避免线程阻塞 -->
		<neverBlock>true</neverBlock>
		<!-- 添加附加的appender,最多只能添加一个 -->
		<appender-ref ref="matchCalcPds" />
	</appender>
	<logger name="com.nci.tunan.clm.impl.imports.pds.impl" level="debug">
		<!-- <appender-ref ref="stdout2" /> -->
		<appender-ref ref="ASYNC_69" />
	</logger>
	<!-- 匹配理算日志 end -->

<!-- 自动分配批处理star -->
	<appender name="claimAutoAssign"
		class="ch.qos.logback.core.rolling.RollingFileAppender">
		<Encoding>UTF-8</Encoding>
		<rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
			<FileNamePattern>./logs/clm/claimAutoAssign/clm-web-%d{yyyy-M-d}.log
			</FileNamePattern>
			<MaxHistory> 30 </MaxHistory>
		</rollingPolicy>
		<encoder>
			<pattern>%d{yyyy-M-d HH:mm:ss SSS}|%t|%p|%m|%F|%L|%n
			</pattern>
		</encoder>
		<tiggeringPolicy
			class="ch.qos.logback.core.rolling.SizeBasedTriggeringPolicy">
			<MaxFileSize>500MB</MaxFileSize>
		</tiggeringPolicy>
	</appender>
	<!-- 异步输出 -->
	<appender name="ASYNC_70" class="ch.qos.logback.classic.AsyncAppender">
		<!-- 不丢失日志.默认的,如果队列的80%已满,则会丢弃TRACT、DEBUG、INFO级别的日志 -->
		<discardingThreshold>0</discardingThreshold>
		<!-- 更改默认的队列的深度,该值会影响性能.默认值为256 -->
		<queueSize>256</queueSize>
		<!-- 输出文件以及行号信息需要 stacktrace 获取 callerdata，因为性能原因 logback 的 AsyncAppender
			默认是不记录类名和行号 -->
		<includeCallerData>true</includeCallerData>
		<!-- 避免线程阻塞 -->
		<neverBlock>true</neverBlock>
		<!-- 添加附加的appender,最多只能添加一个 -->
		<appender-ref ref="claimAutoAssign" />
	</appender>
	<logger name="com.nci.tunan.clm.batch.autoAssignClaimTaskBatch" level="debug">
		<!-- <appender-ref ref="stdout2" /> -->
		<appender-ref ref="ASYNC_70" />
	</logger>
	<!-- 自动分配批处理end -->


	<!-- 事中质检抽取接口日志start -->
	<appender name="caseExtracting"
		class="ch.qos.logback.core.rolling.RollingFileAppender">
		<Encoding>UTF-8</Encoding>
		<rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
			<FileNamePattern>./logs/clm/middleCheckTask/clm-web-%d{yyyy-M-d}.log
			</FileNamePattern>
			<MaxHistory>3</MaxHistory>
		</rollingPolicy>
		<encoder>
			<pattern>%d{yyyy-M-d HH:mm:ss SSS}|%t|%p|%m|%F|%L|%n
			</pattern>
		</encoder>
		<tiggeringPolicy
			class="ch.qos.logback.core.rolling.SizeBasedTriggeringPolicy">
			<MaxFileSize>500MB</MaxFileSize>
		</tiggeringPolicy>
	</appender>
	<!-- 异步输出 -->
	<appender name="ASYNC_71" class="ch.qos.logback.classic.AsyncAppender">
		<!-- 不丢失日志.默认的,如果队列的80%已满,则会丢弃TRACT、DEBUG、INFO级别的日志 -->
		<discardingThreshold>0</discardingThreshold>
		<!-- 更改默认的队列的深度,该值会影响性能.默认值为256 -->
		<queueSize>256</queueSize>
		<!-- 输出文件以及行号信息需要 stacktrace 获取 callerdata，因为性能原因 logback 的 AsyncAppender
			默认是不记录类名和行号 -->
		<includeCallerData>true</includeCallerData>
		<!-- 避免线程阻塞 -->
		<neverBlock>true</neverBlock>
		<!-- 添加附加的appender,最多只能添加一个 -->
		<appender-ref ref="caseExtracting" />
	</appender>
	<logger name="com.nci.tunan.clm.impl.exports.ws" level="DEBUG">
		<!-- <appender-ref ref="stdout2" /> -->
		<appender-ref ref="ASYNC_71" />
	</logger>
	<logger name="com.nci.tunan.clm.impl.inspect.service" level="DEBUG">
		<!-- <appender-ref ref="stdout2" /> -->
		<appender-ref ref="ASYNC_71" />
	</logger>
	<logger name="com.nci.tunan.clm.impl.exports.ws.util" level="DEBUG">
		<!-- <appender-ref ref="stdout2" /> -->
		<appender-ref ref="ASYNC_71" />
	</logger>
	<logger name="com.nci.tunan.clm.impl.approve.service.impl"
		level="DEBUG">
		<!-- <appender-ref ref="stdout2" /> -->
		<appender-ref ref="ASYNC_71" />
	</logger>
	<!-- 事中质检抽取接口日志end -->

	<!-- 方法时间监控日志 start -->
	<appender name="methodTime"
		class="ch.qos.logback.core.rolling.RollingFileAppender">
		<Encoding>UTF-8</Encoding>
		<rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
			<FileNamePattern>./logs/clm/methodTime/clm-web-%d{yyyy-M-d}.log
			</FileNamePattern>
			<MaxHistory>3</MaxHistory>
		</rollingPolicy>
		<encoder>
			<pattern>%d{yyyy-M-d HH:mm:ss SSS}|%t|%p|%m|%F|%L|%n
			</pattern>
		</encoder>
		<tiggeringPolicy
			class="ch.qos.logback.core.rolling.SizeBasedTriggeringPolicy">
			<MaxFileSize>500MB</MaxFileSize>
		</tiggeringPolicy>
	</appender>
	<!-- 异步输出 -->
	<appender name="ASYNC_73" class="ch.qos.logback.classic.AsyncAppender">
		<!-- 不丢失日志.默认的,如果队列的80%已满,则会丢弃TRACT、DEBUG、INFO级别的日志 -->
		<discardingThreshold>0</discardingThreshold>
		<!-- 更改默认的队列的深度,该值会影响性能.默认值为256 -->
		<queueSize>256</queueSize>
		<!-- 输出文件以及行号信息需要 stacktrace 获取 callerdata，因为性能原因 logback 的 AsyncAppender
			默认是不记录类名和行号 -->
		<includeCallerData>true</includeCallerData>
		<!-- 避免线程阻塞 -->
		<neverBlock>true</neverBlock>
		<!-- 添加附加的appender,最多只能添加一个 -->
		<appender-ref ref="methodTime" />
	</appender>
	<logger name="com.nci.tunan.clm.impl.util" level="DEBUG">
		<!-- <appender-ref ref="stdout2" /> -->
		<appender-ref ref="ASYNC_73" />
	</logger>
	<!-- 方法时间监控日志 end -->

	<!-- CLM interface log start -->
	<appender name="CLM"
		class="ch.qos.logback.core.rolling.RollingFileAppender">
		<Encoding>UTF-8</Encoding>
		<rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
			<FileNamePattern>./logstan/clm/CLM-%d{yyyy-M-d}.log</FileNamePattern>
			<MaxHistory>3</MaxHistory>
		</rollingPolicy>
		<encoder>
			<pattern>%d{yyyy-M-d HH:mm:ss SSS}|%t|%p|%m|%F|%L|%n</pattern>
		</encoder>
		<tiggeringPolicy
			class="ch.qos.logback.core.rolling.SizeBasedTriggeringPolicy">
			<MaxFileSize>500MB</MaxFileSize>
		</tiggeringPolicy>
	</appender>
	<!-- 异步输出 -->
	<appender name="ASYNC_CLM" class="ch.qos.logback.classic.AsyncAppender">
		<!-- 不丢失日志.默认的,如果队列的80%已满,则会丢弃TRACT、DEBUG、INFO级别的日志 -->
		<discardingThreshold>0</discardingThreshold>
		<!-- 更改默认的队列的深度,该值会影响性能.默认值为256 -->
		<queueSize>256</queueSize>
		<!-- 输出文件以及行号信息需要 stacktrace 获取 callerdata，因为性能原因 logback 的 AsyncAppender
			默认是不记录类名和行号 -->
		<includeCallerData>true</includeCallerData>
		<!-- 避免线程阻塞 -->
		<neverBlock>true</neverBlock>
		<!-- 添加附加的appender,最多只能添加一个 -->
		<appender-ref ref="CLM" />
	</appender>
	<logger name="CLM" level="debug">
		<appender-ref ref="ASYNC_CLM" />
	</logger>
	<!-- CLM interface log end -->
<!-- 新增页面请求、接口调用日志输出start -->
  	<appender name="requestLogs" class="ch.qos.logback.core.rolling.RollingFileAppender">
		<!--控制日志安全写入文件-->
		<prudent>true</prudent>
		<rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
			<FileNamePattern>./requestlog/pas-requestlog-%d{yyyy-M-d}.log</FileNamePattern>
			<MaxHistory>5</MaxHistory>
			<TimeBasedFileNamingAndTriggeringPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedFNATP">
            	<MaxFileSize>500MB</MaxFileSize>
            </TimeBasedFileNamingAndTriggeringPolicy>
		</rollingPolicy>
		<!--添加控制log文件大小 -->
		<tiggeringPolicy
			class="ch.qos.logback.core.rolling.SizeBasedTriggeringPolicy">
			<MaxFileSize>500MB</MaxFileSize>
		</tiggeringPolicy>
		<encoder>
			<pattern>%d{yyyy-M-d HH:mm:ss SSS}|%t|%p|%m|%F|%L|%n
			</pattern>
		</encoder>
	</appender>
	<!-- 异步输出 -->
	<appender name="ASYNC_REQ" class="ch.qos.logback.classic.AsyncAppender">
		<!-- 不丢失日志.默认的,如果队列的80%已满,则会丢弃TRACT、DEBUG、INFO级别的日志 -->
		<discardingThreshold>0</discardingThreshold>
		<!-- 更改默认的队列的深度,该值会影响性能.默认值为256 -->
		<queueSize>1024</queueSize>
		<!-- 输出文件以及行号信息需要 stacktrace 获取 callerdata，因为性能原因 logback 的 AsyncAppender
			默认是不记录类名和行号 -->
		<includeCallerData>true</includeCallerData>
		<!-- 避免线程阻塞 -->
		<neverBlock>true</neverBlock>
		<!-- 添加附加的appender,最多只能添加一个 -->
		<appender-ref ref="requestLogs" />
	</appender>
  	<logger name="com.nci.udmp.util.requestLogs.RequestLogUtil" level="DEBUG" additivity="false">
     	 <appender-ref ref="ASYNC_REQ" />
 	</logger>
 	<!-- 新增页面请求、接口调用日志输出end -->
	<!-- ######################################理赔日志输出 end###################################### -->

    <!-- 上海医保重新投保请求批处理start -->
	<appender name="CanReinsureJobLogFile"
		class="ch.qos.logback.core.rolling.RollingFileAppender">
		<Encoding>UTF-8</Encoding>
		<rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
			<FileNamePattern>./logstan/CanReinsureJobLogFile/logtest-%d{yyyy-M-d}.log
			</FileNamePattern>
			<MaxHistory>3</MaxHistory>
		</rollingPolicy>
		<encoder>
			<pattern>%d{yyyy-M-d HH:mm:ss SSS}|%t|%p|%m|%F|%L|%n
			</pattern>
		</encoder>
		<tiggeringPolicy
			class="ch.qos.logback.core.rolling.SizeBasedTriggeringPolicy">
			<MaxFileSize>500MB</MaxFileSize>
		</tiggeringPolicy>
	</appender>
	<!-- 异步输出 -->
	<appender name="ASYNC_80" class="ch.qos.logback.classic.AsyncAppender">
		<!-- 不丢失日志.默认的,如果队列的80%已满,则会丢弃TRACT、DEBUG、INFO级别的日志 -->
		<discardingThreshold>0</discardingThreshold>
		<!-- 更改默认的队列的深度,该值会影响性能.默认值为256 -->
		<queueSize>256</queueSize>
		<!-- 输出文件以及行号信息需要 stacktrace 获取 callerdata，因为性能原因 logback 的 AsyncAppender
			默认是不记录类名和行号 -->
		<includeCallerData>true</includeCallerData>
		<!-- 避免线程阻塞 -->
		<neverBlock>true</neverBlock>
		<!-- 添加附加的appender,最多只能添加一个 -->
		<appender-ref ref="CanReinsureJobLogFile" />
	</appender>
	<logger name="com.nci.tunan.pa.batch.canReinsure" level="debug">
		<!-- <appender-ref ref="stdout2" /> -->
		<appender-ref ref="ASYNC_80" />
	</logger>
	<!-- 上海医保重新投保请求批处理 end -->

	<!-- 记录上海医保报文日志  add by lilj start #104_25926 -->
	<!-- 上海医保报文日志输出到文件 -->
	<appender name="SHMEDICALFILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
		<!-- 控制日志安全写入文件 -->
		<prudent>true</prudent>
		<!-- 控制日志输出编码 -->
		<Encoding>UTF-8</Encoding>
		<!-- 基于日期的滚动输出 -->
		<rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
			<!-- 输出路径的配置 -->
			<FileNamePattern>./logs/shyb/shmedical_logtest-%d{yyyy-M-d}.log</FileNamePattern>
			<!-- 日志保留天数 -->
			<MaxHistory>90</MaxHistory>
			<TimeBasedFileNamingAndTriggeringPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedFNATP">
				<MaxFileSize>50MB</MaxFileSize>
			</TimeBasedFileNamingAndTriggeringPolicy>
		</rollingPolicy>
		<!-- 输出格式的配置，同控制台输出 -->
		<encoder>
			<!-- 格式化输出：%d表示日期，%t:表示线程名，%p:输出日志级别,%m：日志消息，%F:输出执行记录请求的java源文件名，%L:输出执行日志请求的行号，%n是换行符 -->
			<pattern>%d{yyyy-M-d HH:mm:ss SSS}|%t|%p|%m|%F|%L|%n</pattern>
		</encoder>
		<!-- 日志文件最大的大小 -->
		<tiggeringPolicy class="ch.qos.logback.core.rolling.SizeBasedTriggeringPolicy">
			<MaxFileSize>50MB</MaxFileSize>
		</tiggeringPolicy>
		<!-- 过滤器，只记录INFO级别的日志 -->
		<filter class="ch.qos.logback.classic.filter.LevelFilter">
			<level>INFO</level>
			<onMatch>ACCEPT</onMatch>
			<onMismatch>DENY</onMismatch>
		</filter>
	</appender>
	<!-- 上海医保报文异步输出 -->
	<appender name="ASYNC_SHMEDICALFILE" class="ch.qos.logback.classic.AsyncAppender">
		<!-- 不丢失日志.默认的,如果队列的80%已满,则会丢弃TRACT、DEBUG、INFO级别的日志 -->
		<discardingThreshold>0</discardingThreshold>
		<!-- 更改默认的队列的深度,该值会影响性能.默认值为256 -->
		<queueSize>512</queueSize>
		<!-- 输出文件以及行号信息需要 stacktrace 获取 callerdata，因为性能原因 logback 的 AsyncAppender
			默认是不记录类名和行号 -->
		<includeCallerData>true</includeCallerData>
		<!-- 避免线程阻塞 -->
		<neverBlock>true</neverBlock>
		<!-- 添加附加的appender,最多只能添加一个 -->
		<appender-ref ref="SHMEDICALFILE" />
	</appender>
	<logger name="com.nci.tunan.cs.shhealthcare.consumeUcc.ShMedicalLogUccImpl" level="INFO" additivity="false">
		<appender-ref ref="ASYNC_SHMEDICALFILE" />
	</logger>
	<logger name="com.nci.tunan.cap.shhealthcare.impl.ucc.impl.ShMedicalLogUccImpl" level="INFO" additivity="false">
		<appender-ref ref="ASYNC_SHMEDICALFILE" />
	</logger>
	<logger name="com.nci.tunan.clm.shhealthcare.ucc.ShMedicalLogUccImpl" level="INFO" additivity="false">
		<appender-ref ref="ASYNC_SHMEDICALFILE" />
	</logger>
	<!-- 记录上海医保报文日志 add by lilj end #104_25926 -->

	<!-- 新增sql耗时日志输出start -->
  	<appender name="sqlPrinttLog" class="ch.qos.logback.core.rolling.RollingFileAppender">
		<!--控制日志安全写入文件-->
		<prudent>true</prudent>
		<rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
			<FileNamePattern>./sqlPrinttLog/pas-sqlPrinttLog-%d{yyyy-M-d}.log</FileNamePattern>
			<MaxHistory>5</MaxHistory>	
			<TimeBasedFileNamingAndTriggeringPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedFNATP">
            	<MaxFileSize>500MB</MaxFileSize>
            </TimeBasedFileNamingAndTriggeringPolicy>	
		</rollingPolicy>
		<!--添加控制log文件大小 -->
		<tiggeringPolicy
			class="ch.qos.logback.core.rolling.SizeBasedTriggeringPolicy">
			<MaxFileSize>500MB</MaxFileSize>
		</tiggeringPolicy>
		<encoder>
			<pattern>%d{yyyy-M-d HH:mm:ss SSS}|%t|%p|%m|%F|%L|%n
			</pattern>
		</encoder>
	</appender>
	<!-- 异步输出 -->
	<appender name="ASYNC_SQL" class="ch.qos.logback.classic.AsyncAppender">
		<!-- 不丢失日志.默认的,如果队列的80%已满,则会丢弃TRACT、DEBUG、INFO级别的日志 -->
		<discardingThreshold>0</discardingThreshold>
		<!-- 更改默认的队列的深度,该值会影响性能.默认值为256 -->
		<queueSize>1024</queueSize>
		<!-- 输出文件以及行号信息需要 stacktrace 获取 callerdata，因为性能原因 logback 的 AsyncAppender 
			默认是不记录类名和行号 -->
		<includeCallerData>true</includeCallerData>
		<!-- 避免线程阻塞 -->
		<neverBlock>true</neverBlock>
		<!-- 添加附加的appender,最多只能添加一个 -->
		<appender-ref ref="sqlPrinttLog" />
	</appender>
  	<logger name="com.nci.udmp.util.sqlPrinttLog" level="DEBUG" additivity="false">
     	 <appender-ref ref="ASYNC_SQL" />  
 	</logger>
 	<!-- 新增sql耗时日志输出end -->

	<!-- 自动核保并行start -->
	<appender name="autoUWRele"
			  class="ch.qos.logback.core.rolling.RollingFileAppender">
		<Encoding>UTF-8</Encoding>
		<rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
			<FileNamePattern>./logs/autoUW/AutoUW-%d{yyyy-M-d}.log
			</FileNamePattern>
			<MaxHistory>30</MaxHistory>
		</rollingPolicy>
		<encoder>
			<pattern>%d{yyyy-M-d HH:mm:ss SSS}|%t|%p|%m|%F|%L|%n</pattern>
		</encoder>
		<tiggeringPolicy
				class="ch.qos.logback.core.rolling.SizeBasedTriggeringPolicy">
			<MaxFileSize>500MB</MaxFileSize>
		</tiggeringPolicy>
	</appender>
	<!-- 异步输出 -->
	<appender name="ASYNCauto" class="ch.qos.logback.classic.AsyncAppender">
		<!-- 不丢失日志.默认的,如果队列的80%已满,则会丢弃TRACT、DEBUG、INFO级别的日志 -->
		<discardingThreshold>0</discardingThreshold>
		<!-- 更改默认的队列的深度,该值会影响性能.默认值为256 -->
		<queueSize>256</queueSize>
		<!-- 输出文件以及行号信息需要 stacktrace 获取 callerdata，因为性能原因 logback 的 AsyncAppender
			默认是不记录类名和行号 -->
		<includeCallerData>true</includeCallerData>
		<!-- 避免线程阻塞 -->
		<neverBlock>true</neverBlock>
		<!-- 添加附加的appender,最多只能添加一个 -->
		<appender-ref ref="autoUWRele" />
	</appender>
	<logger name="autoUWRele" level="debug">
		<appender-ref ref="ASYNCauto" />
	</logger>
	<!-- 自动核保并行 end -->
	
	<!-- 保全全局日志start -->
	<appender name="SysLog"
			  class="ch.qos.logback.core.rolling.RollingFileAppender">
		<Encoding>UTF-8</Encoding>
		<rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
			<FileNamePattern>./logs/sysLog/sysLog-%d{yyyy-M-d}.log
			</FileNamePattern>
			<MaxHistory>30</MaxHistory>
		</rollingPolicy>
		<encoder>
			<pattern>%d{yyyy-M-d HH:mm:ss SSS}|%t|%p|%m|%F|%L|%n</pattern>
		</encoder>
		<tiggeringPolicy
				class="ch.qos.logback.core.rolling.SizeBasedTriggeringPolicy">
			<MaxFileSize>500MB</MaxFileSize>
		</tiggeringPolicy>
	</appender>
	<!-- 异步输出 -->
	<appender name="ASYNCauto" class="ch.qos.logback.classic.AsyncAppender">
		<!-- 不丢失日志.默认的,如果队列的80%已满,则会丢弃TRACT、DEBUG、INFO级别的日志 -->
		<discardingThreshold>0</discardingThreshold>
		<!-- 更改默认的队列的深度,该值会影响性能.默认值为256 -->
		<queueSize>256</queueSize>
		<!-- 输出文件以及行号信息需要 stacktrace 获取 callerdata，因为性能原因 logback 的 AsyncAppender
			默认是不记录类名和行号 -->
		<includeCallerData>true</includeCallerData>
		<!-- 避免线程阻塞 -->
		<neverBlock>true</neverBlock>
		<!-- 添加附加的appender,最多只能添加一个 -->
		<appender-ref ref="SysLog" />
	</appender>
	<logger name="SysLog" level="debug">
		<appender-ref ref="ASYNCauto" />
	</logger>
	<!-- 保全全局日志 end -->
</configuration>