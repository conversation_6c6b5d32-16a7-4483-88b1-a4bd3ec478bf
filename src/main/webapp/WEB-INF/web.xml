<?xml version="1.0" encoding="UTF-8"?>
<web-app xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xmlns="http://java.sun.com/xml/ns/javaee" xmlns:web="http://java.sun.com/xml/ns/javaee/web-app_2_5.xsd"
	xsi:schemaLocation="http://java.sun.com/xml/ns/javaee http://java.sun.com/xml/ns/javaee/web-app_3_0.xsd"
	id="WebApp_ID" version="3.0">
	<!-- web.xml禁止修改，如需要配置，请在META-INF/web-fragment.xml文件中配置 -->
	<resource-ref>
		<res-ref-name>jdbc/UdmpJndiDataSource</res-ref-name>
		<res-type>javax.sql.DataSource</res-type>
		<res-auth>Container</res-auth>
	</resource-ref>
	
	<resource-ref>
		<res-ref-name>jdbc/UdmpCommonDataSource</res-ref-name>
		<res-type>javax.sql.DataSource</res-type>
		<res-auth>Container</res-auth>
	</resource-ref>
	
	<resource-ref>
		<res-ref-name>jdbc/UdmpDataSource</res-ref-name>
		<res-type>javax.sql.DataSource</res-type>
		<res-auth>Container</res-auth>
	</resource-ref>
	 
	<!-- 契约数据源 -->
	<resource-ref>
		<res-ref-name>jdbc/NBSJndiDataSource</res-ref-name>
		<res-type>javax.sql.DataSource</res-type>
		<res-auth>Container</res-auth>
	</resource-ref>
	
    <!-- 接入渠道链接 -->
	<resource-ref>
		<res-ref-name>jdbc/BankDataSource</res-ref-name>
		<res-type>javax.sql.DataSource</res-type>
		<res-auth>Container</res-auth>
	</resource-ref>
	
	<!-- START  31683 并行开发任务 -->
	<!-- /**针对并行开发的代码内容*/ -->
	<!-- 老核心链接 -->
	<!-- <resource-ref>
		<res-ref-name>jdbc/OldDataSource</res-ref-name>
		<res-type>javax.sql.DataSource</res-type>
		<res-auth>Container</res-auth>
	</resource-ref> -->
	<!-- END     31683 并行开发任务 -->
</web-app>