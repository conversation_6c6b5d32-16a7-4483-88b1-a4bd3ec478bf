
<%@page import="java.sql.DriverManager"%>
<%@page language="java" contentType="text/html; charset=utf-8"
	pageEncoding="utf-8"%>
<%@ page
	import="java.io.IOException,java.io.PrintWriter,java.sql.Connection,java.sql.PreparedStatement,java.sql.ResultSet,java.util.Map.Entry,
			java.sql.SQLException,java.util.ArrayList,java.util.HashMap,java.util.LinkedHashMap,java.util.List,java.util.Map,
			java.util.TreeMap,com.nci.udmp.framework.exception.support.SystemUtils,com.nci.udmp.framework.para.*,
			java.util.*,com.nci.udmp.app.bizservice.bo.*,com.nci.udmp.framework.util.*"%>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html>
<head>
<meta http-equiv="X-UA-Compatible" content="IE=9"></meta>
<title></title>
<script src="./udmp/md5/md5.js"   type="text/javascript"></script>
<script src="./udmp/scripts/common.js"   type="text/javascript"></script>
<script src="./udmp/idea/idea.js"   type="text/javascript"></script>
<script src="./udmp/dwz/js/jquery-1.7.2.min.js"></script>
<link href="css/index.css"   rel="stylesheet" />

</head>
<body>
	<div class="bigbox">
		<div class="title">
			<span class="title_text"><img src="image/xxx.png" />
				&nbsp;&nbsp;&nbsp;新核心统一登录页面</span>
		</div>
		<div class="head">
			<img src="image/NCI.png" class="nci" />
		</div>
		<div class="user">
			<div class="user_input">
				用户名:  <input style="" id="userName" value="" placeholder="请输入用户名" />
				密 码:  <input style="" id="userpwd" value="" placeholder="请输入密码" type="password" />
				<h6 id="warnMessage" style="visibility: hidden;">此处用于显示错误信息！例如：您已输入错误三次，请联系管理员进行密码解锁。</h6>
			</div>
		</div>
		<div class="ICON">
			<div>
				<ul>
					<li><a id="nb" class="btn40">新契约</a></li>
					<li><a id="pa" class="btn40">保单管理</a></li>
					<li><a id="cap" class="btn40">收付费</a></li>
					<li><a id="uw" class="btn40">核保</a></li>
					<li><a id="clm" class="btn40">理赔</a></li>
					<li><a id="css" class="btn40">柜面</a></li>
					<li style="visibility: hidden;"><a class="qyfwzx">企业服务总线</a></li>
				</ul>
			</div>
		</div>
	</div>

	<script>
		var x=0;
		 var vin ="";
			$(function(){
				debugger;
				$(".btn40").click(function(){
					var systemId=$(this).attr("id");
					var sysId = getURLById($(this).attr("id"));
					if(!sysId){
						var val = $(this).val();
						if(val.indexOf("登录") != -1){
							val = val.substring(0,val.indexOf("登录"));
						}
						alert("没有找到对应的url,请先为" + val + "的服务器地址赋值！");
						return false;
					}
					var userName = $("#userName").val();
					var userPwd = $("#userpwd").val();
					var checkFalg="";
					var tablePrev="";
					if(userName == ""){
						alert("请输入用户名!");
						return;
					}
					if(userPwd == ""){
						alert("请输入密码!");
						return;
					}
					if(x==0){
						sysId+="&&userFlag=1";
					
					}else{
						sysId+="&&userFlag=2";
						
					}
					screenWidth = window.screen.width - 10;
					screenHeight = window.screen.height;
					$.post('clickLogin_loginAction.action', {userid:userName,orderUserid:"",pwd:encryptString(userPwd),checkcode:hex_md5(""),checkFalg:"1",tablePrev: "APP___PAS__DBUSER."},function(data){
						if(data.flag==300){
							alert(data.msg);
							return;
							//$("#warnMessage").text(data.msg);
							//$("#warnMessage").css("visibility","visible");
						}else  if(data.flag==200 || data.flag==201){
							// 登录成功，将用户存入Cookie
							document.cookie="git_userid="+data.id;
							document.cookie="git_username="+data.name;
							if($(this).attr("id") == "batch"){
								if(sysId.indexOf("?") == -1){
									sysId += "?userName=SYSADMIN" + "&&userPwd=111111" + "&&flag=auto";
								} else{
									sysId += "&&userName=SYSADMIN" + "&&userPwd=111111" + "&&flag=auto";
								}
							}else{
								if(sysId.indexOf("?") == -1){
										sysId += "?userName=" + userName + "&&userPwd=" + userPwd + "&&flag=auto" + "&&ticket=" + "<%=request.getParameter("ticket")%>";
								} else{
										sysId += "&&userName=" + userName + "&&userPwd=" + userPwd + "&&flag=auto"  + "&&ticket=" + "<%=request.getParameter("ticket")%>";
									  }
							}
							console.log("我可以跳转了啊============="+sysId);
								if (x == 0) {
									vin=window.open(sysId,'','width='+ screenWidth+ ',height='+ screenHeight+ ',left=0,top=0,titlebar=no,menubar=no,toolbar=no,location=no,status=no,scrollbars=yes,resizable=yes');
									x++;
								} else {
									vin=vin.close();
									vin=window.open(sysId,'','width='+ screenWidth+ ',height='+ screenHeight+ ',left=0,top=0,titlebar=no,menubar=no,toolbar=no,location=no,status=no,scrollbars=yes,resizable=yes');
								}
						}else  if(data.flag==202 || data.flag==203){
							debugger;
							var modpasswd = 0;
							//若用户选择修改密码，则登录成功后弹出密码修改对话框
			                if(confirm(data.msg)) {
			                	modpasswd = 1;
							}
							if (x!=0) {
								console.log("错误了"+vin);
								vin=vin.close();
							}
							var s='main.action?modpasswd='+modpasswd+'&&flag=updatePasswd';
							console.log("跳转的"+s);
							vin=window.open(s,'myWindow','width='+ screenWidth+ ',height='+ screenHeight+ ',left=0,top=0,titlebar=no,menubar=no,toolbar=no,location=no,status=no,scrollbars=yes,resizable=yes');
							x++;
						} else{
							if(data.flag==3) {
								$("#pwd").val("");
							}
							alert(data.msg);
							if (x!=0) {
								vin=vin.close();
							}
							x=0;
							return;
						}
					},"json");
					
					
						});
		});
		//http://localhost:9080/ls/main.action
		function getURLById(id) {
			debugger;
			//测试人员进行测试时，可以对以下路径进行更改
			//首先将localhost改为对应的was服务器地址
			//若端口号不正确，则将端口号进行更改
			//若上下文不正确，则将上下文进行更改
			//var ss = "http://**********:8080/ls/";
			switch (id) {
		    case "nb":
				return "http://**********:9082/ls/main.action?tablePrev=APP___NB__DBUSER.";
				break;
			case "uw":
				return "http://**********:9082/ls/main.action?tablePrev=APP___UW__DBUSER.";
				break;
			case "css":
				return  "http://**********:9082/css/cssMain.action?tablePrev=APP___CSS__DBUSER.";
				break;
			case "pa":
				return "http://**********:9082/ls/main.action?tablePrev=APP___PAS__DBUSER.";
				break;
			case "clm":
				return "http://**********:9082/ls/main.action?tablePrev=APP___CLM__DBUSER.";
				break;
			case "cap":
				return "http://**********:9082/ls/main.action?tablePrev=APP___CAP__DBUSER.";
				break; 
			default:
				return false;
			}
		}

		function closeNow() {
			window.close();
		}

		//校验安全证书是否正确安装
		try {
			if (window.ActiveXObject || 'ActiveXObject' in window) {
				document
						.writeln("<OBJECT classid='CLSID:3F367B74-92D9-4C5E-AB93-234F8A91D5E6' height=1 id=XTXAPP  style='HEIGHT: 1px; display: none; LEFT: 10px; TOP: 28px; WIDTH: 1px' VIEWASTEXT>");
				document.writeln("</OBJECT>");
				XTXAPP.SOF_GetVersion();
			} else {
				document
						.writeln("<embed id=XTXAPP0 type=application/x-xtx-axhost clsid={3F367B74-92D9-4C5E-AB93-234F8A91D5E6} event_OnUsbkeyChange=OnUsbKeyChange width=1 height=1 style='display: none' />");
				XTXAPP = document.getElementById("XTXAPP0");
			}
			XTXAPP.SOF_GetVersion();
		} catch (e) {
			//	alert("请检查证书应用环境是否正确安装!");
			console.info("请检查证书应用环境是否正确安装!")
		}
	</script>

	<!-- IE浏览器中响应设备插拔事件 -->
	<script language=javascript event=OnUsbKeyChange for=XTXAPP>
		if (GetDeviceCount() == 0) {
			window.open('', '_top');
			window.top.close();
		}
	</script>
</body>
</html>
