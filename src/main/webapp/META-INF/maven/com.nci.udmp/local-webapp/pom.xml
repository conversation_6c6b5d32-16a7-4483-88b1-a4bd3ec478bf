<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<artifactId>local-webapp</artifactId>
	<packaging>war</packaging>
	<parent>
		<groupId>com.nci.udmp</groupId>
		<artifactId>udmp-parent</artifactId>
		<version>0.5.0</version>
		<relativePath>../parent</relativePath>
	</parent>
	<build>
		<plugins>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-dependency-plugin</artifactId>
			</plugin>
			<plugin>
				<groupId>org.apache.tomcat.maven</groupId>
				<artifactId>tomcat7-maven-plugin</artifactId>
<!-- 				<configuration> -->
<!-- 					<port>9090</port> -->
<!-- 				</configuration> -->
			</plugin>
		</plugins>
	</build>
	<dependencies>
		<dependency>
			<groupId>javax.servlet</groupId>
			<artifactId>javax.servlet-api</artifactId>
			<scope>provided</scope>
		</dependency>
		<dependency>
			<groupId>javax.servlet.jsp</groupId>
			<artifactId>javax.servlet.jsp-api</artifactId>
			<scope>provided</scope>
		</dependency>
		<dependency>
			<groupId>com.nci.udmp</groupId>
			<artifactId>udmp-web</artifactId>
			<version>0.5.0</version>
		</dependency>
		<dependency>
			<artifactId>nb-web</artifactId>
			<groupId>com.nci.core.nb</groupId>
			<version>${project.version}</version>
		</dependency>
		<dependency>
			<groupId>com.nci.core.nb</groupId>
			<artifactId>nb-interface</artifactId>
			<version>${project.version}</version>
		</dependency>
		<dependency>
			<groupId>com.nci.core.nb</groupId>
			<artifactId>nb-impl</artifactId>
			<version>${project.version}</version>
		</dependency>
		<dependency>
			<groupId>com.nci.core.uw</groupId>
			<artifactId>uw-web</artifactId>
			<version>${project.version}</version>
		</dependency>
		<dependency>
			<groupId>com.nci.core.uw</groupId>
			<artifactId>uw-impl</artifactId>
			<version>${project.version}</version>
		</dependency>
		<dependency>
			<groupId>com.nci.core.uw</groupId>
			<artifactId>uw-interface</artifactId>
			<version>${project.version}</version>
		</dependency>
		<dependency>
			<artifactId>commonbiz-impl</artifactId>
			<groupId>com.nci.core.common</groupId>
			<version>${project.version}</version>
		</dependency>
		<dependency>
			<artifactId>commonbiz-web</artifactId>
			<groupId>com.nci.core.common</groupId>
			<version>${project.version}</version>
		</dependency>
		<dependency>
			<artifactId>commonbiz-interface</artifactId>
			<groupId>com.nci.core.common</groupId>
			<version>${project.version}</version>
		</dependency>
	</dependencies>									
</project>